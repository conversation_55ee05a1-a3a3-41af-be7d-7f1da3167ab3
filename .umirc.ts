import { defineConfig } from 'umi';

export default defineConfig({
  routes: [{ path: '/', component: 'index' }],
  base:
    process.env.NODE_ENV === 'production'
      ? '/cloud-dictionary-management/'
      : '/',
  publicPath:
    process.env.NODE_ENV === 'production'
      ? '/cloud-dictionary-management/'
      : '/',
  hash: true,
  npmClient: 'yarn',
  title: '信息字典管理',
  outputPath: 'cloud-dictionary-management',
  tailwindcss: {},
  // 配置额外的 Umi 插件。
  plugins: ['@umijs/plugins/dist/tailwindcss'],
  define: {
    'process.env': {
      ENV_ID: 'begingroup-1g6s82e205f983c0',
      API_BASE_URL:
        // 'https://begingroup-1g6s82e205f983c0-**********.ap-shanghai.app.tcloudbase.com',
        'https://biging.cn',
    },
  },
  // 代理配置
  proxy: {
    '/bmc-cloud-service': {
      target: 'https://wegooo.cn',
      changeOrigin: true,
    },
    '/account': {
      target: 'https://biging.cn',
      changeOrigin: true,
    },
    '/dictionary': {
      target: 'https://biging.cn',
      changeOrigin: true,
    },
  },
  // 主题配置
  theme: {
    '@primary-color': '#2b6bff',
  },
  //修复 esbuild 压缩器自动引入的全局变量导致的命名冲突问题。
  esbuildMinifyIIFE: true,
  fastRefresh: true,
  chainWebpack: function (config, { webpack }) {},
});
