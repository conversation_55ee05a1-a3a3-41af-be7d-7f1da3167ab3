# 字典数据多租户智能同步系统

## 🎯 功能概述

本系统实现了字典数据的多租户智能同步功能，支持：

- **ID隔离保护**：确保每个租户维护独立的ID体系，避免ID冲突
- **智能变更检测**：自动识别新增、修改、删除、启用/禁用等变更类型
- **冲突解决机制**：支持多种冲突解决策略
- **增量同步**：只同步发生变更的数据，提高效率
- **关系重建**：自动处理父子关系的映射和重建

## 🏗️ 系统架构

### 核心模块

1. **sync-mapping.js** - 同步映射管理
2. **change-detector.js** - 变更检测引擎
3. **conflict-resolver.js** - 冲突解决器
4. **index.js** - 主同步逻辑

### 数据模型

#### dictionary_sync_mapping 表结构
```sql
{
  _id: String,                    // 映射记录ID
  source_tenant_id: String,       // 源租户ID
  target_tenant_id: String,       // 目标租户ID
  source_record_id: String,       // 源记录ID
  target_record_id: String,       // 目标记录ID
  business_key: String,           // 业务唯一标识
  sync_version: Number,           // 同步版本号
  last_sync_time: Date,          // 最后同步时间
  status: String,                // 状态 (ACTIVE/DELETED)
  created_at: Date,              // 创建时间
  updated_at: Date               // 更新时间
}
```

## 🔄 同步流程

### 1. 数据获取阶段
- 获取源租户和目标租户的字典数据
- 获取现有的同步映射关系

### 2. 变更检测阶段
- 基于业务标识符进行数据对比
- 检测新增、修改、删除、状态变更
- 生成变更摘要报告

### 3. 冲突检测阶段
- 识别并发修改冲突
- 检测目标数据被修改的情况
- 生成冲突报告

### 4. 冲突解决阶段
- 根据策略自动解决冲突
- 支持源优先、目标优先、智能合并等策略

### 5. 同步执行阶段
- 执行创建、更新、删除操作
- 维护同步映射关系
- 重建父子关系

## 🎛️ 使用方式

### 调用示例
```javascript
const syncData = {
  source_tenant_id: "tenant_a",
  target_tenants: ["tenant_b", "tenant_c"],
  dictionary_groups: ["group_1", "group_2"],
  sync_mode: "INCREMENTAL_SYNC",
  conflict_resolution: "SOURCE_PRIORITY"
};

const result = await cloudbaseApp.callFunction({
  name: 'information-dictionary-sync',
  data: {
    action: 'distribute',
    data: syncData
  }
});
```

### 返回结果
```javascript
{
  code: 0,
  message: "智能同步分发完成",
  data: {
    results: [
      {
        target_tenant_id: "tenant_b",
        success: true,
        message: "智能同步完成",
        summary: {
          total: 15,
          created: 5,
          updated: 8,
          deleted: 2
        },
        conflicts: [],
        syncResults: {
          created: 5,
          updated: 8,
          deleted: 2,
          errors: []
        }
      }
    ],
    total_targets: 2,
    success_count: 2,
    failed_count: 0
  }
}
```

## 🔧 配置选项

### 同步模式
- `FULL_SYNC` - 全量同步（清空目标数据后重新同步）
- `INCREMENTAL_SYNC` - 增量同步（只同步变更数据）
- `SELECTIVE_SYNC` - 选择性同步（同步指定分组）

### 冲突解决策略
- `SOURCE_PRIORITY` - 源数据优先
- `TARGET_PRIORITY` - 目标数据优先
- `MANUAL_RESOLVE` - 手动解决
- `MERGE` - 智能合并

## 🛡️ 安全特性

1. **ID隔离**：每个租户独立的ID空间
2. **数据隔离**：基于tenant_id的严格数据隔离
3. **版本控制**：同步版本号追踪
4. **软删除**：删除操作使用状态标记而非物理删除
5. **事务安全**：关键操作使用事务保护

## 📊 监控指标

- 同步成功率
- 冲突检测率
- 数据一致性检查
- 性能指标（同步耗时、数据量等）

## 🚀 部署说明

1. 确保云数据库中存在 `dictionary_sync_mapping` 表
2. 部署云函数到 CloudBase 环境
3. 配置相应的数据库权限
4. 测试同步功能

## 🔍 故障排查

### 常见问题
1. **ID冲突**：检查业务标识符生成逻辑
2. **关系丢失**：检查父子关系重建逻辑
3. **同步失败**：查看错误日志和冲突报告
4. **性能问题**：检查数据量和索引配置

### 日志分析
- 查看云函数执行日志
- 分析冲突报告
- 监控同步映射表状态
