/**
 * 数据变更检测模块
 * 用于检测源数据与目标数据之间的差异
 */

const { generateBusinessKey } = require('./sync-mapping');

/**
 * 变更类型枚举
 */
const CHANGE_TYPES = {
  CREATED: 'CREATED',     // 新增
  UPDATED: 'UPDATED',     // 修改
  DISABLED: 'DISABLED',   // 禁用
  ENABLED: 'ENABLED',     // 启用
  DELETED: 'DELETED'      // 删除
};

/**
 * 检测数据变更
 * @param {Array} sourceRecords - 源数据记录
 * @param {Array} targetRecords - 目标数据记录
 * @param {Array} syncMappings - 同步映射关系
 * @param {string} lastSyncTime - 上次同步时间
 */
function detectChanges(sourceRecords, targetRecords, syncMappings, lastSyncTime) {
  // 构建映射索引
  const mappingIndex = buildMappingIndex(syncMappings);
  const targetIndex = buildTargetIndex(targetRecords);
  const sourceIndex = buildSourceIndex(sourceRecords);

  const changes = [];

  // 1. 检测源数据的变更
  sourceRecords.forEach(sourceRecord => {
    const businessKey = generateBusinessKey(sourceRecord);
    const mapping = mappingIndex[businessKey];
    
    if (!mapping) {
      // 新增的记录
      changes.push({
        type: CHANGE_TYPES.CREATED,
        businessKey,
        sourceRecord,
        targetRecord: null,
        mapping: null,
        reason: '源数据中存在新记录'
      });
    } else {
      const targetRecord = targetIndex[mapping.target_record_id];
      
      if (!targetRecord) {
        // 目标记录已被删除，需要重新创建
        changes.push({
          type: CHANGE_TYPES.CREATED,
          businessKey,
          sourceRecord,
          targetRecord: null,
          mapping,
          reason: '目标记录已被删除，需要重新创建'
        });
      } else {
        // 检测记录是否有更新
        const changeType = detectRecordChange(sourceRecord, targetRecord, mapping, lastSyncTime);
        if (changeType) {
          changes.push({
            type: changeType,
            businessKey,
            sourceRecord,
            targetRecord,
            mapping,
            reason: getChangeReason(changeType, sourceRecord, targetRecord)
          });
        }
      }
    }
  });

  // 2. 检测目标数据中被删除的记录
  syncMappings.forEach(mapping => {
    const sourceRecord = sourceIndex[mapping.source_record_id];
    if (!sourceRecord) {
      const targetRecord = targetIndex[mapping.target_record_id];
      if (targetRecord) {
        changes.push({
          type: CHANGE_TYPES.DELETED,
          businessKey: mapping.business_key,
          sourceRecord: null,
          targetRecord,
          mapping,
          reason: '源数据中记录已被删除'
        });
      }
    }
  });

  return changes;
}

/**
 * 构建映射索引
 */
function buildMappingIndex(syncMappings) {
  const index = {};
  syncMappings.forEach(mapping => {
    index[mapping.business_key] = mapping;
  });
  return index;
}

/**
 * 构建目标数据索引
 */
function buildTargetIndex(targetRecords) {
  const index = {};
  targetRecords.forEach(record => {
    index[record._id] = record;
  });
  return index;
}

/**
 * 构建源数据索引
 */
function buildSourceIndex(sourceRecords) {
  const index = {};
  sourceRecords.forEach(record => {
    index[record._id] = record;
  });
  return index;
}

/**
 * 检测单个记录的变更类型
 */
function detectRecordChange(sourceRecord, targetRecord, mapping, lastSyncTime) {
  // 检查状态变更
  const sourceStatus = sourceRecord.status || 'ENABLED';
  const targetStatus = targetRecord.status || 'ENABLED';

  if (sourceStatus !== targetStatus) {
    if (sourceStatus === 'DISABLED' && targetStatus === 'ENABLED') {
      return CHANGE_TYPES.DISABLED;
    }
    if (sourceStatus === 'ENABLED' && targetStatus === 'DISABLED') {
      return CHANGE_TYPES.ENABLED;
    }
  }

  // 检查内容变更
  if (hasContentChanged(sourceRecord, targetRecord)) {
    return CHANGE_TYPES.UPDATED;
  }

  // 检查时间戳（如果源记录在上次同步后有更新）
  const sourceUpdateTime = new Date(sourceRecord.updateTime || sourceRecord.created_at);
  const lastSync = new Date(lastSyncTime || '1970-01-01');
  
  if (sourceUpdateTime > lastSync) {
    return CHANGE_TYPES.UPDATED;
  }

  return null;
}

/**
 * 检查记录内容是否发生变更
 */
function hasContentChanged(sourceRecord, targetRecord) {
  const fieldsToCompare = [
    'name', 'value', 'description', 'identify', 'attribute', 
    'sort_number', 'directory_category'
  ];

  return fieldsToCompare.some(field => {
    const sourceValue = sourceRecord[field] || '';
    const targetValue = targetRecord[field] || '';
    return sourceValue !== targetValue;
  });
}

/**
 * 获取变更原因描述
 */
function getChangeReason(changeType, sourceRecord, targetRecord) {
  switch (changeType) {
    case CHANGE_TYPES.CREATED:
      return '新增记录';
    case CHANGE_TYPES.UPDATED:
      return '记录内容已更新';
    case CHANGE_TYPES.DISABLED:
      return '记录已被禁用';
    case CHANGE_TYPES.ENABLED:
      return '记录已被启用';
    case CHANGE_TYPES.DELETED:
      return '记录已被删除';
    default:
      return '未知变更类型';
  }
}

/**
 * 按变更类型分组
 */
function groupChangesByType(changes) {
  const grouped = {};
  
  Object.values(CHANGE_TYPES).forEach(type => {
    grouped[type] = [];
  });

  changes.forEach(change => {
    if (grouped[change.type]) {
      grouped[change.type].push(change);
    }
  });

  return grouped;
}

/**
 * 生成变更摘要
 */
function generateChangeSummary(changes) {
  const grouped = groupChangesByType(changes);
  
  return {
    total: changes.length,
    created: grouped[CHANGE_TYPES.CREATED].length,
    updated: grouped[CHANGE_TYPES.UPDATED].length,
    disabled: grouped[CHANGE_TYPES.DISABLED].length,
    enabled: grouped[CHANGE_TYPES.ENABLED].length,
    deleted: grouped[CHANGE_TYPES.DELETED].length,
    details: grouped
  };
}

module.exports = {
  CHANGE_TYPES,
  detectChanges,
  groupChangesByType,
  generateChangeSummary,
  hasContentChanged
};
