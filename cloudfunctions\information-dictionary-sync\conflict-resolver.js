/**
 * 冲突解决模块
 * 用于处理同步过程中的数据冲突
 */

const { hasContentChanged } = require('./change-detector');

/**
 * 冲突解决策略枚举
 */
const CONFLICT_RESOLUTION = {
  SOURCE_PRIORITY: 'SOURCE_PRIORITY',     // 源优先
  TARGET_PRIORITY: 'TARGET_PRIORITY',     // 目标优先
  MANUAL_RESOLVE: 'MANUAL_RESOLVE',       // 手动处理
  MERGE: 'MERGE'                          // 智能合并
};

/**
 * 检测冲突
 * @param {Array} changes - 变更列表
 * @param {string} lastSyncTime - 上次同步时间
 */
function detectConflicts(changes, lastSyncTime) {
  const conflicts = [];

  changes.forEach(change => {
    if (change.targetRecord && change.sourceRecord) {
      const conflict = checkForConflict(change, lastSyncTime);
      if (conflict) {
        conflicts.push({
          ...change,
          conflictType: conflict.type,
          conflictReason: conflict.reason,
          conflictDetails: conflict.details
        });
      }
    }
  });

  return conflicts;
}

/**
 * 检查单个变更是否存在冲突
 */
function checkForConflict(change, lastSyncTime) {
  const { sourceRecord, targetRecord, mapping } = change;
  
  if (!sourceRecord || !targetRecord || !mapping) {
    return null;
  }

  const lastSync = new Date(lastSyncTime || mapping.last_sync_time || '1970-01-01');
  const sourceUpdateTime = new Date(sourceRecord.updateTime || sourceRecord.created_at);
  const targetUpdateTime = new Date(targetRecord.updateTime || targetRecord.created_at);

  // 检查是否存在并发修改
  const sourceModifiedAfterSync = sourceUpdateTime > lastSync;
  const targetModifiedAfterSync = targetUpdateTime > lastSync;

  if (sourceModifiedAfterSync && targetModifiedAfterSync) {
    // 双方都在上次同步后进行了修改
    return {
      type: 'CONCURRENT_MODIFICATION',
      reason: '源数据和目标数据都在上次同步后被修改',
      details: {
        sourceUpdateTime: sourceUpdateTime.toISOString(),
        targetUpdateTime: targetUpdateTime.toISOString(),
        lastSyncTime: lastSync.toISOString(),
        contentDifferences: getContentDifferences(sourceRecord, targetRecord)
      }
    };
  }

  if (targetModifiedAfterSync && hasContentChanged(sourceRecord, targetRecord)) {
    // 目标数据被修改，且与源数据不同
    return {
      type: 'TARGET_MODIFIED',
      reason: '目标数据在上次同步后被修改，与源数据存在差异',
      details: {
        targetUpdateTime: targetUpdateTime.toISOString(),
        lastSyncTime: lastSync.toISOString(),
        contentDifferences: getContentDifferences(sourceRecord, targetRecord)
      }
    };
  }

  return null;
}

/**
 * 解决冲突
 * @param {Array} conflicts - 冲突列表
 * @param {string} strategy - 解决策略
 */
function resolveConflicts(conflicts, strategy) {
  const resolved = [];
  const unresolved = [];

  conflicts.forEach(conflict => {
    const resolution = resolveConflict(conflict, strategy);
    if (resolution.resolved) {
      resolved.push({
        ...conflict,
        resolution: resolution.action,
        resolvedData: resolution.data
      });
    } else {
      unresolved.push({
        ...conflict,
        reason: resolution.reason
      });
    }
  });

  return { resolved, unresolved };
}

/**
 * 解决单个冲突
 */
function resolveConflict(conflict, strategy) {
  const { sourceRecord, targetRecord, conflictType } = conflict;

  switch (strategy) {
    case CONFLICT_RESOLUTION.SOURCE_PRIORITY:
      return {
        resolved: true,
        action: 'USE_SOURCE',
        data: sourceRecord
      };

    case CONFLICT_RESOLUTION.TARGET_PRIORITY:
      return {
        resolved: true,
        action: 'USE_TARGET',
        data: targetRecord
      };

    case CONFLICT_RESOLUTION.MERGE:
      const mergedData = attemptMerge(sourceRecord, targetRecord, conflictType);
      if (mergedData) {
        return {
          resolved: true,
          action: 'MERGE',
          data: mergedData
        };
      } else {
        return {
          resolved: false,
          reason: '无法自动合并，需要手动处理'
        };
      }

    case CONFLICT_RESOLUTION.MANUAL_RESOLVE:
    default:
      return {
        resolved: false,
        reason: '需要手动解决冲突'
      };
  }
}

/**
 * 尝试智能合并数据
 */
function attemptMerge(sourceRecord, targetRecord, conflictType) {
  // 基础合并策略：非空值优先，时间戳较新的优先
  const merged = { ...targetRecord };

  const fieldsToMerge = [
    'name', 'value', 'description', 'identify', 'attribute', 'sort_number'
  ];

  fieldsToMerge.forEach(field => {
    const sourceValue = sourceRecord[field];
    const targetValue = targetRecord[field];

    if (sourceValue && !targetValue) {
      // 源有值，目标无值
      merged[field] = sourceValue;
    } else if (!sourceValue && targetValue) {
      // 源无值，目标有值，保持目标值
      // merged[field] = targetValue; // 已经在初始化时设置
    } else if (sourceValue && targetValue && sourceValue !== targetValue) {
      // 两者都有值但不同，需要根据时间戳或其他规则决定
      const sourceTime = new Date(sourceRecord.updateTime || sourceRecord.created_at);
      const targetTime = new Date(targetRecord.updateTime || targetRecord.created_at);
      
      if (sourceTime > targetTime) {
        merged[field] = sourceValue;
      }
      // 否则保持目标值
    }
  });

  // 状态字段的特殊处理
  if (sourceRecord.status && targetRecord.status) {
    // 如果源数据是禁用状态，优先使用源状态
    if (sourceRecord.status === 'DISABLED') {
      merged.status = sourceRecord.status;
    }
  }

  return merged;
}

/**
 * 获取内容差异详情
 */
function getContentDifferences(sourceRecord, targetRecord) {
  const differences = {};
  const fieldsToCompare = [
    'name', 'value', 'description', 'identify', 'attribute', 
    'sort_number', 'directory_category', 'status'
  ];

  fieldsToCompare.forEach(field => {
    const sourceValue = sourceRecord[field] || '';
    const targetValue = targetRecord[field] || '';
    
    if (sourceValue !== targetValue) {
      differences[field] = {
        source: sourceValue,
        target: targetValue
      };
    }
  });

  return differences;
}

/**
 * 生成冲突报告
 */
function generateConflictReport(conflicts) {
  const report = {
    totalConflicts: conflicts.length,
    conflictTypes: {},
    details: conflicts.map(conflict => ({
      businessKey: conflict.businessKey,
      conflictType: conflict.conflictType,
      reason: conflict.conflictReason,
      sourceRecord: {
        id: conflict.sourceRecord?._id,
        name: conflict.sourceRecord?.name,
        updateTime: conflict.sourceRecord?.updateTime
      },
      targetRecord: {
        id: conflict.targetRecord?._id,
        name: conflict.targetRecord?.name,
        updateTime: conflict.targetRecord?.updateTime
      },
      differences: conflict.conflictDetails?.contentDifferences || {}
    }))
  };

  // 统计冲突类型
  conflicts.forEach(conflict => {
    const type = conflict.conflictType;
    report.conflictTypes[type] = (report.conflictTypes[type] || 0) + 1;
  });

  return report;
}

module.exports = {
  CONFLICT_RESOLUTION,
  detectConflicts,
  resolveConflicts,
  generateConflictReport,
  getContentDifferences
};
