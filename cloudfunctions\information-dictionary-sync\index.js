import { CloudBase } from '@cloudbase/node-sdk';

// 初始化云开发环境
const app = new CloudBase({
  envId: process.env.ENV_ID,
});

const models = app.models;

/**
 * 数据变更类型枚举
 */
const CHANGE_TYPES = {
  CREATED: 'CREATED', // 新增
  UPDATED: 'UPDATED', // 修改
  DISABLED: 'DISABLED', // 禁用
  ENABLED: 'ENABLED', // 启用
  DELETED: 'DELETED', // 删除
};

/**
 * 冲突解决策略枚举
 */
const CONFLICT_RESOLUTION = {
  SOURCE_PRIORITY: 'SOURCE_PRIORITY', // 源优先
  TARGET_PRIORITY: 'TARGET_PRIORITY', // 目标优先
  MANUAL_RESOLVE: 'MANUAL_RESOLVE', // 手动处理
  MERGE: 'MERGE', // 智能合并
};

const {
  getSyncMappings,
  generateBusinessKey,
  batchCreateSyncMappings,
  updateSyncMapping,
  deleteSyncMapping,
} = require('./sync-mapping');

const {
  detectChanges,
  generateChangeSummary,
  CHANGE_TYPES,
} = require('./change-detector');

const {
  detectConflicts,
  resolveConflicts,
  generateConflictReport,
  CONFLICT_RESOLUTION,
} = require('./conflict-resolver');

/**
 * 智能字典同步分发功能
 * 支持变更检测、冲突解决、ID隔离的多租户数据同步
 */
async function distributeDictionary(data) {
  try {
    const {
      source_tenant_id,
      target_tenants,
      dictionary_groups,
      sync_mode,
      conflict_resolution = CONFLICT_RESOLUTION.SOURCE_PRIORITY,
      description,
    } = data;

    if (!source_tenant_id || !target_tenants || target_tenants.length === 0) {
      return {
        code: -1,
        message: '源租户ID和目标租户不能为空',
        data: null,
      };
    }

    const results = [];

    // 对每个目标租户执行智能同步
    for (const target_tenant_id of target_tenants) {
      try {
        const syncResult = await intelligentSyncToTenant({
          source_tenant_id,
          target_tenant_id,
          dictionary_groups,
          sync_mode,
          conflict_resolution,
        });

        results.push({
          target_tenant_id,
          success: true,
          ...syncResult,
        });
      } catch (error) {
        results.push({
          target_tenant_id,
          success: false,
          error: error.message,
        });
      }
    }

    return {
      code: 0,
      message: '智能同步分发完成',
      data: {
        results,
        total_targets: target_tenants.length,
        success_count: results.filter((r) => r.success).length,
        failed_count: results.filter((r) => !r.success).length,
      },
    };
  } catch (error) {
    return {
      code: -1,
      message: error.message,
      data: null,
    };
  }
}

/**
 * 智能同步到单个租户
 * 支持变更检测、冲突解决、增量同步
 */
async function intelligentSyncToTenant({
  source_tenant_id,
  target_tenant_id,
  dictionary_groups,
  sync_mode,
  conflict_resolution,
}) {
  const syncStartTime = new Date().toISOString();

  // 1. 获取源数据和目标数据
  const sourceRecords = await getDictionaryData(
    source_tenant_id,
    dictionary_groups,
  );
  const targetRecords = await getDictionaryData(
    target_tenant_id,
    dictionary_groups,
  );

  if (!sourceRecords || sourceRecords.length === 0) {
    return {
      message: '源租户没有可同步的数据',
      summary: { total: 0, created: 0, updated: 0, deleted: 0 },
      conflicts: [],
    };
  }

  // 2. 获取现有的同步映射关系
  const syncMappings = await getSyncMappings(
    models,
    source_tenant_id,
    target_tenant_id,
  );
  const lastSyncTime = getLastSyncTime(syncMappings);

  // 3. 检测数据变更
  const changes = detectChanges(
    sourceRecords,
    targetRecords,
    syncMappings,
    lastSyncTime,
  );
  const changeSummary = generateChangeSummary(changes);

  if (changes.length === 0) {
    return {
      message: '没有检测到数据变更',
      summary: changeSummary,
      conflicts: [],
    };
  }

  // 4. 检测和解决冲突
  const conflicts = detectConflicts(changes, lastSyncTime);
  const conflictResolution = resolveConflicts(conflicts, conflict_resolution);

  // 5. 执行同步操作
  const syncResults = await executeSyncOperations(
    changes,
    conflictResolution.resolved,
    target_tenant_id,
    source_tenant_id,
    syncStartTime,
  );

  return {
    message: '智能同步完成',
    summary: changeSummary,
    conflicts: conflictResolution.unresolved,
    syncResults,
    conflictReport: generateConflictReport(conflicts),
  };
}

/**
 * 获取字典数据（通用函数）
 */
async function getDictionaryData(tenant_id, dictionary_groups) {
  const filter = {
    where: {
      tenant_id: { $eq: tenant_id },
    },
  };

  // 如果指定了字典分组，添加过滤条件
  if (dictionary_groups && dictionary_groups.length > 0) {
    filter.where['dictionary_group._id'] = {
      $in: dictionary_groups,
    };
  }

  const result = await models.information_dictionary.list({
    filter,
    pageSize: 1000, // 假设单次同步不会超过1000条
    select: { $master: true },
  });

  return result.data?.records || [];
}

/**
 * 获取最后同步时间
 */
function getLastSyncTime(syncMappings) {
  if (!syncMappings || syncMappings.length === 0) {
    return '1970-01-01T00:00:00.000Z';
  }

  // 找到最新的同步时间
  const latestSync = syncMappings.reduce((latest, mapping) => {
    const mappingTime = new Date(mapping.last_sync_time || '1970-01-01');
    const latestTime = new Date(latest);
    return mappingTime > latestTime ? mapping.last_sync_time : latest;
  }, '1970-01-01T00:00:00.000Z');

  return latestSync;
}

/**
 * 执行同步操作
 */
async function executeSyncOperations(
  changes,
  resolvedConflicts,
  target_tenant_id,
  source_tenant_id,
  syncStartTime,
) {
  const results = {
    created: 0,
    updated: 0,
    deleted: 0,
    errors: [],
  };

  // 处理变更和已解决的冲突
  const allOperations = [...changes, ...resolvedConflicts];

  for (const operation of allOperations) {
    try {
      switch (operation.type) {
        case CHANGE_TYPES.CREATED:
          await handleCreateOperation(
            operation,
            target_tenant_id,
            source_tenant_id,
            syncStartTime,
          );
          results.created++;
          break;

        case CHANGE_TYPES.UPDATED:
          await handleUpdateOperation(operation, syncStartTime);
          results.updated++;
          break;

        case CHANGE_TYPES.DELETED:
          await handleDeleteOperation(operation);
          results.deleted++;
          break;

        case CHANGE_TYPES.DISABLED:
        case CHANGE_TYPES.ENABLED:
          await handleStatusChangeOperation(operation, syncStartTime);
          results.updated++;
          break;
      }
    } catch (error) {
      results.errors.push({
        operation: operation.type,
        businessKey: operation.businessKey,
        error: error.message,
      });
    }
  }

  return results;
}

/**
 * 清空目标租户的相关数据（全量同步时使用）
 */
async function clearTargetData(target_tenant_id, dictionary_groups) {
  const filter = {
    where: {
      tenant_id: { $eq: target_tenant_id },
    },
  };

  if (dictionary_groups && dictionary_groups.length > 0) {
    filter.where['dictionary_group._id'] = {
      $in: dictionary_groups,
    };
  }

  await models.information_dictionary.delete({ filter });
}

/**
 * 处理数据以供同步使用
 */
async function processDataForSync(sourceData, target_tenant_id) {
  return sourceData.map((item) => {
    const { _id, pid, tenant_id, createTime, updateTime, ...rest } = item;

    return {
      ...rest,
      tenant_id: target_tenant_id,
      // 保存原始ID信息用于重建关系
      source_id: _id,
      source_pid: pid,
      // 移除时间戳，让系统自动生成
      createTime: undefined,
      updateTime: undefined,
    };
  });
}

/**
 * 批量创建同步的数据
 */
async function createSyncedData(processedData) {
  // 移除用于关系重建的字段
  const dataForCreate = processedData.map((item) => {
    const { source_id, source_pid, ...rest } = item;
    return rest;
  });

  const createResult = await models.information_dictionary.createMany({
    data: dataForCreate,
  });

  // 建立ID映射关系
  const idMapping = {};
  if (createResult.data && createResult.data.idList) {
    processedData.forEach((item, index) => {
      if (item.source_id) {
        idMapping[item.source_id] = createResult.data.idList[index];
      }
    });
  }

  return {
    count: createResult.data?.idList?.length || 0,
    idMapping,
    idList: createResult.data?.idList || [],
  };
}

/**
 * 重建父子关系
 */
async function rebuildRelationships(processedData, idMapping) {
  const updatePromises = processedData.map(async (item, index) => {
    if (item.source_pid && idMapping[item.source_pid]) {
      const newId = idMapping[item.source_id];
      const newPid = idMapping[item.source_pid];

      if (newId && newPid) {
        await models.information_dictionary.update({
          data: { pid: newPid },
          filter: {
            where: { _id: { $eq: newId } },
          },
        });
      }
    }
  });

  await Promise.all(updatePromises);
}

/**
 * 处理创建操作
 */
async function handleCreateOperation(
  operation,
  target_tenant_id,
  source_tenant_id,
  syncStartTime,
) {
  const { sourceRecord, businessKey } = operation;

  // 准备新记录数据
  const newRecord = {
    ...sourceRecord,
    tenant_id: target_tenant_id,
    // 移除原始ID和时间戳，让系统自动生成
    _id: undefined,
    pid: undefined, // 稍后通过映射关系重建
    createTime: undefined,
    updateTime: undefined,
  };

  // 创建新记录
  const createResult = await models.information_dictionary.create({
    data: newRecord,
  });

  // 创建同步映射
  await batchCreateSyncMappings(models, [
    {
      source_tenant_id,
      target_tenant_id,
      source_record_id: sourceRecord._id,
      target_record_id: createResult.data._id,
      business_key: businessKey,
      sync_version: 1,
      last_sync_time: syncStartTime,
    },
  ]);

  return createResult;
}

/**
 * 处理更新操作
 */
async function handleUpdateOperation(operation, syncStartTime) {
  const { sourceRecord, targetRecord, mapping } = operation;

  // 准备更新数据
  const updateData = {
    name: sourceRecord.name,
    value: sourceRecord.value,
    description: sourceRecord.description,
    identify: sourceRecord.identify,
    attribute: sourceRecord.attribute,
    sort_number: sourceRecord.sort_number,
    directory_category: sourceRecord.directory_category,
    status: sourceRecord.status,
  };

  // 更新目标记录
  const updateResult = await models.information_dictionary.update({
    data: updateData,
    filter: {
      where: { _id: { $eq: targetRecord._id } },
    },
  });

  // 更新同步映射
  await updateSyncMapping(models, mapping._id, {
    sync_version: (mapping.sync_version || 0) + 1,
    last_sync_time: syncStartTime,
  });

  return updateResult;
}

/**
 * 处理删除操作
 */
async function handleDeleteOperation(operation) {
  const { targetRecord, mapping } = operation;

  if (targetRecord) {
    // 软删除：标记为已删除状态
    await models.information_dictionary.update({
      data: { status: 'DELETED' },
      filter: {
        where: { _id: { $eq: targetRecord._id } },
      },
    });
  }

  // 删除同步映射
  if (mapping) {
    await deleteSyncMapping(models, mapping._id);
  }
}

/**
 * 处理状态变更操作
 */
async function handleStatusChangeOperation(operation, syncStartTime) {
  const { sourceRecord, targetRecord, mapping } = operation;

  // 更新状态
  const updateResult = await models.information_dictionary.update({
    data: { status: sourceRecord.status },
    filter: {
      where: { _id: { $eq: targetRecord._id } },
    },
  });

  // 更新同步映射
  await updateSyncMapping(models, mapping._id, {
    sync_version: (mapping.sync_version || 0) + 1,
    last_sync_time: syncStartTime,
  });

  return updateResult;
}

exports.main = async (event, context) => {
  const { action, data } = event;

  switch (action) {
    case 'distribute':
      return await distributeDictionary(data);
    default:
      return {
        code: -1,
        message: 'Invalid action',
        data: null,
      };
  }
};
