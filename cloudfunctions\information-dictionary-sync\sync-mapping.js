/**
 * 同步映射管理模块
 * 用于管理源数据与目标数据之间的映射关系
 */

/**
 * 创建同步映射记录
 * @param {Object} models - 数据模型
 * @param {Object} mappingData - 映射数据
 */
async function createSyncMapping(models, mappingData) {
  const {
    source_tenant_id,
    target_tenant_id,
    source_record_id,
    target_record_id,
    business_key,
    sync_version,
    last_sync_time
  } = mappingData;

  return await models.dictionary_sync_mapping.create({
    data: {
      source_tenant_id,
      target_tenant_id,
      source_record_id,
      target_record_id,
      business_key,
      sync_version: sync_version || 1,
      last_sync_time: last_sync_time || new Date().toISOString(),
      status: 'ACTIVE',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  });
}

/**
 * 获取同步映射关系
 * @param {Object} models - 数据模型
 * @param {string} source_tenant_id - 源租户ID
 * @param {string} target_tenant_id - 目标租户ID
 * @param {Array} business_keys - 业务键列表
 */
async function getSyncMappings(models, source_tenant_id, target_tenant_id, business_keys = null) {
  const filter = {
    where: {
      source_tenant_id: { $eq: source_tenant_id },
      target_tenant_id: { $eq: target_tenant_id },
      status: { $eq: 'ACTIVE' }
    }
  };

  if (business_keys && business_keys.length > 0) {
    filter.where.business_key = { $in: business_keys };
  }

  const result = await models.dictionary_sync_mapping.list({
    filter,
    pageSize: 1000,
    select: { $master: true }
  });

  return result.data?.records || [];
}

/**
 * 更新同步映射记录
 * @param {Object} models - 数据模型
 * @param {string} mapping_id - 映射记录ID
 * @param {Object} updateData - 更新数据
 */
async function updateSyncMapping(models, mapping_id, updateData) {
  return await models.dictionary_sync_mapping.update({
    data: {
      ...updateData,
      updated_at: new Date().toISOString()
    },
    filter: {
      where: { _id: { $eq: mapping_id } }
    }
  });
}

/**
 * 删除同步映射记录
 * @param {Object} models - 数据模型
 * @param {string} mapping_id - 映射记录ID
 */
async function deleteSyncMapping(models, mapping_id) {
  return await models.dictionary_sync_mapping.update({
    data: {
      status: 'DELETED',
      updated_at: new Date().toISOString()
    },
    filter: {
      where: { _id: { $eq: mapping_id } }
    }
  });
}

/**
 * 生成业务唯一标识
 * 基于字典项的关键属性生成唯一标识，用于跨租户数据关联
 * @param {Object} record - 字典记录
 */
function generateBusinessKey(record) {
  const {
    dictionary_group,
    name,
    value,
    identify,
    attribute,
    pid
  } = record;

  // 构建层级路径
  const groupKey = dictionary_group?._id || dictionary_group;
  const parentKey = pid || 'ROOT';
  
  // 使用多个字段组合生成唯一标识
  const keyComponents = [
    groupKey,
    parentKey,
    name,
    value || '',
    identify || '',
    attribute || ''
  ].filter(Boolean);

  // 生成哈希值作为业务键
  return Buffer.from(keyComponents.join('|')).toString('base64');
}

/**
 * 批量创建同步映射
 * @param {Object} models - 数据模型
 * @param {Array} mappings - 映射数据数组
 */
async function batchCreateSyncMappings(models, mappings) {
  const mappingData = mappings.map(mapping => ({
    ...mapping,
    status: 'ACTIVE',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }));

  return await models.dictionary_sync_mapping.createMany({
    data: mappingData
  });
}

module.exports = {
  createSyncMapping,
  getSyncMappings,
  updateSyncMapping,
  deleteSyncMapping,
  generateBusinessKey,
  batchCreateSyncMappings
};
