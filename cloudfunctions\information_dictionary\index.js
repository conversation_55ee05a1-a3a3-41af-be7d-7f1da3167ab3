import { serve } from '@cloudbase/functions-framework'
import { CloudBase } from '@cloudbase/node-sdk'

// 初始化云开发环境
const app = new CloudBase({
  envId: process.env.ENV_ID
})

const models = app.models

async function createInfoDictionary(data) {
  try {
    if (!data) {
      return {
        code: -1,
        message: '数据不能为空',
        data: null
      }
    }
    const { directory_category, dictionary_group, tenant_id } = data || {}

    // 如果是字典项，需要处理 dictionary_group
    if (directory_category === 'DIRECTORY_ITEM' && !dictionary_group._id) {

      // 如果不存在，创建新的 dictionary_group
      if (dictionary_group) {
        const newGroup = await models.dictionary_group.create({
          data: {
            name: dictionary_group?.name || dictionary_group,
            sort_number: 1,
          }
        })
        groupId = newGroup._id
      }
      // 更新 data 中的 dictionary_group 为 ID
      data.dictionary_group = {
        _id: groupId
      }
    }
    const result = await models.information_dictionary.create({
      data,
    })
    return {
      code: 0,
      message: '创建成功',
      data: result
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message,
      data: null
    }
  }
}

async function listDictionary(data) {
  const { pageSize, pageNumber, tenantId } = data || {}
  try {
    const data = await models.information_dictionary.list({
      pageSize,
      pageNumber,
      getCount: true,
      select: {
        $master: true,
      },
      filter: {
        where: {
          tenant_id: {
            $eq: tenantId
          }
        }
      }
    });
    return {
      code: 0,
      ...data,
    };
  } catch (error) {
    return {
      success: false,
      message: '获取数据失败: ' + error.message
    };
  }
}

async function getInfoDictionary(params) {
  try {
    const { id } = params
    if (!id) {
      return {
        code: -1,
        message: '参数错误',
        data: null
      }
    }

    const result = await models.information_dictionary.get({
      filter: {
        where: {
          _id: { $eq: id }
        }
      }
    })

    return {
      code: 0,
      message: '获取成功',
      data: result
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message,
      data: null
    }
  }
}

async function updateInfoDictionary(params) {
  const { _id, data } = params
  try {
    if (!_id || !data) {
      return {
        code: -1,
        message: '参数错误',
        data: null
      }
    }
    const result = await models.information_dictionary.update({
      data: data,
      filter: {
        where: {
          _id: {
            $eq: _id, // 推荐传入_id数据标识进行操作
          },
        },
      },
    });
    return {
      code: 0,
      message: '更新成功',
      data: result
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message,
      data: null
    }
  }
}

async function deleteInfoDictionary(id) {
  try {
    if (!id) {
      return {
        code: -1,
        message: '参数错误',
        data: null
      }
    }
    const result = await models.information_dictionary.delete({
      filter: {
        where: {
          _id: {
            $eq: id, // 推荐传入_id数据标识进行操作
          },
        },
      },
    })

    return {
      code: 0,
      message: '删除成功',
      data: result
    }
  } catch (error) {
    return {
      code: -1,
      message: error.message,
      data: null
    }
  }
}



async function importCreateMany(data) {
  try {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return {
        code: -1,
        message: '数据不能为空',
        data: null
      }
    }

    // 创建ID映射表，用于存储旧ID到新ID的映射
    const idMapping = {};
    
    // 第一遍：创建所有记录，并建立ID映射
    const createResult = await models.information_dictionary.createMany({
      data: data.map(item => {
        // 移除old_id和old_pid字段，这些字段只用于建立关系
        const { old_id, old_pid, ...rest } = item;
        return rest;
      })
    });
    
    // 建立ID映射关系
    if (createResult.data && createResult.data.idList) {
      data.forEach((item, index) => {
        if (item.old_id) {
          idMapping[item.old_id] = createResult.data.idList[index];
        }
      });
    }
    
    // 第二遍：更新所有记录的pid字段，使用新生成的ID
    const updatePromises = data.map(async (item, index) => {
      if (item.old_pid && idMapping[item.old_pid]) {
        // 更新pid为新的ID
        await models.information_dictionary.update({
          data: {
            pid: idMapping[item.old_pid]
          },
          filter: {
            where: {
              _id: {
                $eq: createResult.data.idList[index]
              }
            }
          }
        });
      }
    });
    
    // 等待所有更新完成
    await Promise.all(updatePromises);

    return {
      code: 0,
      message: '导入成功',
      data: {
        count: createResult.data.idList.length,
        idList: createResult.data.idList,
        idMapping // 返回ID映射，方便调试
      }
    };
  } catch (error) {
    return {
      code: -1,
      message: error.message,
      data: null
    }
  }
}

exports.main = async (event, context) => {
  const { action, data } = event;
  switch (action) {
    case 'create':
      return await createInfoDictionary(data);
    case 'details':
      return await getInfoDictionary(data.id);
    case 'update':
      return await updateInfoDictionary(data);
    case 'delete':
      return await deleteInfoDictionary(data.id);
    case 'list':
      return await listDictionary(data);
    case 'importCreateMany':
      return await importCreateMany(data)
    default:
      throw new Error('Invalid action');
  }
};
