{"private": true, "author": "zhangyuhao <<EMAIL>>", "scripts": {"build": "umi build", "dev": "umi dev", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev"}, "lint-staged": {"*.{js,jsx,ts,tsx,css,less}": ["umi lint"]}, "dependencies": {"@cloudbase/js-sdk": "^2.11.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fancyapps/ui": "^5.0.36", "@grapecity/spread-excelio": "18.0.4", "@grapecity/spread-sheets": "18.0.4", "@grapecity/spread-sheets-barcode": "18.0.4", "@grapecity/spread-sheets-charts": "18.0.4", "@grapecity/spread-sheets-designer": "18.0.4", "@grapecity/spread-sheets-designer-react": "18.0.4", "@grapecity/spread-sheets-designer-resources-cn": "18.0.4", "@grapecity/spread-sheets-io": "18.0.4", "@grapecity/spread-sheets-languagepackages": "18.0.4", "@grapecity/spread-sheets-pdf": "18.0.4", "@grapecity/spread-sheets-pivot-addon": "18.0.4", "@grapecity/spread-sheets-print": "18.0.4", "@grapecity/spread-sheets-react": "18.0.4", "@grapecity/spread-sheets-resources-zh": "18.0.4", "@grapecity/spread-sheets-shapes": "18.0.4", "@grapecity/spread-sheets-tablesheet": "18.0.4", "@grapecity/wijmo.react.all": "^5.20251.34", "@mui/icons-material": "^7.0.0", "@mui/material": "^7.0.0", "@reduxjs/toolkit": "^2.6.1", "ahooks": "^3.8.4", "bmc-common-resource": "^1.1.44", "compressorjs": "^1.2.1", "lodash": "^4.17.21", "rc-tree": "^5.13.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "umi": "^4.4.6", "umi-request": "^1.4.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/lodash": "^4.17.16", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.5", "@umijs/lint": "^4.4.6", "@umijs/plugins": "^4.4.6", "eslint": "^8.56.0", "lint-staged": "^15.5.0", "prettier": "^2.8.8", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-packagejson": "^2.5.10", "stylelint": "^14.16.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.2"}}