# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
**/node_modules
# roadhog-api-doc ignore
/src/utils/request-temp.js
_roadhog-api-doc

# production
/project-management
/.vscode

# misc
.DS_Store
npm-debug.log*
yarn-error.log

/coverage
.idea
*bak
.vscode

# visual studio code
.history
*.log
functions/*
.temp/**

# umi
.umi
.umi-production

# screenshot
screenshot
.firebase
.eslintcache

build
.idea/
*.iml
dist/
*.css
*.map
