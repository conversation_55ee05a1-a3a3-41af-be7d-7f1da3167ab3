# 把此文件放到 develop 分支项目根目录下，然后 push 即可
# 注意，原来 master 分支下的自动部署文件应该删除，避免引起问题
stages:
  - deploy
build-job:
  variables:
    # 添加此参数，每次运行前不删除 node_modules 目录，防止重复下载依赖，只是第一次运行会下载，后面只会增量修改
    GIT_CLEAN_FLAGS: -ffdx -e node_modules
  stage: deploy
  only:
    # 前端只是 develop 分支会自动部署，若没有则新建一个分支
    - develop
  script:
    # 第一个参数为部署后的访问路径末级的 uri，如此处为 home 则部署后的访问路径就是 http://*************/bmc-app/home
    # 第二个参数是使用的包管理器可不填默认是 yarn
    # 第三个参数是打包的目录，一般为 dist，可不填默认是 dist，若要修改则第二个参数也必须传
    - deploy-web project-management yarn dist
  tags:
    # 固定为此
    - bmc-web
