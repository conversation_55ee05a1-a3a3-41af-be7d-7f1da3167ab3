# bd-project-front

# 百达屋经营管理平台前端项目

-- 代码仓库地址：http://192.168.0.239:81/bmc-app-client/project-management-pc

```
此项目是百达屋经营管理平台，访问路径：百达屋经营管理平台
```

```
代码拉取运行步骤说明：

1.拉取仓库代码
2.安装项目依赖：npm install
3.项目启动：npm start
4.项目打包：npm run build

注意：node版本推荐使用 v14.16.1

注意：联合调试运行查看效果，需要nginx代理百达屋项目
```


```
编码规范遵循百达屋项目编码规范

1.文件命名：统一使用 - 做分隔，正确示例：bdw-input，错误示例：BdwInput、bdwInput
2.pages一个模块一个文件夹，文件夹中再细分页面
3.会重复使用的组件都放在components里面，只可能一个页面使用的组件放在模块文件夹内
4.service根据后端的action进行分类，方便以后维护
5.根据返回的参数不同，使用oldBdwRequest, bdwRequest进行请求
6.页面统一使用useRequest进行请求
7.封装的公用组件通过在 components/index.tsx 去暴露出来

```

