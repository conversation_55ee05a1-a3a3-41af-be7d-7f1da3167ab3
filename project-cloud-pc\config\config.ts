const isPro = process.env.NODE_ENV == 'production';
import { defineConfig } from 'umi';
import ProjectRoutes from './router';

const publicPath = "/project-management/"

export default defineConfig({
  // 注释掉base 以解决url会出现两个/project-pc-front/
  // base: publicPath,
  history:{
    type: "hash"
  },
  publicPath: publicPath,
  outputPath: 'project-management',
  hash: true,
  antd: {},
  dva: {
    hmr: true,
  },
  proxy:  {
    '/cloud-base-service': {
      // target: 'http://192.168.1.234/',
      target: 'http://192.168.1.46/',
      changeOrigin: true,
      // pathRewrite: { '^/cloud-base-service': '' }
    },
    '/static-resource/': {
      target: 'http://192.168.0.46/static-resource',
      changeOrigin: true,
      // pathRewrite: { '^/cloud-base-service': '' }
    },
  },
  locale: false,
  // favicon: isPro ? '/bmc-app/receivables-payable-bill/favicon.png' : '/favicon.png',
  dynamicImport: {
    loading: '@/components/page-loading/index',
  },
  targets: {
    ie: 11,
  },
  routes: ProjectRoutes,
  theme: {},
  // @ts-ignore
  title: false,
  ignoreMomentLocale: true,
  manifest: {
    basePath: '/',
  },
  request:{
    dataField: "",
  },
  externals:{
    "bdw-config": "window.bdwConfig"
  },
  scripts:[
    {src: `${publicPath}bdw_config.js`},
  ],
  lessLoader: {
    modifyVars: {
      'hack': `true; @import"~@/styles/base.less";`
    }
  },
  define: {
    'process.env': {
      uploadConfig: {
        uploadUrl: '/common/attachment/upload',
        multipleUploadUrl: '/common/attachment/save-batch',
        getFileUrl: '/common/attachment/batch',
        deleteFileUrl: '/common/attachment',
      },
      REACT_APP_BASE_URL:'/cloud-base-service',
      BASE_URL: '/cloud-base-service'
    },
  },
  chainWebpack(config) {
    config.module
    .rule('pdf')
    .test(/\.pdf$/)
    .use('file-loader')
    .loader('file-loader')
  }
});
