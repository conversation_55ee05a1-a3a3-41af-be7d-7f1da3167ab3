export default  [
  {
    component: './layout',
    routes: [
      {
        path: '/',
        name: "主页",
        redirect: "/index"
      },
      {
        path: '/index',
        name: '主页',
        icon: 'smile',
        component: './index',
      },
      {
        path: '/my-task',
        name: '我的任务',
        icon: 'smile',
        component: './myTask',
      },
      // {
      //   path: '/my-schedule',
      //   name: '我的日程',
      //   component: './mySchedule',
      // },
      {
        path: '/team-report/:id',
        name: '团队汇报',
        component: './teamReport',
      },
      {
        path: '/team-task',
        name: '团队任务',
        component: './teamTask',
      },
      {
        path: '/my-project',
        name: '我的项目',
        icon: 'smile',
        component: './myProject',
      },
      {
        path: '/my-project/create-new-project',
        name: '新建项目',
        icon: 'smile',
        component: './myProject/createNewProject',
      },
      {
        path: '/my-project-detail/:projectId',
        name: '我的项目详情',
        icon: 'smile',
        component: './my-project-detail',
      },
      {
        path: '/project-template',
        name: '项目模板',
        icon: 'smile',
        component: './projectTemplate',
      },
      {
        path: '/project-template-detail/:projectId',
        name: '项目模板详情',
        icon: 'smile',
        component: './projectTemplate/createTemplate',
      },
      //甘特图测试 begin
      {
        path: '/gant/:projectId',
        name: '项目模板详情',
        icon: 'smile',
        component: './my-project-detail/gantSheet',
      },
      {
        path: '/project-accounting-management/task-list/:estateId',
        name: '项目管理核算',
        icon: 'smile',
        component: './project-accounting-management',
      },
      // end
    ]
  }
]
