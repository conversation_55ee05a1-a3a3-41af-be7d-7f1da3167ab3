###一、  [项目规范文件](../README.md);

###二、 [百达屋JS代码规范文档](Airbnb.md)

###三、 pages模块包说明
```
[group-task] 团队任务模块

[index] 首页模块

[layout] 项目管理外框layout

[my-schedule] 我的日程模块

[my-task] 我的任务管理模块

[project-detail] 项目中的任务详情模块

[project-management] 项目创建管理

[project-overview] 项目概览模块

[project-template] 项目模板管理

[project-value-assessment] 项目价值评估模块

[user-task] 单人任务模块
```

###四、 [百达屋组件说明文档](项目管理组件说明文档.md)

###五、 功能交互说明文档

- 1、[项目详情按钮权限判定](../src/pages/project-detail/project-mission/项目任务按钮权限判断交互说明.md)

- 2、[项目概览按钮权限控制](../src/pages/project-overview/项目概览按钮权限控制.md)

- 3、[项目管理字符串长度限制](../src/pages/project-detail/project-mission/项目相关字符串长度限制详情.md)

- 4、[分派任务警告信息展示逻辑](../src/pages/project-detail/project-mission/任务分派时的警告信息说明.md)

- 5、[项目详情任务列表，任务显示状态逻辑](../src/pages/project-detail/project-mission/TaskTable任务状态说明详情.md)

- 6、[模板详情页面按钮权限判定](../src/pages/project-template/project-template-import-view/按钮权限文档.md)

- 7、[我的待处理任务页面按钮权限说明](../src/pages/my-task/list/components/my-wait-task-table/按钮权限说明.md)

- 8、[百达屋项目查看任务详情交互说明](../src/pages/project-detail/project-mission/项目查看任务详情交互说明.md)

