### 百达屋组件说明文档

```
组件内定义的interface是组件需要的参数，都有说明。
```

- 1、 bdw-card-status 显示状态的颜色组件

- 2、 bdw-card-table 部分地方需要展示card列表，这个table暴露了获取到的数据，可以直接拿到数据遍历

- 3、 bdw-custom-select 自定义的选择组件，点击显示的row元素，出现选择框。选择框里的内容可以自己定义

- 4、 bdw-choose-company-staff 基于bdw-custom-select 封装的选择公司员工组件

- 5、 bdw-choose-customer-staff 基于bdw-custom-select 封装的选择公司客户的组件

- 6、 bdw-choose-history-project-staff 基于bdw-custom-select 封装的选择历史项目的组件

- 7、 bdw-choose-order-staff 基于bdw-custom-select 封装的选择客户订单的组件

- 8、 bdw-choose-project-audit-person-staff 基于bdw-custom-select 封装的选择项目审核人的组件

- 9、 bdw-choose-project-template-staff 基于bdw-custom-select 封装的选择项目模板的组件

- 10、 bdw-general-message-show 已经提交的日报展示，已经已经提交的日报中含有回复功能

- 11、 bdw-leave-message 带粘贴截图功能的留言组件

- 12、 bdw-leave-message-readonly 留言组件只读展示

- 13、 bdw-single-picture-upload 封面、头像单张图片上传组件

- 14、 menu-item-show-name-icon menu,card等显示名称的组件

- 15、 menu-item-show-number menu显示数量的组件

// new add  模块内自封装的组件

- 16 TaskItem  任务项看板展示

- 17 CustomCalendar 自定义日历

- 18 IconTitle 带图标的标题

- 18 PageWrapper 包裹页面

- 18 bdw-card-show-item-component   card卡片类型的数据展示组件

- 19 BdwSelect  BdwOption 下拉选择框 
- 20 BdwEnumSelect 下拉选择框,enumList属性传入options,数据组成的数组

