import { Request, Response } from 'express';

const getNotices = (req: Request, res: Response) => {
  res.json({
    code: 400,
    success: true,
    data: [
      {
        id: '000000001',
        name: '李旭东',
        post: '总经理',
        department: '总经办'
      },
      {
        id: '000000002',
        name: '李叶',
        post: '小助理',
        department: '总经办'
      },
      {
        id: '000000003',
        name: '易涛',
        post: '杠精',
        department: '杠精总公司'
      },
    ],
  });
};

export default {
  'GET /api/choose/project/person': getNotices,
};
