{"name": "project-management-pc", "description": "百达屋经营管理平台", "version": "2.0", "private": true, "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "umi build", "deploy": "npm run site && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "cp CNAME ./dist/ && gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "prettier": "prettier -c --write \"**/*\"", "start": "cross-env UMI_UI=none umi dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none umi dev", "start:no-mock": "cross-env MOCK=none umi dev", "start:no-ui": "cross-env UMI_UI=none umi dev", "start:pre": "cross-env REACT_APP_ENV=pre umi dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none umi dev", "pretest": "node ./tests/beforeTest", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components", "tsc": "tsc"}, "husky": {"hooks": {"pre-commit2": "npm run lint-staged"}}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 version", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.0.0", "@ant-design/pro-layout": "6.0.0-8", "@ant-design/pro-table": "^2.3.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@grapecity/spread-excelio": "latest", "@grapecity/spread-sheets": "16.2.6", "@grapecity/spread-sheets-barcode": "latest", "@grapecity/spread-sheets-charts": "latest", "@grapecity/spread-sheets-ganttsheet": "latest", "@grapecity/spread-sheets-languagepackages": "latest", "@grapecity/spread-sheets-pdf": "latest", "@grapecity/spread-sheets-pivot-addon": "latest", "@grapecity/spread-sheets-print": "latest", "@grapecity/spread-sheets-react": "16.2.6", "@grapecity/spread-sheets-resources-ja": "latest", "@grapecity/spread-sheets-resources-ko": "latest", "@grapecity/spread-sheets-resources-zh": "^16.2.6", "@grapecity/spread-sheets-shapes": "latest", "@grapecity/spread-sheets-tablesheet": "latest", "@mui/material": "^5.15.6", "@reduxjs/toolkit": "^1.9.7", "@tinymce/tinymce-react": "4.1.0", "@types/react-highlight-words": "^0.16.1", "@types/react-window": "^1.8.2", "@types/styled-components": "^5.1.1", "ahooks": "^2.1.0", "antd": "^4.7.0", "array-move": "^2.2.2", "bizcharts": "^4.0.7", "bmc-app-react-component": "^1.1.105", "classnames": "^2.2.6", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.10", "file-loader": "^6.2.0", "immutability-helper": "^3.1.1", "insert-css": "^2.0.0", "lodash": "^4.17.15", "moment": "^2.27.0", "numeral": "^2.0.6", "omit.js": "^1.0.2", "qs": "^6.9.0", "rc-animation": "^1.0.3", "rc-banner-anim": "^2.4.4", "rc-queue-anim": "^1.8.5", "rc-resize-observer": "^0.2.5", "rc-scroll-anim": "^2.7.4", "rc-texty": "^0.2.0", "rc-tween-one": "^2.7.2", "react": "17.x", "react-datasheet": "^1.4.4", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dom": "^16.8.6", "react-file-viewer": "^1.2.1", "react-highlight-words": "^0.16.0", "react-resize-detector": "^11.0.1", "react-sortable-hoc": "^1.11.0", "react-window": "^1.8.5", "react-zmage": "^0.8.5-beta.37", "rsuite": "^4.8.1", "rsuite-table": "^3.13.1", "rxjs": "^6.6.0", "styled-components": "^5.1.1", "systemjs": "0.19.40", "systemjs-plugin-babel": "0.0.25", "systemjs-plugin-css": "0.1.37", "typescript": "3.1.2", "umi": "3.5.21", "umi-request": "^1.0.8", "use-merge-value": "^1.0.1", "webpack": "^4.44.0", "xlsx": "^0.16.3"}, "devDependencies": {"@ant-design/pro-cli": "^2.0.2", "@types/classnames": "^2.2.7", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.144", "@types/qs": "^6.5.3", "@types/react": "^16.9.17", "@types/react-dom": "^16.8.4", "@types/react-helmet": "^5.0.13", "@umijs/fabric": "^2.0.5", "@umijs/plugin-blocks": "^2.0.5", "@umijs/plugin-esbuild": "^1.0.1", "@umijs/preset-ant-design-pro": "^1.2.0", "@umijs/preset-react": "^1.4.24", "@umijs/preset-ui": "^2.1.11", "carlo": "^0.9.46", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "detect-installer": "^1.0.1", "eslint": "^7.2.0", "express": "^4.17.1", "gh-pages": "^3.0.0", "husky": "^4.0.7", "lint-staged": "^10.0.0", "mockjs": "^1.0.1-beta3", "prettier": "^2.0.1", "pro-download": "1.0.1", "puppeteer-core": "^3.0.0", "stylelint": "^13.0.0"}, "engines": {"node": ">=10.0.0"}, "resolutions": {"@tinymce/tinymce-react/tinymce": "6.0.3"}}