/**
 * TinyMCE version 6.0.3 (2022-05-25)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=(e,t)=>(...o)=>e(t.apply(null,o)),y=e=>()=>e,x=e=>e,w=(e,t)=>e===t;function S(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const k=e=>t=>!e(t),C=e=>()=>{throw new Error(e)},O=e=>e(),_=y(!1),T=y(!0);var E=tinymce.util.Tools.resolve("tinymce.ThemeManager");class B{constructor(e,t){this.tag=e,this.value=t}static some(e){return new B(!0,e)}static none(){return B.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?B.some(e(this.value)):B.none()}bind(e){return this.tag?e(this.value):B.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:B.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?B.some(e):B.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}B.singletonNone=new B(!1);const M=Array.prototype.slice,A=Array.prototype.indexOf,D=Array.prototype.push,F=(e,t)=>A.call(e,t),I=(e,t)=>{const o=F(e,t);return-1===o?B.none():B.some(o)},V=(e,t)=>F(e,t)>-1,R=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},z=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},H=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},P=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},L=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},W=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},U=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t,o)=>(N(e,((e,n)=>{o=t(o,e,n)})),o),G=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return B.some(s);if(o(s,n))break}return B.none()})(e,t,_),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return B.some(o);return B.none()},q=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);D.apply(t,e[o])}return t},X=(e,t)=>q(P(e,t)),K=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},Y=e=>{const t=M.call(e,0);return t.reverse(),t},J=(e,t)=>W(e,(e=>!V(t,e))),Z=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],ee=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},te=(e,t)=>t>=0&&t<e.length?B.some(e[t]):B.none(),oe=e=>te(e,0),ne=e=>te(e,e.length-1),se=p(Array.from)?Array.from:e=>M.call(e),re=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return B.none()},ae=Object.keys,ie=Object.hasOwnProperty,le=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},ce=(e,t)=>de(e,((e,o)=>({k:o,v:t(e,o)}))),de=(e,t)=>{const o={};return le(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},ue=e=>(t,o)=>{e[o]=t},me=(e,t,o,n)=>(le(e,((e,s)=>{(t(e,s)?o:n)(e,s)})),{}),ge=(e,t)=>{const o=[];return le(e,((e,n)=>{o.push(t(e,n))})),o},pe=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return B.some(r)}return B.none()},he=e=>ge(e,x),fe=(e,t)=>be(e,t)?B.from(e[t]):B.none(),be=(e,t)=>ie.call(e,t),ve=(e,t)=>be(e,t)&&void 0!==e[t]&&null!==e[t],ye=(e,t,o=w)=>e.exists((e=>o(e,t))),xe=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},we=(e,t,o)=>e.isSome()&&t.isSome()?B.some(o(e.getOrDie(),t.getOrDie())):B.none(),Se=(e,t)=>e?B.some(t):B.none(),ke=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,Ce=(e,t)=>-1!==e.indexOf(t),Oe=(e,t)=>ke(e,t,e.length-t.length),_e=(Ie=/^\s+|\s+$/g,e=>e.replace(Ie,"")),Te=e=>e.length>0,Ee=e=>void 0!==e.style&&p(e.style.getPropertyValue),Be=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Me=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Be(o.childNodes[0])},Ae=(e,t)=>{const o=(t||document).createElement(e);return Be(o)},De=(e,t)=>{const o=(t||document).createTextNode(e);return Be(o)},Fe=Be;var Ie;"undefined"!=typeof window?window:Function("return this;")();const Ve=e=>e.dom.nodeName.toLowerCase(),Re=e=>t=>(e=>e.dom.nodeType)(t)===e,ze=Re(1),He=Re(3),Pe=Re(9),Ne=Re(11),Le=e=>t=>ze(t)&&Ve(t)===e,We=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Ue=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,je=(e,t)=>e.dom===t.dom,Ge=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},$e=e=>Fe(e.dom.ownerDocument),qe=e=>Pe(e)?e:$e(e),Xe=e=>Fe(qe(e).dom.documentElement),Ke=e=>Fe(qe(e).dom.defaultView),Ye=e=>B.from(e.dom.parentNode).map(Fe),Je=e=>B.from(e.dom.parentElement).map(Fe),Ze=e=>B.from(e.dom.offsetParent).map(Fe),Qe=e=>P(e.dom.childNodes,Fe),et=(e,t)=>{const o=e.dom.childNodes;return B.from(o[t]).map(Fe)},tt=(e,t)=>({element:e,offset:t}),ot=(e,t)=>{const o=Qe(e);return o.length>0&&t<o.length?tt(o[t],0):tt(e,t)},nt=e=>Ne(e)&&g(e.dom.host),st=p(Element.prototype.attachShadow)&&p(Node.prototype.getRootNode),rt=y(st),at=st?e=>Fe(e.dom.getRootNode()):qe,it=e=>nt(e)?e:Fe(qe(e).dom.body),lt=e=>{const t=at(e);return nt(t)?B.some(t):B.none()},ct=e=>Fe(e.dom.host),dt=e=>{const t=He(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return lt(Fe(t)).fold((()=>o.body.contains(t)),(n=dt,s=ct,e=>n(s(e))));var n,s},ut=()=>mt(Fe(document)),mt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return Fe(t)},gt=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},pt=(e,t,o)=>{gt(e.dom,t,o)},ht=(e,t)=>{const o=e.dom;le(t,((e,t)=>{gt(o,t,e)}))},ft=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},bt=(e,t)=>B.from(ft(e,t)),vt=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},yt=(e,t)=>{e.dom.removeAttribute(t)},xt=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Ee(e)&&e.style.setProperty(t,o)},wt=(e,t)=>{Ee(e)&&e.style.removeProperty(t)},St=(e,t,o)=>{const n=e.dom;xt(n,t,o)},kt=(e,t)=>{const o=e.dom;le(t,((e,t)=>{xt(o,t,e)}))},Ct=(e,t)=>{const o=e.dom;le(t,((e,t)=>{e.fold((()=>{wt(o,t)}),(e=>{xt(o,t,e)}))}))},Ot=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||dt(e)?n:_t(o,t)},_t=(e,t)=>Ee(e)?e.style.getPropertyValue(t):"",Tt=(e,t)=>{const o=e.dom,n=_t(o,t);return B.from(n).filter((e=>e.length>0))},Et=e=>{const t={},o=e.dom;if(Ee(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},Bt=(e,t,o)=>{const n=Ae(e);return St(n,t,o),Tt(n,t).isSome()},Mt=(e,t)=>{const o=e.dom;wt(o,t),ye(bt(e,"style").map(_e),"")&&yt(e,"style")},At=e=>e.dom.offsetWidth,Dt=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Ot(o,e);return parseFloat(t)||0}return n},n=(e,t)=>j(t,((t,o)=>{const n=Ot(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Ee(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},Ft=Dt("height",(e=>{const t=e.dom;return dt(e)?t.getBoundingClientRect().height:t.offsetHeight})),It=e=>Ft.get(e),Vt=e=>Ft.getOuter(e),Rt=(e,t)=>({left:e,top:t,translate:(o,n)=>Rt(e+o,t+n)}),zt=Rt,Ht=(e,t)=>void 0!==e?e:void 0!==t?t:0,Pt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return zt(o.offsetLeft,o.offsetTop);const r=Ht(null==n?void 0:n.pageYOffset,s.scrollTop),a=Ht(null==n?void 0:n.pageXOffset,s.scrollLeft),i=Ht(s.clientTop,o.clientTop),l=Ht(s.clientLeft,o.clientLeft);return Nt(e).translate(a-l,r-i)},Nt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?zt(o.offsetLeft,o.offsetTop):dt(e)?(e=>{const t=e.getBoundingClientRect();return zt(t.left,t.top)})(t):zt(0,0)},Lt=Dt("width",(e=>e.dom.offsetWidth)),Wt=e=>Lt.get(e),Ut=e=>Lt.getOuter(e),jt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},Gt=()=>$t(0,0),$t=(e,t)=>({major:e,minor:t}),qt={nu:$t,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?Gt():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return $t(n(1),n(2))})(e,o)},unknown:Gt},Xt=(e,t)=>{const o=String(t).toLowerCase();return G(e,(e=>e.search(o)))},Kt=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Yt=e=>t=>Ce(t,e),Jt=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Ce(e,"edge/")&&Ce(e,"chrome")&&Ce(e,"safari")&&Ce(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Kt],search:e=>Ce(e,"chrome")&&!Ce(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Ce(e,"msie")||Ce(e,"trident")},{name:"Opera",versionRegexes:[Kt,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Yt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Yt("firefox")},{name:"Safari",versionRegexes:[Kt,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Ce(e,"safari")||Ce(e,"mobile/"))&&Ce(e,"applewebkit")}],Zt=[{name:"Windows",search:Yt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Ce(e,"iphone")||Ce(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Yt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Yt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Yt("linux"),versionRegexes:[]},{name:"Solaris",search:Yt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Yt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Yt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Qt={browsers:y(Jt),oses:y(Zt)},eo="Edge",to="Chromium",oo="Opera",no="Firefox",so="Safari",ro=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(eo),isChromium:n(to),isIE:n("IE"),isOpera:n(oo),isFirefox:n(no),isSafari:n(so)}},ao=()=>ro({current:void 0,version:qt.unknown()}),io=ro,lo=(y(eo),y(to),y("IE"),y(oo),y(no),y(so),"Windows"),co="Android",uo="Linux",mo="macOS",go="Solaris",po="FreeBSD",ho="ChromeOS",fo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(lo),isiOS:n("iOS"),isAndroid:n(co),isMacOS:n(mo),isLinux:n(uo),isSolaris:n(go),isFreeBSD:n(po),isChromeOS:n(ho)}},bo=()=>fo({current:void 0,version:qt.unknown()}),vo=fo,yo=(y(lo),y("iOS"),y(co),y(uo),y(mo),y(go),y(po),y(ho),e=>window.matchMedia(e).matches);let xo=jt((()=>((e,t,o)=>{const n=Qt.browsers(),s=Qt.oses(),r=t.bind((e=>((e,t)=>re(t.brands,(t=>{const o=t.brand.toLowerCase();return G(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:qt.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>Xt(e,t).map((e=>{const o=qt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(ao,io),a=((e,t)=>Xt(e,t).map((e=>{const o=qt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(bo,vo),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:y(s),isiPhone:y(r),isTablet:y(l),isPhone:y(c),isTouch:y(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:y(d),isDesktop:y(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(navigator.userAgent,B.from(navigator.userAgentData),yo)));const wo=()=>xo(),So=e=>{const t=Fe((e=>{if(rt()&&g(e.target)){const t=Fe(e.target);if(ze(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return oe(t)}}return B.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=v(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},ko=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(So(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:S(Co,e,t,r,s)}},Co=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Oo=(e,t)=>{Ye(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},_o=(e,t)=>{const o=(e=>B.from(e.dom.nextSibling).map(Fe))(e);o.fold((()=>{Ye(e).each((e=>{Eo(e,t)}))}),(e=>{Oo(e,t)}))},To=(e,t)=>{const o=(e=>et(e,0))(e);o.fold((()=>{Eo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},Eo=(e,t)=>{e.dom.appendChild(t.dom)},Bo=(e,t)=>{N(t,(t=>{Eo(e,t)}))},Mo=e=>{e.dom.textContent="",N(Qe(e),(e=>{Ao(e)}))},Ao=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Do=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return zt(o,n)},Fo=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},Io=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Vo=e=>{const t=void 0===e?window:e,o=t.document,n=Do(Fe(o));return(e=>{const t=void 0===e?window:e;return wo().browser.isFirefox()?B.none():B.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return Io(n.left,n.top,o,s)}),(e=>Io(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},Ro=()=>Fe(document),zo=(e,t)=>e.view(t).fold(y([]),(t=>{const o=e.owner(t),n=zo(e,o);return[t].concat(n)}));var Ho=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?B.none():B.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(Fe)},owner:e=>$e(e)});const Po=e=>{const t=Ro(),o=Do(t),n=((e,t)=>{const o=t.owner(e),n=zo(t,o);return B.some(n)})(e,Ho);return n.fold(S(Pt,e),(t=>{const n=Nt(e),s=U(t,((e,t)=>{const o=Nt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return zt(s.left+n.left+o.left,s.top+n.top+o.top)}))},No=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Lo=e=>{const t=Pt(e),o=Ut(e),n=Vt(e);return No(t.left,t.top,o,n)},Wo=e=>{const t=Po(e),o=Ut(e),n=Vt(e);return No(t.left,t.top,o,n)},Uo=()=>Vo(window),jo=e=>{const t=t=>t(e),o=y(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:T,isError:_,map:t=>$o.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>B.some(e)};return s},Go=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:_,isError:T,map:t,mapError:t=>$o.error(t(e)),bind:t,exists:_,forall:T,getOr:x,or:x,getOrThunk:O,orThunk:O,getOrDie:C(String(e)),each:b,toOptional:B.none};return o},$o={value:jo,error:Go,fromOption:(e,t)=>e.fold((()=>Go(t)),jo)};var qo;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(qo||(qo={}));const Xo=(e,t,o)=>e.stype===qo.Error?t(e.serror):o(e.svalue),Ko=e=>({stype:qo.Value,svalue:e}),Yo=e=>({stype:qo.Error,serror:e}),Jo=Ko,Zo=Yo,Qo=Xo,en=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),tn=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},on=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)be(s,t)&&(o[t]=e(o[t],s[t]))}return o},nn=on(((e,t)=>i(e)&&i(t)?nn(e,t):t)),sn=on(((e,t)=>t)),rn=e=>({tag:"defaultedThunk",process:e}),an=e=>rn(y(e)),ln=e=>({tag:"mergeWithThunk",process:e}),cn=e=>{const t=(e=>{const t=[],o=[];return N(e,(e=>{Xo(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,v(Zo,q)(o)):Jo(t.values);var o},dn=e=>a(e)&&ae(e).length>100?" removed due to size":JSON.stringify(e,null,2),un=(e,t)=>Zo([{path:e,getErrorInfo:t}]),mn=e=>({extract:(t,o)=>{return n=e(o),s=e=>((e,t)=>un(e,y(t)))(t,e),n.stype===qo.Error?s(n.serror):n;var n,s},toString:y("val")}),gn=mn(Jo),pn=(e,t,o,n)=>n(fe(e,t).getOrThunk((()=>o(e)))),hn=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>Jo(B.none())),(e=>{const o=s.extract(t.concat([n]),e);return r=o,a=B.some,r.stype===qo.Value?{stype:qo.Value,svalue:a(r.svalue)}:r;var r,a}));switch(e.tag){case"required":return((e,t,o,n)=>fe(t,o).fold((()=>((e,t,o)=>un(e,(()=>'Could not find valid *required* value for "'+t+'" in '+dn(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return pn(o,n,e.process,r);case"option":return((e,t,o)=>o(fe(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(fe(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return pn(o,n,y({}),(t=>{const n=nn(e.process(o),t);return r(n)}))}},fn=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),bn=e=>ae(((e,t)=>{const o={};return me(e,t,ue(o),b),o})(e,g)),vn=e=>{const t=yn(e),o=U(e,((e,t)=>tn(t,(t=>nn(e,{[t]:!0})),y(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:bn(n),r=W(s,(e=>!ve(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>un(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},yn=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)tn(r,((o,r,a,i)=>{const l=hn(a,e,t,o,i);Qo(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?Zo(s):Jo(n)})(t,o,e),toString:()=>{const t=P(e,(e=>tn(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),xn=e=>({extract:(t,o)=>{const n=P(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return cn(n)},toString:()=>"array("+e.toString()+")"}),wn=e=>({extract:(t,o)=>{const n=[];for(const s of e){const e=s.extract(t,o);if(e.stype===qo.Value)return e;n.push(e)}return cn(n)},toString:()=>"oneOf("+P(e,(e=>e.toString())).join(", ")+")"}),Sn=(e,t)=>({extract:(o,n)=>{const s=ae(n),r=((t,o)=>xn(mn(e)).extract(t,o))(o,s);return i=e=>{const s=P(e,(e=>en(e,e,{tag:"required",process:{}},t)));return yn(s).extract(o,n)},(a=r).stype===qo.Value?i(a.svalue):a;var a,i},toString:()=>"setOf("+t.toString()+")"}),kn=v(xn,yn),Cn=y(gn),On=(e,t)=>mn((o=>{const n=typeof o;return e(o)?Jo(o):Zo(`Expected type: ${t} but got: ${n}`)})),_n=On(h,"number"),Tn=On(r,"string"),En=On(d,"boolean"),Bn=On(p,"function"),Mn=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>Mn(e[t])));default:return!1}},An=mn((e=>Mn(e)?Jo(e):Zo("Expected value to be acceptable for sending via postMessage"))),Dn=(e,t)=>({extract:(o,n)=>fe(n,e).fold((()=>((e,t)=>un(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>fe(o,n).fold((()=>((e,t,o)=>un(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+dn(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+ae(t)}),Fn=e=>mn((t=>e(t).fold(Zo,Jo))),In=(e,t)=>Sn((t=>e(t).fold(Yo,Ko)),t),Vn=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===qo.Error?{stype:qo.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),Xo(n,$o.error,$o.value);var n},Rn=e=>e.fold((e=>{throw new Error(Hn(e))}),x),zn=(e,t,o)=>Rn(Vn(e,t,o)),Hn=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:y("... (only showing first ten failures)")}]):e;return P(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+dn(e.input),Pn=(e,t)=>Dn(e,ce(t,yn)),Nn=en,Ln=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),Wn=e=>Fn((t=>V(e,t)?$o.value(t):$o.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),Un=e=>Nn(e,e,{tag:"required",process:{}},Cn()),jn=(e,t)=>Nn(e,e,{tag:"required",process:{}},t),Gn=e=>jn(e,_n),$n=e=>jn(e,Tn),qn=(e,t)=>Nn(e,e,{tag:"required",process:{}},Wn(t)),Xn=e=>jn(e,Bn),Kn=(e,t)=>Nn(e,e,{tag:"required",process:{}},yn(t)),Yn=(e,t)=>Nn(e,e,{tag:"required",process:{}},kn(t)),Jn=(e,t)=>Nn(e,e,{tag:"required",process:{}},xn(t)),Zn=e=>Nn(e,e,{tag:"option",process:{}},Cn()),Qn=(e,t)=>Nn(e,e,{tag:"option",process:{}},t),es=e=>Qn(e,_n),ts=e=>Qn(e,Tn),os=(e,t)=>Qn(e,Wn(t)),ns=e=>Qn(e,Bn),ss=(e,t)=>Qn(e,xn(t)),rs=(e,t)=>Qn(e,yn(t)),as=(e,t)=>Nn(e,e,an(t),Cn()),is=(e,t,o)=>Nn(e,e,an(t),o),ls=(e,t)=>is(e,t,_n),cs=(e,t)=>is(e,t,Tn),ds=(e,t,o)=>is(e,t,Wn(o)),us=(e,t)=>is(e,t,En),ms=(e,t)=>is(e,t,Bn),gs=(e,t,o)=>is(e,t,xn(o)),ps=(e,t,o)=>is(e,t,yn(o)),hs=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},fs=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return N(e,((n,s)=>{const r=ae(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=ae(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!K(t,(e=>V(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};fs([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const bs=(e,t)=>((e,t)=>({[e]:t}))(e,t),vs=e=>(e=>{const t={};return N(e,(e=>{t[e.key]=e.value})),t})(e),ys=e=>p(e)?e:_,xs=(e,t,o)=>{let n=e.dom;const s=ys(o);for(;n.parentNode;){n=n.parentNode;const e=Fe(n),o=t(e);if(o.isSome())return o;if(s(e))break}return B.none()},ws=(e,t,o)=>{const n=t(e),s=ys(o);return n.orThunk((()=>s(e)?B.none():xs(e,t,s)))},Ss=(e,t)=>je(e.element,t.event.target),ks={can:T,abort:_,run:b},Cs=e=>{if(!ve(e,"can")&&!ve(e,"abort")&&!ve(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...ks,...e}},Os=y,_s=Os("touchstart"),Ts=Os("touchmove"),Es=Os("touchend"),Bs=Os("touchcancel"),Ms=Os("mousedown"),As=Os("mousemove"),Ds=Os("mouseout"),Fs=Os("mouseup"),Is=Os("mouseover"),Vs=Os("focusin"),Rs=Os("focusout"),zs=Os("keydown"),Hs=Os("keyup"),Ps=Os("input"),Ns=Os("change"),Ls=Os("click"),Ws=Os("transitioncancel"),Us=Os("transitionend"),js=Os("transitionstart"),Gs=Os("selectstart"),$s=e=>y("alloy."+e),qs={tap:$s("tap")},Xs=$s("focus"),Ks=$s("blur.post"),Ys=$s("paste.post"),Js=$s("receive"),Zs=$s("execute"),Qs=$s("focus.item"),er=qs.tap,tr=$s("longpress"),or=$s("sandbox.close"),nr=$s("typeahead.cancel"),sr=$s("system.init"),rr=$s("system.touchmove"),ar=$s("system.touchend"),ir=$s("system.scroll"),lr=$s("system.resize"),cr=$s("system.attached"),dr=$s("system.detached"),ur=$s("system.dismissRequested"),mr=$s("system.repositionRequested"),gr=$s("focusmanager.shifted"),pr=$s("slotcontainer.visibility"),hr=$s("change.tab"),fr=$s("dismiss.tab"),br=$s("highlight"),vr=$s("dehighlight"),yr=(e,t)=>{kr(e,e.element,t,{})},xr=(e,t,o)=>{kr(e,e.element,t,o)},wr=e=>{yr(e,Zs())},Sr=(e,t,o)=>{kr(e,t,o,{})},kr=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},Cr=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Or=e=>vs(e),_r=(e,t)=>({key:e,value:Cs({abort:t})}),Tr=e=>({key:e,value:Cs({run:(e,t)=>{t.event.prevent()}})}),Er=(e,t)=>({key:e,value:Cs({run:t})}),Br=(e,t,o)=>({key:e,value:Cs({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),Mr=e=>t=>({key:e,value:Cs({run:(e,o)=>{Ss(e,o)&&t(e,o)}})}),Ar=(e,t,o)=>((e,t)=>Er(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{Cr(t,t.element,e,n)}))})))(e,t.partUids[o]),Dr=(e,t)=>Er(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>ws(n.target,(t=>e.getSystem().getByDom(t).toOptional()),_).getOr(e)));t(e,s,o)})),Fr=e=>Er(e,((e,t)=>{t.cut()})),Ir=e=>Er(e,((e,t)=>{t.stop()})),Vr=(e,t)=>Mr(e)(t),Rr=Mr(cr()),zr=Mr(dr()),Hr=Mr(sr()),Pr=(Gr=Zs(),e=>Er(Gr,e)),Nr=e=>e.dom.innerHTML,Lr=(e,t)=>{const o=$e(e).dom,n=Fe(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,Qe(Fe(o))})(t,o);Bo(n,s),Mo(e),Eo(e,n)},Wr=e=>nt(e)?"#shadow-root":(e=>{const t=Ae("div"),o=Fe(e.dom.cloneNode(!0));return Eo(t,o),Nr(t)})((e=>((e,t)=>Fe(e.dom.cloneNode(!1)))(e))(e)),Ur=e=>Wr(e),jr=Or([((e,t)=>({key:e,value:Cs({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>je(t,e.element)&&!je(t,o))(e,n,s)||(console.warn(Xs()+" did not get interpreted by the desired target. \nOriginator: "+Ur(n)+"\nTarget: "+Ur(s)+"\nCheck the "+Xs()+" event handlers"),!1)}})}))(Xs())]);var Gr,$r=Object.freeze({__proto__:null,events:jr});let qr=0;const Xr=e=>{const t=(new Date).getTime(),o=Math.floor(1e9*Math.random());return qr++,e+"_"+o+qr+String(t)},Kr=y("alloy-id-"),Yr=y("data-alloy-id"),Jr=Kr(),Zr=Yr(),Qr=(e,t)=>{Object.defineProperty(e.dom,Zr,{value:t,writable:!0})},ea=e=>{const t=ze(e)?e.dom[Zr]:null;return B.from(t)},ta=e=>Xr(e),oa=x,na=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+Ur(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:y("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:_}},sa=na(),ra=e=>P(e,(e=>Oe(e,"/*")?e.substring(0,e.length-"/*".length):e)),aa=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ra(r)}),e},ia=Xr("alloy-premade"),la=e=>(Object.defineProperty(e.element.dom,ia,{value:e.uid,writable:!0}),bs(ia,e)),ca=e=>fe(e,ia),da=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:ra(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),ua={init:()=>ma({readState:y("No State required")})},ma=e=>e,ga=(e,t)=>{const o={};return le(e,((e,n)=>{le(e,((e,s)=>{const r=fe(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},pa=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),ha=e=>e.cHandler,fa=(e,t)=>({name:e,handler:t}),ba=(e,t)=>{const o={};return N(e,(e=>{o[e.name()]=e.handlers(t)})),o},va=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const t=ee(o,((t,o)=>{const s=t.name,r=o.name,a=n.indexOf(s),i=n.indexOf(r);if(-1===a)throw new Error("The ordering for "+e+" does not have an entry for "+s+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));return a<i?-1:i<a?1:0}));return $o.value(t)}catch(e){return $o.error([e])}})("Event: "+o,0,e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>j(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>j(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{N(e,(e=>{e.run.apply(void 0,t)}))}}})(P(e,(e=>e.handler))))):((e,t)=>$o.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(P(t,(e=>e.name)),null,2)]))(o,e)},ya=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return N(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,$o.error(q(n))):((e,t)=>0===e.length?$o.value(t):$o.value(nn(t,sn.apply(void 0,e))))(o.values,t);var n})(ge(e,((e,o)=>(1===e.length?$o.value(e[0].handler):va(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:T,abort:_,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?W(t[o],(t=>R(e,(e=>e.name===t)))).join(" > "):e[0].name;return bs(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),xa="alloy.base.behaviour",wa=yn([Nn("dom","dom",{tag:"required",process:{}},yn([Un("tag"),as("styles",{}),as("classes",[]),as("attributes",{}),Zn("value"),Zn("innerHtml")])),Un("components"),Un("uid"),as("events",{}),as("apis",{}),Nn("eventOrder","eventOrder",($a={[Zs()]:["disabling",xa,"toggling","typeaheadevents"],[Xs()]:[xa,"focusing","keying"],[sr()]:[xa,"disabling","toggling","representing"],[Ps()]:[xa,"representing","streaming","invalidating"],[dr()]:[xa,"representing","item-events","tooltipping"],[Ms()]:["focusing",xa,"item-type-events"],[_s()]:["focusing",xa,"item-type-events"],[Is()]:["item-type-events","tooltipping"],[Js()]:["receiving","reflecting","tooltipping"]},ln(y($a))),Cn()),Zn("domModification")]),Sa=e=>e.events,ka=(e,t)=>{const o=ft(e,t);return void 0===o||""===o?[]:o.split(" ")},Ca=e=>void 0!==e.dom.classList,Oa=e=>ka(e,"class"),_a=(e,t)=>{Ca(e)?e.dom.classList.add(t):((e,t)=>{((e,t,o)=>{const n=ka(e,t).concat([o]);pt(e,t,n.join(" "))})(e,"class",t)})(e,t)},Ta=(e,t)=>{Ca(e)?e.dom.classList.remove(t):((e,t)=>{((e,t,o)=>{const n=W(ka(e,t),(e=>e!==o));n.length>0?pt(e,t,n.join(" ")):yt(e,t)})(e,"class",t)})(e,t),(e=>{0===(Ca(e)?e.dom.classList:Oa(e)).length&&yt(e,"class")})(e)},Ea=(e,t)=>Ca(e)&&e.dom.classList.contains(t),Ba=(e,t)=>{N(t,(t=>{_a(e,t)}))},Ma=(e,t)=>{N(t,(t=>{Ta(e,t)}))},Aa=e=>e.dom.value,Da=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},Fa=(e,t,o)=>{o.fold((()=>Eo(e,t)),(e=>{je(e,t)||(Oo(e,t),Ao(e))}))},Ia=(e,t,o)=>{const n=P(t,o),s=Qe(e);return N(s.slice(n.length),Ao),n},Va=(e,t,o,n)=>{const s=et(e,t),r=n(o,s),a=((e,t,o)=>et(e,t).map((e=>{if(o.exists((t=>!je(t,e)))){const t=o.map(Ve).getOr("span"),n=Ae(t);return Oo(e,n),n}return e})))(e,t,s);return Fa(e,r.element,a),r},Ra=(e,t)=>{const o=ae(e),n=ae(t);return{toRemove:J(n,o),toSet:((e,o)=>{const n={},s={};return me(e,((e,o)=>!be(t,o)||e!==t[o]),ue(n),ue(s)),{t:n,f:s}})(e).t}},za=(e,t)=>{const{class:o,style:n,...s}=(e=>j(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=Ra(e.attributes,s),i=Et(t),{toSet:l,toRemove:c}=Ra(e.styles,i),d=(e=>Ca(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Oa(e))(t),u=J(d,e.classes),m=J(e.classes,d);return N(a,(e=>yt(t,e))),ht(t,r),Ba(t,m),Ma(t,u),N(c,(e=>Mt(t,e))),kt(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{Ia(e,t,((t,o)=>{const n=et(e,o);return Fa(e,t,n),t}))})(t,o)}),(e=>{Lr(t,e)})),(()=>{const o=t;e.value.filter((e=>e!==Aa(o))).each((e=>Da(o,e)))})(),t},Ha=e=>{const t=(e=>{const t=fe(e,"behaviours").getOr({});return X(ae(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=P(t,(e=>rs(e.name(),[Un("config"),as("state",ua)]))),n=Vn("component.behaviours",yn(o),e.behaviours).fold((t=>{throw new Error(Hn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),x);return{list:t,data:ce(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return y(t)}))}})(e,t))(e,t)},Pa=(e,t)=>{const o=()=>m,n=hs(sa),s=Rn((e=>Vn("custom.definition",wa,e))(e)),r=Ha(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:P(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>pa({})),pa))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};N(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=ga(s,((e,t)=>({name:e,modification:t}))),a=e=>U(e,((e,t)=>({...t.modification,...e})),{}),i=U(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return pa({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=((e,t)=>{const o=t.filter((t=>Ve(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>be(e.dom,ia))(t))).bind((t=>((e,t)=>{try{const o=za(e,t);return B.some(o)}catch(e){return B.none()}})(e,t))).getOrThunk((()=>(e=>{const t=Ae(e.tag);ht(t,e.attributes),Ba(t,e.classes),kt(t,e.styles),e.innerHtml.each((e=>Lr(t,e)));const o=e.domChildren;return Bo(t,o),e.value.each((e=>{Da(t,e)})),t})(e)));return Qr(o,e.uid),o})(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":Sa(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...ba(t,e)};return ga(n,fa)})(e,o,n);return ya(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=hs(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(na(o))},element:c,syncComponents:()=>{const e=Qe(c),t=X(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},Na=e=>{const t=De(e);return La({element:t})},La=e=>{const t=zn("external.component",vn([Un("element"),Zn("uid")]),e),o=hs(na()),n=t.uid.getOrThunk((()=>ta("external")));Qr(t.element,n);const s={uid:n,getSystem:o.get,config:B.none,hasConfigured:_,connect:e=>{o.set(e)},disconnect:()=>{o.set(na((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:y("No state"),syncComponents:b,components:y([]),events:{}};return la(s)},Wa=ta,Ua=(e,t)=>ca(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=oa(e),s=((e,t)=>{const o=fe(e,"components").getOr([]);return t.fold((()=>P(o,ja)),(e=>P(o,((t,o)=>Ua(t,et(e,o))))))})(n,t),r={...n,events:{...$r,...o},components:s};return $o.value(Pa(r,t))})((e=>be(e,"uid"))(e)?e:{uid:Wa(""),...e},t).getOrDie())),ja=e=>Ua(e,B.none()),Ga=la;var $a,qa=(e,t,o,n,s)=>e(o,n)?B.some(o):p(s)&&s(o)?B.none():t(o,n,s);const Xa=(e,t,o)=>{let n=e.dom;const s=p(o)?o:_;for(;n.parentNode;){n=n.parentNode;const e=Fe(n);if(t(e))return B.some(e);if(s(e))break}return B.none()},Ka=(e,t,o)=>qa(((e,t)=>t(e)),Xa,e,t,o),Ya=(e,t,o)=>Ka(e,t,o).isSome(),Ja=(e,t,o)=>Xa(e,(e=>We(e,t)),o),Za=(e,t)=>((e,o)=>G(e.dom.childNodes,(e=>{return o=Fe(e),We(o,t);var o})).map(Fe))(e),Qa=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Ue(o)?B.none():B.from(o.querySelector(e)).map(Fe)})(t,e),ei=(e,t,o)=>qa(((e,t)=>We(e,t)),Ja,e,t,o),ti="aria-controls",oi=()=>{const e=Xr(ti);return{id:e,link:t=>{pt(t,ti,e)},unlink:e=>{yt(e,ti)}}},ni=(e,t)=>Ya(t,(t=>je(t,e.element)),_)||((e,t)=>(e=>Ka(e,(e=>{if(!ze(e))return!1;const t=ft(e,"id");return void 0!==t&&t.indexOf(ti)>-1})).bind((e=>{const t=ft(e,"id"),o=at(e);return Qa(o,`[${ti}="${t}"]`)})))(t).exists((t=>ni(e,t))))(e,t);var si;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(si||(si={}));const ri=hs({}),ai=["alloy/data/Fields","alloy/debugging/Debugging"],ii=(e,t,o)=>((e,t,o)=>{switch(fe(ri.get(),e).orThunk((()=>{const t=ae(ri.get());return re(t,(t=>e.indexOf(t)>-1?B.some(ri.get()[t]):B.none()))})).getOr(si.NORMAL)){case si.NORMAL:return o(li());case si.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();V(["mousemove","mouseover","mouseout",sr()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:P(o,(e=>V(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+Ur(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case si.STOP:return!0}})(e,t,o),li=y({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),ci=y([Un("menu"),Un("selectedMenu")]),di=y([Un("item"),Un("selectedItem")]);y(yn(di().concat(ci())));const ui=y(yn(di())),mi=Kn("initSize",[Un("numColumns"),Un("numRows")]),gi=()=>Kn("markers",[Un("backgroundMenu")].concat(ci()).concat(di())),pi=e=>Kn("markers",P(e,Un)),hi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");G(t,(e=>e.indexOf("alloy")>0&&!R(ai,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),Nn(t,t,o,Fn((e=>$o.value(((...t)=>e.apply(void 0,t))))))),fi=e=>hi(0,e,an(b)),bi=e=>hi(0,e,an(B.none)),vi=e=>hi(0,e,{tag:"required",process:{}}),yi=e=>hi(0,e,{tag:"required",process:{}}),xi=(e,t)=>Ln(e,y(t)),wi=e=>Ln(e,x),Si=y(mi),ki=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),Ci=fs([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Oi=Ci.southeast,_i=Ci.southwest,Ti=Ci.northeast,Ei=Ci.northwest,Bi=Ci.south,Mi=Ci.north,Ai=Ci.east,Di=Ci.west,Fi=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Ii=(e,t,o)=>Math.min(Math.max(e,t),o),Vi=(e,t)=>Z(["left","right","top","bottom"],(o=>fe(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),Ri="layout",zi=e=>e.x,Hi=(e,t)=>e.x+e.width/2-t.width/2,Pi=(e,t)=>e.x+e.width-t.width,Ni=(e,t)=>e.y-t.height,Li=e=>e.y+e.height,Wi=(e,t)=>e.y+e.height/2-t.height/2,Ui=(e,t,o)=>ki(zi(e),Li(e),o.southeast(),Oi(),"southeast",Vi(e,{left:1,top:3}),Ri),ji=(e,t,o)=>ki(Pi(e,t),Li(e),o.southwest(),_i(),"southwest",Vi(e,{right:0,top:3}),Ri),Gi=(e,t,o)=>ki(zi(e),Ni(e,t),o.northeast(),Ti(),"northeast",Vi(e,{left:1,bottom:2}),Ri),$i=(e,t,o)=>ki(Pi(e,t),Ni(e,t),o.northwest(),Ei(),"northwest",Vi(e,{right:0,bottom:2}),Ri),qi=(e,t,o)=>ki(Hi(e,t),Ni(e,t),o.north(),Mi(),"north",Vi(e,{bottom:2}),Ri),Xi=(e,t,o)=>ki(Hi(e,t),Li(e),o.south(),Bi(),"south",Vi(e,{top:3}),Ri),Ki=(e,t,o)=>ki((e=>e.x+e.width)(e),Wi(e,t),o.east(),Ai(),"east",Vi(e,{left:0}),Ri),Yi=(e,t,o)=>ki(((e,t)=>e.x-t.width)(e,t),Wi(e,t),o.west(),Di(),"west",Vi(e,{right:1}),Ri),Ji=()=>[Ui,ji,Gi,$i,Xi,qi,Ki,Yi],Zi=()=>[ji,Ui,$i,Gi,Xi,qi,Ki,Yi],Qi=()=>[Gi,$i,Ui,ji,qi,Xi],el=()=>[$i,Gi,ji,Ui,qi,Xi],tl=()=>[Ui,ji,Gi,$i,Xi,qi],ol=()=>[ji,Ui,$i,Gi,Xi,qi];var nl=Object.freeze({__proto__:null,events:e=>Or([Er(Js(),((t,o)=>{const n=e.channels,s=ae(n),r=o,a=((e,t)=>t.universal?e:W(e,(e=>V(t.channels,e))))(s,r);N(a,(e=>{const o=n[e],s=o.schema,a=zn("channel["+e+"] data\nReceiver: "+Ur(t.element),s,r.data);o.onReceive(t,a)}))}))])}),sl=[jn("channels",In($o.value,vn([vi("onReceive"),as("schema",Cn())])))];const rl=(e,t,o)=>Hr(((n,s)=>{o(n,e,t)})),al=e=>({key:e,value:void 0}),il=(e,t,o,n,s,r,a)=>{const i=e=>ve(e,o)?e[o]():B.none(),l=ce(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ra(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:y(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...ce(r,((e,t)=>aa(e,t))),...l,revoke:S(al,o),config:t=>{const n=zn(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:jt((()=>zn(o+"-config",e,t))),initialConfig:t,state:a}}},schema:y(t),exhibit:(e,t)=>we(i(e),fe(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>pa({}))),name:y(o),handlers:e=>i(e).map((e=>fe(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},ll=e=>vs(e),cl=vn([Un("fields"),Un("name"),as("active",{}),as("apis",{}),as("state",ua),as("extra",{})]),dl=e=>{const t=zn("Creating behaviour: "+e.name,cl,e);return((e,t,o,n,s,r)=>{const a=vn(e),i=rs(t,[("config",l=e,Qn("config",vn(l)))]);var l;return il(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},ul=vn([Un("branchKey"),Un("branches"),Un("name"),as("active",{}),as("apis",{}),as("state",ua),as("extra",{})]),ml=e=>{const t=zn("Creating behaviour: "+e.name,ul,e);return((e,t,o,n,s,r)=>{const a=e,i=rs(t,[Qn("config",e)]);return il(a,i,t,o,n,s,r)})(Pn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},gl=y(void 0),pl=dl({fields:sl,name:"receiving",active:nl});var hl=Object.freeze({__proto__:null,exhibit:(e,t)=>pa({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const fl=e=>e.dom.focus(),bl=e=>{const t=at(e).dom;return e.dom===t.activeElement},vl=(e=Ro())=>B.from(e.dom.activeElement).map(Fe),yl=e=>vl(at(e)).filter((t=>e.dom.contains(t.dom))),xl=(e,t)=>{const o=at(t),n=vl(o).bind((e=>{const o=t=>je(e,t);return o(t)?B.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=Fe(e.childNodes[n]);if(t(s))return B.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return B.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{vl(o).filter((t=>je(t,e))).fold((()=>{fl(e)}),b)})),s},wl=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},Sl=(e,t)=>{Ct(e,(e=>({...e,position:B.some(e.position)}))(t))},kl=fs([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Cl=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=B.some(i),m=B.some(l),g=B.some(c),p=B.some(d),h=B.none();return t.direction.fold((()=>wl(e,u,m,h,h)),(()=>wl(e,h,m,g,h)),(()=>wl(e,u,h,h,p)),(()=>wl(e,h,h,g,p)),(()=>wl(e,u,m,h,h)),(()=>wl(e,u,h,h,p)),(()=>wl(e,u,m,h,h)),(()=>wl(e,h,m,g,h)))},Ol=(e,t)=>e.fold((()=>{const e=t.rect;return wl("absolute",B.some(e.x),B.some(e.y),B.none(),B.none())}),((e,o,n,s)=>Cl("absolute",t,e,o,n,s)),((e,o,n,s)=>Cl("fixed",t,e,o,n,s))),_l=(e,t)=>{const o=S(Po,t),n=e.fold(o,o,(()=>{const e=Do();return Po(t).translate(-e.left,-e.top)})),s=Ut(t),r=Vt(t);return No(n.left,n.top,s,r)},Tl=(e,t)=>t.fold((()=>e.fold(Uo,Uo,No)),(t=>e.fold(t,t,(()=>{const o=t(),n=El(e,o.x,o.y);return No(n.left,n.top,o.width,o.height)})))),El=(e,t,o)=>{const n=zt(t,o);return e.fold(y(n),y(n),(()=>{const e=Do();return n.translate(-e.left,-e.top)}))};kl.none;const Bl=kl.relative,Ml=kl.fixed,Al="data-alloy-placement",Dl=e=>bt(e,Al),Fl=fs([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Il=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Ii(i,e.y,e.bottom):Ii(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return No(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=No(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Ii(a,o,d),g=Ii(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return No(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=y(t.bottom-o.y),s=y(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=y(t.right-o.x),i=y(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),x={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?Fl.fit(x):Fl.nofit(x,m,g,f)},Vl=e=>{const t=hs(B.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(B.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(B.some(e))}}},Rl=()=>Vl((e=>e.unbind())),zl=()=>{const e=Vl(b);return{...e,on:t=>e.get().each(t)}},Hl=T,Pl=(e,t,o)=>((e,t,o,n)=>ko(e,t,o,n,!1))(e,t,Hl,o),Nl=(e,t,o)=>((e,t,o,n)=>ko(e,t,o,n,!0))(e,t,Hl,o),Ll=So,Wl=["top","bottom","right","left"],Ul="data-alloy-transition-timer",jl=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>((e,t)=>K(t,(t=>Ea(e,t))))(e,t.classes))(e,n)){St(e,"position",o.position);const a=_l(t,e),l=Ol(t,{...s,rect:a}),c=Z(Wl,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return pe(t,((t,n)=>!((e,t,o=w)=>we(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Ct(e,c),i&&((e,t)=>{Ba(e,t.classes),bt(e,Ul).each((t=>{clearTimeout(parseInt(t,10)),yt(e,Ul)})),((e,t)=>{const o=Rl(),n=Rl();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return je(t.target,e)&&!Te(n)&&V(Wl,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===Us())&&(clearTimeout(s),yt(e,Ul),Ma(e,t.classes))}},l=Pl(e,js(),(t=>{a(t)&&(l.unbind(),o.set(Pl(e,Us(),i)),n.set(Pl(e,Ws(),i)))})),c=(e=>{const t=t=>{const o=Ot(e,t).split(/\s*,\s*/);return W(o,Te)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Oe(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return j(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),pt(e,Ul,s)}))})(e,t)})(e,n),At(e))}else Ma(e,n.classes)},Gl=(e,t)=>{((e,t)=>{const o=Ft.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);St(e,"max-height",o+"px")})(e,Math.floor(t))},$l=y(((e,t)=>{Gl(e,t),kt(e,{"overflow-x":"hidden","overflow-y":"auto"})})),ql=y(((e,t)=>{Gl(e,t)})),Xl=(e,t,o)=>void 0===e[t]?o:e[t],Kl=(e,t,o,n)=>{const s=((e,t,o,n)=>{Mt(t,"max-height"),Mt(t,"max-width");const s={width:Ut(r=t),height:Vt(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=Il(m,a,i,r);return g.fold(y(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:Fl.nofit(l,c,d,u)))};return j(t,((e,t)=>{const o=S(l,t);return e.fold(y(e),o)}),Fl.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Oi(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(x,x)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=Ol(o.origin,t);o.transition.each((s=>{jl(e,o.origin,n,s,t,o.lastPlacement)})),Sl(e,n)})(t,s,n),((e,t)=>{((e,t)=>{pt(e,Al,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;Ma(e,o.off),Ba(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},Yl=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],Jl=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>fe(o,e).getOr([]),i=(e,t,o)=>{const n=J(Yl,o);return{offset:zt(e,t),classesOn:X(o,a),classesOff:X(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},Zl=()=>Jl(0,0,{}),Ql=x,ec=(e,t)=>o=>"rtl"===tc(o)?t:e,tc=e=>"rtl"===Ot(e,"direction")?"rtl":"ltr";var oc;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(oc||(oc={}));const nc="data-alloy-vertical-dir",sc=e=>Ya(e,(e=>ze(e)&&ft(e,"data-alloy-vertical-dir")===oc.BottomToTop)),rc=()=>rs("layouts",[Un("onLtr"),Un("onRtl"),Zn("onBottomLtr"),Zn("onBottomRtl")]),ac=(e,t,o,n,s,r,a)=>{const i=a.map(sc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return ec(d,u)(e)};var ic=[Un("hotspot"),Zn("bubble"),as("overrides",{}),rc(),xi("placement",((e,t,o)=>{const n=t.hotspot,s=_l(o,n.element),r=ac(e.element,t,tl(),ol(),Qi(),el(),B.some(t.hotspot.element));return B.some(Ql({anchorBox:s,bubble:t.bubble.getOr(Zl()),overrides:t.overrides,layouts:r,placer:B.none()}))}))],lc=[Un("x"),Un("y"),as("height",0),as("width",0),as("bubble",Zl()),as("overrides",{}),rc(),xi("placement",((e,t,o)=>{const n=El(o,t.x,t.y),s=No(n.left,n.top,t.width,t.height),r=ac(e.element,t,Ji(),Zi(),Ji(),Zi(),B.none());return B.some(Ql({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r,placer:B.none()}))}))];const cc=fs([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),dc=e=>e.fold(x,((e,t,o)=>e.translate(-t,-o))),uc=e=>e.fold(x,x),mc=e=>j(e,((e,t)=>e.translate(t.left,t.top)),zt(0,0)),gc=e=>{const t=P(e,uc);return mc(t)},pc=cc.screen,hc=cc.absolute,fc=(e,t,o)=>{const n=$e(e.element),s=Do(n),r=((e,t,o)=>{const n=Ke(o.root).dom;return B.from(n.frameElement).map(Fe).filter((t=>{const o=$e(t),n=$e(e.element);return je(o,n)})).map(Pt)})(e,0,o).getOr(s);return hc(r,s.left,s.top)},bc=(e,t,o,n)=>{const s=pc(zt(e,t));return B.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},vc=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>gc(r),l=()=>gc(r),c=()=>(e=>{const t=P(e,dc);return mc(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?Qi():tl(),m=o.showAbove?el():ol(),g=ac(s,o,u,m,u,m,B.none());var p,h,f,b;return Ql({anchorBox:d,bubble:o.bubble.getOr(Zl()),overrides:o.overrides,layouts:g,placer:B.none()})}));var yc=[Un("node"),Un("root"),Zn("bubble"),rc(),as("overrides",{}),as("showAbove",!1),xi("placement",((e,t,o)=>{const n=fc(e,0,t);return t.node.filter(dt).bind((s=>{const r=s.dom.getBoundingClientRect(),a=bc(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return vc(a,n,t,o,i)}))}))];const xc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),wc=fs([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Sc=(wc.before,wc.on,wc.after,e=>e.fold(x,x,x)),kc=fs([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Cc={domRange:kc.domRange,relative:kc.relative,exact:kc.exact,exactFromRange:e=>kc.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>Fe(e.startContainer),relative:(e,t)=>Sc(e),exact:(e,t,o,n)=>e}))(e);return Ke(t)},range:xc},Oc=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},_c=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},Tc=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),Ec=fs([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Bc=(e,t,o)=>t(Fe(o.startContainer),o.startOffset,Fe(o.endContainer),o.endOffset),Mc=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:y(e),rtl:B.none}),relative:(t,o)=>({ltr:jt((()=>Oc(e,t,o))),rtl:jt((()=>B.some(Oc(e,o,t))))}),exact:(t,o,n,s)=>({ltr:jt((()=>_c(e,t,o,n,s))),rtl:jt((()=>B.some(_c(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>Ec.rtl(Fe(e.endContainer),e.endOffset,Fe(e.startContainer),e.startOffset))).getOrThunk((()=>Bc(0,Ec.ltr,o))):Bc(0,Ec.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});Ec.ltr,Ec.rtl;const Ac=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Ue(o)?[]:P(o.querySelectorAll(e),Fe)})(t,e),Dc=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return B.some(xc(Fe(t.startContainer),t.startOffset,Fe(o.endContainer),o.endOffset))}return B.none()},Fc=e=>{if(null===e.anchorNode||null===e.focusNode)return Dc(e);{const t=Fe(e.anchorNode),o=Fe(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=$e(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=je(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?B.some(xc(t,e.anchorOffset,o,e.focusOffset)):Dc(e)}},Ic=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?B.some(o).map(Tc):B.none()})(Mc(e,t)),Vc=((e,t)=>{const o=t=>e(t)?B.from(t.dom.nodeValue):B.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(He),Rc=(e,t)=>({element:e,offset:t}),zc=(e,t)=>He(e)?Rc(e,t):((e,t)=>{const o=Qe(e);if(0===o.length)return Rc(e,t);if(t<o.length)return Rc(o[t],0);{const e=o[o.length-1],t=He(e)?(e=>Vc.get(e))(e).length:Qe(e).length;return Rc(e,t)}})(e,t),Hc=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>B.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(Fc))(e)))().map((e=>{const t=zc(e.start,e.soffset),o=zc(e.finish,e.foffset);return Cc.range(t.element,t.offset,o.element,o.offset)}));var Pc=[Zn("getSelection"),Un("root"),Zn("bubble"),rc(),as("overrides",{}),as("showAbove",!1),xi("placement",((e,t,o)=>{const n=Ke(t.root).dom,s=fc(e,0,t),r=Hc(n,t).bind((e=>{const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?B.some(t).map(Tc):B.none()})(Mc(e,t)))(n,Cc.exactFromRange(e)).orThunk((()=>{const t=De("\ufeff");Oo(e.start,t);const o=Ic(n,Cc.exact(t,0,t,1));return Ao(t),o}));return t.bind((e=>bc(e.left,e.top,e.width,e.height)))})),a=Hc(n,t).bind((e=>ze(e.start)?B.some(e.start):Je(e.start))).getOr(e.element);return vc(r,s,t,o,a)}))];const Nc="link-layout",Lc=e=>e.x+e.width,Wc=(e,t)=>e.x-t.width,Uc=(e,t)=>e.y-t.height+e.height,jc=e=>e.y,Gc=(e,t,o)=>ki(Lc(e),jc(e),o.southeast(),Oi(),"southeast",Vi(e,{left:0,top:2}),Nc),$c=(e,t,o)=>ki(Wc(e,t),jc(e),o.southwest(),_i(),"southwest",Vi(e,{right:1,top:2}),Nc),qc=(e,t,o)=>ki(Lc(e),Uc(e,t),o.northeast(),Ti(),"northeast",Vi(e,{left:0,bottom:3}),Nc),Xc=(e,t,o)=>ki(Wc(e,t),Uc(e,t),o.northwest(),Ei(),"northwest",Vi(e,{right:1,bottom:3}),Nc),Kc=()=>[Gc,$c,qc,Xc],Yc=()=>[$c,Gc,Xc,qc];var Jc=[Un("item"),rc(),as("overrides",{}),xi("placement",((e,t,o)=>{const n=_l(o,t.item.element),s=ac(e.element,t,Kc(),Yc(),Kc(),Yc(),B.none());return B.some(Ql({anchorBox:n,bubble:Zl(),overrides:t.overrides,layouts:s,placer:B.none()}))}))],Zc=Pn("type",{selection:Pc,node:yc,hotspot:ic,submenu:Jc,makeshift:lc});const Qc=[Jn("classes",Tn),ds("mode","all",["all","layout","placement"])],ed=[as("useFixed",_),Zn("getBounds")],td=[jn("anchor",Zc),rs("transition",Qc)],od=(e,t,o,n,s,r,a)=>((e,t,o,n,s,r,a,i)=>{const l=Xl(a,"maxHeightFunction",$l()),c=Xl(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:Tl(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return Kl(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(o.anchorBox,t),s.element,o.bubble,o.layouts,r,n,o.overrides,a),nd=(e,t,o,n,s,r)=>{const a=r.map(Lo);return sd(e,t,o,n,s,a)},sd=(e,t,o,n,s,r)=>{const a=zn("placement.info",yn(td),s),i=a.anchor,l=n.element,c=o.get(n.uid);xl((()=>{St(l,"position","fixed");const s=Tt(l,"visibility");St(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return Ml(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Pt(e.element),o=e.element.dom.getBoundingClientRect();return Bl(t.left,t.top,o.width,o.height)})(e),u=i.placement,m=r.map(y).or(t.getBounds);u(e,i,d).each((t=>{const s=t.placer.getOr(od)(e,d,t,m,n,c,a.transition);o.set(n.uid,s)})),s.fold((()=>{Mt(l,"visibility")}),(e=>{St(l,"visibility",e)})),Tt(l,"left").isNone()&&Tt(l,"top").isNone()&&Tt(l,"right").isNone()&&Tt(l,"bottom").isNone()&&ye(Tt(l,"position"),"fixed")&&Mt(l,"position")}),l)};var rd=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{nd(e,t,o,n,s,B.none())},positionWithin:nd,positionWithinBounds:sd,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;N(["position","left","right","top","bottom"],(e=>Mt(s,e))),(e=>{yt(e,Al)})(s),o.clear(n.uid)}});const ad=dl({fields:ed,name:"positioning",active:hl,apis:rd,state:Object.freeze({__proto__:null,init:()=>{let e={};return ma({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>fe(e,t)})}})}),id=e=>e.getSystem().isConnected(),ld=e=>{yr(e,dr());const t=e.components();N(t,ld)},cd=e=>{const t=e.components();N(t,cd),yr(e,cr())},dd=(e,t)=>{e.getSystem().addToWorld(t),dt(e.element)&&cd(t)},ud=e=>{ld(e),e.getSystem().removeFromWorld(e)},md=(e,t)=>{Eo(e.element,t.element)},gd=(e,t)=>{pd(e,t,Eo)},pd=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),dt(e.element)&&cd(t),e.syncComponents()},hd=e=>{ld(e),Ao(e.element),e.getSystem().removeFromWorld(e)},fd=e=>{const t=Ye(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));hd(e),t.each((e=>{e.syncComponents()}))},bd=e=>{const t=e.components();N(t,hd),Mo(e.element),e.syncComponents()},vd=(e,t)=>{yd(e,t,Eo)},yd=(e,t,o)=>{o(e,t.element);const n=Qe(t.element);N(n,(e=>{t.getByDom(e).each(cd)}))},xd=e=>{const t=Qe(e.element);N(t,(t=>{e.getByDom(t).each(ld)})),Ao(e.element)},wd=(e,t,o,n)=>{o.get().each((t=>{bd(e)}));const s=t.getAttachPoint(e);gd(s,e);const r=e.getSystem().build(n);return gd(e,r),o.set(r),r},Sd=(e,t,o,n)=>{const s=wd(e,t,o,n);return t.onOpen(e,s),s},kd=(e,t,o)=>{o.get().each((n=>{bd(e),fd(e),t.onClose(e,n),o.clear()}))},Cd=(e,t,o)=>o.isOpen(),Od=(e,t,o)=>{const n=t.getAttachPoint(e);St(e.element,"position",ad.getMode(n)),((e,t,o,n)=>{Tt(e.element,t).fold((()=>{yt(e.element,o)}),(t=>{pt(e.element,o,t)})),St(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},_d=(e,t,o)=>{(e=>R(["top","left","right","bottom"],(t=>Tt(e,t).isSome())))(e.element)||Mt(e.element,"position"),((e,t,o)=>{bt(e.element,o).fold((()=>Mt(e.element,t)),(o=>St(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var Td=Object.freeze({__proto__:null,cloak:Od,decloak:_d,open:Sd,openWhileCloaked:(e,t,o,n,s)=>{Od(e,t),Sd(e,t,o,n),s(),_d(e,t)},close:kd,isOpen:Cd,isPartOf:(e,t,o,n)=>Cd(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>wd(e,t,o,n)))}),Ed=Object.freeze({__proto__:null,events:(e,t)=>Or([Er(or(),((o,n)=>{kd(o,e,t)}))])}),Bd=[fi("onOpen"),fi("onClose"),Un("isPartOf"),Un("getAttachPoint"),as("cloakVisibilityAttr","data-precloak-visibility")],Md=Object.freeze({__proto__:null,init:()=>{const e=zl(),t=y("not-implemented");return ma({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Ad=dl({fields:Bd,name:"sandboxing",active:Ed,apis:Td,state:Md}),Dd=y("dismiss.popups"),Fd=y("reposition.popups"),Id=y("mouse.released"),Vd=vn([as("isExtraPart",_),rs("fireEventInstead",[as("event",ur())])]),Rd=e=>{const t=zn("Dismissal",Vd,e);return{[Dd()]:{schema:vn([Un("target")]),onReceive:(e,o)=>{Ad.isOpen(e)&&(Ad.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Ad.close(e)),(t=>yr(e,t.event))))}}}},zd=vn([rs("fireEventInstead",[as("event",mr())]),Xn("doReposition")]),Hd=e=>{const t=zn("Reposition",zd,e);return{[Fd()]:{onReceive:e=>{Ad.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>yr(e,t.event)))}}}},Pd=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},Nd=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var Ld=Object.freeze({__proto__:null,onLoad:Pd,onUnload:Nd,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),Wd=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Rr(((o,n)=>{Pd(o,e,t)})),zr(((o,n)=>{Nd(o,e,t)}))]:[rl(e,t,Pd)];return Or(o)}});const Ud=()=>{const e=hs(null);return ma({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},jd=()=>{const e=hs({}),t=hs({});return ma({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>fe(e.get(),o).orThunk((()=>fe(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};N(o,(e=>{r[e.value]=e,fe(e,"meta").each((t=>{fe(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var Gd=Object.freeze({__proto__:null,memory:Ud,dataset:jd,manual:()=>ma({readState:b}),init:e=>e.store.manager.state(e)});const $d=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var qd=[Zn("initialValue"),Un("getFallbackEntry"),Un("getDataKey"),Un("setValue"),xi("manager",{setValue:$d,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{$d(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:jd})],Xd=[Un("getValue"),as("setValue",b),Zn("initialValue"),xi("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:ua.init})],Kd=[Zn("initialValue"),xi("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:Ud})],Yd=[is("store",{mode:"memory"},Pn("mode",{memory:Kd,manual:Xd,dataset:qd})),fi("onSetValue"),as("resetOnDom",!1)];const Jd=dl({fields:Yd,name:"representing",active:Wd,apis:Ld,extra:{setValueFrom:(e,t)=>{const o=Jd.getValue(t);Jd.setValue(e,o)}},state:Gd}),Zd=(e,t)=>ps(e,{},P(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,Nn(o,o,{tag:"option",process:{}},mn((e=>Zo("The field: "+o+" is forbidden. "+n))));var o,n})).concat([Ln("dump",x)])),Qd=e=>e.dump,eu=(e,t)=>({...ll(t),...e.dump}),tu=Zd,ou=eu,nu="placeholder",su=fs([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),ru=e=>be(e,"uiType"),au=(e,t,o,n)=>((e,t,o,n)=>ru(o)&&o.uiType===nu?((e,t,o,n)=>e.exists((e=>e!==o.owner))?su.single(!0,y(o)):fe(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+ae(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):su.single(!1,y(o)))(e,0,o,n).fold(((s,r)=>{const a=ru(o)?r(t,o.config,o.validated):r(t),i=fe(a,"components").getOr([]),l=X(i,(o=>au(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(ru(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(x)(e)}return n(t)})),iu=su.single,lu=su.multiple,cu=y(nu),du=fs([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),uu=as("factory",{sketch:x}),mu=as("schema",[]),gu=Un("name"),pu=Nn("pname","pname",rn((e=>"<alloy."+Xr(e.name)+">")),Cn()),hu=Ln("schema",(()=>[Zn("preprocess")])),fu=as("defaults",y({})),bu=as("overrides",y({})),vu=yn([uu,mu,gu,pu,fu,bu]),yu=yn([uu,mu,gu,fu,bu]),xu=yn([uu,mu,gu,pu,fu,bu]),wu=yn([uu,hu,gu,Un("unit"),pu,fu,bu]),Su=e=>e.fold(B.some,B.none,B.some,B.some),ku=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},Cu=(e,t)=>o=>{const n=zn("Converting part type",t,o);return e(n)},Ou=Cu(du.required,vu),_u=Cu(du.external,yu),Tu=Cu(du.optional,xu),Eu=Cu(du.group,wu),Bu=y("entirety");var Mu=Object.freeze({__proto__:null,required:Ou,external:_u,optional:Tu,group:Eu,asNamedPart:Su,name:ku,asCommon:e=>e.fold(x,x,x,x),original:Bu});const Au=(e,t,o,n)=>nn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),Du=(e,t)=>{const o={};return N(t,(t=>{Su(t).each((t=>{const n=Fu(e,t.pname);o[t.name]=o=>{const s=zn("Part: "+t.name+" in "+e,yn(t.schema),o);return{...n,config:o,validated:s}}}))})),o},Fu=(e,t)=>({uiType:cu(),owner:e,name:t}),Iu=(e,t,o)=>({uiType:cu(),owner:e,name:t,config:o,validated:{}}),Vu=e=>X(e,(e=>e.fold(B.none,B.some,B.none,B.none).map((e=>Kn(e.name,e.schema.concat([wi(Bu())])))).toArray())),Ru=e=>P(e,ku),zu=(e,t,o)=>((e,t,o)=>{const n={},s={};return N(o,(e=>{e.fold((e=>{n[e.pname]=iu(!0,((t,o,n)=>e.factory.sketch(Au(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=y(e.factory.sketch(Au(t,e,o[Bu()]),o))}),(e=>{n[e.pname]=iu(!1,((t,o,n)=>e.factory.sketch(Au(t,e,o,n))))}),(e=>{n[e.pname]=lu(!0,((t,o,n)=>{const s=t[e.name];return P(s,(o=>e.factory.sketch(nn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:y(n),externals:y(s)}})(0,t,o),Hu=(e,t,o)=>((e,t,o,n)=>{const s=ce(n,((e,t)=>((e,t)=>{let o=!1;return{name:y(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>X(o,(o=>au(e,t,o,n))))(e,t,o,s);return le(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(B.some(e),t,t.components,o),Pu=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},Nu=(e,t,o)=>Pu(e,t,o).getOrDie("Could not find part: "+o),Lu=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return N(o,(e=>{n[e]=y(r.getByUid(s[e]))})),n},Wu=(e,t)=>{const o=e.getSystem();return ce(t.partUids,((e,t)=>y(o.getByUid(e))))},Uu=e=>ae(e.partUids),ju=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return N(o,(e=>{n[e]=y(r.getByUid(s[e]).getOrDie())})),n},Gu=(e,t)=>{const o=Ru(t);return vs(P(o,(t=>({key:t,value:e+"-"+t}))))},$u=e=>Nn("partUids","partUids",ln((t=>Gu(t.uid,e))),Cn());var qu=Object.freeze({__proto__:null,generate:Du,generateOne:Iu,schemas:Vu,names:Ru,substitutes:zu,components:Hu,defaultUids:Gu,defaultUidsSchema:$u,getAllParts:Wu,getAllPartNames:Uu,getPart:Pu,getPartOrDie:Nu,getParts:Lu,getPartsOrDie:ju});const Xu=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[Kn("parts",e)]:[]).concat([Un("uid"),as("dom",{}),as("components",[]),wi("originalSpec"),as("debug.sketcher",{})]).concat(t))(n,s);return zn(e+" [SpecSchema]",vn(r.concat(t)),o)},Ku=(e,t,o,n,s)=>{const r=Yu(s),a=Vu(o),i=$u(o),l=Xu(e,t,r,a,[i]),c=zu(0,l,o);return n(l,Hu(e,l,c.internals()),r,c.externals())},Yu=e=>(e=>be(e,"uid"))(e)?e:{...e,uid:ta("uid")},Ju=vn([Un("name"),Un("factory"),Un("configFields"),as("apis",{}),as("extraApis",{})]),Zu=vn([Un("name"),Un("factory"),Un("configFields"),Un("partFields"),as("apis",{}),as("extraApis",{})]),Qu=e=>{const t=zn("Sketcher for "+e.name,Ju,e),o=ce(t.apis,da),n=ce(t.extraApis,((e,t)=>aa(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=Yu(n);return o(Xu(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},em=e=>{const t=zn("Sketcher for "+e.name,Zu,e),o=Du(t.name,t.partFields),n=ce(t.apis,da),s=ce(t.extraApis,((e,t)=>aa(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>Ku(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},tm=e=>Le("input")(e)&&"radio"!==ft(e,"type")||Le("textarea")(e);var om=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const nm=[Un("find")],sm=dl({fields:nm,name:"composing",apis:om}),rm=["input","button","textarea","select"],am=(e,t,o)=>{(t.disabled()?mm:gm)(e,t)},im=(e,t)=>!0===t.useNative&&V(rm,Ve(e.element)),lm=e=>{pt(e.element,"disabled","disabled")},cm=e=>{yt(e.element,"disabled")},dm=e=>{pt(e.element,"aria-disabled","true")},um=e=>{pt(e.element,"aria-disabled","false")},mm=(e,t,o)=>{t.disableClass.each((t=>{_a(e.element,t)})),(im(e,t)?lm:dm)(e),t.onDisabled(e)},gm=(e,t,o)=>{t.disableClass.each((t=>{Ta(e.element,t)})),(im(e,t)?cm:um)(e),t.onEnabled(e)},pm=(e,t)=>im(e,t)?(e=>vt(e.element,"disabled"))(e):(e=>"true"===ft(e.element,"aria-disabled"))(e);var hm=Object.freeze({__proto__:null,enable:gm,disable:mm,isDisabled:pm,onLoad:am,set:(e,t,o,n)=>{(n?mm:gm)(e,t)}}),fm=Object.freeze({__proto__:null,exhibit:(e,t)=>pa({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Or([_r(Zs(),((t,o)=>pm(t,e))),rl(e,t,am)])}),bm=[ms("disabled",_),as("useNative",!0),Zn("disableClass"),fi("onDisabled"),fi("onEnabled")];const vm=dl({fields:bm,name:"disabling",active:fm,apis:hm}),ym=(e,t,o,n)=>{const s=Ac(e.element,"."+t.highlightClass);N(s,(o=>{R(n,(e=>e.element===o))||(Ta(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),yr(o,vr())})))}))},xm=(e,t,o,n)=>{ym(e,t,0,[n]),wm(e,t,o,n)||(_a(n.element,t.highlightClass),t.onHighlight(e,n),yr(n,br()))},wm=(e,t,o,n)=>Ea(n.element,t.highlightClass),Sm=(e,t,o)=>Qa(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),km=(e,t,o)=>{const n=Ac(e.element,"."+t.itemClass);return(n.length>0?B.some(n[n.length-1]):B.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},Cm=(e,t,o,n)=>{const s=Ac(e.element,"."+t.itemClass);return $(s,(e=>Ea(e,t.highlightClass))).bind((t=>{const o=Fi(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},Om=(e,t,o)=>{const n=Ac(e.element,"."+t.itemClass);return xe(P(n,(t=>e.getSystem().getByDom(t).toOptional())))};var _m=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>ym(e,t,0,[]),dehighlight:(e,t,o,n)=>{wm(e,t,o,n)&&(Ta(n.element,t.highlightClass),t.onDehighlight(e,n),yr(n,vr()))},highlight:xm,highlightFirst:(e,t,o)=>{Sm(e,t).each((n=>{xm(e,t,o,n)}))},highlightLast:(e,t,o)=>{km(e,t).each((n=>{xm(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=Ac(e.element,"."+t.itemClass);return B.from(s[n]).fold((()=>$o.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{xm(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=Om(e,t);G(s,n).each((n=>{xm(e,t,o,n)}))},isHighlighted:wm,getHighlighted:(e,t,o)=>Qa(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:Sm,getLast:km,getPrevious:(e,t,o)=>Cm(e,t,0,-1),getNext:(e,t,o)=>Cm(e,t,0,1),getCandidates:Om}),Tm=[Un("highlightClass"),Un("itemClass"),fi("onHighlight"),fi("onDehighlight")];const Em=dl({fields:Tm,name:"highlighting",apis:_m}),Bm=[8],Mm=[9],Am=[13],Dm=[27],Fm=[32],Im=[37],Vm=[38],Rm=[39],zm=[40],Hm=(e,t,o)=>{const n=Y(e.slice(0,t)),s=Y(e.slice(t+1));return G(n.concat(s),o)},Pm=(e,t,o)=>{const n=Y(e.slice(0,t));return G(n,o)},Nm=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return G(s.concat(n),o)},Lm=(e,t,o)=>{const n=e.slice(t+1);return G(n,o)},Wm=e=>t=>{const o=t.raw;return V(e,o.which)},Um=e=>t=>K(e,(e=>e(t))),jm=e=>!0===e.raw.shiftKey,Gm=e=>!0===e.raw.ctrlKey,$m=k(jm),qm=(e,t)=>({matches:e,classification:t}),Xm=(e,t,o)=>{t.exists((e=>o.exists((t=>je(t,e)))))||xr(e,gr(),{prevFocus:t,newFocus:o})},Km=()=>{const e=e=>yl(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);Xm(t,n,s)}}},Ym=()=>{const e=e=>Em.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Em.highlight(t,e)}));const s=e(t);Xm(t,n,s)}}};var Jm;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(Jm||(Jm={}));const Zm=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,G(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([as("focusManager",Km()),is("focusInside","onFocus",Fn((e=>V(["onFocus","onEnterOrSpace","onApi"],e)?$o.value(e):$o.error("Invalid value for focusInside")))),xi("handler",a),xi("state",t),xi("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==Jm.OnFocusMode?B.none():s(e).map((o=>Er(Xs(),((n,s)=>{o(n,e,t),s.stop()})))),i=[Er(zs(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=Wm(Fm.concat(Am))(n.event);e.focusInside===Jm.OnEnterOrSpaceMode&&r&&Ss(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Er(Hs(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Or(a.toArray().concat(i))}};return a},Qm=e=>{const t=[Zn("onEscape"),Zn("onEnter"),as("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),as("firstTabstop",0),as("useTabstopAt",T),Zn("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>ei(t,e))).getOr(t);return It(o)>0},n=(e,t,n)=>{((e,t)=>{const n=Ac(e.element,t.selector),s=W(n,(e=>o(t,e)));return B.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},s=(e,t,n,s)=>{const r=Ac(e.element,n.selector);return((e,t)=>t.focusManager.get(e).bind((e=>ei(e,t.selector))))(e,n).bind((t=>$(r,S(je,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?B.some(!0):B.none()),(t=>(s.focusManager.set(e,t),B.some(!0)))))(e,r,t,n,s)))))},r=y([qm(Um([jm,Wm(Mm)]),((e,t,o)=>{const n=o.cyclic?Hm:Pm;return s(e,0,o,n)})),qm(Wm(Mm),((e,t,o)=>{const n=o.cyclic?Nm:Lm;return s(e,0,o,n)})),qm(Wm(Dm),((e,t,o)=>o.onEscape.bind((o=>o(e,t))))),qm(Um([$m,Wm(Am)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),a=y([]);return Zm(t,ua.init,r,a,(()=>B.some(n)))};var eg=Qm(Ln("cyclic",_)),tg=Qm(Ln("cyclic",T));const og=(e,t,o)=>tm(o)&&Wm(Fm)(t.event)?B.none():((e,t,o)=>(Sr(e,o,Zs()),B.some(!0)))(e,0,o),ng=(e,t)=>B.some(!0),sg=[as("execute",og),as("useSpace",!1),as("useEnter",!0),as("useControlEnter",!1),as("useDown",!1)],rg=(e,t,o)=>o.execute(e,t,e.element);var ag=Zm(sg,ua.init,((e,t,o,n)=>{const s=o.useSpace&&!tm(e.element)?Fm:[],r=o.useEnter?Am:[],a=o.useDown?zm:[],i=s.concat(r).concat(a);return[qm(Wm(i),rg)].concat(o.useControlEnter?[qm(Um([Gm,Wm(Am)]),rg)]:[])}),((e,t,o,n)=>o.useSpace&&!tm(e.element)?[qm(Wm(Fm),ng)]:[]),(()=>B.none()));const ig=()=>{const e=zl();return ma({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var lg=Object.freeze({__proto__:null,flatgrid:ig,init:e=>e.state(e)});const cg=e=>(t,o,n,s)=>{const r=e(t.element);return gg(r,t,o,n,s)},dg=(e,t)=>{const o=ec(e,t);return cg(o)},ug=(e,t)=>{const o=ec(t,e);return cg(o)},mg=e=>(t,o,n,s)=>gg(e,t,o,n,s),gg=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),pg=mg,hg=mg,fg=mg,bg=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),vg=(e,t,o)=>{const n=Ac(e,o);return((e,o)=>$(e,(e=>je(e,t))).map((t=>({index:t,candidates:e}))))(W(n,bg))},yg=(e,t)=>$(e,(e=>je(t,e))),xg=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?B.some(e[n]):B.none()})),wg=(e,t,o,n,s)=>xg(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Fi(r,s,0,a-1);return B.some({row:t,column:i})})),Sg=(e,t,o,n,s)=>xg(e,t,n,((t,r)=>{const a=Fi(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Ii(r,0,i-1);return B.some({row:a,column:l})})),kg=[Un("selector"),as("execute",og),bi("onEscape"),as("captureTab",!1),Si()],Cg=(e,t,o)=>{Qa(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},Og=e=>(t,o,n,s)=>vg(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),_g=(e,t,o)=>o.captureTab?B.some(!0):B.none(),Tg=Og(((e,t,o,n)=>wg(e,t,o,n,-1))),Eg=Og(((e,t,o,n)=>wg(e,t,o,n,1))),Bg=Og(((e,t,o,n)=>Sg(e,t,o,n,-1))),Mg=Og(((e,t,o,n)=>Sg(e,t,o,n,1))),Ag=y([qm(Wm(Im),dg(Tg,Eg)),qm(Wm(Rm),ug(Tg,Eg)),qm(Wm(Vm),pg(Bg)),qm(Wm(zm),hg(Mg)),qm(Um([jm,Wm(Mm)]),_g),qm(Um([$m,Wm(Mm)]),_g),qm(Wm(Dm),((e,t,o)=>o.onEscape(e,t))),qm(Wm(Fm.concat(Am)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>ei(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),Dg=y([qm(Wm(Fm),ng)]);var Fg=Zm(kg,ig,Ag,Dg,(()=>B.some(Cg)));const Ig=(e,t,o,n)=>{const s=(e,t,o)=>{const r=Fi(t,n,0,o.length-1);return r===e?B.none():(a=o[r],"button"===Ve(a)&&"disabled"===ft(a,"disabled")?s(e,r,o):B.from(o[r]));var a};return vg(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return s(t,t,o)}))},Vg=[Un("selector"),as("getInitial",B.none),as("execute",og),bi("onEscape"),as("executeOnMove",!1),as("allowVertical",!0)],Rg=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>ei(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),zg=(e,t,o)=>{t.getInitial(e).orThunk((()=>Qa(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},Hg=(e,t,o)=>Ig(e,o.selector,t,-1),Pg=(e,t,o)=>Ig(e,o.selector,t,1),Ng=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?Rg(t,o,n):B.some(!0))),Lg=(e,t,o)=>o.onEscape(e,t),Wg=y([qm(Wm(Fm),ng)]);var Ug=Zm(Vg,ua.init,((e,t,o,n)=>{const s=Im.concat(o.allowVertical?Vm:[]),r=Rm.concat(o.allowVertical?zm:[]);return[qm(Wm(s),Ng(dg(Hg,Pg))),qm(Wm(r),Ng(ug(Hg,Pg))),qm(Wm(Am),Rg),qm(Wm(Fm),Rg),qm(Wm(Dm),Lg)]}),Wg,(()=>B.some(zg)));const jg=(e,t,o)=>B.from(e[t]).bind((e=>B.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),Gg=(e,t,o,n)=>{const s=e[t].length,r=Fi(o,n,0,s-1);return jg(e,t,r)},$g=(e,t,o,n)=>{const s=Fi(o,n,0,e.length-1),r=e[s].length,a=Ii(t,0,r-1);return jg(e,s,a)},qg=(e,t,o,n)=>{const s=e[t].length,r=Ii(o+n,0,s-1);return jg(e,t,r)},Xg=(e,t,o,n)=>{const s=Ii(o+n,0,e.length-1),r=e[s].length,a=Ii(t,0,r-1);return jg(e,s,a)},Kg=[Kn("selectors",[Un("row"),Un("cell")]),as("cycles",!0),as("previousSelector",B.none),as("execute",og)],Yg=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return Qa(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},Jg=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return ei(n,s.selectors.row).bind((e=>{const t=Ac(e,s.selectors.cell);return yg(t,n).bind((t=>{const n=Ac(o,s.selectors.row);return yg(n,e).bind((e=>{const o=((e,t)=>P(e,(e=>Ac(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},Zg=Jg(((e,t,o)=>Gg(e,t,o,-1)),((e,t,o)=>qg(e,t,o,-1))),Qg=Jg(((e,t,o)=>Gg(e,t,o,1)),((e,t,o)=>qg(e,t,o,1))),ep=Jg(((e,t,o)=>$g(e,o,t,-1)),((e,t,o)=>Xg(e,o,t,-1))),tp=Jg(((e,t,o)=>$g(e,o,t,1)),((e,t,o)=>Xg(e,o,t,1))),op=y([qm(Wm(Im),dg(Zg,Qg)),qm(Wm(Rm),ug(Zg,Qg)),qm(Wm(Vm),pg(ep)),qm(Wm(zm),hg(tp)),qm(Wm(Fm.concat(Am)),((e,t,o)=>yl(e.element).bind((n=>o.execute(e,t,n)))))]),np=y([qm(Wm(Fm),ng)]);var sp=Zm(Kg,ua.init,op,np,(()=>B.some(Yg)));const rp=[Un("selector"),as("execute",og),as("moveOnTab",!1)],ap=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),ip=(e,t,o)=>{Qa(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},lp=(e,t,o)=>Ig(e,o.selector,t,-1),cp=(e,t,o)=>Ig(e,o.selector,t,1),dp=y([qm(Wm(Vm),fg(lp)),qm(Wm(zm),fg(cp)),qm(Um([jm,Wm(Mm)]),((e,t,o,n)=>o.moveOnTab?fg(lp)(e,t,o,n):B.none())),qm(Um([$m,Wm(Mm)]),((e,t,o,n)=>o.moveOnTab?fg(cp)(e,t,o,n):B.none())),qm(Wm(Am),ap),qm(Wm(Fm),ap)]),up=y([qm(Wm(Fm),ng)]);var mp=Zm(rp,ua.init,dp,up,(()=>B.some(ip)));const gp=[bi("onSpace"),bi("onEnter"),bi("onShiftEnter"),bi("onLeft"),bi("onRight"),bi("onTab"),bi("onShiftTab"),bi("onUp"),bi("onDown"),bi("onEscape"),as("stopSpaceKeyup",!1),Zn("focusIn")];var pp=Zm(gp,ua.init,((e,t,o)=>[qm(Wm(Fm),o.onSpace),qm(Um([$m,Wm(Am)]),o.onEnter),qm(Um([jm,Wm(Am)]),o.onShiftEnter),qm(Um([jm,Wm(Mm)]),o.onShiftTab),qm(Um([$m,Wm(Mm)]),o.onTab),qm(Wm(Vm),o.onUp),qm(Wm(zm),o.onDown),qm(Wm(Im),o.onLeft),qm(Wm(Rm),o.onRight),qm(Wm(Fm),o.onSpace),qm(Wm(Dm),o.onEscape)]),((e,t,o)=>o.stopSpaceKeyup?[qm(Wm(Fm),ng)]:[]),(e=>e.focusIn));const hp=eg.schema(),fp=tg.schema(),bp=Ug.schema(),vp=Fg.schema(),yp=sp.schema(),xp=ag.schema(),wp=mp.schema(),Sp=pp.schema(),kp=ml({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:hp,cyclic:fp,flow:bp,flatgrid:vp,matrix:yp,execution:xp,menu:wp,special:Sp}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ve(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:lg}),Cp=(e,t)=>{xl((()=>{((e,t,o)=>{const n=e.components();(e=>{N(e.components(),(e=>Ao(e.element))),Mo(e.element),e.syncComponents()})(e);const s=o(t),r=J(n,s);N(r,(t=>{ld(t),e.getSystem().removeFromWorld(t)})),N(s,(t=>{id(t)?md(e,t):(e.getSystem().addToWorld(t),md(e,t),dt(e.element)&&cd(t))})),e.syncComponents()})(e,t,(()=>P(t,e.getSystem().build)))}),e.element)},Op=(e,t)=>{xl((()=>{((o,n,s)=>{const r=o.components(),a=X(n,(e=>ca(e).toArray()));N(r,(e=>{V(a,e)||ud(e)}));const i=((e,t,o)=>Ia(e,t,((t,n)=>Va(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=J(r,i);N(l,(e=>{id(e)&&ud(e)})),N(i,(e=>{id(e)||dd(o,e)})),o.syncComponents()})(e,t)}),e.element)},_p=(e,t,o,n)=>{ud(t);const s=Va(e.element,o,n,e.getSystem().buildOrPatch);dd(e,s),e.syncComponents()},Tp=(e,t,o)=>{const n=e.getSystem().build(o);pd(e,n,t)},Ep=(e,t,o,n)=>{fd(t),Tp(e,((e,t)=>((e,t,o)=>{et(e,o).fold((()=>{Eo(e,t)}),(e=>{Oo(e,t)}))})(e,t,o)),n)},Bp=(e,t)=>e.components(),Mp=(e,t,o,n,s)=>{const r=Bp(e);return B.from(r[n]).map((o=>(s.fold((()=>fd(o)),(s=>{(t.reuseDom?_p:Ep)(e,o,n,s)})),o)))};var Ap=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Tp(e,Eo,n)},prepend:(e,t,o,n)=>{Tp(e,To,n)},remove:(e,t,o,n)=>{const s=Bp(e),r=G(s,(e=>je(n.element,e.element)));r.each(fd)},replaceAt:Mp,replaceBy:(e,t,o,n,s)=>{const r=Bp(e);return $(r,n).bind((o=>Mp(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?Op:Cp)(e,n),contents:Bp});const Dp=dl({fields:[us("reuseDom",!0)],name:"replacing",apis:Ap}),Fp=(e,t)=>{const o=((e,t)=>{const o=Or(t);return dl({fields:[Un("enabled")],name:e,active:{events:y(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:y({}),initialConfig:{},state:ua}}},Ip=(e,t)=>{t.ignore||(fl(e.element),t.onFocus(e))};var Vp=Object.freeze({__proto__:null,focus:Ip,blur:(e,t)=>{t.ignore||(e=>{e.dom.blur()})(e.element)},isFocused:e=>bl(e.element)}),Rp=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return pa(o)},events:e=>Or([Er(Xs(),((t,o)=>{Ip(t,e),o.stop()}))].concat(e.stopMousedown?[Er(Ms(),((e,t)=>{t.event.prevent()}))]:[]))}),zp=[fi("onFocus"),as("stopMousedown",!1),as("ignore",!1)];const Hp=dl({fields:zp,name:"focusing",active:Rp,apis:Vp}),Pp=(e,t,o)=>{const n=t.aria;n.update(e,n,o.get())},Np=(e,t,o)=>{t.toggleClass.each((t=>{o.get()?_a(e.element,t):Ta(e.element,t)}))},Lp=(e,t,o)=>{jp(e,t,o,!o.get())},Wp=(e,t,o)=>{o.set(!0),Np(e,t,o),Pp(e,t,o)},Up=(e,t,o)=>{o.set(!1),Np(e,t,o),Pp(e,t,o)},jp=(e,t,o,n)=>{(n?Wp:Up)(e,t,o)},Gp=(e,t,o)=>{jp(e,t,o,t.selected)};var $p=Object.freeze({__proto__:null,onLoad:Gp,toggle:Lp,isOn:(e,t,o)=>o.get(),on:Wp,off:Up,set:jp}),qp=Object.freeze({__proto__:null,exhibit:()=>pa({}),events:(e,t)=>{const o=(n=e,s=t,r=Lp,Pr((e=>{r(e,n,s)})));var n,s,r;const a=rl(e,t,Gp);return Or(q([e.toggleOnExecute?[o]:[],[a]]))}});const Xp=(e,t,o)=>{pt(e.element,"aria-expanded",o)};var Kp=[as("selected",!1),Zn("toggleClass"),as("toggleOnExecute",!0),is("aria",{mode:"none"},Pn("mode",{pressed:[as("syncWithExpanded",!1),xi("update",((e,t,o)=>{pt(e.element,"aria-pressed",o),t.syncWithExpanded&&Xp(e,0,o)}))],checked:[xi("update",((e,t,o)=>{pt(e.element,"aria-checked",o)}))],expanded:[xi("update",Xp)],selected:[xi("update",((e,t,o)=>{pt(e.element,"aria-selected",o)}))],none:[xi("update",b)]}))];const Yp=dl({fields:Kp,name:"toggling",active:qp,apis:$p,state:(!1,{init:()=>{const e=hs(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const Jp=()=>{const e=(e,t)=>{t.stop(),wr(e)};return[Er(Ls(),e),Er(er(),e),Fr(_s()),Fr(Ms())]},Zp=e=>Or(q([e.map((e=>Pr(((t,o)=>{e(t),o.stop()})))).toArray(),Jp()])),Qp="alloy.item-hover",eh="alloy.item-focus",th=e=>{(yl(e.element).isNone()||Hp.isFocused(e))&&(Hp.isFocused(e)||Hp.focus(e),xr(e,Qp,{item:e}))},oh=e=>{xr(e,eh,{item:e})},nh=y(Qp),sh=y(eh),rh=[Un("data"),Un("components"),Un("dom"),as("hasSubmenu",!1),Zn("toggling"),tu("itemBehaviours",[Yp,Hp,kp,Jd]),as("ignoreFocus",!1),as("domModification",{}),xi("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:e.toggling.isSome()?"menuitemcheckbox":"menuitem",...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:ou(e.itemBehaviours,[e.toggling.fold(Yp.revoke,(e=>Yp.config({aria:{mode:"checked"},...e}))),Hp.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{oh(e)}}),kp.config({mode:"execution"}),Jd.config({store:{mode:"memory",initialValue:e.data}}),Fp("item-type-events",[...Jp(),Er(Is(),th),Er(Qs(),Hp.focus)])]),components:e.components,eventOrder:e.eventOrder}))),as("eventOrder",{})],ah=[Un("dom"),Un("components"),xi("builder",(e=>({dom:e.dom,components:e.components,events:Or([Ir(Qs())])})))],ih=y("item-widget"),lh=y([Ou({name:"widget",overrides:e=>({behaviours:ll([Jd.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),ch=[Un("uid"),Un("data"),Un("components"),Un("dom"),as("autofocus",!1),as("ignoreFocus",!1),tu("widgetBehaviours",[Jd,Hp,kp]),as("domModification",{}),$u(lh()),xi("builder",(e=>{const t=zu(ih(),e,lh()),o=Hu(ih(),e,t.internals()),n=t=>Pu(t,e,"widget").map((e=>(kp.focusIn(e),e))),s=(t,o)=>tm(o.event.target)?B.none():e.autofocus?(o.setSource(t.element),B.none()):B.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Or([Pr(((e,t)=>{n(e).each((e=>{t.stop()}))})),Er(Is(),th),Er(Qs(),((t,o)=>{e.autofocus?n(t):Hp.focus(t)}))]),behaviours:ou(e.widgetBehaviours,[Jd.config({store:{mode:"memory",initialValue:e.data}}),Hp.config({ignore:e.ignoreFocus,onFocus:e=>{oh(e)}}),kp.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:gl(),onLeft:s,onRight:s,onEscape:(t,o)=>Hp.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),B.none()):B.none():(Hp.focus(t),B.some(!0))})])}}))],dh=Pn("type",{widget:ch,item:rh,separator:ah}),uh=y([Eu({factory:{sketch:e=>{const t=zn("menu.spec item",dh,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>be(t,"uid")?t:{...t,uid:ta("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),mh=y([Un("value"),Un("items"),Un("dom"),Un("components"),as("eventOrder",{}),Zd("menuBehaviours",[Em,Jd,sm,kp]),is("movement",{mode:"menu",moveOnTab:!0},Pn("mode",{grid:[Si(),xi("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[xi("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},focusManager:e.focusManager}))),Un("rowSelector")],menu:[as("moveOnTab",!0),xi("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),jn("markers",ui()),as("fakeFocus",!1),as("focusManager",Km()),fi("onHighlight")]),gh=y("alloy.menu-focus"),ph=em({name:"Menu",configFields:mh(),partFields:uh(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:eu(e.menuBehaviours,[Em.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight}),Jd.config({store:{mode:"memory",initialValue:e.value}}),sm.config({find:B.some}),kp.config(e.movement.config(e,e.movement))]),events:Or([Er(sh(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Em.highlight(e,o),t.stop(),xr(e,gh(),{menu:e,item:o})}))})),Er(nh(),((e,t)=>{const o=t.event.item;Em.highlight(e,o)}))]),components:t,eventOrder:e.eventOrder,domModification:{attributes:{role:"menu"}}})}),hh=(e,t,o,n)=>fe(o,n).bind((n=>fe(e,n).bind((n=>{const s=hh(e,t,o,n);return B.some([n].concat(s))})))).getOr([]),fh=e=>"prepared"===e.type?B.some(e.menu):B.none(),bh=()=>{const e=hs({}),t=hs({}),o=hs({}),n=zl(),s=hs({}),r=e=>a(e).bind(fh),a=e=>fe(t.get(),e),i=t=>fe(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};le(e,((e,t)=>{N(e,(e=>{o[e]=t}))}));const n=t,s=de(t,((e,t)=>({k:e,v:t}))),r=ce(s,((e,t)=>[t].concat(hh(o,n,s,t))));return ce(o,(e=>fe(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>fe(e.get(),t).map((e=>{const n=fe(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>fe(o.get(),e),collapse:e=>fe(o.get(),e).bind((e=>e.length>1?B.some(e.slice(1)):B.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return J(ae(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=W(i(t).toArray(),(e=>r(e).isSome()));return fe(o.get(),t).bind((t=>{const o=Y(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return B.none();t.push(n.getOrDie())}return B.some(t)})(X(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>pe(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>ye(n.get(),t)?[]:[B.none()]),(e=>[B.some(e)])))))}))}}},vh=fh,yh=y("collapse-item"),xh=Qu({name:"TieredMenu",configFields:[yi("onExecute"),yi("onEscape"),vi("onOpenMenu"),vi("onOpenSubmenu"),fi("onRepositionMenu"),fi("onCollapseMenu"),as("highlightImmediately",!0),Kn("data",[Un("primary"),Un("menus"),Un("expansions")]),as("fakeFocus",!1),fi("onHighlight"),fi("onHover"),gi(),Un("dom"),as("navigateOnHover",!0),as("stayInDom",!1),Zd("tmenuBehaviours",[kp,Em,sm,Dp]),as("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=zl(),n=bh(),s=e=>Jd.getValue(e).value,r=t=>ce(e.data.menus,((e,t)=>X(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=(e,t)=>{Em.highlight(e,t),Em.getHighlighted(t).orThunk((()=>Em.getFirst(t))).each((t=>{Sr(e,t.element,Qs())}))},i=(e,t)=>xe(P(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?B.some(e.menu):B.none()))))),l=(t,o,n)=>{const s=i(o,o.otherMenus(n));N(s,(o=>{Ma(o.element,[e.markers.backgroundMenu]),e.stayInDom||Dp.remove(t,o)}))},c=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=Ac(t.element,`.${e.markers.item}`),a=W(r,(e=>"true"===ft(e,"aria-haspopup")));return N(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);le(r,((e,t)=>{const o=V(n,t);pt(e.element,"aria-expanded",o)}))},d=(t,o,n)=>B.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return B.none();{const r=s.menu,c=i(o,n.slice(1));return N(c,(t=>{_a(t.element,e.markers.backgroundMenu)})),dt(r.element)||Dp.append(t,Ga(r)),Ma(r.element,[e.markers.backgroundMenu]),a(t,r),l(t,o,n),B.some(r)}}))));let u;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(u||(u={}));const m=(t,o,r=u.HighlightSubmenu)=>{if(o.hasConfigured(vm)&&vm.isDisabled(o))return B.some(o);{const a=s(o);return n.expand(a).bind((s=>(c(t,s),B.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return dt(l.element)||Dp.append(t,Ga(l)),e.onOpenSubmenu(t,o,l,Y(s)),r===u.HighlightSubmenu?(Em.highlightFirst(l),d(t,n,s)):(Em.dehighlightAll(l),B.some(o))})))))))}},g=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(c(t,s),d(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},p=t=>(o,n)=>ei(n.getSource(),"."+e.markers.item).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(T))))),h=Or([Er(gh(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Em.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>l(e,n,t)))}))})),Pr(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&g(t,o),m(t,o,u.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Rr(((t,o)=>{(t=>{const o=((t,o,n)=>ce(n,((n,s)=>{const r=()=>ph.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:e.onHighlight,focusManager:e.fakeFocus?Ym():Km()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{Dp.append(t,Ga(o)),e.onOpenMenu(t,o),e.highlightImmediately&&a(t,o)}))}))].concat(e.navigateOnHover?[Er(nh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(c(e,t),d(e,n,t))))})(t,r),m(t,r,u.HighlightParent),e.onHover(t,r)}))]:[])),f=e=>Em.getHighlighted(e).bind(Em.getHighlighted),v={collapseMenu:e=>{f(e).each((t=>{g(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{a(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>f(t).bind((e=>{const t=s(e),o=he(n.getMenus()),r=xe(P(o,vh));return n.getTriggeringPath(t,(e=>((e,t,o)=>re(t,(e=>{if(!e.getSystem().isConnected())return B.none();const t=Em.getCandidates(e);return G(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>B.from(e.components()[0]).filter((e=>"menu"===ft(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:eu(e.tmenuBehaviours,[kp.config({mode:"special",onRight:p(((e,t)=>tm(t.element)?B.none():m(e,t,u.HighlightSubmenu))),onLeft:p(((e,t)=>tm(t.element)?B.none():g(e,t))),onEscape:p(((t,o)=>g(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{Sr(e,t.element,Qs())}))}}),Em.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),sm.config({find:e=>Em.getHighlighted(e)}),Dp.config({})]),eventOrder:e.eventOrder,apis:v,events:h}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:bs(e,t),expansions:{}}),collapseItem:e=>({value:Xr(yh()),meta:{text:e}})}}),wh=Qu({name:"InlineView",configFields:[Un("lazySink"),fi("onShow"),fi("onHide"),ns("onEscape"),Zd("inlineBehaviours",[Ad,Jd,pl]),rs("fireDismissalEventInstead",[as("event",ur())]),rs("fireRepositionEventInstead",[as("event",mr())]),as("getRelated",B.none),as("isExtraPart",_),as("eventOrder",B.none)],factory:(e,t)=>{const o=(e,t,o,s)=>{n(e,t,o,(()=>s.map((e=>Lo(e)))))},n=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();Ad.openWhileCloaked(t,o,(()=>ad.positionWithinBounds(r,t,n,s()))),Jd.setValue(t,B.some({mode:"position",config:n,getBounds:s}))},s=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>tl(),onRtl:()=>ol()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return xh.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightImmediately:n.menu.highlightImmediately,onEscape:()=>(Ad.close(t),e.onEscape.map((e=>e(t))),B.some(!0)),onExecute:()=>B.some(!0),onOpenMenu:(e,t)=>{ad.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();ad.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();ad.positionWithinBounds(a,t,o,s()),N(n,(e=>{const t=i(e.triggeringPath);ad.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);Ad.open(t,r),Jd.setValue(t,B.some({mode:"menu",menu:r}))},r=t=>{Ad.isOpen(t)&&Jd.getValue(t).each((o=>{switch(o.mode){case"menu":Ad.getState(t).each(xh.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();ad.positionWithinBounds(n,t,o.config,o.getBounds())}}))},a={setContent:(e,t)=>{Ad.setContent(e,t)},showAt:(e,t,n)=>{o(e,t,n,B.none())},showWithin:o,showWithinBounds:n,showMenuAt:(e,t,o)=>{s(e,t,o,B.none)},showMenuWithinBounds:s,hide:e=>{Ad.isOpen(e)&&(Jd.setValue(e,B.none()),Ad.close(e))},getContent:e=>Ad.getState(e),reposition:r,isOpen:Ad.isOpen};return{uid:e.uid,dom:e.dom,behaviours:eu(e.inlineBehaviours,[Ad.config({isPartOf:(t,o,n)=>ni(o,n)||((t,o)=>e.getRelated(t).exists((e=>ni(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),Jd.config({store:{mode:"memory",initialValue:B.none()}}),pl.config({channels:{...Rd({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Hd({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:r})}})]),eventOrder:e.eventOrder,apis:a}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithin:(e,t,o,n,s)=>{e.showWithin(t,o,n,s)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var Sh=tinymce.util.Tools.resolve("tinymce.util.Delay");const kh=Qu({name:"Button",factory:e=>{const t=Zp(e.action),o=e.dom.tag,n=t=>fe(e.dom,"attributes").bind((e=>fe(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:ou(e.buttonBehaviours,[Hp.config({}),kp.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:n("role").getOr("button")}},eventOrder:e.eventOrder}},configFields:[as("uid",void 0),Un("dom"),as("components",[]),tu("buttonBehaviours",[Hp,kp]),Zn("action"),Zn("role"),as("eventOrder",{})]}),Ch=e=>{const t=(e=>void 0!==e.uid)(e)&&ve(e,"uid")?e.uid:ta("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}};var Oh=tinymce.util.Tools.resolve("tinymce.util.I18n");const _h={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},Th="temporary-placeholder",Eh=e=>()=>fe(e,Th).getOr("!not found!"),Bh=(e,t)=>{const o=e.toLowerCase();if(Oh.isRtl()){const e=((e,t)=>Oe(e,t)?e:((e,t)=>e+"-rtl")(e))(o,"-rtl");return be(t,e)?e:o}return o},Mh=(e,t)=>fe(t,Bh(e,t)),Ah=(e,t)=>{const o=t();return Mh(e,o).getOrThunk(Eh(o))},Dh=()=>Fp("add-focusable",[Rr((e=>{Za(e.element,"svg").each((e=>pt(e,"focusable","false")))}))]),Fh=(e,t,o,n)=>{var s,r;const a=(e=>!!Oh.isRtl()&&be(_h,e))(t)?["tox-icon--flip"]:[],i=fe(o,Bh(t,o)).or(n).getOrThunk(Eh(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:ll([...null!==(r=e.behaviours)&&void 0!==r?r:[],Dh()])}},Ih=(e,t,o,n=B.none())=>Fh(t,e,o(),n),Vh={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},Rh=Qu({name:"Notification",factory:e=>{const t=Ch({dom:{tag:"p",innerHtml:e.translationProvider(e.text)},behaviours:ll([Dp.config({})])}),o=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),n=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),s=Ch({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(0)]},n(0)],behaviours:ll([Dp.config({})])}),r={updateProgress:(e,t)=>{e.getSystem().isConnected()&&s.getOpt(e).each((e=>{Dp.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(t)]},n(t)])}))},updateText:(e,o)=>{if(e.getSystem().isConnected()){const n=t.get(e);Dp.set(n,[Na(o)])}}},a=q([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>B.from(Vh[e]))).toArray()]),i=Ch(kh.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[Ih("close",{tag:"div",classes:["tox-icon"],attributes:{"aria-label":e.translationProvider("Close")}},e.iconProvider)],action:t=>{e.onAction(t)}})),l=((e,t,o)=>{const n=o(),s=G(e,(e=>be(n,Bh(e,n))));return Fh({tag:"div",classes:["tox-notification__icon"]},s.getOr(Th),n,B.none())})(a,0,e.iconProvider),c=[l,{dom:{tag:"div",classes:["tox-notification__body"]},components:[t.asSpec()],behaviours:ll([Dp.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert"},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:ll([Hp.config({}),Fp("notification-events",[Er(Vs(),(e=>{i.getOpt(e).each(Hp.focus)}))])]),components:c.concat(e.progress?[s.asSpec()]:[]).concat(e.closeButton?[i.asSpec()]:[]),apis:r}},configFields:[Zn("level"),Un("progress"),Un("icon"),Un("onAction"),Un("text"),Un("iconProvider"),Un("translationProvider"),us("closeButton",!0)],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var zh,Hh,Ph=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Nh=tinymce.util.Tools.resolve("tinymce.EditorManager"),Lh=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(zh||(zh={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(Hh||(Hh={}));const Wh=e=>t=>t.options.get(e),Uh=e=>t=>B.from(e(t)),jh=e=>{const t=Lh.deviceType.isPhone(),o=Lh.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:Ph.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),z(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:Hh.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!Lh.deviceType.isTouch()})},Gh=Wh("readonly"),$h=Wh("height"),qh=Wh("width"),Xh=Uh(Wh("min_width")),Kh=Uh(Wh("min_height")),Yh=Uh(Wh("max_width")),Jh=Uh(Wh("max_height")),Zh=Uh(Wh("style_formats")),Qh=Wh("style_formats_merge"),ef=Wh("style_formats_autohide"),tf=Wh("content_langs"),of=Wh("removed_menuitems"),nf=Wh("toolbar_mode"),sf=Wh("toolbar_groups"),rf=Wh("toolbar_location"),af=Wh("fixed_toolbar_container"),lf=Wh("fixed_toolbar_container_target"),cf=Wh("toolbar_persist"),df=Wh("toolbar_sticky_offset"),uf=Wh("menubar"),mf=Wh("toolbar"),gf=Wh("file_picker_callback"),pf=Wh("file_picker_validator_handler"),hf=Wh("file_picker_types"),ff=Wh("typeahead_urls"),bf=Wh("anchor_top"),vf=Wh("anchor_bottom"),yf=Wh("draggable_modal"),xf=Wh("statusbar"),wf=Wh("elementpath"),Sf=Wh("branding"),kf=Wh("resize"),Cf=Wh("paste_as_text"),Of=e=>!1===e.options.get("skin"),_f=e=>!1!==e.options.get("menubar"),Tf=e=>{const t=e.options.get("skin_url");if(Of(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return Nh.baseURL+"/skins/ui/"+t}},Ef=e=>e.options.get("line_height_formats").split(" "),Bf=e=>{const t=mf(e),o=r(t),n=l(t)&&t.length>0;return!Af(e)&&(n||o||!0===t)},Mf=e=>{const t=z(9,(t=>e.options.get("toolbar"+(t+1)))),o=W(t,r);return Se(o.length>0,o)},Af=e=>Mf(e).fold((()=>{const t=mf(e);return f(t,r)&&t.length>0}),T),Df=e=>rf(e)===Hh.bottom,Ff=e=>{if(!e.inline)return B.none();const t=af(e);if(t.length>0)return Qa(ut(),t);const o=lf(e);return g(o)?B.some(Fe(o)):B.none()},If=e=>e.inline&&Ff(e).isSome(),Vf=e=>Ff(e).getOrThunk((()=>it(at(Fe(e.getElement()))))),Rf=e=>e.inline&&!_f(e)&&!Bf(e)&&!Af(e),zf=e=>(e.options.get("toolbar_sticky")||e.inline)&&!If(e)&&!Rf(e),Hf=e=>{const t=e.options.get("menu");return ce(t,(e=>({...e,items:e.items})))};var Pf=Object.freeze({__proto__:null,get ToolbarMode(){return zh},get ToolbarLocation(){return Hh},register:jh,getSkinUrl:Tf,isReadOnly:Gh,isSkinDisabled:Of,getHeightOption:$h,getWidthOption:qh,getMinWidthOption:Xh,getMinHeightOption:Kh,getMaxWidthOption:Yh,getMaxHeightOption:Jh,getUserStyleFormats:Zh,shouldMergeStyleFormats:Qh,shouldAutoHideStyleFormats:ef,getLineHeightFormats:Ef,getContentLanguages:tf,getRemovedMenuItems:of,isMenubarEnabled:_f,isMultipleToolbars:Af,isToolbarEnabled:Bf,isToolbarPersist:cf,getMultipleToolbarsOption:Mf,getUiContainer:Vf,useFixedContainer:If,getToolbarMode:nf,isDraggableModal:yf,isDistractionFree:Rf,isStickyToolbar:zf,getStickyToolbarOffset:df,getToolbarLocation:rf,isToolbarLocationBottom:Df,getToolbarGroups:sf,getMenus:Hf,getMenubar:uf,getToolbar:mf,getFilePickerCallback:gf,getFilePickerTypes:hf,useTypeaheadUrls:ff,getAnchorTop:bf,getAnchorBottom:vf,getFilePickerValidatorHandler:pf,useStatusBar:xf,useElementPath:wf,useBranding:Sf,getResize:kf,getPasteAsText:Cf});const Nf="[data-mce-autocompleter]",Lf=e=>ei(e,Nf);var Wf;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(Wf||(Wf={}));var Uf=Wf;const jf="tox-menu-nav__js",Gf="tox-collection__item",$f={normal:jf,color:"tox-swatch"},qf="tox-collection__item--enabled",Xf="tox-collection__item-icon",Kf="tox-collection__item-label",Yf="tox-collection__item-caret",Jf="tox-collection__item--active",Zf="tox-collection__item-container",Qf="tox-collection__item-container--row",eb=e=>fe($f,e).getOr(jf),tb=e=>"color"===e?"tox-swatches":"tox-menu",ob=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:tb(e),tieredMenu:"tox-tiered-menu"}),nb=e=>{const t=ob(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:eb(e)}},sb=(e,t,o)=>{const n=ob(o);return{tag:"div",classes:q([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},rb=[ph.parts.items({})],ab=(e,t,o)=>{const n=ob(o);return{dom:{tag:"div",classes:q([[n.tieredMenu]])},markers:nb(o)}},ib=(e,t)=>o=>{const n=H(o,t);return P(n,(t=>({dom:e,components:t})))},lb=(e,t)=>{const o=[];let n=[];return N(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(be(e.dom,"innerHtml")||e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),P(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},cb=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[ph.parts.items({preprocess:o=>"auto"!==e&&e>1?ib({tag:"div",classes:["tox-collection__group"]},e)(o):lb(o,((e,o)=>"separator"===t[o].type))})]}),db=e=>R(e,(e=>"icon"in e&&void 0!==e.icon)),ub=e=>(console.error(Hn(e)),console.log(e),B.none()),mb=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[ph.parts.items({preprocess:e=>lb(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},gb=(e,t,o,n,s)=>{if("color"===s){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[ph.parts.items({preprocess:"auto"!==e?ib({tag:"div",classes:["tox-swatches__row"]},e):x})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s&&"auto"===n){const t=cb(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s&&1===n){const t=cb(1,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s){const t=cb(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[ph.parts.items({preprocess:ib({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:sb(t,n,s),components:rb,items:o}},pb=$n("type"),hb=$n("name"),fb=$n("label"),bb=$n("text"),vb=$n("title"),yb=$n("icon"),xb=$n("value"),wb=Xn("fetch"),Sb=Xn("getSubmenuItems"),kb=Xn("onAction"),Cb=Xn("onItemAction"),Ob=ms("onSetup",(()=>b)),_b=ts("name"),Tb=ts("text"),Eb=ts("icon"),Bb=ts("tooltip"),Mb=ts("label"),Ab=ts("shortcut"),Db=ns("select"),Fb=us("active",!1),Ib=us("borderless",!1),Vb=us("enabled",!0),Rb=us("primary",!1),zb=e=>as("columns",e),Hb=as("meta",{}),Pb=ms("onAction",b),Nb=e=>cs("type",e),Lb=e=>Nn("name","name",rn((()=>Xr(`${e}-name`))),Tn),Wb=yn([pb,Tb]),Ub=yn([Nb("autocompleteitem"),Fb,Vb,Hb,xb,Tb,Eb]),jb=[Vb,Bb,Eb,Tb,Ob],Gb=yn([pb,kb].concat(jb)),$b=e=>Vn("toolbarbutton",Gb,e),qb=[Fb].concat(jb),Xb=yn(qb.concat([pb,kb])),Kb=e=>Vn("ToggleButton",Xb,e),Yb=[ms("predicate",_),ds("scope","node",["node","editor"]),ds("position","selection",["node","selection","line"])],Jb=jb.concat([Nb("contextformbutton"),Rb,kb,Ln("original",x)]),Zb=qb.concat([Nb("contextformbutton"),Rb,kb,Ln("original",x)]),Qb=jb.concat([Nb("contextformbutton")]),ev=qb.concat([Nb("contextformtogglebutton")]),tv=Pn("type",{contextformbutton:Jb,contextformtogglebutton:Zb}),ov=yn([Nb("contextform"),ms("initValue",y("")),Mb,Jn("commands",tv),Qn("launch",Pn("type",{contextformbutton:Qb,contextformtogglebutton:ev}))].concat(Yb)),nv=yn([Nb("contexttoolbar"),$n("items")].concat(Yb)),sv=[pb,$n("src"),ts("alt"),gs("classes",[],Tn)],rv=yn(sv),av=[pb,bb,_b,gs("classes",["tox-collection__item-label"],Tn)],iv=yn(av),lv=fn((()=>Dn("type",{cardimage:rv,cardtext:iv,cardcontainer:cv}))),cv=yn([pb,cs("direction","horizontal"),cs("align","left"),cs("valign","middle"),Jn("items",lv)]),dv=[Vb,Tb,Ab,("menuitem",Nn("value","value",rn((()=>Xr("menuitem-value"))),Cn())),Hb];const uv=yn([pb,Mb,Jn("items",lv),Ob,Pb].concat(dv)),mv=yn([pb,Fb,Eb].concat(dv)),gv=[pb,$n("fancytype"),Pb],pv=[as("initData",{})].concat(gv),hv=[ps("initData",{},[us("allowCustomColors",!0),ss("colors",Cn())])].concat(gv),fv=Pn("fancytype",{inserttable:pv,colorswatch:hv}),bv=yn([pb,Ob,Pb,Eb].concat(dv)),vv=yn([pb,Sb,Ob,Eb].concat(dv)),yv=yn([pb,Eb,Fb,Ob,kb].concat(dv)),xv=(e,t,o)=>{const n=Ac(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return B.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return B.none()},wv=e=>((e,t)=>ll([Fp(e,t)]))(Xr("unnamed-events"),e),Sv=Xr("tooltip.exclusive"),kv=Xr("tooltip.show"),Cv=Xr("tooltip.hide"),Ov=(e,t,o)=>{e.getSystem().broadcastOn([Sv],{})};var _v=Object.freeze({__proto__:null,hideAllExclusive:Ov,setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&Dp.set(e,n)}))}}),Tv=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{fd(n),e.onHide(o,n),t.clearTooltip()})),t.clearTimer()};return Or(q([[Er(kv,(o=>{t.resetTimer((()=>{(o=>{if(!t.isShowing()){Ov(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Or("normal"===e.mode?[Er(Is(),(e=>{yr(o,kv)})),Er(Ds(),(e=>{yr(o,Cv)}))]:[]),behaviours:ll([Dp.config({})])});t.setTooltip(s),gd(n,s),e.onShow(o,s),ad.position(n,s,{anchor:e.anchor(o)})}})(o)}),e.delay)})),Er(Cv,(n=>{t.resetTimer((()=>{o(n)}),e.delay)})),Er(Js(),((e,t)=>{const n=t;n.universal||V(n.channels,Sv)&&o(e)})),zr((e=>{o(e)}))],"normal"===e.mode?[Er(Vs(),(e=>{yr(e,kv)})),Er(Ks(),(e=>{yr(e,Cv)})),Er(Is(),(e=>{yr(e,kv)})),Er(Ds(),(e=>{yr(e,Cv)}))]:[Er(br(),((e,t)=>{yr(e,kv)})),Er(vr(),(e=>{yr(e,Cv)}))]]))}}),Ev=[Un("lazySink"),Un("tooltipDom"),as("exclusive",!0),as("tooltipComponents",[]),as("delay",300),ds("mode","normal",["normal","follow-highlight"]),as("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:y([Xi,qi,Ui,Gi,ji,$i]),onRtl:y([Xi,qi,Ui,Gi,ji,$i])}}))),fi("onHide"),fi("onShow")];const Bv=dl({fields:Ev,name:"tooltipping",active:Tv,state:Object.freeze({__proto__:null,init:()=>{const e=zl(),t=zl(),o=()=>{e.on(clearTimeout)},n=y("not-implemented");return ma({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:o,resetTimer:(t,n)=>{o(),e.set(setTimeout(t,n))},readState:n})}}),apis:_v}),Mv="silver.readonly",Av=yn([("readonly",jn("readonly",En))]);const Dv=(e,t)=>{const o=e.outerContainer.element;t&&(e.mothership.broadcastOn([Dd()],{target:o}),e.uiMothership.broadcastOn([Dd()],{target:o})),e.mothership.broadcastOn([Mv],{readonly:t}),e.uiMothership.broadcastOn([Mv],{readonly:t})},Fv=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&Dv(t,!0)})),e.on("SwitchMode",(()=>Dv(t,e.mode.isReadOnly()))),Gh(e)&&e.mode.set("readonly")},Iv=()=>pl.config({channels:{[Mv]:{schema:Av,onReceive:(e,t)=>{vm.set(e,t.readonly)}}}}),Vv=e=>vm.config({disabled:e}),Rv=e=>vm.config({disabled:e,disableClass:"tox-tbtn--disabled"}),zv=e=>vm.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),Hv=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},Pv=(e,t)=>Rr((o=>{Hv(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),Nv=(e,t)=>zr((o=>Hv(e,o)(t.get()))),Lv=(e,t)=>Pr(((o,n)=>{Hv(e,o)(e.onAction),e.triggersSubmenu||t!==Uf.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&yr(o,or()),n.stop())})),Wv={[Zs()]:["disabling","alloy.base.behaviour","toggling","item-events"]},Uv=xe,jv=(e,t,o,n)=>{const s=hs(b);return{type:"item",dom:t.dom,components:Uv(t.optComponents),data:e.data,eventOrder:Wv,hasSubmenu:e.triggersSubmenu,itemBehaviours:ll([Fp("item-events",[Lv(e,o),Pv(e,s),Nv(e,s)]),(r=()=>!e.enabled||n.isDisabled(),vm.config({disabled:r,disableClass:"tox-collection__item--state-disabled"})),Iv(),Dp.config({})].concat(e.itemBehaviours))};var r},Gv=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),$v=e=>{const t=Lh.os.isMacOS()||Lh.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=P(n,(e=>{const t=e.toLowerCase().trim();return be(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},qv=(e,t,o=[Xf])=>Ih(e,{tag:"div",classes:o},t),Xv=e=>({dom:{tag:"div",classes:[Kf]},components:[Na(Oh.translate(e))]}),Kv=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),Yv=(e,t)=>({dom:{tag:"div",classes:[Kf]},components:[{dom:{tag:e.tag,styles:e.styles},components:[Na(Oh.translate(t))]}]}),Jv=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[Na($v(e))]}),Zv=e=>qv("checkmark",e,["tox-collection__item-checkmark"]),Qv=e=>{const t=e.map((e=>({attributes:{title:Oh.translate(e)}}))).getOr({});return{tag:"div",classes:[jf,Gf],...t}},ey=(e,t,o,n=B.none())=>"color"===e.presets?((e,t,o)=>{const n=e.ariaLabel,s=e.value,r=e.iconContent.map((e=>((e,t,o)=>{const n=t();return Mh(e,n).or(o).getOrThunk(Eh(n))})(e,t.icons,o)));return{dom:(()=>{const e=r.getOr(""),o=n.map((e=>({title:t.translate(e)}))).getOr({}),a={tag:"div",attributes:o,classes:["tox-swatch"]};return"custom"===s?{...a,tag:"button",classes:[...a.classes,"tox-swatches__picker-btn"],innerHtml:e}:"remove"===s?{...a,classes:[...a.classes,"tox-swatch--remove"],innerHtml:e}:{...a,attributes:{...a.attributes,"data-mce-color":s},styles:{"background-color":s}}})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const s={tag:"div",classes:[Xf]},r=o?e.iconContent.map((e=>Ih(e,s,t.icons,n))).orThunk((()=>B.some({dom:s}))):B.none(),a=e.checkMark,i=B.from(e.meta).fold((()=>Xv),(e=>be(e,"style")?S(Yv,e.style):Xv)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>B.some(Kv(e,[Kf]))));return{dom:Qv(e.ariaLabel),optComponents:[r,l,e.shortcutContent.map(Jv),a,e.caret]}})(e,t,o,n),ty=(e,t)=>fe(e,"tooltipWorker").map((e=>[Bv.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:ql}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{Bv.setComponents(t,[La({element:Fe(e)})])}))}})])).getOr([]),oy=(e,t)=>{const o=(e=>Ph.DOM.encode(e))(Oh.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},ny=(e,t)=>P(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":Qf,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[Zf,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,ny(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>V(t.cardText.highlightOn,e))),n=o?B.from(t.cardText.matchText).getOr(""):"";return Kv(oy(e.text,n),e.classes)}})),sy=Du(ih(),lh()),ry=e=>({value:e}),ay=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,iy=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,ly=e=>ay.test(e)||iy.test(e),cy=e=>{return(t=e,((e,t)=>ke(e,t,0))(t,"#")?((e,t)=>e.substring(t))(t,"#".length):t).toUpperCase();var t},dy=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},uy=e=>{const t=dy(e.red)+dy(e.green)+dy(e.blue);return ry(t)},my=Math.min,gy=Math.max,py=Math.round,hy=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,fy=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,by=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),vy=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},yy=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=gy(0,my(r,1)),a=gy(0,my(a,1)),0===r)return t=o=n=py(255*a),by(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=py(255*(t+d)),o=py(255*(o+d)),n=py(255*(n+d)),by(t,o,n,1)},xy=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(ay,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=iy.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return by(o,n,s,1)},wy=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return by(s,r,a,i)},Sy=e=>{if("transparent"===e)return B.some(by(0,0,0,0));const t=hy.exec(e);if(null!==t)return B.some(wy(t[1],t[2],t[3],"1"));const o=fy.exec(e);return null!==o?B.some(wy(o[1],o[2],o[3],o[4])):B.none()},ky=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,Cy=by(255,0,0,1),Oy=(e,t)=>e.dispatch("ResizeContent",t),_y=(e,t,o)=>({hue:e,saturation:t,value:o}),Ty=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,_y(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,_y(Math.round(t),Math.round(100*o),Math.round(100*n)))},Ey=e=>uy(yy(e)),By=e=>{return(t=e,ly(t)?B.some({value:cy(t)}):B.none()).orThunk((()=>Sy(e).map(uy))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return uy(by(s,r,a,i))}));var t};var My=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const Ay="tinymce-custom-colors",Dy=((e=10)=>{const t=My.getItem(Ay),o=r(t)?JSON.parse(t):[],n=e-(s=o).length<0?s.slice(0,e):s;var s;const a=e=>{n.splice(e,1)};return{add:t=>{I(n,t).each(a),n.unshift(t),n.length>e&&n.pop(),My.setItem(Ay,JSON.stringify(n))},state:()=>n.slice(0)}})(10),Fy=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+By(e[o]).value,type:"choiceitem"});return t},Iy=e=>t=>t.options.get(e),Vy=Iy("color_cols"),Ry=Iy("custom_colors"),zy=Iy("color_map"),Hy=e=>{Dy.add(e)},Py="#000000",Ny=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},Ly=(e,t,o,n)=>{"custom"===o?qy(e)((o=>{o.each((o=>{Hy(o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),Py):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},Wy=(e,t)=>e.concat(P(Dy.state(),(e=>({type:"choiceitem",text:e,value:e}))).concat(Ny(t))),Uy=(e,t)=>o=>{o(Wy(e,t))},jy=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},Gy=(e,t,o,n,s)=>{e.ui.registry.addSplitButton(t,{tooltip:n,presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:t=>{const n=((e,t)=>{let o;return e.dom.getParents(e.selection.getStart(),(e=>{let n;(n=e.style["forecolor"===t?"color":"background-color"])&&(o=o||n)})),B.from(o)})(e,o);return n.bind((e=>Sy(e).map((e=>{const o=uy(e).value;return Ce(t.toLowerCase(),o)})))).getOr(!1)},columns:Vy(e),fetch:Uy(zy(e),Ry(e)),onAction:t=>{Ly(e,o,s.get(),b)},onItemAction:(n,r)=>{Ly(e,o,r,(o=>{s.set(o),((e,t)=>{e.dispatch("TextColorChange",t)})(e,{name:t,color:o})}))},onSetup:o=>{jy(o,t,s.get());const n=e=>{e.name===t&&jy(o,e.name,e.color)};return e.on("TextColorChange",n),()=>{e.off("TextColorChange",n)}}})},$y=(e,t,o,n)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",onAction:t=>{Ly(e,o,t.value,b)}}]})},qy=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(B.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(B.none())}})},Xy=(e,t,o,n,s,r,a,i)=>{const l=db(t),c=Ky(t,o,n,"color"!==s?"normal":"color",r,a,i);return gb(e,l,c,n,s)},Ky=(e,t,o,n,s,r,a)=>xe(P(e,(i=>{return"choiceitem"===i.type?(l=i,Vn("choicemenuitem",mv,l)).fold(ub,(l=>B.some(((e,t,o,n,s,r,a,i=!0)=>{const l=ey({presets:o,textContent:t?e.text:B.none(),htmlContent:B.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:B.none(),checkMark:t?B.some(Zv(a.icons)):B.none(),caret:B.none(),value:e.value},a,i);return nn(jv({data:Gv(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Yp.set(e,t)},isActive:()=>Yp.isOn(e),isEnabled:()=>!vm.isDisabled(e),setEnabled:t=>vm.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[]},l,r,a),{toggling:{toggleClass:qf,toggleOnExecute:!1,selected:e.active}})})(l,1===o,n,t,r(i.value),s,a,db(e))))):B.none();var l}))),Yy=(e,t)=>{const o=nb(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group")}},Jy=Xr("cell-over"),Zy=Xr("cell-execute"),Qy=(e,t,o)=>{const n=o=>xr(o,Zy,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return ja({dom:{tag:"div",attributes:{role:"button","aria-labelledby":o}},behaviours:ll([Fp("insert-table-picker-cell",[Er(Is(),Hp.focus),Er(Zs(),n),Er(Ls(),s),Er(er(),s)]),Yp.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),Hp.config({onFocus:o=>xr(o,Jy,{row:e,col:t})})])})},ex=e=>X(e,(e=>P(e,Ga))),tx=(e,t)=>Na(`${t}x${e}`),ox={inserttable:e=>{const t=Xr("size-label"),o=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++)o.push(Qy(t,n,e));n.push(o)}return n})(t),n=tx(0,0),s=Ch({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[n],behaviours:ll([Dp.config({})])});return{type:"widget",data:{value:Xr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[sy.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:ex(o).concat(s.asSpec()),behaviours:ll([Fp("insert-table-picker",[Rr((e=>{Dp.set(s.get(e),[n])})),Dr(Jy,((e,t,n)=>{const{row:r,col:a}=n.event;((e,t,o,n,s)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)Yp.set(e[n][s],n<=t&&s<=o)})(o,r,a),Dp.set(s.get(e),[tx(r+1,a+1)])})),Dr(Zy,((t,o,n)=>{const{row:s,col:r}=n.event;e.onAction({numRows:s+1,numColumns:r+1}),yr(t,or())}))]),kp.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>Wy(t.colorinput.getColors(),o)),(e=>e.concat(Ny(o))))})(e,t),n=t.colorinput.getColorCols(),s="color",r={...Xy(Xr("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,Uf.CLOSE_ON_EXECUTE,_,t.shared.providers),markers:nb(s),movement:Yy(n,s)};return{type:"widget",data:{value:Xr("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[sy.widget(ph.sketch(r))]}}},nx=e=>({type:"separator",dom:{tag:"div",classes:[Gf,"tox-collection__group-heading"]},components:e.text.map(Na).toArray()});var sx;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(sx||(sx={}));const rx=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:B.none(),icon:e.text.isSome()?B.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,Vn("menuitem",bv,i)).fold(ub,(e=>B.some(((e,t,o,n=!0)=>{const s=ey({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:B.none(),ariaLabel:e.text,caret:B.none(),checkMark:B.none(),shortcutContent:e.shortcut},o,n);return jv({data:Gv(e),getApi:e=>({isEnabled:()=>!vm.isDisabled(e),setEnabled:t=>vm.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>Vn("nestedmenuitem",vv,e))(e).fold(ub,(e=>B.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,qv("chevron-down",a,[Yf])):(e=>qv("chevron-right",e,[Yf]))(o.icons);var a;const i=ey({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:B.none(),ariaLabel:e.text,caret:B.some(r),checkMark:B.none(),shortcutContent:e.shortcut},o,n);return jv({data:Gv(e),getApi:e=>({isEnabled:()=>!vm.isDisabled(e),setEnabled:t=>vm.set(e,!t)}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>Vn("togglemenuitem",yv,e))(e).fold(ub,(e=>B.some(((e,t,o,n=!0)=>{const s=ey({iconContent:e.icon,textContent:e.text,htmlContent:B.none(),ariaLabel:e.text,checkMark:B.some(Zv(o.icons)),caret:B.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return nn(jv({data:Gv(e),enabled:e.enabled,getApi:e=>({setActive:t=>{Yp.set(e,t)},isActive:()=>Yp.isOn(e),isEnabled:()=>!vm.isDisabled(e),setEnabled:t=>vm.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:qf,toggleOnExecute:!1,selected:e.active}})})(a(e),t,r,n))));case"separator":return(e=>Vn("separatormenuitem",Wb,e))(e).fold(ub,(e=>B.some(nx(e))));case"fancymenuitem":return(e=>Vn("fancymenuitem",fv,e))(e).fold(ub,(e=>((e,t)=>fe(ox,e.fancytype).map((o=>o(e,t))))(a(e),o)));default:return console.error("Unknown item in general menu",e),B.none()}var i},ax=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||db(e);return xe(P(e,(e=>{switch(e.type){case"separator":return(n=e,Vn("Autocompleter.Separator",Wb,n)).fold(ub,(e=>B.some(nx(e))));case"cardmenuitem":return(e=>Vn("cardmenuitem",uv,e))(e).fold(ub,(e=>B.some(((e,t,o,n)=>{const s={dom:Qv(e.label),optComponents:[B.some({dom:{tag:"div",classes:[Zf,Qf]},components:ny(e.items,n)})]};return jv({data:Gv({text:B.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!vm.isDisabled(e),setEnabled:t=>{vm.set(e,!t),N(Ac(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(vm)&&vm.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:B.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:ty(e.meta,r),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>Vn("Autocompleter.Item",Ub,e))(e).fold(ub,(e=>B.some(((e,t,o,n,s,r,a,i=!0)=>{const l=ey({presets:n,textContent:B.none(),htmlContent:o?e.text.map((e=>oy(e,t))):B.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:B.none(),checkMark:B.none(),caret:B.none(),value:e.value},a.providers,i,e.icon);return jv({data:Gv(e),enabled:e.enabled,getApi:y({}),onAction:t=>s(e.value,e.meta),onSetup:y(b),triggersSubmenu:!1,itemBehaviours:ty(e.meta,a)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},ix=(e,t,o,n,s)=>{const r=db(t),a=xe(P(t,(e=>{const t=e=>rx(e,o,n,(e=>s?!be(e,"text"):r)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)})));return(s?mb:gb)(e,r,a,1,"normal")},lx=e=>xh.singleData(e.value,e),cx=(e,t)=>{const o=hs(!1),n=hs(!1),s=ja(wh.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:ll([Fp("dismissAutocompleter",[Er(ur(),(()=>l()))])]),lazySink:t.getSink})),r=()=>wh.isOpen(s),a=n.get,i=()=>{r()&&wh.hide(s)},l=()=>e.execCommand("mceAutocompleterClose"),c=n=>{const r=(n=>{const s=re(n,(e=>B.from(e.columns))).getOr(1);return X(n,(n=>{const r=n.items;return ax(r,n.matchText,((t,s)=>{const r=e.selection.getRng();((e,t)=>Lf(Fe(t.startContainer)).map((t=>{const o=e.createRng();return o.selectNode(t.dom),o})))(e.dom,r).each((r=>{const a={hide:()=>l(),reload:t=>{i(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};o.set(!0),n.onAction(a,r,t,s),o.set(!1)}))}),s,Uf.BUBBLE_TO_SANDBOX,t,n.highlightOn)}))})(n);r.length>0?((t,o)=>{var n;(n=Fe(e.getBody()),Qa(n,Nf)).each((n=>{const r=re(t,(e=>B.from(e.columns))).getOr(1);wh.showAt(s,ph.sketch(((e,t,o,n)=>{const s=o===sx.ContentFocus?Ym():Km(),r=Yy(t,n),a=nb(n);return{dom:e.dom,components:e.components,items:e.items,value:e.value,markers:{selectedItem:a.selectedItem,item:a.item},movement:r,fakeFocus:o===sx.ContentFocus,focusManager:s,menuBehaviours:wv("auto"!==t?[]:[Rr(((e,t)=>{xv(e,4,a.item).each((({numColumns:t,numRows:o})=>{kp.setGridSize(e,o,t)}))}))])}})(gb("autocompleter-value",!0,o,r,"normal"),r,sx.ContentFocus,"normal")),{anchor:{type:"node",root:Fe(e.getBody()),node:B.from(n)}}),wh.getContent(s).each(Em.highlightFirst)}))})(n,r):i()};e.on("AutocompleterStart",(({lookupData:e})=>{n.set(!0),o.set(!1),c(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>c(e))),e.on("AutocompleterEnd",(()=>{i(),n.set(!1),o.set(!1)}));((e,t)=>{const o=(e,t)=>{xr(e,zs(),{raw:t})},n=()=>e.getView().bind(Em.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(wr),t.preventDefault()):40===s?(n().fold((()=>{e.getView().each(Em.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(t=>{e.isActive()&&!e.isProcessingAction()&&Lf(Fe(t.element)).isNone()&&e.cancelIfNecessary()}))})({cancelIfNecessary:l,isMenuOpen:r,isActive:a,isProcessingAction:o.get,getView:()=>wh.getContent(s)},e)},dx=(e,t,o)=>ei(e,t,o).isSome(),ux=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},mx=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?B.none():B.some(t.touches[0])},gx=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=zl(),o=hs(!1),n=ux((t=>{e.triggerEvent(tr(),t),o.set(!0)}),400),s=vs([{key:_s(),value:e=>(mx(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),B.none())},{key:Ts(),value:e=>(n.cancel(),mx(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),B.none())},{key:Es(),value:s=>(n.cancel(),t.get().filter((e=>je(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(er(),s))))}]);return{fireIfReady:(e,t)=>fe(s,t).bind((t=>t(e)))}})(o),s=P(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>Pl(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=zl(),a=Pl(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(Ys(),e)}),0))})),i=Pl(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===Bm[0]&&!V(["input","textarea"],Ve(e.target))&&!dx(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=Pl(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=zl(),d=Pl(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(Ks(),e)}),0))}));return{unbind:()=>{N(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},px=(e,t)=>{const o=fe(e,"target").getOr(t);return hs(o)},hx=fs([{stopped:[]},{resume:["element"]},{complete:[]}]),fx=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=hs(!1),n=hs(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),hx.complete())),(e=>{const o=e.descHandler;return ha(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),hx.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),hx.complete()):Ye(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),hx.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),hx.resume(n))))}))},bx=(e,t,o,n,s,r)=>fx(e,t,o,n,s,r).fold(T,(n=>bx(e,t,o,n,s,r)),_),vx=(e,t,o,n,s)=>{const r=px(o,n);return bx(e,t,o,n,r,s)},yx=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{le(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:S.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{le(e,((e,o)=>{be(e,t)&&delete e[t]}))},filterByType:t=>fe(e,t).map((e=>ge(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>fe(e,o).bind((e=>ws(n,(t=>((e,t)=>ea(t).bind((t=>fe(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{ea(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return ea(t).getOrThunk((()=>((e,t)=>{const o=Xr(Jr+"uid-");return Qr(t,o),o})(0,e.element)))})(n);ve(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+Ur(s.element)+"\nCannot use it for: "+Ur(e.element)+"\nThe conflicting element is"+(dt(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>fe(t,e)}},xx=Qu({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:Qd(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[as("components",[]),Zd("containerBehaviours",[]),as("events",{}),as("domModification",{}),as("eventOrder",{})]}),wx=e=>{const t=t=>Ye(e.element).fold(T,(e=>je(t,e))),o=yx(),n=(e,n)=>o.find(t,e,n),s=gx(e.element,{triggerEvent:(e,t)=>ii(e,t.target,(o=>((e,t,o,n)=>vx(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:y("real"),triggerEvent:(e,t,o)=>{ii(e,t,(s=>vx(n,e,o,t,s)))},triggerFocus:(e,t)=>{ea(e).fold((()=>{fl(e)}),(o=>{ii(Xs(),e,(o=>(((e,t,o,n,s)=>{const r=px(o,n);fx(e,t,o,n,r,s)})(n,Xs(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:ja,buildOrPatch:Ua,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:T},a=e=>{e.connect(r),He(e.element)||(o.register(e),N(e.components(),a),r.triggerEvent(sr(),e.element,{target:e.element}))},i=e=>{He(e.element)||(N(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{gd(e,t)},c=e=>{fd(e)},d=e=>{const t=o.filter(Js());N(t,(t=>{const o=t.descHandler;ha(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=hs(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:_,event:e,setSource:C("Cannot set source of a broadcasted event"),getSource:C("Cannot get source of a broadcasted event")}})(t);return N(e,(e=>{const t=e.descHandler;ha(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>$o.error(new Error('Could not find component with uid: "'+e+'" in system.'))),$o.value),h=e=>{const t=ea(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Ao(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},Sx=y([as("prefix","form-field"),Zd("fieldBehaviours",[sm,Jd])]),kx=y([Tu({schema:[Un("dom")],name:"label"}),Tu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[Un("text")],name:"aria-descriptor"}),Ou({factory:{sketch:e=>{const t=((e,t)=>{const o={};return le(e,((e,n)=>{V(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[Un("factory")],name:"field"})]),Cx=em({name:"FormField",configFields:Sx(),partFields:kx(),factory:(e,t,o,n)=>{const s=eu(e.fieldBehaviours,[sm.config({find:t=>Pu(t,e,"field")}),Jd.config({store:{mode:"manual",getValue:e=>sm.getCurrent(e).bind(Jd.getValue),setValue:(e,t)=>{sm.getCurrent(e).each((e=>{Jd.setValue(e,t)}))}}})]),r=Or([Rr(((t,o)=>{const n=Lu(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=Xr(e.prefix);n.label().each((e=>{pt(e.element,"for",o),pt(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=Xr(e.prefix);pt(o.element,"id",n),pt(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>Pu(t,e,"field"),getLabel:t=>Pu(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var Ox=Object.freeze({__proto__:null,exhibit:(e,t)=>pa({attributes:vs([{key:t.tabAttr,value:"true"}])})}),_x=[as("tabAttr","data-alloy-tabstop")];const Tx=dl({fields:_x,name:"tabstopping",active:Ox});var Ex=tinymce.util.Tools.resolve("tinymce.html.Entities");const Bx=(e,t,o,n)=>{const s=Mx(e,t,o,n);return Cx.sketch(s)},Mx=(e,t,o,n)=>({dom:Ax(o),components:e.toArray().concat([t]),fieldBehaviours:ll(n)}),Ax=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),Dx=(e,t)=>Cx.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Na(t.translate(e))]}),Fx=Xr("form-component-change"),Ix=Xr("form-close"),Vx=Xr("form-cancel"),Rx=Xr("form-action"),zx=Xr("form-submit"),Hx=Xr("form-block"),Px=Xr("form-unblock"),Nx=Xr("form-tabchange"),Lx=Xr("form-resize"),Wx=y([Zn("data"),as("inputAttributes",{}),as("inputStyles",{}),as("tag","input"),as("inputClasses",[]),fi("onSetValue"),as("styles",{}),as("eventOrder",{}),Zd("inputBehaviours",[Jd,Hp]),as("selectOnFocus",!0)]),Ux=e=>ll([Hp.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=Aa(t);t.dom.setSelectionRange(0,o.length)}:b})]),jx=e=>({...Ux(e),...eu(e.inputBehaviours,[Jd.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>Aa(e.element),setValue:(e,t)=>{Aa(e.element)!==t&&Da(e.element,t)}},onSetValue:e.onSetValue})])}),Gx=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),$x=Qu({name:"Input",configFields:Wx(),factory:(e,t)=>({uid:e.uid,dom:Gx(e),components:[],behaviours:jx(e),eventOrder:e.eventOrder})}),qx=e=>{let t=B.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=B.some(e),N(o,r),o=[])})),{get:n,map:e=>qx((t=>{n((o=>{t(e(o))}))})),isReady:s}},Xx={nu:qx,pure:e=>qx((t=>{t(e)}))},Kx=e=>{setTimeout((()=>{throw e}),0)},Yx=e=>{const t=t=>{e().then(t,Kx)};return{map:t=>Yx((()=>e().then(t))),bind:t=>Yx((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>Yx((()=>e().then((()=>t.toPromise())))),toLazy:()=>Xx.nu(t),toCached:()=>{let t=null;return Yx((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},Jx=e=>Yx((()=>new Promise(e))),Zx=e=>Yx((()=>Promise.resolve(e))),Qx=["input","textarea"],ew=e=>{const t=Ve(e);return V(Qx,t)},tw=(e,t)=>{const o=t.getRoot(e).getOr(e.element);Ta(o,t.invalidClass),t.notify.each((t=>{ew(e.element)&&pt(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{Lr(e,t.validHtml)})),t.onValid(e)}))},ow=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);_a(s,t.invalidClass),t.notify.each((t=>{ew(e.element)&&pt(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{Lr(e,n)})),t.onInvalid(e,n)}))},nw=(e,t,o)=>t.validator.fold((()=>Zx($o.value(!0))),(t=>t.validate(e))),sw=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),nw(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(ow(e,t,0,o),$o.error(o))),(o=>(tw(e,t),$o.value(o)))):$o.error("No longer in system"))));var rw=Object.freeze({__proto__:null,markValid:tw,markInvalid:ow,query:nw,run:sw,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return Ea(o,t.invalidClass)}}),aw=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Or([Er(t.onEvent,(t=>{sw(t,e).get(x)}))].concat(t.validateOnLoad?[Rr((t=>{sw(t,e).get(b)}))]:[])))).getOr({})}),iw=[Un("invalidClass"),as("getRoot",B.none),rs("notify",[as("aria","alert"),as("getContainer",B.none),as("validHtml",""),fi("onValid"),fi("onInvalid"),fi("onValidate")]),rs("validator",[Un("validate"),as("onEvent","input"),as("validateOnLoad",!0)])];const lw=dl({fields:iw,name:"invalidating",active:aw,apis:rw,extra:{validation:e=>t=>{const o=Jd.getValue(t);return Zx(e(o))}}});var cw=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n)}),dw=[jn("others",In($o.value,Cn()))],uw=Object.freeze({__proto__:null,init:()=>{const e={},t=y({});return ma({readState:t,getOrCreate:(t,o,n)=>{const s=ae(o.others);if(s)return fe(e,n).getOrThunk((()=>{const s=fe(o.others,n).getOrDie("No information found for coupled component: "+n)(t),r=t.getSystem().build(s);return e[n]=r,r}));throw new Error("Cannot find coupled component: "+n+". Known coupled components: "+JSON.stringify(s,null,2))}})}});const mw=dl({fields:dw,name:"coupling",apis:cw,state:uw}),gw=y("sink"),pw=y(Tu({name:gw(),overrides:y({dom:{tag:"div"},behaviours:ll([ad.config({useFixed:T})]),events:Or([Fr(zs()),Fr(Ms()),Fr(Ls())])})}));var hw;!function(e){e[e.HighlightFirst=0]="HighlightFirst",e[e.HighlightNone=1]="HighlightNone"}(hw||(hw={}));const fw=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},bw=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=xw(n,e);return i.map((e=>e.bind((e=>B.from(xh.sketch({...r.menu(),uid:ta(""),data:e,highlightImmediately:a===hw.HighlightFirst,onOpenMenu:(e,t)=>{const n=l().getOrDie();ad.position(n,t,{anchor:o}),Ad.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();ad.position(n,o,{anchor:{type:"submenu",item:t}}),Ad.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();ad.position(s,t,{anchor:o}),N(n,(e=>{ad.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(Hp.focus(n),Ad.close(s),B.some(!0))}))))))})(e,t,fw(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{Ad.isOpen(n)&&Ad.close(n)}),(e=>{Ad.cloak(n),Ad.open(n,e),r(n)})),n)))},vw=(e,t,o,n,s,r,a)=>(Ad.close(n),Zx(n)),yw=(e,t,o,n,s,r)=>{const a=mw.getCoupled(o,"sandbox");return(Ad.isOpen(a)?vw:bw)(e,t,o,a,n,s,r)},xw=(e,t)=>e.getSystem().getByUid(t.uid+"-"+gw()).map((e=>()=>$o.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>$o.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),ww=e=>{Ad.getState(e).each((e=>{xh.repositionMenus(e)}))},Sw=(e,t,o)=>{const n=oi(),s=xw(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id,role:"listbox"}},behaviours:ou(e.sandboxBehaviours,[Jd.config({store:{mode:"memory",initialValue:t}}),Ad.config({onOpen:(s,r)=>{const a=fw(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=sm.getCurrent(t).getOr(t),s=Wt(e.element);o?St(n.element,"min-width",s+"px"):((e,t)=>{Lt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,s)=>{n.unlink(t.element),void 0!==o&&void 0!==o.onClose&&o.onClose(e,s)},isPartOf:(e,o,n)=>ni(o,n)||ni(t,n),getAttachPoint:()=>s().getOrDie()}),sm.config({find:e=>Ad.getState(e).bind((e=>sm.getCurrent(e)))}),pl.config({channels:{...Rd({isExtraPart:_}),...Hd({doReposition:ww})}})])}},kw=e=>{const t=mw.getCoupled(e,"sandbox");ww(t)},Cw=()=>[as("sandboxClasses",[]),tu("sandboxBehaviours",[sm,pl,Ad,Jd])],Ow=y([Un("dom"),Un("fetch"),fi("onOpen"),bi("onExecute"),as("getHotspot",B.some),as("getAnchorOverrides",y({})),rc(),Zd("dropdownBehaviours",[Yp,mw,kp,Hp]),Un("toggleClass"),as("eventOrder",{}),Zn("lazySink"),as("matchWidth",!1),as("useMinWidth",!1),Zn("role")].concat(Cw())),_w=y([_u({schema:[gi()],name:"menu",defaults:e=>({onExecute:e.onExecute})}),pw()]),Tw=em({name:"Dropdown",configFields:Ow(),partFields:_w(),factory:(e,t,o,n)=>{const s=e=>{Ad.getState(e).each((e=>{xh.highlightPrimary(e)}))},r={expand:t=>{Yp.isOn(t)||yw(e,x,t,n,b,hw.HighlightNone).get(b)},open:t=>{Yp.isOn(t)||yw(e,x,t,n,b,hw.HighlightFirst).get(b)},isOpen:Yp.isOn,close:t=>{Yp.isOn(t)&&yw(e,x,t,n,b,hw.HighlightFirst).get(b)},repositionMenus:e=>{Yp.isOn(e)&&kw(e)}},a=(e,t)=>(wr(e),B.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:eu(e.dropdownBehaviours,[Yp.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),mw.config({others:{sandbox:t=>Sw(e,t,{onOpen:()=>Yp.on(t),onClose:()=>Yp.off(t)})}}),kp.config({mode:"special",onSpace:a,onEnter:a,onDown:(e,t)=>{if(Tw.isOpen(e)){const t=mw.getCoupled(e,"sandbox");s(t)}else Tw.open(e);return B.some(!0)},onEscape:(e,t)=>Tw.isOpen(e)?(Tw.close(e),B.some(!0)):B.none()}),Hp.config({})]),events:Zp(B.some((t=>{yw(e,x,t,n,s,hw.HighlightFirst).get(b)}))),eventOrder:{...e.eventOrder,[Zs()]:["disabling","toggling","alloy.base.behaviour"]},apis:r,domModification:{attributes:{"aria-haspopup":"true",...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",fe(e.dom,"attributes").bind((e=>fe(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),Ew=dl({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Or([_r(Gs(),T)]),exhibit:()=>pa({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),Bw=Xr("color-input-change"),Mw=Xr("color-swatch-change"),Aw=Xr("color-picker-cancel"),Dw=Tu({schema:[Un("dom")],name:"label"}),Fw=e=>Tu({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Or([Br(_s(),((t,o,n)=>e(t,n)),[t]),Br(Ms(),((t,o,n)=>e(t,n)),[t]),Br(As(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),Iw=Fw("top-left"),Vw=Fw("top"),Rw=Fw("top-right"),zw=Fw("right"),Hw=Fw("bottom-right"),Pw=Fw("bottom"),Nw=Fw("bottom-left");var Lw=[Dw,Fw("left"),zw,Vw,Pw,Iw,Rw,Nw,Hw,Ou({name:"thumb",defaults:y({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Or([Ar(_s(),e,"spectrum"),Ar(Ts(),e,"spectrum"),Ar(Es(),e,"spectrum"),Ar(Ms(),e,"spectrum"),Ar(As(),e,"spectrum"),Ar(Fs(),e,"spectrum")])})}),Ou({schema:[Ln("mouseIsDown",(()=>hs(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:ll([kp.config({mode:"special",onLeft:o=>t.onLeft(o,e),onRight:o=>t.onRight(o,e),onUp:o=>t.onUp(o,e),onDown:o=>t.onDown(o,e)}),Hp.config({})]),events:Or([Er(_s(),o),Er(Ts(),o),Er(Ms(),o),Er(As(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const Ww=y("slider.change.value"),Uw=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?B.some(e.touches[0]).map((e=>zt(e.clientX,e.clientY))):B.none()}{const e=t;return void 0!==e.clientX?B.some(e).map((e=>zt(e.clientX,e.clientY))):B.none()}},jw=e=>e.model.minX,Gw=e=>e.model.minY,$w=e=>e.model.minX-1,qw=e=>e.model.minY-1,Xw=e=>e.model.maxX,Kw=e=>e.model.maxY,Yw=e=>e.model.maxX+1,Jw=e=>e.model.maxY+1,Zw=(e,t,o)=>t(e)-o(e),Qw=e=>Zw(e,Xw,jw),eS=e=>Zw(e,Kw,Gw),tS=e=>Qw(e)/2,oS=e=>eS(e)/2,nS=e=>e.stepSize,sS=e=>e.snapToGrid,rS=e=>e.snapStart,aS=e=>e.rounded,iS=(e,t)=>void 0!==e[t+"-edge"],lS=e=>iS(e,"left"),cS=e=>iS(e,"right"),dS=e=>iS(e,"top"),uS=e=>iS(e,"bottom"),mS=e=>e.model.value.get(),gS=(e,t)=>({x:e,y:t}),pS=(e,t)=>{xr(e,Ww(),{value:t})},hS=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),fS=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),bS=(e,t,o)=>Math.max(t,Math.min(o,e)),vS=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=bS(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return bS(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},yS=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},xS="top",wS="right",SS="bottom",kS="left",CS=e=>e.element.dom.getBoundingClientRect(),OS=(e,t)=>e[t],_S=e=>{const t=CS(e);return OS(t,kS)},TS=e=>{const t=CS(e);return OS(t,wS)},ES=e=>{const t=CS(e);return OS(t,xS)},BS=e=>{const t=CS(e);return OS(t,SS)},MS=e=>{const t=CS(e);return OS(t,"width")},AS=e=>{const t=CS(e);return OS(t,"height")},DS=(e,t,o)=>(e+t)/2-o,FS=(e,t)=>{const o=CS(e),n=CS(t),s=OS(o,kS),r=OS(o,wS),a=OS(n,kS);return DS(s,r,a)},IS=(e,t)=>{const o=CS(e),n=CS(t),s=OS(o,xS),r=OS(o,SS),a=OS(n,xS);return DS(s,r,a)},VS=(e,t)=>{xr(e,Ww(),{value:t})},RS=(e,t,o)=>{const n={min:jw(t),max:Xw(t),range:Qw(t),value:o,step:nS(t),snap:sS(t),snapStart:rS(t),rounded:aS(t),hasMinEdge:lS(t),hasMaxEdge:cS(t),minBound:_S(e),maxBound:TS(e),screenRange:MS(e)};return vS(n)},zS=e=>(t,o)=>((e,t,o)=>{const n=(e>0?fS:hS)(mS(o),jw(o),Xw(o),nS(o));return VS(t,n),B.some(n)})(e,t,o).map(T),HS=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=MS(e),a=n.bind((t=>B.some(FS(t,e)))).getOr(0),i=s.bind((t=>B.some(FS(t,e)))).getOr(r),l={min:jw(t),max:Xw(t),range:Qw(t),value:o,hasMinEdge:lS(t),hasMaxEdge:cS(t),minBound:_S(e),minOffset:0,maxBound:TS(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return yS(l)})(t,r,o,n,s);return _S(t)-_S(e)+a},PS=zS(-1),NS=zS(1),LS=B.none,WS=B.none,US={"top-left":B.none(),top:B.none(),"top-right":B.none(),right:B.some(((e,t)=>{pS(e,Yw(t))})),"bottom-right":B.none(),bottom:B.none(),"bottom-left":B.none(),left:B.some(((e,t)=>{pS(e,$w(t))}))};var jS=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=RS(e,t,o);return VS(e,n),n},setToMin:(e,t)=>{const o=jw(t);VS(e,o)},setToMax:(e,t)=>{const o=Xw(t);VS(e,o)},findValueOfOffset:RS,getValueFromEvent:e=>Uw(e).map((e=>e.left)),findPositionOfValue:HS,setPositionFromValue:(e,t,o,n)=>{const s=mS(o),r=HS(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Wt(t.element)/2;St(t.element,"left",r-a+"px")},onLeft:PS,onRight:NS,onUp:LS,onDown:WS,edgeActions:US});const GS=(e,t)=>{xr(e,Ww(),{value:t})},$S=(e,t,o)=>{const n={min:Gw(t),max:Kw(t),range:eS(t),value:o,step:nS(t),snap:sS(t),snapStart:rS(t),rounded:aS(t),hasMinEdge:dS(t),hasMaxEdge:uS(t),minBound:ES(e),maxBound:BS(e),screenRange:AS(e)};return vS(n)},qS=e=>(t,o)=>((e,t,o)=>{const n=(e>0?fS:hS)(mS(o),Gw(o),Kw(o),nS(o));return GS(t,n),B.some(n)})(e,t,o).map(T),XS=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=AS(e),a=n.bind((t=>B.some(IS(t,e)))).getOr(0),i=s.bind((t=>B.some(IS(t,e)))).getOr(r),l={min:Gw(t),max:Kw(t),range:eS(t),value:o,hasMinEdge:dS(t),hasMaxEdge:uS(t),minBound:ES(e),minOffset:0,maxBound:BS(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return yS(l)})(t,r,o,n,s);return ES(t)-ES(e)+a},KS=B.none,YS=B.none,JS=qS(-1),ZS=qS(1),QS={"top-left":B.none(),top:B.some(((e,t)=>{pS(e,qw(t))})),"top-right":B.none(),right:B.none(),"bottom-right":B.none(),bottom:B.some(((e,t)=>{pS(e,Jw(t))})),"bottom-left":B.none(),left:B.none()};var ek=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=$S(e,t,o);return GS(e,n),n},setToMin:(e,t)=>{const o=Gw(t);GS(e,o)},setToMax:(e,t)=>{const o=Kw(t);GS(e,o)},findValueOfOffset:$S,getValueFromEvent:e=>Uw(e).map((e=>e.top)),findPositionOfValue:XS,setPositionFromValue:(e,t,o,n)=>{const s=mS(o),r=XS(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=It(t.element)/2;St(t.element,"top",r-a+"px")},onLeft:KS,onRight:YS,onUp:JS,onDown:ZS,edgeActions:QS});const tk=(e,t)=>{xr(e,Ww(),{value:t})},ok=(e,t)=>({x:e,y:t}),nk=(e,t)=>(o,n)=>((e,t,o,n)=>{const s=e>0?fS:hS,r=t?mS(n).x:s(mS(n).x,jw(n),Xw(n),nS(n)),a=t?s(mS(n).y,Gw(n),Kw(n),nS(n)):mS(n).y;return tk(o,ok(r,a)),B.some(r)})(e,t,o,n).map(T),sk=nk(-1,!1),rk=nk(1,!1),ak=nk(-1,!0),ik=nk(1,!0),lk={"top-left":B.some(((e,t)=>{pS(e,gS($w(t),qw(t)))})),top:B.some(((e,t)=>{pS(e,gS(tS(t),qw(t)))})),"top-right":B.some(((e,t)=>{pS(e,gS(Yw(t),qw(t)))})),right:B.some(((e,t)=>{pS(e,gS(Yw(t),oS(t)))})),"bottom-right":B.some(((e,t)=>{pS(e,gS(Yw(t),Jw(t)))})),bottom:B.some(((e,t)=>{pS(e,gS(tS(t),Jw(t)))})),"bottom-left":B.some(((e,t)=>{pS(e,gS($w(t),Jw(t)))})),left:B.some(((e,t)=>{pS(e,gS($w(t),oS(t)))}))};var ck=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=RS(e,t,o.left),s=$S(e,t,o.top),r=ok(n,s);return tk(e,r),r},setToMin:(e,t)=>{const o=jw(t),n=Gw(t);tk(e,ok(o,n))},setToMax:(e,t)=>{const o=Xw(t),n=Kw(t);tk(e,ok(o,n))},getValueFromEvent:e=>Uw(e),setPositionFromValue:(e,t,o,n)=>{const s=mS(o),r=HS(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=XS(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Wt(t.element)/2,l=It(t.element)/2;St(t.element,"left",r-i+"px"),St(t.element,"top",a-l+"px")},onLeft:sk,onRight:rk,onUp:ak,onDown:ik,edgeActions:lk});const dk=em({name:"Slider",configFields:[as("stepSize",1),as("onChange",b),as("onChoose",b),as("onInit",b),as("onDragStart",b),as("onDragEnd",b),as("snapToGrid",!1),as("rounded",!0),Zn("snapStart"),jn("model",Pn("mode",{x:[as("minX",0),as("maxX",100),Ln("value",(e=>hs(e.mode.minX))),Un("getInitialValue"),xi("manager",jS)],y:[as("minY",0),as("maxY",100),Ln("value",(e=>hs(e.mode.minY))),Un("getInitialValue"),xi("manager",ek)],xy:[as("minX",0),as("maxX",100),as("minY",0),as("maxY",100),Ln("value",(e=>hs({x:e.mode.minX,y:e.mode.minY}))),Un("getInitialValue"),xi("manager",ck)]})),Zd("sliderBehaviours",[kp,Jd]),Ln("mouseIsDown",(()=>hs(!1)))],partFields:Lw,factory:(e,t,o,n)=>{const s=t=>Nu(t,e,"thumb"),r=t=>Nu(t,e,"spectrum"),a=t=>Pu(t,e,"left-edge"),i=t=>Pu(t,e,"right-edge"),l=t=>Pu(t,e,"top-edge"),c=t=>Pu(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&Pu(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)};return{uid:e.uid,dom:e.dom,components:t,behaviours:eu(e.sliderBehaviours,[kp.config({mode:"special",focusIn:t=>Pu(t,e,"spectrum").map(kp.focusIn).map(T)}),Jd.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),pl.config({channels:{[Id()]:{onReceive:p}}})]),events:Or([Er(Ww(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),B.some(!0)})(t,o.event.value)})),Rr(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),Er(_s(),h),Er(Es(),f),Er(Ms(),h),Er(Fs(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),uk=Xr("rgb-hex-update"),mk=Xr("slider-update"),gk=Xr("palette-update"),pk="form",hk=[Zd("formBehaviours",[Jd])],fk=e=>"<alloy.field."+e+">",bk=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:eu(e.formBehaviours,[Jd.config({store:{mode:"manual",getValue:t=>{const o=Wu(t,e);return ce(o,((e,t)=>e().bind((e=>{return o=sm.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+Ur(e.element)),o.fold((()=>$o.error(n)),$o.value);var o,n})).map(Jd.getValue)))},setValue:(t,o)=>{le(o,((o,n)=>{Pu(t,e,n).each((e=>{sm.getCurrent(e).each((e=>{Jd.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>Pu(t,e,o).bind(sm.getCurrent)}}),vk={getField:da(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),Iu(pk,fk(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=P(n,(e=>Ou({name:e,pname:fk(e)})));return Ku(pk,hk,s,bk,o)}},yk=Xr("valid-input"),xk=Xr("invalid-input"),wk=Xr("validating-input"),Sk="colorcustom.rgb.",kk=(e,t,o,n)=>{const s=(o,n)=>lw.config({invalidClass:t("invalid"),notify:{onValidate:e=>{xr(e,wk,{type:o})},onValid:e=>{xr(e,yk,{type:o,value:Jd.getValue(e)})},onInvalid:e=>{xr(e,xk,{type:o,value:Jd.getValue(e)})}},validator:{validate:t=>{const o=Jd.getValue(t),s=n(o)?$o.value(!0):$o.error(e("aria.input.invalid"));return Zx(s)},validateOnLoad:!1}}),r=(o,n,r,a,i)=>{const l=e("colorcustom.rgb.range"),c=Cx.parts.label({dom:{tag:"label",attributes:{"aria-label":a}},components:[Na(r)]}),d=Cx.parts.field({data:i,factory:$x,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:ll([s(n,o),Tx.config({})]),onSetValue:e=>{lw.isInvalid(e)&&lw.run(e).get(b)}}),u=[c,d],m="hex"!==n?[Cx.parts["aria-descriptor"]({text:l})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(m)}},a=(e,t)=>{const o=t.red,n=t.green,s=t.blue;Jd.setValue(e,{red:o,green:n,blue:s})},i=Ch({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),l=(e,t)=>{i.getOpt(e).each((e=>{St(e.element,"background-color","#"+t.value)}))},c=Qu({factory:()=>{const s={red:hs(B.some(255)),green:hs(B.some(255)),blue:hs(B.some(255)),hex:hs(B.some("ffffff"))},c=e=>s[e].get(),d=(e,t)=>{s[e].set(t)},u=e=>{const t=e.red,o=e.green,n=e.blue;d("red",B.some(t)),d("green",B.some(o)),d("blue",B.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?d(o.type,B.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=ry(t);d("hex",B.some(t));const s=xy(n);a(e,s),u(s),xr(e,uk,{hex:n}),l(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);d(t,B.some(n)),c("red").bind((e=>c("green").bind((t=>c("blue").map((o=>by(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=uy(t);return vk.getField(e,"hex").each((t=>{Hp.isFocused(t)||Jd.setValue(e,{hex:o.value})})),o})(e,t);xr(e,uk,{hex:o}),l(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(Sk+t+".label"),description:e(Sk+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return nn(vk.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",Cx.sketch(r(vy,"red",h.label,h.description,255))),o.field("green",Cx.sketch(r(vy,"green",f.label,f.description,255))),o.field("blue",Cx.sketch(r(vy,"blue",b.label,b.description,255))),o.field("hex",Cx.sketch(r(ly,"hex",v.label,v.description,"ffffff"))),i.asSpec()],formBehaviours:ll([lw.config({invalidClass:t("form-invalid")}),Fp("rgb-form-events",[Er(yk,g),Er(xk,m),Er(wk,m)])])}))),{apis:{updateHex:(e,t)=>{Jd.setValue(e,{hex:t.value}),((e,t)=>{const o=xy(t);a(e,o),u(o)})(e,t),l(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return c},Ck=(e,t)=>{const o=Qu({name:"ColourPicker",configFields:[Un("dom"),as("onValidHex",b),as("onInvalidHex",b)],factory:o=>{const n=kk(e,t,o.onValidHex,o.onInvalidHex),s=((e,t)=>{const o=dk.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=dk.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return Qu({factory:e=>{const r=y({x:0,y:0}),a=ll([sm.config({find:B.some}),Hp.config({})]);return dk.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:r},rounded:!1,components:[o,n],onChange:(e,t,o)=>{xr(e,gk,{value:o})},onInit:(e,t,o,n)=>{s(o.element.dom,ky(Cy))},sliderBehaviours:a})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=_y(t,100,100),r=yy(n);s(o,ky(r))})(t,o)},setThumb:(e,t,o)=>{((e,t)=>{const o=Ty(xy(t));dk.setValue(e,{x:o.saturation,y:100-o.value})})(t,o)}},extraApis:{}})})(0,t),r={paletteRgba:hs(Cy),paletteHue:hs(0)},a=Ch(((e,t)=>{const o=dk.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=dk.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return dk.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:y(0)},components:[o,n],sliderBehaviours:ll([Hp.config({})]),onChange:(e,t,o)=>{xr(e,mk,{value:o})}})})(0,t)),i=Ch(s.sketch({})),l=Ch(n.sketch({})),c=(e,t,o)=>{i.getOpt(e).each((e=>{s.setHue(e,o)}))},d=(e,t)=>{l.getOpt(e).each((e=>{n.updateHex(e,t)}))},u=(e,t,o)=>{a.getOpt(e).each((e=>{dk.setValue(e,(e=>100-e/360*100)(o))}))},m=(e,t)=>{i.getOpt(e).each((e=>{s.setThumb(e,t)}))},g=(e,t,o,n)=>{((e,t)=>{const o=xy(e);r.paletteRgba.set(o),r.paletteHue.set(t)})(t,o),N(n,(n=>{n(e,t,o)}))};return{uid:o.uid,dom:o.dom,components:[i.asSpec(),a.asSpec(),l.asSpec()],behaviours:ll([Fp("colour-picker-events",[Er(uk,(()=>{const e=[c,u,m];return(t,o)=>{const n=o.event.hex,s=(e=>Ty(xy(e)))(n);g(t,n,s.hue,e)}})()),Er(gk,(()=>{const e=[d];return(t,o)=>{const n=o.event.value,s=r.paletteHue.get(),a=_y(s,n.x,100-n.y),i=Ey(a);g(t,i,s,e)}})()),Er(mk,(()=>{const e=[c,d];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=r.paletteRgba.get(),a=Ty(s),i=_y(n,a.saturation,a.value),l=Ey(i);g(t,l,n,e)}})())]),sm.config({find:e=>l.getOpt(e)}),kp.config({mode:"acyclic"})])}}});return o},Ok=()=>sm.config({find:B.some}),_k=e=>sm.config({find:t=>et(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),Tk=yn([as("preprocess",x),as("postprocess",x)]),Ek=(e,t,o)=>Jd.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),Bk=(e,t,o)=>Ek(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),Mk=(e,t)=>{const o=zn("RepresentingConfigs.memento processors",Tk,t);return Jd.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=Jd.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);Jd.setValue(r,s)}}})},Ak=Bk,Dk=Ek,Fk=e=>Jd.config({store:{mode:"memory",initialValue:e}}),Ik={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var Vk=tinymce.util.Tools.resolve("tinymce.Resource"),Rk=tinymce.util.Tools.resolve("tinymce.util.Tools");const zk=Xr("alloy-fake-before-tabstop"),Hk=Xr("alloy-fake-after-tabstop"),Pk=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:ll([Hp.config({ignore:!0}),Tx.config({})])}),Nk=e=>({dom:{tag:"div",classes:["tox-navobj"]},components:[Pk([zk]),e,Pk([Hk])],behaviours:ll([_k(1)])}),Lk=(e,t)=>{xr(e,zs(),{raw:{which:9,shiftKey:t}})},Wk=(e,t)=>{const o=t.element;Ea(o,zk)?Lk(e,!0):Ea(o,Hk)&&Lk(e,!1)},Uk=e=>dx(e,["."+zk,"."+Hk].join(","),_),jk=Xr("toolbar.button.execute"),Gk={[Zs()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},$k=(e,t,o)=>Ih(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),qk=(e,t)=>$k(e,t,[]),Xk=(e,t)=>$k(e,t,[Dp.config({})]),Kk=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[Na(o.translate(e))],behaviours:ll([Dp.config({})])}),Yk=Xr("update-menu-text"),Jk=Xr("update-menu-icon"),Zk=(e,t,o)=>{const n=hs(b),s=e.text.map((e=>Ch(Kk(e,t,o.providers)))),r=e.icon.map((e=>Ch(Xk(e,o.providers.icons)))),a=(e,t)=>{const o=Jd.getValue(e);return Hp.focus(o),xr(o,"keydown",{raw:t.event.raw}),Tw.close(o),B.some(!0)},i=e.role.fold((()=>({})),(e=>({role:e}))),l=e.tooltip.fold((()=>({})),(e=>{const t=o.providers.translate(e);return{title:t,"aria-label":t}})),c=Ih("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons);return Ch(Tw.sketch({...e.uid?{uid:e.uid}:{},...i,dom:{tag:"button",classes:[t,`${t}--select`].concat(P(e.classes,(e=>`${t}--${e}`))),attributes:{...l}},components:Uv([r.map((e=>e.asSpec())),s.map((e=>e.asSpec())),B.some(c)]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:ll([...e.dropdownBehaviours,Vv((()=>e.disabled||o.providers.isDisabled())),Iv(),Ew.config({}),Dp.config({}),Fp("dropdown-events",[Pv(e,n),Nv(e,n)]),Fp("menubutton-update-display-text",[Er(Yk,((e,t)=>{s.bind((t=>t.getOpt(e))).each((e=>{Dp.set(e,[Na(o.providers.translate(t.event.text))])}))})),Er(Jk,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Dp.set(e,[Xk(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:nn(Gk,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:ll([kp.config({mode:"special",onLeft:a,onRight:a})]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:ab(0,e.columns,e.presets)},fetch:t=>Jx(S(e.fetch,t))})).asSpec()},Qk=e=>"separator"===e.type,eC={type:"separator"},tC=(e,t)=>{const o=((e,t)=>{const o=j(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!Qk(e[e.length-1])?e.concat([eC]):e:be(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&Qk(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return U(o,((e,o)=>{const n=(e=>{if(Qk(e))return e;{const t=fe(e,"value").getOrThunk((()=>Xr("generated-menu-item")));return nn({value:t},e)}})(o),s=((e,t)=>(e=>be(e,"getSubmenuItems"))(e)?((e,t)=>{const o=e.getSubmenuItems(),n=tC(o,t);return{item:e,menus:nn(n.menus,bs(e.value,n.items)),expansions:nn(n.expansions,bs(e.value,e.value))}})(e,t):{item:e,menus:{},expansions:{}})(n,t);return{menus:nn(e.menus,s.menus),items:[s.item].concat(e.items),expansions:nn(e.expansions,s.expansions)}}),{menus:{},expansions:{},items:[]})},oC=(e,t,o,n)=>{const s=Xr("primary-menu"),r=tC(e,o.shared.providers.menuItems());if(0===r.items.length)return B.none();const a=ix(s,r.items,t,o,n),i=ce(r.menus,((e,n)=>ix(n,e,t,o,!1))),l=nn(i,bs(s,a));return B.from(xh.tieredData(s,l,r.expansions))},nC=e=>!be(e,"items"),sC="data-value",rC=(e,t,o,n)=>P(o,(o=>nC(o)?{type:"togglemenuitem",text:o.text,value:o.value,active:o.value===n,onAction:()=>{Jd.setValue(e,o.value),xr(e,Fx,{name:t}),Hp.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>rC(e,t,o.items,n)})),aC=(e,t)=>re(e,(e=>nC(e)?Se(e.value===t,e):aC(e.items,t))),iC=Qu({name:"HtmlSelect",configFields:[Un("options"),Zd("selectBehaviours",[Hp,Jd]),as("selectClasses",[]),as("selectAttributes",{}),Zn("data")],factory:(e,t)=>{const o=P(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>bs("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:eu(e.selectBehaviours,[Hp.config({}),Jd.config({store:{mode:"manual",getValue:e=>Aa(e.element),setValue:(t,o)=>{G(e.options,(e=>e.value===o)).isSome()&&Da(t.element,o)},...n}})])}}}),lC=y([as("field1Name","field1"),as("field2Name","field2"),vi("onLockedChange"),pi(["lockClass"]),as("locked",!1),tu("coupledFieldBehaviours",[sm,Jd])]),cC=(e,t)=>Ou({factory:Cx,name:e,overrides:e=>({fieldBehaviours:ll([Fp("coupled-input-behaviour",[Er(Ps(),(o=>{((e,t,o)=>Pu(e,t,o).bind(sm.getCurrent))(o,e,t).each((t=>{Pu(o,e,"lock").each((n=>{Yp.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),dC=y([cC("field1","field2"),cC("field2","field1"),Ou({factory:kh,schema:[Un("dom")],name:"lock",overrides:e=>({buttonBehaviours:ll([Yp.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),uC=em({name:"FormCoupledInputs",configFields:lC(),partFields:dC(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:ou(e.coupledFieldBehaviours,[sm.config({find:B.some}),Jd.config({store:{mode:"manual",getValue:t=>{const o=ju(t,e,["field1","field2"]);return{[e.field1Name]:Jd.getValue(o.field1()),[e.field2Name]:Jd.getValue(o.field2())}},setValue:(t,o)=>{const n=ju(t,e,["field1","field2"]);ve(o,e.field1Name)&&Jd.setValue(n.field1(),o[e.field1Name]),ve(o,e.field2Name)&&Jd.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>Pu(t,e,"field1"),getField2:t=>Pu(t,e,"field2"),getLock:t=>Pu(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),mC=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return $o.value({value:e,unit:o})}return $o.error(e)},gC=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>be(o,e);return e.unit===t?B.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?B.some(e.value):B.some(e.value/o[e.unit]*o[t]):B.none()},pC=e=>B.none(),hC=(e,t)=>{const o=e.label.map((e=>Dx(e,t))),n=[vm.config({disabled:()=>e.disabled||t.isDisabled()}),Iv(),kp.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(yr(e,zx),B.some(!0))}),Fp("textfield-change",[Er(Ps(),((t,o)=>{xr(t,Fx,{name:e.name})})),Er(Ys(),((t,o)=>{xr(t,Fx,{name:e.name})}))]),Tx.config({})],s=e.validation.map((e=>lw.config({getRoot:e=>Je(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=Jd.getValue(t),n=e.validator(o);return Zx(!0===n?$o.value(o):$o.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(y({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(y({}),(e=>({inputmode:e})))},a=Cx.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:ll(q([n,s])),selectOnFocus:!1,factory:$x}),i=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),l=[vm.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{Cx.getField(e).each(vm.disable)},onEnabled:e=>{Cx.getField(e).each(vm.enable)}}),Iv()];return Bx(o,a,i,l)};var fC=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Or([Er(e.event,o),zr((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Er(e,(()=>t.cancel()))])).getOr([])))}});const bC=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},vC=e=>{const t=hs(null);return ma({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var yC=Object.freeze({__proto__:null,throttle:vC,init:e=>e.stream.streams.state(e)}),xC=[jn("stream",Pn("mode",{throttle:[Un("delay"),as("stopEvent",!0),xi("streams",{setup:(e,t)=>{const o=e.stream,n=bC(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:vC})]})),as("event","input"),Zn("cancelEvent"),vi("onStream")];const wC=dl({fields:xC,name:"streaming",active:fC,state:yC}),SC=(e,t,o)=>{const n=Jd.getValue(o);Jd.setValue(t,n),CC(t)},kC=(e,t)=>{const o=e.element,n=Aa(o),s=o.dom;"number"!==ft(o,"type")&&t(s,n)},CC=e=>{kC(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},OC=y("alloy.typeahead.itemexecute"),_C=y([Zn("lazySink"),Un("fetch"),as("minChars",5),as("responseTime",1e3),fi("onOpen"),as("getHotspot",B.some),as("getAnchorOverrides",y({})),as("layouts",B.none()),as("eventOrder",{}),ps("model",{},[as("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),as("selectsOver",!0),as("populateFromBrowse",!0)]),fi("onSetValue"),bi("onExecute"),fi("onItemExecute"),as("inputClasses",[]),as("inputAttributes",{}),as("inputStyles",{}),as("matchWidth",!0),as("useMinWidth",!1),as("dismissOnBlur",!0),pi(["openClass"]),Zn("initialData"),Zd("typeaheadBehaviours",[Hp,Jd,wC,kp,Yp,mw]),Ln("previewing",(()=>hs(!0)))].concat(Wx()).concat(Cw())),TC=y([_u({schema:[gi()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlight:(t,o)=>{e.previewing.get()?t.getSystem().getByUid(e.uid).each((n=>{((e,t,o)=>{if(e.selectsOver){const n=Jd.getValue(t),s=e.getDisplayText(n),r=Jd.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?B.some((()=>{SC(0,t,o),((e,t)=>{kC(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):B.none()}return B.none()})(e.model,n,o).fold((()=>Em.dehighlight(t,o)),(e=>e()))})):t.getSystem().getByUid(e.uid).each((t=>{e.model.populateFromBrowse&&SC(e.model,t,o)})),e.previewing.set(!1)},onExecute:(t,o)=>t.getSystem().getByUid(e.uid).toOptional().map((e=>(xr(e,OC(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),t.getSystem().getByUid(e.uid).each((t=>{e.model.populateFromBrowse&&SC(e.model,t,o)}))}})})]),EC=em({name:"Typeahead",configFields:_C(),partFields:TC(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=mw.getCoupled(t,"sandbox");if(Ad.isOpen(r))sm.getCurrent(r).each((e=>{Em.getHighlighted(e).fold((()=>{s(e)}),(()=>{Cr(r,e.element,"keydown",o)}))}));else{const o=e=>{sm.getCurrent(e).each(s)};bw(e,a(t),t,r,n,o,hw.HighlightFirst).get(b)}},r=Ux(e),a=e=>t=>t.map((t=>{const o=he(t.menus),n=X(o,(e=>W(e.items,(e=>"item"===e.type))));return Jd.getState(e).update(P(n,(e=>e.data))),t})),i=[Hp.config({}),Jd.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>Aa(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{Da(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>bs("initialValue",e))).getOr({})}}),wC.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=mw.getCoupled(t,"sandbox");if(Hp.isFocused(t)&&Aa(t.element).length>=e.minChars){const o=sm.getCurrent(s).bind((e=>Em.getHighlighted(e).map(Jd.getValue)));e.previewing.set(!0);const r=t=>{sm.getCurrent(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Em.highlightFirst(t)}),(e=>{Em.highlightBy(t,(t=>Jd.getValue(t).value===e.value)),Em.getHighlighted(t).orThunk((()=>(Em.highlightFirst(t),B.none())))}))}))};bw(e,a(t),t,s,n,r,hw.HighlightFirst).get(b)}},cancelEvent:nr()}),kp.config({mode:"special",onDown:(e,t)=>(s(e,t,Em.highlightFirst),B.some(!0)),onEscape:e=>{const t=mw.getCoupled(e,"sandbox");return Ad.isOpen(t)?(Ad.close(t),B.some(!0)):B.none()},onUp:(e,t)=>(s(e,t,Em.highlightLast),B.some(!0)),onEnter:t=>{const o=mw.getCoupled(t,"sandbox"),n=Ad.isOpen(o);if(n&&!e.previewing.get())return sm.getCurrent(o).bind((e=>Em.getHighlighted(e))).map((e=>(xr(t,OC(),{item:e}),!0)));{const s=Jd.getValue(t);return yr(t,nr()),e.onExecute(o,t,s),n&&Ad.close(o),B.some(!0)}}}),Yp.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),mw.config({others:{sandbox:t=>Sw(e,t,{onOpen:()=>Yp.on(t),onClose:()=>Yp.off(t)})}}),Fp("typeaheadevents",[Pr((t=>{const o=b;yw(e,a(t),t,n,o,hw.HighlightFirst).get(b)})),Er(OC(),((t,o)=>{const n=mw.getCoupled(t,"sandbox");SC(e.model,t,o.event.item),yr(t,nr()),e.onItemExecute(t,n,o.event.item,Jd.getValue(t)),Ad.close(n),CC(t)}))].concat(e.dismissOnBlur?[Er(Ks(),(e=>{const t=mw.getCoupled(e,"sandbox");yl(t.element).isNone()&&Ad.close(t)}))]:[]))];return{uid:e.uid,dom:Gx(nn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...eu(e.typeaheadBehaviours,i)},eventOrder:e.eventOrder}}}),BC=e=>({...e,toCached:()=>BC(e.toCached()),bindFuture:t=>BC(e.bind((e=>e.fold((e=>Zx($o.error(e))),(e=>t(e)))))),bindResult:t=>BC(e.map((e=>e.bind(t)))),mapResult:t=>BC(e.map((e=>e.map(t)))),mapError:t=>BC(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>BC(Jx((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n($o.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),MC=e=>BC(Jx(e)),AC=e=>({isEnabled:()=>!vm.isDisabled(e),setEnabled:t=>vm.set(e,!t),setActive:t=>{const o=e.element;t?(_a(o,"tox-tbtn--enabled"),pt(o,"aria-pressed",!0)):(Ta(o,"tox-tbtn--enabled"),yt(o,"aria-pressed"))},isActive:()=>Ea(e.element,"tox-tbtn--enabled")}),DC=(e,t,o,n)=>Zk({text:e.text,icon:e.icon,tooltip:e.tooltip,role:n,fetch:(t,n)=>{e.fetch((e=>{n(oC(e,Uf.CLOSE_ON_EXECUTE,o,!1))}))},onSetup:e.onSetup,getApi:AC,columns:1,presets:"normal",classes:[],dropdownBehaviours:[Tx.config({})]},t,o.shared),FC=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{fl(t.element),xr(t,Rx,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(P(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:s(e)}})))}},IC=(e,t,o=[],n,s,r)=>{const a=t.fold((()=>({})),(e=>({action:e}))),i={buttonBehaviours:ll([Vv((()=>!e.enabled||r.isDisabled())),Iv(),Tx.config({}),Fp("button press",[Tr("click"),Tr("mousedown")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...a},l=nn(i,{dom:n});return nn(l,{components:s})},VC=(e,t,o,n=[])=>{const s={tag:"button",classes:["tox-tbtn"],attributes:e.tooltip.map((e=>({"aria-label":o.translate(e),title:o.translate(e)}))).getOr({})},r=e.icon.map((e=>qk(e,o.icons))),a=Uv([r]);return IC(e,t,n,s,a,o)},RC=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>qk(e,o.icons))),i=[a.getOrThunk((()=>Na(r)))],l=[...(e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}})(e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary")),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s];return IC(e,t,n,{tag:"button",classes:l,attributes:{title:r}},i,o)},zC=(e,t,o,n=[],s=[])=>{const r=RC(e,B.some(t),o,n,s);return kh.sketch(r)},HC=(e,t)=>o=>{"custom"===t?xr(o,Rx,{name:e,value:{}}):"submit"===t?yr(o,zx):"cancel"===t?yr(o,Vx):console.error("Unknown button type: ",t)},PC=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,onSetup:t=>(t.setEnabled(e.enabled),b),fetch:FC(n.items,t,o)},r=Ch(DC(s,"tox-tbtn",o,B.none()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=HC(e.name,t),s={...e,borderless:!1};return zC(s,n,o.shared.providers,[])}console.error("Unknown footer button type: ",t)},NC={type:"separator"},LC=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),WC=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),UC=(e,t)=>(e=>P(e,LC))(((e,t)=>W(t,(t=>t.type===e)))(e,t)),jC=e=>UC("header",e.targets),GC=e=>UC("anchor",e.targets),$C=e=>B.from(e.anchorTop).map((e=>WC("<top>",e))).toArray(),qC=e=>B.from(e.anchorBottom).map((e=>WC("<bottom>",e))).toArray(),XC=(e,t)=>{const o=e.toLowerCase();return W(t,(e=>{const t=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text;return Ce(t.toLowerCase(),o)||Ce(e.value.toLowerCase(),o)}))},KC=Xr("aria-invalid"),YC=(e,t)=>{e.dom.checked=t},JC=e=>e.dom.checked,ZC=e=>(t,o,n,s)=>fe(o,"name").fold((()=>e(o,s,B.none())),(r=>t.field(r,e(o,s,fe(n,r))))),QC={bar:ZC(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:P(e.items,t.interpreter)}))(e,t.shared))),collection:ZC(((e,t,o)=>((e,t,o)=>{const n=e.label.map((e=>Dx(e,t))),s=e=>(t,o)=>{ei(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,ft(n,"data-collection-item-value"))}))},r=s(((o,n,s,r)=>{n.stop(),t.isDisabled()||xr(o,Rx,{name:e.name,value:r})})),a=[Er(Is(),s(((e,t,o)=>{fl(o)}))),Er(Ls(),r),Er(er(),r),Er(Vs(),s(((e,t,o)=>{Qa(e.element,"."+Jf).each((e=>{Ta(e,Jf)})),_a(o,Jf)}))),Er(Rs(),s((e=>{Qa(e.element,"."+Jf).each((e=>{Ta(e,Jf)}))}))),Pr(s(((t,o,n,s)=>{xr(t,Rx,{name:e.name,value:s})})))],i=(e,t)=>P(Ac(e.element,".tox-collection__item"),t),l=Cx.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:x},behaviours:ll([vm.config({disabled:t.isDisabled,onDisabled:e=>{i(e,(e=>{_a(e,"tox-collection__item--state-disabled"),pt(e,"aria-disabled",!0)}))},onEnabled:e=>{i(e,(e=>{Ta(e,"tox-collection__item--state-disabled"),yt(e,"aria-disabled")}))}}),Iv(),Dp.config({}),Jd.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const s=P(n,(o=>{const n=Oh.translate(o.text),s=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",r=`<div class="tox-collection__item-icon">${o.icon}</div>`,a={_:" "," - ":" ","-":" "},i=n.replace(/\_| \- |\-/g,(e=>a[e]));return`<div class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${Ex.encodeAllRaw(o.value)}" title="${i}" aria-label="${i}">${r}${s}</div>`})),r="auto"!==e.columns&&e.columns>1?H(s,e.columns):[s],a=P(r,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));Lr(o.element,a.join(""))})(o,n),"auto"===e.columns&&xv(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{kp.setGridSize(o,e,t)})),yr(o,Lx)}}),Tx.config({}),kp.config((c=e.columns,1===c?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===c?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${Gf}`}})),Fp("collection-events",a)]),eventOrder:{[Zs()]:["disabling","alloy.base.behaviour","collection-events"]}});var c;return Bx(n,l,["tox-form__group--collection"],[])})(e,t.shared.providers,o))),alertbanner:ZC(((e,t)=>((e,t)=>xx.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[kh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:Ah(e.icon,t.icons),attributes:{title:t.translate(e.iconTooltip)}},action:t=>{xr(t,Rx,{name:"alert-banner",value:e.url})},buttonBehaviours:ll([Dh()])})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]}))(e,t.shared.providers))),input:ZC(((e,t,o)=>((e,t,o)=>hC({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:B.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:ZC(((e,t,o)=>((e,t,o)=>hC({name:e.name,multiline:!0,label:e.label,inputMode:B.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:B.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:ZC(((e,t)=>((e,t)=>{return{dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"label",classes:["tox-label"]},components:[Na(t.providers.translate(e.label))]},...P(e.items,t.interpreter)],behaviours:ll([Ok(),Dp.config({}),(o=B.none(),Bk(o,Nr,Lr)),kp.config({mode:"acyclic"})])};var o})(e,t.shared))),iframe:(C_=(e,t,o)=>((e,t,o)=>{const n=e.sandboxed,s={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...n?{sandbox:"allow-scripts allow-same-origin"}:{}},r=(e=>{const t=hs(e.getOr(""));return{getValue:e=>t.get(),setValue:(e,o)=>{t.get()!==o&&pt(e.element,"srcdoc",o),t.set(o)}}})(o),a=e.label.map((e=>Dx(e,t))),i=Cx.parts.field({factory:{sketch:e=>Nk({uid:e.uid,dom:{tag:"iframe",attributes:s},behaviours:ll([Tx.config({}),Hp.config({}),Dk(o,r.getValue,r.setValue)])})}});return Bx(a,i,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n)=>{const s=nn(t,{source:"dynamic"});return ZC(C_)(e,s,o,n)}),button:ZC(((e,t)=>((e,t)=>{const o=HC(e.name,"custom");return n=B.none(),s=Cx.parts.field({factory:kh,...RC(e,B.some(o),t,[Fk(""),Ok()])}),Bx(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:ZC(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),B.some(!0)),s=Cx.parts.field({factory:{sketch:x},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:ll([Ok(),vm.config({disabled:()=>!e.enabled||t.isDisabled()}),Tx.config({}),Hp.config({}),Ak(o,JC,YC),kp.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Fp("checkbox-events",[Er(Ns(),((t,o)=>{xr(t,Fx,{name:e.name})}))])])}),r=Cx.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[Na(t.translate(e.label))],behaviours:ll([Ew.config({})])}),a=e=>Ih("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=Ch({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return Cx.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:ll([vm.config({disabled:()=>!e.enabled||t.isDisabled(),disableClass:"tox-checkbox--disabled",onDisabled:e=>{Cx.getField(e).each(vm.disable)},onEnabled:e=>{Cx.getField(e).each(vm.enable)}}),Iv()])})})(e,t.shared.providers,o))),colorinput:ZC(((e,t,o)=>((e,t,o,n)=>{const s=Cx.parts.field({factory:$x,inputClasses:["tox-textfield"],data:n,onSetValue:e=>lw.run(e).get(b),inputBehaviours:ll([vm.config({disabled:t.providers.isDisabled}),Iv(),Tx.config({}),lw.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>Je(e.element),notify:{onValid:e=>{const t=Jd.getValue(e);xr(e,Bw,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=Jd.getValue(e);if(0===t.length)return Zx($o.value(!0));{const e=Ae("span");St(e,"background-color",t);const o=Tt(e,"background-color").fold((()=>$o.error("blah")),(e=>$o.value(t)));return Zx(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>Dx(e,t.providers))),a=(e,t)=>{xr(e,Mw,{value:t})},i=Ch(((e,t)=>Tw.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:ll([Vv(t.providers.isDisabled),Iv(),Ew.config({}),Tx.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>Jx((t=>e.fetch(t))).map((n=>B.from(lx(nn(Xy(Xr("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,Uf.CLOSE_ON_EXECUTE,_,t.providers),{movement:Yy(e.columns,e.presets)}))))),parts:{menu:ab(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[ji,Ui,Xi],onLtr:()=>[Ui,ji,Xi]},components:[],fetch:Uy(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:(e,t)=>{i.getOpt(e).each((e=>{"custom"===t?o.colorPicker((t=>{t.fold((()=>yr(e,Aw)),(t=>{a(e,t),Hy(t)}))}),"#ffffff"):a(e,"remove"===t?"":t)}))}},t));return Cx.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:ll([Fp("form-field-events",[Er(Bw,((t,o)=>{i.getOpt(t).each((e=>{St(e.element,"background-color",o.event.color)})),xr(t,Fx,{name:e.name})})),Er(Mw,((e,t)=>{Cx.getField(e).each((o=>{Jd.setValue(o,t.event.value),sm.getCurrent(e).each(Hp.focus)}))})),Er(Aw,((e,t)=>{Cx.getField(e).each((t=>{sm.getCurrent(e).each(Hp.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:ZC(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=Ck((e=>t=>e.translate(Ik[t]))(t),n),r=Ch(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{xr(e,Rx,{name:"hex-valid",value:!0})},onInvalidHex:e=>{xr(e,Rx,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:ll([Dk(o,(e=>{const t=r.get(e);return sm.getCurrent(t).bind((e=>Jd.getValue(e).hex)).map((e=>"#"+e)).getOr("")}),((e,t)=>{const o=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t),n=r.get(e);sm.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{Jd.setValue(e,{hex:B.from(o[1]).getOr("")}),vk.getField(e,"hex").each((e=>{yr(e,Ps())}))}))})),Ok()])}})(0,t.shared.providers,o))),dropzone:ZC(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{N(e,(e=>{e(t,o)}))},r=(e,t)=>{if(!vm.isDisabled(e)){const o=t.event.raw;i(e,o.dataTransfer.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{Jd.setValue(o,((e,t)=>{const o=Rk.explode(t.getOption("images_file_types"));return W(se(e),(e=>R(o,(t=>Oe(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),xr(o,Fx,{name:e.name})},l=Ch({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:ll([Fp("input-file-events",[Fr(Ls()),Fr(er())])])}),c=e.label.map((e=>Dx(e,t))),d=Cx.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:ll([Fk(o.getOr([])),Ok(),vm.config({}),Yp.config({toggleClass:"dragenter",toggleOnExecute:!1}),Fp("dropzone-events",[Er("dragenter",s([n,Yp.toggle])),Er("dragleave",s([n,Yp.toggle])),Er("dragover",n),Er("drop",s([n,r])),Er(Ns(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[Na(t.translate("Drop an image here"))]},kh.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[Na(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:ll([Tx.config({}),Vv(t.isDisabled),Iv()])})]}]})}});return Bx(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:ZC(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:P(e.items,t.interpreter)}))(e,t.shared))),listbox:ZC(((e,t,o)=>((e,t,o)=>{const n=t.shared.providers,s=o.bind((t=>aC(e.items,t))).orThunk((()=>oe(e.items).filter(nC))),r=e.label.map((e=>Dx(e,n))),a=Cx.parts.field({dom:{},factory:{sketch:o=>Zk({uid:o.uid,text:s.map((e=>e.text)),icon:B.none(),tooltip:e.label,role:B.none(),fetch:(o,n)=>{const s=rC(o,e.name,e.items,Jd.getValue(o));n(oC(s,Uf.CLOSE_ON_EXECUTE,t,!1))},onSetup:y(b),getApi:y({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[Tx.config({}),Dk(s.map((e=>e.value)),(e=>ft(e.element,sC)),((t,o)=>{aC(e.items,o).each((e=>{pt(t.element,sC,e.value),xr(t,Yk,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),i={dom:{tag:"div",classes:["tox-listboxfield"]},components:[a]};return Cx.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([r.toArray(),[i]]),fieldBehaviours:ll([vm.config({disabled:y(!e.enabled),onDisabled:e=>{Cx.getField(e).each(vm.disable)},onEnabled:e=>{Cx.getField(e).each(vm.enable)}})])})})(e,t,o))),selectbox:ZC(((e,t,o)=>((e,t,o)=>{const n=P(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>Dx(e,t))),r=Cx.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:iC,selectBehaviours:ll([vm.config({disabled:()=>!e.enabled||t.isDisabled()}),Tx.config({}),Fp("selectbox-change",[Er(Ns(),((t,o)=>{xr(t,Fx,{name:e.name})}))])])}),a=e.size>1?B.none():B.some(Ih("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:q([[r],a.toArray()])};return Cx.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([s.toArray(),[i]]),fieldBehaviours:ll([vm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{Cx.getField(e).each(vm.disable)},onEnabled:e=>{Cx.getField(e).each(vm.enable)}}),Iv()])})})(e,t.shared.providers,o))),sizeinput:ZC(((e,t)=>((e,t)=>{let o=pC;const n=Xr("ratio-event"),s=e=>Ih(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=uC.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(e.label.getOr("Constrain proportions"))}},components:[s("lock"),s("unlock")],buttonBehaviours:ll([vm.config({disabled:()=>!e.enabled||t.isDisabled()}),Iv(),Tx.config({})])}),a=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),i=o=>Cx.parts.field({factory:$x,inputClasses:["tox-textfield"],inputBehaviours:ll([vm.config({disabled:()=>!e.enabled||t.isDisabled()}),Iv(),Tx.config({}),Fp("size-input-events",[Er(Vs(),((e,t)=>{xr(e,n,{isField1:o})})),Er(Ns(),((t,o)=>{xr(t,Fx,{name:e.name})}))])]),selectOnFocus:!1}),l=e=>({dom:{tag:"label",classes:["tox-label"]},components:[Na(t.translate(e))]}),c=uC.parts.field1(a([Cx.parts.label(l("Width")),i(!0)])),d=uC.parts.field2(a([Cx.parts.label(l("Height")),i(!1)]));return uC.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,d,a([l("\xa0"),r])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{mC(Jd.getValue(e)).each((e=>{o(e).each((e=>{Jd.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:ll([vm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{uC.getField1(e).bind(Cx.getField).each(vm.disable),uC.getField2(e).bind(Cx.getField).each(vm.disable),uC.getLock(e).each(vm.disable)},onEnabled:e=>{uC.getField1(e).bind(Cx.getField).each(vm.enable),uC.getField2(e).bind(Cx.getField).each(vm.enable),uC.getLock(e).each(vm.enable)}}),Iv(),Fp("size-input-events2",[Er(n,((e,t)=>{const n=t.event.isField1,s=n?uC.getField1(e):uC.getField2(e),r=n?uC.getField2(e):uC.getField1(e),a=s.map(Jd.getValue).getOr(""),i=r.map(Jd.getValue).getOr("");o=((e,t)=>{const o=mC(e).toOptional(),n=mC(t).toOptional();return we(o,n,((e,t)=>gC(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>gC(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(pC))).getOr(pC)})(a,i)}))])])})})(e,t.shared.providers))),slider:ZC(((e,t,o)=>((e,t,o)=>{const n=dk.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[Na(t.translate(e.label))]}),s=dk.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=dk.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return dk.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:y(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:ll([Ok(),Hp.config({})]),onChoose:(t,o,n)=>{xr(t,Fx,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:ZC(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=Jd.getValue(t);o.addToHistory(n.value,e.filetype)},a=Cx.parts.field({factory:EC,...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":KC,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{const n=Jd.getValue(t),s=void 0!==n.meta.text?n.meta.text:n.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=XC(s,(e=>P(e,(e=>WC(e,e))))(o.getHistory(e)));return"file"===e?(r=[n,XC(s,jC(t)),XC(s,q([$C(t),GC(t),qC(t)]))],j(r,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(NC,t)),[])):n;var r}))})(e.filetype,n,o),r=oC(s,Uf.BUBBLE_TO_SANDBOX,t,!1);return Zx(r)},getHotspot:e=>m.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(lw)&&lw.run(e).get(b)},typeaheadBehaviours:ll(q([o.getValidationHandler().map((t=>lw.config({getRoot:e=>Je(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{l.getOpt(e).each((e=>{pt(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=Jd.getValue(o);return MC((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=$o.error(e.message);o(t)}else{const t=$o.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),[vm.config({disabled:()=>!e.enabled||s.isDisabled()}),Tx.config({}),Fp("urlinput-events",q(["file"===e.filetype?[Er(Ps(),(t=>{xr(t,Fx,{name:e.name})}))]:[],[Er(Ns(),(t=>{xr(t,Fx,{name:e.name}),r(t)})),Er(Ys(),(t=>{xr(t,Fx,{name:e.name}),r(t)}))]]))]])),eventOrder:{[Ps()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:ab(0,0,"normal")},onExecute:(e,t,o)=>{xr(t,zx,{})},onItemExecute:(t,o,n,s)=>{r(t),xr(t,Fx,{name:e.name})}}),i=e.label.map((e=>Dx(e,s))),l=Ch(((e,t,o=e,n=e)=>Ih(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",B.some(KC),"warning")),c=Ch({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[l.asSpec()]}),d=o.getUrlPicker(e.filetype),u=Xr("browser.url.event"),m=Ch({dom:{tag:"div",classes:["tox-control-wrap"]},components:[a,c.asSpec()],behaviours:ll([vm.config({disabled:()=>!e.enabled||s.isDisabled()})])}),g=Ch(zC({name:e.name,icon:B.some("browse"),text:e.label.getOr(""),enabled:e.enabled,primary:!1,buttonType:B.none(),borderless:!0},(e=>yr(e,u)),s,[],["tox-browse-url"]));return Cx.sketch({dom:Ax([]),components:i.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:q([[m.asSpec()],d.map((()=>g.asSpec())).toArray()])}]),fieldBehaviours:ll([vm.config({disabled:()=>!e.enabled||s.isDisabled(),onDisabled:e=>{Cx.getField(e).each(vm.disable),g.getOpt(e).each(vm.disable)},onEnabled:e=>{Cx.getField(e).each(vm.enable),g.getOpt(e).each(vm.enable)}}),Iv(),Fp("url-input-events",[Er(u,(t=>{sm.getCurrent(t).each((o=>{const n=Jd.getValue(o),s={fieldname:e.name,...n};d.each((n=>{n(s).get((n=>{Jd.setValue(o,n),xr(t,Fx,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:ZC((e=>{const t=zl(),o=Ch({dom:{tag:e.tag}}),n=zl();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:ll([Fp("custom-editor-events",[Rr((s=>{o.getOpt(s).each((o=>{((e=>be(e,"init"))(e)?e.init(o.element.dom):Vk.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),Dk(B.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),Ok()]),components:[o.asSpec()]}})),htmlpanel:ZC((e=>"presentation"===e.presets?xx.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html}}):xx.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:ll([Tx.config({}),Hp.config({})])}))),imagepreview:ZC(((e,t,o)=>((e,t)=>{const o=hs(t.getOr({url:""})),n=Ch({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=Ch({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:B.from(e.zoom),cachedWidth:B.from(e.cachedWidth),cachedHeight:B.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:ll([Ok(),Dk(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const t=r.cachedWidth,o=r.cachedHeight;if(u(r.zoom)){const n=((e,t,o)=>{const n=Wt(e),s=It(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const n=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Wt(e.element),It(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{kt(e.element,n)}))};n.getOpt(e).each((o=>{const n=o.element;var s;t.url!==ft(n,"src")&&(pt(n,"src",t.url),Ta(e.element,"tox-imagepreview__loaded")),u(r.cachedWidth)||u(r.cachedHeight)||a(),(s=n,new Promise(((e,t)=>{const o=()=>{r(),e(s)},n=[Pl(s,"load",o),Pl(s,"error",(()=>{r(),t("Unable to load data from image: "+s.dom.src)}))],r=()=>N(n,(e=>e.unbind()));s.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(_a(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:ZC(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:P(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:P(n,(e=>({dom:{tag:"tr"},components:P(e,o)})))})],behaviours:ll([Tx.config({}),Hp.config({})])};var n,s})(e,t.shared.providers))),panel:ZC(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:P(e.items,t.shared.interpreter)}))(e,t)))},eO={field:(e,t)=>t,record:y([])},tO=(e,t,o,n)=>{const s=nn(n,{shared:{interpreter:t=>oO(e,t,o,s)}});return oO(e,t,o,s)},oO=(e,t,o,n)=>fe(QC,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(s=>s(e,t,o,n))),nO="layout-inset",sO=e=>e.x,rO=(e,t)=>e.x+e.width/2-t.width/2,aO=(e,t)=>e.x+e.width-t.width,iO=e=>e.y,lO=(e,t)=>e.y+e.height-t.height,cO=(e,t)=>e.y+e.height/2-t.height/2,dO=(e,t,o)=>ki(aO(e,t),lO(e,t),o.insetSouthwest(),Ei(),"southwest",Vi(e,{right:0,bottom:3}),nO),uO=(e,t,o)=>ki(sO(e),lO(e,t),o.insetSoutheast(),Ti(),"southeast",Vi(e,{left:1,bottom:3}),nO),mO=(e,t,o)=>ki(aO(e,t),iO(e),o.insetNorthwest(),_i(),"northwest",Vi(e,{right:0,top:2}),nO),gO=(e,t,o)=>ki(sO(e),iO(e),o.insetNortheast(),Oi(),"northeast",Vi(e,{left:1,top:2}),nO),pO=(e,t,o)=>ki(rO(e,t),iO(e),o.insetNorth(),Bi(),"north",Vi(e,{top:2}),nO),hO=(e,t,o)=>ki(rO(e,t),lO(e,t),o.insetSouth(),Mi(),"south",Vi(e,{bottom:3}),nO),fO=(e,t,o)=>ki(aO(e,t),cO(e,t),o.insetEast(),Di(),"east",Vi(e,{right:0}),nO),bO=(e,t,o)=>ki(sO(e),cO(e,t),o.insetWest(),Ai(),"west",Vi(e,{left:1}),nO),vO=e=>{switch(e){case"north":return pO;case"northeast":return gO;case"northwest":return mO;case"south":return hO;case"southeast":return uO;case"southwest":return dO;case"east":return fO;case"west":return bO}},yO=(e,t,o,n,s)=>Dl(n).map(vO).getOr(pO)(e,t,o,n,s),xO=e=>{switch(e){case"north":return hO;case"northeast":return uO;case"northwest":return dO;case"south":return pO;case"southeast":return gO;case"southwest":return mO;case"east":return bO;case"west":return fO}},wO=(e,t,o,n,s)=>Dl(n).map(xO).getOr(pO)(e,t,o,n,s),SO={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},kO=(e,t,o)=>{const n={maxHeightFunction:ql()};return()=>o()?{type:"node",root:it(e()),node:B.from(e()),bubble:Jl(12,12,SO),layouts:{onRtl:()=>[gO],onLtr:()=>[mO]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:Jl(-12,12,SO),layouts:{onRtl:()=>[Ui],onLtr:()=>[ji]},overrides:n}},CO=(e,t,o)=>()=>o()?{type:"node",root:it(e()),node:B.from(e()),layouts:{onRtl:()=>[pO],onLtr:()=>[pO]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[Xi],onLtr:()=>[Xi]}},OO=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng();return B.some(Cc.range(Fe(t.startContainer),t.startOffset,Fe(t.endContainer),t.endOffset))}}),_O=e=>t=>({type:"node",root:e(),node:t}),TO=(e,t,o)=>{const n=If(e),s=()=>Fe(e.getBody()),r=()=>Fe(e.getContentAreaContainer()),a=()=>n||!o();return{inlineDialog:kO(r,t,a),banner:CO(r,t,a),cursor:OO(e,s),node:_O(s)}},EO=e=>(t,o)=>{qy(e)(t,o)},BO=e=>()=>Ry(e),MO=e=>()=>zy(e),AO=e=>()=>Vy(e),DO=e=>({colorPicker:EO(e),hasCustomColors:BO(e),getColors:MO(e),getColorCols:AO(e)}),FO=e=>()=>yf(e),IO=e=>({isDraggableModal:FO(e)}),VO=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],RO=e=>j(e,((e,t)=>{if(be(t,"items")){const o=RO(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(be(t,"inline")||(e=>be(e,"block"))(t)||(e=>be(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),zO=e=>Zh(e).map((t=>{const o=((e,t)=>{const o=RO(t),n=t=>{N(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return Qh(e)?VO.concat(o):o})).getOr(VO),HO=(e,t,o)=>{const n={type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)};return nn(e,n)},PO=(e,t,o,n)=>{const s=t=>P(t,(t=>{const a=ae(t);if(ve(t,"items")){const e=s(t.items);return nn((e=>nn(e,{type:"submenu"}))(t),{getStyleItems:y(e)})}return ve(t,"format")?(e=>HO(e,o,n))(t):1===a.length&&V(a,"title")?nn(t,{type:"separator"}):(t=>{const s=r(t.name)?t.name:Xr(t.title),a=`custom-${s}`,i={type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)},l=nn(t,i);return e.formatter.register(s,l),l})(t)}));return s(t)},NO=e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?B.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):B.none()},n=e=>{const t=e.items;return void 0!==t&&t.length>0?X(t,n):[e.format]},s=hs([]),r=hs([]),a=hs([]),i=hs([]),l=hs(!1);return e.on("PreInit",(a=>{const i=zO(e),l=PO(e,i,t,o);s.set(l),r.set(X(l,n))})),e.on("addStyleModifications",(s=>{const r=PO(e,s.items,t,o);a.set(r),l.set(s.replace),i.set(X(r,n))})),{getData:()=>{const e=l.get()?[]:s.get(),t=a.get();return e.concat(t)},getFlattenedKeys:()=>{const e=l.get()?[]:r.get(),t=i.get();return e.concat(t)}}},LO=Rk.trim,WO=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},UO=WO("true"),jO=WO("false"),GO=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),$O=e=>e.innerText||e.textContent,qO=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&KO(e),XO=e=>e&&/^(H[1-6])$/.test(e.nodeName),KO=e=>(e=>{for(;e=e.parentNode;){const t=e.contentEditable;if(t&&"inherit"!==t)return UO(e)}return!1})(e)&&!jO(e),YO=e=>XO(e)&&KO(e),JO=e=>{const t=(e=>e.id?e.id:Xr("h"))(e);return GO("header",$O(e),"#"+t,(e=>XO(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=t}))},ZO=e=>{const t=e.id||e.name,o=$O(e);return GO("anchor",o||"#"+t,"#"+t,0,b)},QO=e=>LO(e.title).length>0,e_=e=>{const t=(e=>{const t=P(Ac(Fe(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return W((e=>P(W(e,YO),JO))(t).concat((e=>P(W(e,qO),ZO))(t)),QO)},t_="tinymce-url-history",o_=e=>r(e)&&/^https?/.test(e),n_=e=>a(e)&&pe(e,(e=>{return!(l(t=e)&&t.length<=5&&K(t,o_));var t})).isNone(),s_=()=>{const e=My.getItem(t_);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+t_+" was not valid JSON",e),{};throw e}return n_(t)?t:(console.log("Local storage "+t_+" was not valid format",t),{})},r_=e=>{const t=s_();return fe(t,e).getOr([])},a_=(e,t)=>{if(!o_(e))return;const o=s_(),n=fe(o,t).getOr([]),s=W(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!n_(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));My.setItem(t_,JSON.stringify(e))})(o)},i_=e=>!!e,l_=e=>ce(Rk.makeMap(e,/[, ]/),i_),c_=e=>B.from(gf(e)),d_=e=>B.from(e).filter(r).getOrUndefined(),u_=e=>({getHistory:r_,addToHistory:a_,getLinkInformation:()=>(e=>ff(e)?B.some({targets:e_(e.getBody()),anchorTop:d_(bf(e)),anchorBottom:d_(vf(e))}):B.none())(e),getValidationHandler:()=>(e=>B.from(pf(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=B.from(hf(e)).filter(i_).map(l_);return c_(e).fold(_,(e=>t.fold(T,(e=>ae(e).length>0&&e))))})(e);return d(o)?o?c_(e):B.none():o[t]?c_(e):B.none()})(e,t).map((o=>n=>Jx((s=>{const i={filetype:t,fieldname:n.fieldname,...B.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),m_=qu,g_=Mu,p_=y([as("shell",!1),Un("makeItem"),as("setupItem",b),tu("listBehaviours",[Dp])]),h_=Tu({name:"items",overrides:()=>({behaviours:ll([Dp.config({})])})}),f_=y([h_]),b_=em({name:y("CustomList")(),configFields:p_(),partFields:f_(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Dp.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:eu(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?B.some(n):Pu(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=Dp.contents(n),r=o.length,a=r-s.length,i=a>0?z(a,(()=>e.makeItem())):[],l=s.slice(r);N(l,(e=>Dp.remove(n,e))),N(i,(e=>Dp.append(n,e)));const c=Dp.contents(n);N(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),v_=y([Un("dom"),as("shell",!0),Zd("toolbarBehaviours",[Dp])]),y_=y([Tu({name:"groups",overrides:()=>({behaviours:ll([Dp.config({})])})})]),x_=em({name:"Toolbar",configFields:v_(),partFields:y_(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Dp.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:eu(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?B.some(n):Pu(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{Dp.set(e,o)}))}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),w_=b,S_=_,k_=y([]);var C_,O_=Object.freeze({__proto__:null,setup:w_,isDocked:S_,getBehaviours:k_});const __=e=>(ye(Tt(e,"position"),"fixed")?B.none():Ze(e)).orThunk((()=>{const t=Ae("span");return Ye(e).bind((e=>{Eo(e,t);const o=Ze(t);return Ao(t),o}))})),T_=e=>__(e).map(Pt).getOrThunk((()=>zt(0,0))),E_=fs([{static:[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),B_=(e,t)=>{const o=e.element;_a(o,t.transitionClass),Ta(o,t.fadeOutClass),_a(o,t.fadeInClass),t.onShow(e)},M_=(e,t)=>{const o=e.element;_a(o,t.transitionClass),Ta(o,t.fadeInClass),_a(o,t.fadeOutClass),t.onHide(e)},A_=(e,t,o)=>K(e,(e=>{switch(e){case"bottom":return((e,t)=>e.bottom<=t.bottom)(t,o);case"top":return((e,t)=>e.y>=t.y)(t,o)}})),D_=(e,t)=>t.getInitialPos().map((t=>No(t.bounds.x,t.bounds.y,Wt(e),It(e)))),F_=(e,t,o)=>o.getInitialPos().bind((n=>{switch(o.clearInitialPos(),n.position){case"static":return B.some(E_.static());case"absolute":const o=__(e).map(Lo).getOrThunk((()=>Lo(ut())));return B.some(E_.absolute(wl("absolute",fe(n.style,"left").map((e=>t.x-o.x)),fe(n.style,"top").map((e=>t.y-o.y)),fe(n.style,"right").map((e=>o.right-t.right)),fe(n.style,"bottom").map((e=>o.bottom-t.bottom)))));default:return B.none()}})),I_=(e,t,o)=>{const n=e.element;return ye(Tt(n,"position"),"fixed")?((e,t,o)=>D_(e,o).filter((e=>A_(o.getModes(),e,t))).bind((t=>F_(e,t,o))))(n,t,o):((e,t,o)=>{const n=Lo(e);if(A_(o.getModes(),n,t))return B.none();{((e,t,o)=>{o.setInitialPos({style:Et(e),position:Ot(e,"position")||"static",bounds:t})})(e,n,o);const s=Uo(),r=n.x-s.x,a=t.y-s.y,i=s.bottom-t.bottom,l=n.y<=t.y;return B.some(E_.fixed(wl("fixed",B.some(r),l?B.some(a):B.none(),B.none(),l?B.none():B.some(i))))}})(n,t,o)},V_=(e,t,o)=>{o.setDocked(!1),N(["left","right","top","bottom","position"],(t=>Mt(e.element,t))),t.onUndocked(e)},R_=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),Sl(e.element,n),(s?t.onDocked:t.onUndocked)(e)},z_=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(Ba(e.element,[t.fadeOutClass]),t.onHide(e)):(a?B_:M_)(e,t))}))}))},H_=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);o.isDocked()&&z_(e,t,o,n),I_(e,n,o).each((s=>{s.fold((()=>V_(e,t,o)),(n=>R_(e,t,o,n)),(s=>{z_(e,t,o,n,!0),R_(e,t,o,s)}))}))})(e,t,o)},P_=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1),((e,t)=>{const o=e.element;return D_(o,t).bind((e=>F_(o,e,t)))})(e,o).each((n=>{n.fold((()=>V_(e,t,o)),(n=>R_(e,t,o,n)),b)})),o.setVisible(!0),t.contextual.each((t=>{Ma(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),H_(e,t,o)})(e,t,o)};var N_=Object.freeze({__proto__:null,refresh:H_,reset:P_,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n)}),L_=Object.freeze({__proto__:null,events:(e,t)=>Or([Vr(Us(),((o,n)=>{e.contextual.each((e=>{Ea(o.element,e.transitionClass)&&(Ma(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Er(ir(),((o,n)=>{H_(o,e,t)})),Er(lr(),((o,n)=>{P_(o,e,t)}))])}),W_=[rs("contextual",[$n("fadeInClass"),$n("fadeOutClass"),$n("transitionClass"),Xn("lazyContext"),fi("onShow"),fi("onShown"),fi("onHide"),fi("onHidden")]),ms("lazyViewport",Uo),gs("modes",["top","bottom"],Tn),fi("onDocked"),fi("onUndocked")];const U_=dl({fields:W_,name:"docking",active:L_,apis:N_,state:Object.freeze({__proto__:null,init:e=>{const t=hs(!1),o=hs(!0),n=zl(),s=hs(e.modes);return ma({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),j_=y(Xr("toolbar-height-change")),G_={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},$_="tox-tinymce--toolbar-sticky-on",q_="tox-tinymce--toolbar-sticky-off",X_=(e,t)=>V(U_.getModes(e),t),K_=e=>{const t=e.element;Ye(t).each((o=>{const n="padding-"+U_.getModes(e)[0];if(U_.isDocked(e)){const e=Wt(o);St(t,"width",e+"px"),St(o,n,(e=>Vt(e)+(parseInt(Ot(e,"margin-top"),10)||0)+(parseInt(Ot(e,"margin-bottom"),10)||0))(t)+"px")}else Mt(t,"width"),Mt(o,n)}))},Y_=(e,t)=>{t?(Ta(e,G_.fadeOutClass),Ba(e,[G_.transitionClass,G_.fadeInClass])):(Ta(e,G_.fadeInClass),Ba(e,[G_.fadeOutClass,G_.transitionClass]))},J_=(e,t)=>{const o=Fe(e.getContainer());t?(_a(o,$_),Ta(o,q_)):(_a(o,q_),Ta(o,$_))},Z_=(e,t)=>{const o=zl(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||K_(t),J_(e,U_.isDocked(t)),t.getSystem().broadcastOn([Fd()],{}),n().each((e=>e.getSystem().broadcastOn([Fd()],{})))},a=e.inline?[]:[pl.config({channels:{[j_()]:{onReceive:K_}}})];return[Hp.config({}),U_.config({contextual:{lazyContext:t=>{const o=Vt(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer(),s=Lo(Fe(n)),r=s.height-o,a=s.y+(X_(t,"top")?0:o);return B.some(No(s.x,a,s.width,r))},onShow:()=>{s((e=>Y_(e,!0)))},onShown:e=>{s((e=>Ma(e,[G_.transitionClass,G_.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=$e(t);vl(o).filter((e=>!je(t,e))).filter((t=>je(t,Fe(o.dom.body))||Ge(e,t))).each((()=>fl(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>yl(e).orThunk((()=>t().toOptional().bind((e=>yl(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>Y_(e,!1)))},onHidden:()=>{s((e=>Ma(e,[G_.transitionClass])))},...G_},lazyViewport:t=>{const o=Uo(),n=df(e),s=o.y+(X_(t,"top")?n:0),r=o.height-(X_(t,"bottom")?n:0);return No(o.x,s,o.width,r)},modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var Q_=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each(U_.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(K_)})),e.on("SkinLoaded",(()=>{o().each((e=>{U_.isDocked(e)?U_.reset(e):U_.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each(U_.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{U_.refresh(t);const o=t.element;bg(o)&&((e,t)=>{const o=$e(t),n=o.dom.defaultView.innerHeight,s=Do(o),r=Fe(e.elm),a=Wo(r),i=It(r),l=a.y,c=l+i,d=Pt(t),u=It(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Fo(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Fo(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{J_(e,!1)}))},isDocked:e=>e().map(U_.isDocked).getOr(!1),getBehaviours:Z_});const eT=yn([pb,jn("items",wn([kn([hb,Jn("items",Tn)]),Tn]))].concat(jb)),tT=[ts("text"),ts("tooltip"),ts("icon"),Xn("fetch"),ms("onSetup",(()=>b))],oT=yn([pb,...tT]),nT=e=>Vn("menubutton",oT,e),sT=yn([pb,Bb,Eb,Tb,Db,wb,Ob,ds("presets","normal",["normal","color","listpreview"]),zb(1),kb,Cb]);var rT=Qu({factory:(e,t)=>{const o={focus:kp.focusIn,setMenus:(e,o)=>{const n=P(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=nT(o).mapError((e=>Hn(e))).getOrDie();return DC(n,"tox-mbtn",t.backstage,B.some("menuitem"))}));Dp.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:ll([Dp.config({}),Fp("menubar-events",[Rr((t=>{e.onSetup(t)})),Er(Is(),((e,t)=>{Qa(e.element,".tox-mbtn--active").each((o=>{ei(t.event.target,".tox-mbtn").each((t=>{je(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{Tw.expand(e),Tw.close(o),Hp.focus(e)}))}))}))}))})),Er(gr(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{Tw.isOpen(o)&&(Tw.expand(e),Tw.close(o))}))}))}))]),kp.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),B.some(!0))}),Tx.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[Un("dom"),Un("uid"),Un("onEscape"),Un("backstage"),as("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const aT=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),iT=e=>e.dimension.property,lT=(e,t)=>e.dimension.getDimension(t),cT=(e,t)=>{const o=aT(e,t);Ma(o,[t.shrinkingClass,t.growingClass])},dT=(e,t)=>{Ta(e.element,t.openClass),_a(e.element,t.closedClass),St(e.element,iT(t),"0px"),At(e.element)},uT=(e,t)=>{Ta(e.element,t.closedClass),_a(e.element,t.openClass),Mt(e.element,iT(t))},mT=(e,t,o,n)=>{o.setCollapsed(),St(e.element,iT(t),lT(t,e.element)),At(e.element),cT(e,t),dT(e,t),t.onStartShrink(e),t.onShrunk(e)},gT=(e,t,o,n)=>{const s=n.getOrThunk((()=>lT(t,e.element)));o.setCollapsed(),St(e.element,iT(t),s),At(e.element);const r=aT(e,t);Ta(r,t.growingClass),_a(r,t.shrinkingClass),dT(e,t),t.onStartShrink(e)},pT=(e,t,o)=>{const n=lT(t,e.element);("0px"===n?mT:gT)(e,t,o,B.some(n))},hT=(e,t,o)=>{const n=aT(e,t),s=Ea(n,t.shrinkingClass),r=lT(t,e.element);uT(e,t);const a=lT(t,e.element);(s?()=>{St(e.element,iT(t),r),At(e.element)}:()=>{dT(e,t)})(),Ta(n,t.shrinkingClass),_a(n,t.growingClass),uT(e,t),St(e.element,iT(t),a),o.setExpanded(),t.onStartGrow(e)},fT=(e,t,o)=>{const n=aT(e,t);return!0===Ea(n,t.growingClass)},bT=(e,t,o)=>{const n=aT(e,t);return!0===Ea(n,t.shrinkingClass)};var vT=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){Mt(e.element,iT(t));const o=lT(t,e.element);St(e.element,iT(t),o)}},grow:(e,t,o)=>{o.isExpanded()||hT(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&pT(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&mT(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:fT,isShrinking:bT,isTransitioning:(e,t,o)=>fT(e,t)||bT(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?pT:hT)(e,t,o)},disableTransitions:cT}),yT=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return pa(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:bs(t.dimension.property,"0px")})},events:(e,t)=>Or([Vr(Us(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(cT(o,e),t.isExpanded()&&Mt(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),xT=[Un("closedClass"),Un("openClass"),Un("shrinkingClass"),Un("growingClass"),Zn("getAnimationRoot"),fi("onShrunk"),fi("onStartShrink"),fi("onGrown"),fi("onStartGrow"),as("expanded",!1),jn("dimension",Pn("property",{width:[xi("property","width"),xi("getDimension",(e=>Wt(e)+"px"))],height:[xi("property","height"),xi("getDimension",(e=>It(e)+"px"))]}))];const wT=dl({fields:xT,name:"sliding",active:yT,apis:vT,state:Object.freeze({__proto__:null,init:e=>{const t=hs(e.expanded);return ma({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:S(t.set,!1),setExpanded:S(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),ST="container",kT=[Zd("slotBehaviours",[])],CT=e=>"<alloy.field."+e+">",OT=(e,t)=>{const o=t=>Uu(e),n=(t,o)=>(n,s)=>Pu(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==ft(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;St(o,"display","none"),pt(o,"aria-hidden","true"),xr(e,pr(),{name:t,visible:!1})}})),i=(l=a,(e,t)=>{N(t,(t=>l(e,t)))});var l;const c=n(((e,t)=>{if(!s(e)){const o=e.element;Mt(o,"display"),yt(o,"aria-hidden"),xr(e,pr(),{name:t,visible:!0})}})),d={getSlotNames:o,getSlot:(t,o)=>Pu(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:c};return{uid:e.uid,dom:e.dom,components:t,behaviours:Qd(e.slotBehaviours),apis:d}},_T=ce({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>da(e))),TT={..._T,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),Iu(ST,CT(t),o)),record:y(e)}})(),o=e(t),n=t.record(),s=P(n,(e=>Ou({name:e,pname:CT(e)})));return Ku(ST,kT,s,OT,o)}},ET=yn([Eb,Bb,ms("onShow",b),ms("onHide",b),Ob]),BT=e=>({element:()=>e.element.dom}),MT=(e,t)=>{const o=P(ae(t),(e=>{const o=t[e],n=Rn((e=>Vn("sidebar",ET,e))(o));return{name:e,getApi:BT,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return P(o,(t=>{const n=hs(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:wv([Pv(t,n),Nv(t,n),Er(pr(),((e,t)=>{const n=t.event,s=G(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},AT=e=>TT.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:MT(t,e),slotBehaviours:wv([Rr((e=>TT.hideAllSlots(e)))])}))),DT=e=>sm.getCurrent(e).bind((e=>wT.isGrowing(e)||wT.hasGrown(e)?sm.getCurrent(e).bind((e=>G(TT.getSlotNames(e),(t=>TT.isShowing(e,t))))):B.none())),FT=Xr("FixSizeEvent"),IT=Xr("AutoSizeEvent");var VT=Object.freeze({__proto__:null,block:(e,t,o,n)=>{pt(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=ll([kp.config({mode:"special",onTab:()=>B.some(!0),onShiftTab:()=>B.some(!0)}),Hp.config({})]),a=n(s,r),i=s.getSystem().build(a);Dp.append(s,Ga(i)),i.hasConfigured(kp)&&t.focus&&kp.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>Dp.remove(s,i)))},unblock:(e,t,o)=>{yt(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()}}),RT=[ms("getRoot",B.none),us("focus",!0),fi("onBlock"),fi("onUnblock")];const zT=dl({fields:RT,name:"blocking",apis:VT,state:Object.freeze({__proto__:null,init:()=>{const e=Vl((e=>e.destroy()));return ma({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),HT=e=>{const t=Me(e),o=Qe(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return j(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:Nr(t)};return{tag:Ve(t),classes:s,attributes:n,...r}},PT=e=>sm.getCurrent(e).each((e=>fl(e.element))),NT=(e,t,o)=>{const n=hs(!1),s=zl(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?oe(s.composedPath()):B.from(s.target)).map(Fe).filter(ze).exists((e=>Ea(e,"mce-pastebin"))))&&(o.preventDefault(),PT(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n="data-mce-tabindex";B.from(e.iframeElement).map(Fe).each((e=>{t?(bt(e,o).each((t=>pt(e,n,t))),pt(e,o,-1)):(yt(e,o),bt(e,n).each((t=>{pt(e,o,t),yt(e,n)})))}))})(e,o),o)zT.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:HT('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),Mt(s,"display"),yt(s,"aria-hidden"),e.hasFocus()&&PT(t);else{const o=sm.getCurrent(t).exists((e=>bl(e.element)));zT.unblock(t),St(s,"display","none"),pt(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),e.dispatch("AfterProgressState",{state:s}))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=Sh.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},LT=(e,t,o)=>({within:e,extra:t,withinWidth:o}),WT=(e,t,o)=>{const n=j(e,((e,t)=>((e,t)=>{const n=o(e);return B.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(y(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=W(n,(e=>e.finish<=t)),r=U(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},UT=e=>P(e,(e=>e.element)),jT=(e,t)=>{const o=P(t,(e=>Ga(e)));x_.setGroups(e,o)},GT=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=Nu(e,t,"primary"),r=mw.getCoupled(e,"overflowGroup");St(s.element,"visibility","hidden");const a=n.concat([r]),i=re(a,(e=>yl(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),jT(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=WT(t,e,o);return 0===n.extra.length?B.some(n):B.none()})(e,t,o).getOrThunk((()=>WT(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=UT(e.concat(t));return LT(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=UT(e).concat([o]);return LT(s,UT(t),n)})(r,a,n,i):((e,t,o)=>LT(UT(e),[],o))(r,0,i)})(Wt(s.element),t.builtGroups.get(),(e=>Wt(e.element)),r);0===l.extra.length?(Dp.remove(s,r),o([])):(jT(s,l.within),o(l.extra)),Mt(s.element,"visibility"),At(s.element),i.each(Hp.focus)},$T=y([Zd("splitToolbarBehaviours",[mw]),Ln("builtGroups",(()=>hs([])))]),qT=y([pi(["overflowToggledClass"]),ns("getOverflowBounds"),Un("lazySink"),Ln("overflowGroups",(()=>hs([])))].concat($T())),XT=y([Ou({factory:x_,schema:v_(),name:"primary"}),_u({schema:v_(),name:"overflow"}),_u({name:"overflow-button"}),_u({name:"overflow-group"})]),KT=y(((e,t)=>{((e,t)=>{const o=Lt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);St(e,"max-width",o+"px")})(e,Math.floor(t))})),YT=y([pi(["toggledClass"]),Un("lazySink"),Xn("fetch"),ns("getBounds"),rs("fireDismissalEventInstead",[as("event",ur())]),rc()]),JT=y([_u({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:ll([Yp.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])})}),_u({factory:x_,schema:v_(),name:"toolbar",overrides:e=>({toolbarBehaviours:ll([kp.config({mode:"cyclic",onEscape:t=>(Pu(t,e,"button").each(Hp.focus),B.none())})])})})]),ZT=(e,t)=>{const o=mw.getCoupled(e,"toolbarSandbox");Ad.isOpen(o)?Ad.close(o):Ad.open(o,t.toolbar())},QT=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();ad.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:KT()}}},s)},eE=(e,t,o,n,s)=>{x_.setGroups(t,s),QT(e,t,o,n),Yp.on(e)},tE=em({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...kh.sketch({...n.button(),action:e=>{ZT(e,n)},buttonBehaviours:ou({dump:n.button().buttonBehaviours},[mw.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=oi();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:ll([kp.config({mode:"special",onEscape:e=>(Ad.close(e),B.some(!0))}),Ad.config({onOpen:(s,r)=>{o.fetch().get((s=>{eE(e,r,o,t.layouts,s),n.link(e.element),kp.focusIn(r)}))},onClose:()=>{Yp.off(e),Hp.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>ni(o,n)||ni(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),pl.config({channels:{...Rd({isExtraPart:_,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...Hd({doReposition:()=>{Ad.getState(mw.getCoupled(e,"toolbarSandbox")).each((n=>{QT(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Ad.getState(mw.getCoupled(t,"toolbarSandbox")).each((s=>{eE(t,s,e,o.layouts,n)}))},reposition:t=>{Ad.getState(mw.getCoupled(t,"toolbarSandbox")).each((n=>{QT(t,n,e,o.layouts)}))},toggle:e=>{ZT(e,n)},getToolbar:e=>Ad.getState(mw.getCoupled(e,"toolbarSandbox")),isOpen:e=>Ad.isOpen(mw.getCoupled(e,"toolbarSandbox"))}}),configFields:YT(),partFields:JT(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),oE=y([Un("items"),pi(["itemSelector"]),Zd("tgroupBehaviours",[kp])]),nE=y([Eu({name:"items",unit:"item"})]),sE=em({name:"ToolbarGroup",configFields:oE(),partFields:nE(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:eu(e.tgroupBehaviours,[kp.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),rE=e=>P(e,(e=>Ga(e))),aE=(e,t,o)=>{GT(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{tE.setGroups(e,rE(n))}))}))},iE=em({name:"SplitFloatingToolbar",configFields:qT(),partFields:XT(),factory:(e,t,o,n)=>{const s=Ch(tE.sketch({fetch:()=>Jx((t=>{t(rE(e.overflowGroups.get()))})),layouts:{onLtr:()=>[ji,Ui],onRtl:()=>[Ui,ji],onBottomLtr:()=>[$i,Gi],onBottomRtl:()=>[Gi,$i]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()}}));return{uid:e.uid,dom:e.dom,components:t,behaviours:eu(e.splitToolbarBehaviours,[mw.config({others:{overflowGroup:()=>sE.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(P(o,t.getSystem().build)),aE(t,s,e)},refresh:t=>aE(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{tE.toggle(e)}))},isOpen:e=>s.getOpt(e).map(tE.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{tE.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(tE.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),lE=y([pi(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),fi("onOpened"),fi("onClosed")].concat($T())),cE=y([Ou({factory:x_,schema:v_(),name:"primary"}),Ou({factory:x_,schema:v_(),name:"overflow",overrides:e=>({toolbarBehaviours:ll([wT.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{Pu(t,e,"overflow-button").each((e=>{Yp.off(e),Hp.focus(e)})),e.onClosed(t)},onGrown:t=>{kp.focusIn(t),e.onOpened(t)},onStartGrow:t=>{Pu(t,e,"overflow-button").each(Yp.on)}}),kp.config({mode:"acyclic",onEscape:t=>(Pu(t,e,"overflow-button").each(Hp.focus),B.some(!0))})])})}),_u({name:"overflow-button",overrides:e=>({buttonBehaviours:ll([Yp.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])})}),_u({name:"overflow-group"})]),dE=(e,t)=>{Pu(e,t,"overflow-button").bind((()=>Pu(e,t,"overflow"))).each((o=>{uE(e,t),wT.toggleGrow(o)}))},uE=(e,t)=>{Pu(e,t,"overflow").each((o=>{GT(e,t,(e=>{const t=P(e,(e=>Ga(e)));x_.setGroups(o,t)})),Pu(e,t,"overflow-button").each((e=>{wT.hasGrown(o)&&Yp.on(e)})),wT.refresh(o)}))},mE=em({name:"SplitSlidingToolbar",configFields:lE(),partFields:cE(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:eu(e.splitToolbarBehaviours,[mw.config({others:{overflowGroup:e=>sE.sketch({...n["overflow-group"](),items:[kh.sketch({...n["overflow-button"](),action:t=>{yr(e,s)}})]})}}),Fp("toolbar-toggle-events",[Er(s,(t=>{dE(t,e)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=P(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),uE(t,e)},refresh:t=>uE(t,e),toggle:t=>dE(t,e),isOpen:t=>((e,t)=>Pu(e,t,"overflow").map(wT.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),gE=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[sE.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:ll([Tx.config({}),Hp.config({})])}},pE=e=>sE.sketch(gE(e)),hE=(e,t)=>{const o=Rr((t=>{const o=P(e.initGroups,pE);x_.setGroups(t,o)}));return ll([zv(e.providers.isDisabled),Iv(),kp.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Fp("toolbar-events",[o])])},fE=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":gE({title:B.none(),items:[]}),"overflow-button":VC({name:"more",icon:B.some("more-drawer"),enabled:!0,tooltip:B.some("More..."),primary:!1,buttonType:B.none(),borderless:!1},B.none(),e.providers)},splitToolbarBehaviours:hE(e,t)}},bE=e=>{const t=fE(e),o=iE.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return iE.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=Wo(t),n=Xe(t),s=Wo(n),r=Math.max(n.dom.scrollHeight,s.height);return No(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"}})},vE=e=>{const t=mE.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=mE.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=fE(e);return mE.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:e=>{e.getSystem().broadcastOn([j_()],{type:"opened"})},onClosed:e=>{e.getSystem().broadcastOn([j_()],{type:"closed"})}})},yE=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return x_.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===zh.scrolling?["tox-toolbar--scrolling"]:[])},components:[x_.parts.groups({})],toolbarBehaviours:hE(e,t)})},xE=g_.optional({factory:rT,name:"menubar",schema:[Un("backstage")]}),wE=g_.optional({factory:{sketch:e=>b_.sketch({uid:e.uid,dom:e.dom,listBehaviours:ll([kp.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>yE({type:e.type,uid:Xr("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),B.some(!0))}),setupItem:(e,t,o,n)=>{x_.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[Un("dom"),Un("onEscape")]}),SE=g_.optional({factory:{sketch:e=>{const t=(e=>e.type===zh.sliding?vE:e.type===zh.floating?bE:yE)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),B.some(!0)),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[Un("dom"),Un("onEscape"),Un("getSink")]}),kE=g_.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?Z_:k_;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:ll(o(t,e.sharedBackstage))}}},name:"header",schema:[Un("dom")]}),CE=g_.optional({name:"socket",schema:[Un("dom")]}),OE=g_.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:ll([Tx.config({}),Hp.config({}),wT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{sm.getCurrent(e).each(TT.hideAllSlots),yr(e,IT)},onGrown:e=>{yr(e,IT)},onStartGrow:e=>{xr(e,FT,{width:Tt(e.element,"width").getOr("")})},onStartShrink:e=>{xr(e,FT,{width:Wt(e.element)+"px"})}}),Dp.config({}),sm.config({find:e=>{const t=Dp.contents(e);return oe(t)}})])}],behaviours:ll([_k(0),Fp("sidebar-sliding-events",[Er(FT,((e,t)=>{St(e.element,"width",t.event.width)})),Er(IT,((e,t)=>{Mt(e.element,"width")}))])])})},name:"sidebar",schema:[Un("dom")]}),_E=g_.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:ll([Dp.config({}),zT.config({focus:!1}),sm.config({find:e=>oe(e.components())})]),components:[]})},name:"throbber",schema:[Un("dom")]});var TE=em({name:"OuterContainer",factory:(e,t,o)=>{const n={getSocket:t=>m_.getPart(t,e,"socket"),setSidebar:(t,o)=>{m_.getPart(t,e,"sidebar").each((e=>((e,t)=>{sm.getCurrent(e).each((e=>Dp.set(e,[AT(t)])))})(e,o)))},toggleSidebar:(t,o)=>{m_.getPart(t,e,"sidebar").each((e=>((e,t)=>{sm.getCurrent(e).each((e=>{sm.getCurrent(e).each((o=>{wT.hasGrown(e)?TT.isShowing(o,t)?wT.shrink(e):(TT.hideAllSlots(o),TT.showSlot(o,t)):(TT.hideAllSlots(o),TT.showSlot(o,t),wT.grow(e))}))}))})(e,o)))},whichSidebar:t=>m_.getPart(t,e,"sidebar").bind(DT).getOrNull(),getHeader:t=>m_.getPart(t,e,"header"),getToolbar:t=>m_.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{m_.getPart(t,e,"toolbar").each((e=>{e.getApis().setGroups(e,o)}))},setToolbars:(t,o)=>{m_.getPart(t,e,"multiple-toolbar").each((e=>{b_.setItems(e,o)}))},refreshToolbar:t=>{m_.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{m_.getPart(t,e,"toolbar").each((e=>{var t,o;o=t=>t(e),null!=(t=e.getApis().toggle)?B.some(o(t)):B.none()}))},isToolbarDrawerToggled:t=>m_.getPart(t,e,"toolbar").bind((e=>B.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>m_.getPart(t,e,"throbber"),focusToolbar:t=>{m_.getPart(t,e,"toolbar").orThunk((()=>m_.getPart(t,e,"multiple-toolbar"))).each((e=>{kp.focusIn(e)}))},setMenubar:(t,o)=>{m_.getPart(t,e,"menubar").each((e=>{rT.setMenus(e,o)}))},focusMenubar:t=>{m_.getPart(t,e,"menubar").each((e=>{rT.focus(e)}))}};return{uid:e.uid,dom:e.dom,components:t,apis:n,behaviours:e.behaviours}},configFields:[Un("dom"),Un("behaviours")],partFields:[kE,xE,SE,wE,CE,OE,_E],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o)=>{e.setSidebar(t,o)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{const n=P(o,(e=>pE(e)));e.setToolbar(t,n)},setToolbars:(e,t,o)=>{const n=P(o,(e=>P(e,pE)));e.setToolbars(t,n)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)}}});const EE={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},BE=e=>"string"==typeof e?e.split(" "):e,ME=(e,t)=>{const o={...EE,...t.menus},n=ae(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?BE("file edit view insert format tools table help"):BE(!1===t.menubar?"":t.menubar),r=W(s,(e=>{const o=be(EE,e);return n?o||fe(t.menus,e).exists((e=>be(e,"items"))):o})),a=P(r,(n=>{const s=o[n];return((e,t,o)=>{const n=of(o).split(/[ ,]/);return{text:e.title,getItems:()=>X(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||R(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:BE(s.items)},t,e)}));return W(a,(e=>e.getItems().length>0&&R(e.getItems(),(e=>"separator"!==e.type))))},AE=e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}},DE=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),FE=(e,t)=>DE(e,t+"/skin.min.css",e.ui.styleSheetLoader),IE=(e,t)=>{var o;return o=Fe(e.getElement()),lt(o).isSome()?DE(e,t+"/skin.shadowdom.min.css",Ph.DOM.styleSheetLoader):Promise.resolve()},VE=(e,t)=>{const o=Tf(t);o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css"),!Of(t)&&r(o)?Promise.all([FE(t,o),IE(t,o)]).then(AE(t),((e,t)=>()=>((e,t)=>e.dispatch("SkinLoadError",t))(e,{message:"Skin could not be loaded"}))(t)):AE(t)()},RE=S(VE,!1),zE=S(VE,!0),HE=(e,t)=>o=>{const n=Rl(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}},PE=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},NE=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},LE=(e,t)=>()=>e.execCommand(t),WE=(e,t,o)=>{const n=(e,n,r,a)=>{const i=t.shared.providers.translate(e.title);if("separator"===e.type)return B.some({type:"separator",text:i});if("submenu"===e.type){const t=X(e.getStyleItems(),(e=>s(e,n,a)));return 0===n&&t.length<=0?B.none():B.some({type:"nestedmenuitem",text:i,enabled:t.length>0,getSubmenuItems:()=>X(e.getStyleItems(),(e=>s(e,n,a)))})}return B.some({type:"togglemenuitem",text:i,icon:e.icon,active:e.isSelected(a),enabled:!r,onAction:o.onAction(e),...e.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},s=(e,t,s)=>{const r="formatter"===e.type&&o.isInvalid(e);return 0===t?r?[]:n(e,t,!1,s).toArray():n(e,t,r,s).toArray()},r=e=>{const t=o.getCurrentValue(),n=o.shouldHide?0:1;return X(e,(e=>s(e,n,t)))};return{validateItems:r,getFetch:(e,t)=>(o,n)=>{const s=t(),a=r(s);n(oC(a,Uf.CLOSE_ON_EXECUTE,e,!1))}}},UE=(e,t,o)=>{const n=o.dataset,s="basic"===n.type?()=>P(n.data,(e=>HO(e,o.isSelectedFor,o.getPreviewFor))):n.getData;return{items:WE(0,t,o),getStyleItems:s}},jE=(e,t,o)=>{const{items:n,getStyleItems:s}=UE(0,t,o),r=PE(e,"NodeChange",(e=>{const t=e.getComponent();o.updateText(t)}));return Zk({text:o.icon.isSome()?B.none():o.text,icon:o.icon,tooltip:B.from(o.tooltip),role:B.none(),fetch:n.getFetch(t,s),onSetup:r,getApi:e=>({getComponent:y(e)}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};var GE;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(GE||(GE={}));const $E=(e,t,o)=>{const n=(s=((e,t)=>t===GE.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),P(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},qE=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],XE=e=>{const t={type:"basic",data:qE};return{tooltip:"Align",text:B.none(),icon:B.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:B.none,getPreviewFor:e=>B.none,onAction:t=>()=>G(qE,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=G(qE,(t=>e.formatter.match(t.format))).fold(y("left"),(e=>e.title.toLowerCase()));xr(t,Jk,{icon:`align-${o}`})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},KE=(e,t)=>{const o=t(),n=P(o,(e=>e.format));return B.from(e.formatter.closest(n)).bind((e=>G(o,(t=>t.format===e)))).orThunk((()=>Se(e.formatter.match("p"),{title:"Paragraph",format:"p"})))},YE=e=>{const t="Paragraph",o=$E(e,"block_formats",GE.SemiColon);return{tooltip:"Blocks",text:B.some(t),icon:B.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:B.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return B.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))})},onAction:NE(e),updateText:n=>{const s=KE(e,(()=>o.data)).fold(y(t),(e=>e.title));xr(n,Yk,{text:s})},dataset:o,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},JE=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],ZE=e=>{const t=e.split(/\s*,\s*/);return P(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},QE=e=>{const t="System Font",o=()=>{const o=e=>e?ZE(e)[0]:"",s=e.queryCommandValue("FontName"),r=n.data,a=s?s.toLowerCase():"",i=G(r,(e=>{const t=e.format;return t.toLowerCase()===a||o(t).toLowerCase()===o(a).toLowerCase()})).orThunk((()=>Se((e=>0===e.indexOf("-apple-system")&&(()=>{const t=ZE(e.toLowerCase());return K(JE,(e=>t.indexOf(e.toLowerCase())>-1))})())(a),{title:t,format:a})));return{matchOpt:i,font:s}},n=$E(e,"font_family_formats",GE.SemiColon);return{tooltip:"Fonts",text:B.some(t),icon:B.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=o();return e},getPreviewFor:e=>()=>B.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:e=>{const{matchOpt:t,font:n}=o(),s=t.fold(y(n),(e=>e.title));xr(e,Yk,{text:s})},dataset:n,shouldHide:!1,isInvalid:_}},eB={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},tB={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},oB=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":fe(tB,e).getOr(e),nB=e=>fe(eB,e).getOr(""),sB=e=>{const t=()=>{let t=B.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=oB(s,e),r=nB(n);t=G(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=y(B.none),n=$E(e,"font_size_formats",GE.Space);return{tooltip:"Font sizes",text:B.some("12pt"),icon:B.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:e=>{const{matchOpt:o,size:n}=t(),s=o.fold(y(n),(e=>e.title));xr(e,Yk,{text:s})},dataset:n,shouldHide:!1,isInvalid:_}},rB=(e,t)=>{const o="Paragraph";return{tooltip:"Formats",text:B.some(o),icon:B.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:B.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?B.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):B.none()},onAction:NE(e),updateText:t=>{const n=e=>{const t=e.items;return void 0!==t&&t.length>0?X(t,n):[{title:e.title,format:e.format}]},s=X(zO(e),n),r=KE(e,y(s)).fold(y(o),(e=>e.title));xr(t,Yk,{text:r})},shouldHide:ef(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}};var aB=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?Op:Cp)(o,r)}))};return Or([Er(Js(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;V(s.channels,n)&&o(t,s.data)}})),Rr(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),iB=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),lB=[Un("channel"),Zn("renderComponents"),Zn("updateState"),Zn("initialData"),us("reuseDom",!0)];const cB=dl({fields:lB,name:"reflecting",active:aB,apis:iB,state:Object.freeze({__proto__:null,init:()=>{const e=hs(B.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(B.none())}}})}),dB=y([Un("toggleClass"),Un("fetch"),vi("onExecute"),as("getHotspot",B.some),as("getAnchorOverrides",y({})),rc(),vi("onItemExecute"),Zn("lazySink"),Un("dom"),fi("onOpen"),Zd("splitDropdownBehaviours",[mw,kp,Hp]),as("matchWidth",!1),as("useMinWidth",!1),as("eventOrder",{}),Zn("role")].concat(Cw())),uB=Ou({factory:kh,schema:[Un("dom")],name:"arrow",defaults:()=>({buttonBehaviours:ll([Hp.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(wr)},buttonBehaviours:ll([Yp.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),mB=Ou({factory:kh,schema:[Un("dom")],name:"button",defaults:()=>({buttonBehaviours:ll([Hp.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),gB=y([uB,mB,Tu({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[Un("text")],name:"aria-descriptor"}),_u({schema:[gi()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),pw()]),pB=em({name:"SplitDropdown",configFields:dB(),partFields:gB(),factory:(e,t,o,n)=>{const s=e=>{sm.getCurrent(e).each((e=>{Em.highlightFirst(e),kp.focusIn(e)}))},r=t=>{yw(e,x,t,n,s,hw.HighlightFirst).get(b)},a=t=>{const o=Nu(t,e,"button");return wr(o),B.some(!0)},i={...Or([Rr(((t,o)=>{Pu(t,e,"aria-descriptor").each((e=>{const o=Xr("aria");pt(e.element,"id",o),pt(t.element,"aria-describedby",o)}))}))]),...Zp(B.some(r))},l={repositionMenus:e=>{Yp.isOn(e)&&kw(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[Zs()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:eu(e.splitDropdownBehaviours,[mw.config({others:{sandbox:t=>{const o=Nu(t,e,"arrow");return Sw(e,t,{onOpen:()=>{Yp.on(o),Yp.on(t)},onClose:()=>{Yp.off(o),Yp.off(t)}})}}}),kp.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),B.some(!0))}),Hp.config({}),Yp.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),hB=e=>({isEnabled:()=>!vm.isDisabled(e),setEnabled:t=>vm.set(e,!t)}),fB=e=>({setActive:t=>{Yp.set(e,t)},isActive:()=>Yp.isOn(e),isEnabled:()=>!vm.isDisabled(e),setEnabled:t=>vm.set(e,!t)}),bB=(e,t)=>e.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),vB=Xr("focus-button"),yB=(e,t,o,n,s,r)=>({dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:bB(o,r)},components:Uv([e.map((e=>qk(e,r.icons))),t.map((e=>Kk(e,"tox-tbtn",r)))]),eventOrder:{[Ms()]:["focusing","alloy.base.behaviour","common-button-display-events"]},buttonBehaviours:ll([zv(r.isDisabled),Iv(),Fp("common-button-display-events",[Er(Ms(),((e,t)=>{t.event.prevent(),yr(e,vB)}))])].concat(n.map((o=>cB.config({channel:o,initialData:{icon:e,text:t},renderComponents:(e,t)=>Uv([e.icon.map((e=>qk(e,r.icons))),e.text.map((e=>Kk(e,"tox-tbtn",r)))])}))).toArray()).concat(s.getOr([])))}),xB=(e,t,o)=>{const n=hs(b),s=yB(e.icon,e.text,e.tooltip,B.none(),B.none(),o);return kh.sketch({dom:s.dom,components:s.components,eventOrder:Gk,buttonBehaviours:ll([Fp("toolbar-button-events",[(r={onAction:e.onAction,getApi:t.getApi},Pr(((e,t)=>{Hv(r,e)((t=>{xr(e,jk,{buttonApi:t}),r.onAction(t)}))}))),Pv(t,n),Nv(t,n)]),zv((()=>!e.enabled||o.isDisabled())),Iv()].concat(t.toolbarButtonBehaviours))});var r},wB=(e,t,o)=>xB(e,{toolbarButtonBehaviours:[].concat(o.length>0?[Fp("toolbarButtonWith",o)]:[]),getApi:hB,onSetup:e.onSetup},t),SB=(e,t,o)=>nn(xB(e,{toolbarButtonBehaviours:[Dp.config({}),Yp.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[Fp("toolbarToggleButtonWith",o)]:[]),getApi:fB,onSetup:e.onSetup},t)),kB=(e,t,o)=>n=>Jx((e=>t.fetch(e))).map((s=>B.from(lx(nn(Xy(Xr("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,Uf.CLOSE_ON_EXECUTE,t.select.getOr(_),o),{movement:Yy(t.columns,t.presets),menuBehaviours:wv("auto"!==t.columns?[]:[Rr(((e,o)=>{xv(e,4,eb(t.presets)).each((({numRows:t,numColumns:o})=>{kp.setGridSize(e,t,o)}))}))])}))))),CB=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],OB=(e,t)=>(o,n,s)=>{const r=e(o).mapError((e=>Hn(e))).getOrDie();return t(r,n,s)},_B={button:OB($b,((e,t)=>{return o=e,n=t.backstage.shared.providers,wB(o,n,[]);var o,n})),togglebutton:OB(Kb,((e,t)=>{return o=e,n=t.backstage.shared.providers,SB(o,n,[]);var o,n})),menubutton:OB(nT,((e,t)=>DC(e,"tox-tbtn",t.backstage,B.none()))),splitbutton:OB((e=>Vn("SplitButton",sT,e)),((e,t)=>((e,t)=>{const o=Xr("channel-update-split-dropdown-display"),n=e=>({isEnabled:()=>!vm.isDisabled(e),setEnabled:t=>vm.set(e,!t),setIconFill:(t,o)=>{Qa(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each((e=>{pt(e,"fill",o)}))},setActive:t=>{pt(e.element,"aria-pressed",t),Qa(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>Yp.set(e,t)))}))},isActive:()=>Qa(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(Yp.isOn)))}),s=hs(b),r={getApi:n,onSetup:e.onSetup};return pB.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...bB(e.tooltip,t.providers)}},onExecute:t=>{e.onAction(n(t))},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:ll([Rv(t.providers.isDisabled),Iv(),Fp("split-dropdown-events",[Er(vB,Hp.focus),Pv(r,s),Nv(r,s)]),Ew.config({})]),eventOrder:{[cr()]:["alloy.base.behaviour","split-dropdown-events"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:kB(n,e,t.providers),parts:{menu:ab(0,e.columns,e.presets)},components:[pB.parts.button(yB(e.icon,e.text,B.none(),B.some(o),B.some([Yp.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),pB.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:Ah("chevron-down",t.providers.icons)},buttonBehaviours:ll([Rv(t.providers.isDisabled),Iv(),Dh()])}),pB.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.backstage.shared))),grouptoolbarbutton:OB((e=>Vn("GroupToolbarButton",eT,e)),((e,t,o)=>{const n=o.ui.registry.getAll().buttons,s={[nc]:t.backstage.shared.header.isPositionedAtTop()?oc.TopToBottom:oc.BottomToTop};if(nf(o)===zh.floating)return((e,t,o,n)=>{const s=t.shared;return tE.sketch({lazySink:s.getSink,fetch:()=>Jx((t=>{t(P(o(e.items),pE))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:yB(e.icon,e.text,e.tooltip,B.none(),B.none(),s.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t.backstage,(e=>EB(o,{buttons:n,toolbar:e,allowToolbarGroups:!1},t,B.none())),s);throw new Error("Toolbar groups are only supported when using floating toolbar mode")})),styleSelectButton:(e,t)=>((e,t)=>{const o={type:"advanced",...t.styles};return jE(e,t,rB(e,o))})(e,t.backstage),fontsizeSelectButton:(e,t)=>((e,t)=>jE(e,t,sB(e)))(e,t.backstage),fontSelectButton:(e,t)=>((e,t)=>jE(e,t,QE(e)))(e,t.backstage),formatButton:(e,t)=>((e,t)=>jE(e,t,YE(e)))(e,t.backstage),alignMenuButton:(e,t)=>((e,t)=>jE(e,t,XE(e)))(e,t.backstage)},TB={styles:_B.styleSelectButton,fontsize:_B.fontsizeSelectButton,fontfamily:_B.fontSelectButton,blocks:_B.formatButton,align:_B.alignMenuButton},EB=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=P(CB,(t=>{const o=W(t.items,(t=>be(e,t)||be(TB,t)));return{name:t.name,items:o}}));return W(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return P(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>be(e,"name")&&be(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=P(s,(s=>{const r=X(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>fe(t,o.toLowerCase()).orThunk((()=>r.bind((e=>re(e,(e=>fe(t,e+o.toLowerCase()))))))).fold((()=>fe(TB,o.toLowerCase()).map((t=>t(e,s))).orThunk((()=>B.none()))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o)=>fe(_B,e.type).fold((()=>(console.error("skipping button defined by",e),B.none())),(n=>B.some(n(e,t,o)))))(t,s,e):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),B.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:B.from(e.translate(s.name)),items:r}}));return W(a,(e=>e.items.length>0))},BB=(e,t,o,n)=>{const s=t.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return EB(e,s,{backstage:n},B.none())}));TE.setToolbars(s,t)}else TE.setToolbar(s,EB(e,o,{backstage:n},B.none()))},MB=wo(),AB=MB.os.isiOS()&&MB.os.version.major<=12;var DB=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const r=hs(0),a=t.outerContainer;RE(e);const i=Fe(s.targetNode),l=it(at(i));((e,t)=>{yd(e,t,_o)})(i,t.mothership),vd(l,t.uiMothership),e.on("PostRender",(()=>{BB(e,t,o,n),r.set(e.getWin().innerWidth),TE.setMenubar(a,ME(e,o)),TE.setSidebar(a,o.sidebar),((e,t)=>{const o=e.dom;let n=e.getWin();const s=e.getDoc().documentElement,r=hs(zt(n.innerWidth,n.innerHeight)),a=hs(zt(s.offsetWidth,s.offsetHeight)),i=()=>{const t=r.get();t.left===n.innerWidth&&t.top===n.innerHeight||(r.set(zt(n.innerWidth,n.innerHeight)),Oy(e))},l=()=>{const t=e.getDoc().documentElement,o=a.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(a.set(zt(t.offsetWidth,t.offsetHeight)),Oy(e))},c=t=>((e,t)=>e.dispatch("ScrollContent",t))(e,t);o.bind(n,"resize",i),o.bind(n,"scroll",c);const d=Nl(Fe(e.getBody()),"load",l),u=t.uiMothership.element;e.on("hide",(()=>{St(u,"display","none")})),e.on("show",(()=>{Mt(u,"display")})),e.on("NodeChange",l),e.on("remove",(()=>{d.unbind(),o.unbind(n,"resize",i),o.unbind(n,"scroll",c),n=null}))})(e,t)}));const d=TE.getSocket(a).getOrDie("Could not find expected socket element");if(AB){kt(d.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=Pl(d.element,"scroll",t.throttle);e.on("remove",o.unbind)}Fv(e,t),e.addCommand("ToggleSidebar",((t,o)=>{TE.toggleSidebar(a,o),e.dispatch("ToggleSidebar")})),e.addQueryValueHandler("ToggleSidebar",(()=>TE.whichSidebar(a)));const u=nf(e);u!==zh.sliding&&u!==zh.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==r.get()&&(TE.refreshToolbar(t.outerContainer),r.set(o))}));const m={setEnabled:e=>{Dv(t,!e)},isEnabled:()=>!vm.isDisabled(a)};return{iframeContainer:d.element.dom,editorContainer:a.element.dom,api:m}}});const FB=e=>/^[0-9\.]+(|px)$/i.test(""+e)?B.some(parseInt(""+e,10)):B.none(),IB=e=>h(e)?e+"px":e,VB=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},RB=e=>{const t=qh(e),o=Xh(e),n=Yh(e);return FB(t).map((e=>VB(e,o,n)))},{ToolbarLocation:zB,ToolbarMode:HB}=Pf,PB=(e,t)=>{const o=Lo(e);return{pos:t?o.y:o.bottom,bounds:o}};var NB=Object.freeze({__proto__:null,render:(e,t,o,n,s)=>{const{mothership:r,uiMothership:a,outerContainer:i}=t,l=hs(null),c=Fe(s.targetNode),d=((e,t,o,n,s)=>{const{uiMothership:r,outerContainer:a}=o,i=Ph.DOM,l=If(e),c=zf(e),d=Yh(e).or(RB(e)),u=n.shared.header,m=u.isPositionedAtTop,g=nf(e),p=g===HB.sliding||g===HB.floating,h=hs(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(y(0),(e=>e.components().length>1?It(e.components()[1].element):0)):0,v=()=>{r.broadcastOn([Fd()],{})},x=(e=!1)=>{if(f()){if(l||(()=>{const e=d.getOrThunk((()=>{const e=FB(Ot(ut(),"margin-left")).getOr(0);return Wt(ut())-Pt(t).left+e}));St(s.get().element,"max-width",e+"px")})(),p&&TE.refreshToolbar(a),l||(()=>{const e=TE.getToolbar(a),o=b(e),n=Lo(t),r=m()?Math.max(n.y-It(s.get().element)+o,0):n.bottom;kt(a.element,{position:"absolute",top:Math.round(r)+"px",left:Math.round(n.x)+"px"})})(),c){const t=s.get();e?U_.reset(t):U_.refresh(t)}v()}},w=(o=!0)=>{if(l||!c||!f())return;const n=u.getDockingMode(),r=(o=>{switch(rf(e)){case zB.auto:const e=TE.getToolbar(a),n=b(e),s=It(o.element)-n,r=Lo(t);if(r.y>s)return"top";{const e=Xe(t),o=Math.max(e.dom.scrollHeight,It(e));return r.bottom<o-s||Uo().bottom<r.bottom-s?"bottom":"top"}case zB.bottom:return"bottom";case zB.top:default:return"top"}})(s.get());r!==n&&((e=>{const t=s.get();U_.setModes(t,[e]),u.setDockingMode(e);const o=m()?oc.TopToBottom:oc.BottomToTop;pt(t.element,nc,o)})(r),o&&x(!0))};return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),St(a.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),Mt(r.element,"display"),w(!1),x()},hide:()=>{h.set(!1),o.outerContainer&&(St(a.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus")),St(r.element,"display","none")},update:x,updateMode:w,repositionPopups:v}})(e,c,t,n,l),u=cf(e);zE(e);const m=()=>{if(l.get())return void d.show();l.set(TE.getHeader(i).getOrDie());const s=Vf(e);vd(s,r),vd(s,a),BB(e,t,o,n),TE.setMenubar(i,ME(e,o)),d.show(),((e,t,o,n)=>{const s=hs(PB(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=PB(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&Oy(e,n),o.isVisible()&&(i!==r?o.update(!0):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update(!0))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))})),e.on("ScrollWindow",(()=>o.updateMode()));const a=Rl();a.set(Nl(Fe(e.getBody()),"load",r)),e.on("remove",(()=>{a.clear()}))})(e,c,d,u),e.nodeChanged()};e.on("show",m),e.on("hide",d.hide),u||(e.on("focus",m),e.on("blur",d.hide)),e.on("init",(()=>{(e.hasFocus()||u)&&m()})),Fv(e,t);const g={show:()=>{m()},hide:()=>{d.hide()},setEnabled:e=>{Dv(t,!e)},isEnabled:()=>!vm.isDisabled(i)};return{editorContainer:i.element.dom,api:g}}});const LB="contexttoolbar-hide",WB=(e,t)=>Er(jk,((o,n)=>{const s=(e=>({hide:()=>yr(e,or()),getValue:()=>Jd.getValue(e)}))(e.get(o));t.onAction(s,n.event.buttonApi)})),UB=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=Ch($x.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:ll([kp.config({mode:"special",onEnter:e=>s.findPrimary(e).map((e=>(wr(e),!0))),onLeft:(e,t)=>(t.cut(),B.none()),onRight:(e,t)=>(t.cut(),B.none())})])})),s=((e,t,o)=>{const n=P(t,(t=>Ch(((e,t,o)=>{const n={backstage:{shared:{providers:o}}};return"contextformtogglebutton"===t.type?((e,t,o)=>{const{primary:n,...s}=t.original,r=Rn(Kb({...s,type:"togglebutton",onAction:b}));return SB(r,o.backstage.shared.providers,[WB(e,t)])})(e,t,n):((e,t,o)=>{const{primary:n,...s}=t.original,r=Rn($b({...s,type:"button",onAction:b}));return wB(r,o.backstage.shared.providers,[WB(e,t)])})(e,t,n)})(e,t,o))));return{asSpecs:()=>P(n,(e=>e.asSpec())),findPrimary:e=>re(t,((t,o)=>t.primary?B.from(n[o]).bind((t=>t.getOpt(e))).filter(k(vm.isDisabled)):B.none()))}})(n,e.commands,t);return[{title:B.none(),items:[n.asSpec()]},{title:B.none(),items:s.asSpecs()}]},jB=(e,t,o=.01)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,GB=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=ot(Fe(e.startContainer),e.startOffset).element;return(He(o)?Ye(o):B.some(o)).filter(ze).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Do();return No(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=Wo(Fe(e.getBody()));return No(o.x+t.left,o.y+t.top,t.width,t.height)}},$B=(e,t,o,n=0)=>{const s=Vo(window),r=Lo(Fe(e.getContentAreaContainer())),a=_f(e)||Bf(e)||Af(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return No(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=Fe(e.getContainer()),i=Qa(a,".tox-editor-header").getOr(a),l=Lo(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Lo(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return No(i,c,l,d-c)}},qB={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},XB={maxHeightFunction:ql(),maxWidthFunction:KT()},KB=e=>"node"===e,YB=(e,t,o,n,s)=>{const r=GB(e),a=n.lastElement().exists((e=>je(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=ot(Fe(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&je(n.element,t)})(e,o)?a?yO:pO:a?((e,o,s)=>{const a=Tt(e,"position");St(e,"position",o);const i=jB(r,Lo(t))&&!n.isReposition()?wO:yO;return a.each((t=>St(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?s.y+Do().top:s.y)+(It(t)+12)<=r.y?pO:hO},JB=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...YB(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>KB(n)?[s(e)]:[];return t?{onLtr:e=>[Xi,Ui,ji,Gi,$i,qi].concat(r(e)),onRtl:e=>[Xi,ji,Ui,$i,Gi,qi].concat(r(e))}:{onLtr:e=>[qi,Xi,Gi,Ui,$i,ji].concat(r(e)),onRtl:e=>[qi,Xi,$i,ji,Gi,Ui].concat(r(e))}},ZB=(e,t)=>{const o=W(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=L(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},QB=(e,t)=>{const o={},n=[],s=[],r={},a={},i=ae(e);return N(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=Rn(Vn("ContextForm",ov,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,Vn("ContextToolbar",nv,o)).each((o=>{"editor"===t.scope?s.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},eM=Xr("forward-slide"),tM=Xr("backward-slide"),oM=Xr("change-slide-event"),nM="tox-pop--resizing",sM="tox-pop--transition",rM=(e,t,o,n)=>{const s=n.backstage,r=s.shared,a=wo().deviceType.isTouch,i=zl(),l=zl(),c=zl(),d=ja((e=>{const t=hs([]);return wh.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),wh.getContent(e).each((e=>{Mt(e.element,"visibility")})),Ta(e.element,nM),Mt(e.element,"width")},inlineBehaviours:ll([Fp("context-toolbar-events",[Vr(Us(),((e,t)=>{"width"===t.event.raw.propertyName&&(Ta(e.element,nM),Mt(e.element,"width"))})),Er(oM,((e,t)=>{const o=e.element;Mt(o,"width");const n=Wt(o);wh.setContent(e,t.event.contents),_a(o,nM);const s=Wt(o);St(o,"width",n+"px"),wh.getContent(e).each((e=>{t.event.focus.bind((e=>(fl(e),yl(o)))).orThunk((()=>(kp.focusIn(e),vl(at(o)))))})),setTimeout((()=>{St(e.element,"width",s+"px")}),0)})),Er(eM,((e,o)=>{wh.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:vl(at(e.element))}]))})),xr(e,oM,{contents:o.event.forwardContents,focus:B.none()})})),Er(tM,((e,o)=>{ne(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),xr(e,oM,{contents:Ga(o.bar),focus:o.focus})}))}))]),kp.config({mode:"special",onEscape:o=>ne(t.get()).fold((()=>e.onEscape()),(e=>(yr(o,tM),B.some(!0))))})]),lazySink:()=>$o.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),B.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=KB(t)?1:0;return $B(e,r,t,o)},m=()=>!(e.removed||a()&&s.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=ye(c.get(),"node")?((e,t)=>t.filter(dt).map(Wo).getOrThunk((()=>GB(e))))(e,i.get()):GB(e);return t.height<=0||!jB(o,t)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),wh.hide(d)},h=()=>{if(wh.isOpen(d)){const e=d.element;Mt(e,"display"),g()?St(e,"display","none"):(l.set(0),wh.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:ll([kp.config({mode:"acyclic"}),Fp("pop-dialog-wrap-events",[Rr((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>kp.focusIn(t)))})),zr((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=jt((()=>QB(t,(e=>{const t=y([e]);xr(d,eM,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...v().formNavigators},a=nf(e)===zh.scrolling?zh.scrolling:zh.default,i=q(P(t,(t=>"contexttoolbar"===t.type?((t,o)=>EB(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n,B.some(["form:"])))(s,t):((e,t)=>UB(e,t))(t,r.providers))));return yE({type:a,uid:Xr("context-toolbar"),initGroups:i,onEscape:B.none,cyclicKeying:!0,providers:r.providers})},x=(t,n)=>{if(w.cancel(),!m())return;const s=y(t),p=t[0].position,h=((t,n)=>{const s="node"===t?r.anchors.node(n):r.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:Jl(12,0,qB),layouts:{onLtr:()=>[Ki],onRtl:()=>[Yi]},overrides:XB}:{bubble:Jl(0,12,qB,1/12),layouts:JB(e,o,n,t),overrides:XB})(e,t,a(),{lastElement:i.get,isReposition:()=>ye(l.get(),0),getMode:()=>ad.getMode(o)});return nn(s,c)})(p,n);c.set(p),l.set(1);const b=d.element;Mt(b,"display"),(e=>ye(we(e,i.get(),je),!0))(n)||(Ta(b,sM),ad.reset(o,d)),wh.showWithinBounds(d,f(s),{anchor:h,transition:{classes:[sM],mode:"placement"}},(()=>B.some(u()))),n.fold(i.clear,i.set),g()&&St(b,"display","none")},w=bC((()=>{e.hasFocus()&&!e.removed&&(Ea(d.element,sM)?w.throttle():((e,t)=>{const o=Fe(t.getBody()),n=e=>je(e,o),s=Fe(t.selection.getNode());return(e=>!n(e)&&!Ge(o,e))(s)?B.none():((e,t,o)=>{const n=ZB(e,t);if(n.contextForms.length>0)return B.some({elem:e,toolbars:[n.contextForms[0]]});{const t=ZB(e,o);if(t.contextForms.length>0)return B.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>R(e,(e=>e.position===t)),o=t=>W(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=P(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return B.some({elem:e,toolbars:o})}return B.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?B.none():xs(t,(e=>{if(ze(e)){const{contextToolbars:t,contextForms:n}=ZB(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>G(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>W(e,(e=>e.position===t))))}})(t);return s.length>0?B.some({elem:e,toolbars:s}):B.none()}return B.none()}),e))(n,s,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,B.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",w.throttle),e.on(LB,p),e.on("contexttoolbar-show",(t=>{const o=v();fe(o.lookupTable,t.toolbarKey).each((o=>{x([o],Se(t.target!==e,t.target)),wh.getContent(d).each(kp.focusIn)}))})),e.on("focusout",(t=>{Sh.setEditorTimeout(e,(()=>{yl(o.element).isNone()&&yl(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&w.throttle()})),e.on("NodeChange",(e=>{yl(d.element).fold(w.throttle,b)}))}))},aM={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},iM=(()=>{const e="[0-9]+",t="[eE][+-]?[0-9]+",o=e=>`(?:${e})?`,n=["Infinity","[0-9]+\\."+o(e)+o(t),"\\.[0-9]+"+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),lM=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=zl();return P(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(ye(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},cM={name:"lineheight",text:"Line height",icon:"line-height",getOptions:Ef,hash:e=>((e,t)=>((e,t)=>B.from(iM.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>R(t,(t=>R(aM[t],(t=>e===t)))))(n,t)?B.some({value:o,unit:n}):B.none()})))(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:x,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>B.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t)},dM=e=>PE(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent"))})),uM=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),()=>e.off("PastePlainTextToggle",n)},mM=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},gM=e=>{(e=>{(e=>{Rk.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:HE(e,t.name),onAction:mM(e,t.name)})}));for(let t=1;t<=6;t++){const o="h"+t;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:HE(e,o),onAction:mM(e,o)})}})(e),(e=>{Rk.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"print",text:"Print",action:"mcePrint",icon:"print"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:LE(e,t.action)})}))})(e),(e=>{Rk.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:LE(e,t.action),onSetup:HE(e,t.name)})}))})(e)})(e),(e=>{Rk.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:LE(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:mM(e,"code")})})(e)},pM=(e,t)=>PE(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),hM=e=>PE(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),fM=(e,t)=>{(e=>{N([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:LE(e,t.cmd),onSetup:HE(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onAction:LE(e,"JustifyNone")})})(e),gM(e),((e,t)=>{((e,t)=>{const o=UE(0,t,XE(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=UE(0,t,QE(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=UE(0,t,rB(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=UE(0,t,YE(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=UE(0,t,sB(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:pM(e,"hasUndo"),onAction:LE(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:pM(e,"hasRedo"),onAction:LE(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:pM(e,"hasUndo"),onAction:LE(e,"undo")}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:pM(e,"hasRedo"),onAction:LE(e,"redo")})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},null,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=hs(Py),o=hs(Py);Gy(e,"forecolor","forecolor","Text color",t),Gy(e,"backcolor","hilitecolor","Background color",o),$y(e,"forecolor","forecolor","Text color"),$y(e,"backcolor","hilitecolor","Background color")})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:LE(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:hM(e),onAction:LE(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:dM(e),onAction:LE(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:LE(e,"indent")})})(e)})(e),(e=>{lM(e,cM),(e=>B.from(tf(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:y(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:t.customCode}).unbind,getCurrent:e=>{const t=Fe(e.selection.getNode());return ws(t,(e=>B.some(e).filter(ze).bind((e=>bt(e,"lang").map((t=>({code:t,customCode:bt(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=Rl();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),o.clear}}))))(e).each((t=>lM(e,t)))})(e),(e=>{const t=hs(Cf(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:uM(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:uM(e,t)})})(e)},bM=e=>r(e)?e.split(/[ ,]/):e,vM=e=>t=>t.options.get(e),yM=vM("contextmenu_never_use_native"),xM=vM("contextmenu_avoid_overlap"),wM=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:W(o,(e=>be(t,e)))},SM=(e,t)=>({type:"makeshift",x:e,y:t}),kM=e=>"longpress"===e.type||0===e.type.indexOf("touch"),CM=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(kM(e)){const t=e.touches[0];return SM(t.pageX,t.pageY)}return SM(e.pageX,e.pageY)})(t):((e,t)=>{const o=Ph.DOM.getPos(e);return((e,t,o)=>SM(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(kM(e)){const t=e.touches[0];return SM(t.clientX,t.clientY)}return SM(e.clientX,e.clientY)})(t)):OM(e),OM=e=>({type:"selection",root:Fe(e.selection.getNode())}),_M=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:B.some(Fe(e.selection.getNode())),root:Fe(e.getBody())}))(e);case"point":return CM(e,t);case"selection":return OM(e)}},TM=(e,t,o,n,s,r)=>{const a=o(),i=_M(e,t,r);oC(a,Uf.CLOSE_ON_EXECUTE,n,!1).map((e=>{t.preventDefault(),wh.showMenuAt(s,{anchor:i},{menu:{markers:nb("normal")},data:e})}))},EM={onLtr:()=>[Xi,Ui,ji,Gi,$i,qi,pO,hO,gO,uO,mO,dO],onRtl:()=>[Xi,ji,Ui,$i,Gi,qi,pO,hO,mO,dO,gO,uO]},BM={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},MM=(e,t,o,n,s,r)=>{const a=wo(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=_M(e,t,o);return{bubble:Jl(0,"point"===o?12:0,BM),layouts:EM,overrides:{maxWidthFunction:KT(),maxHeightFunction:ql()},...n}})(e,t,r);oC(o,Uf.CLOSE_ON_EXECUTE,n,!0).map((o=>{t.preventDefault(),wh.showMenuWithinBounds(s,{anchor:i},{menu:{markers:nb("normal"),highlightImmediately:a},data:o,type:"horizontal"},(()=>B.some($B(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(LB)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{Sh.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return Ic(e.getWin(),Cc.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},AM=e=>r(e)?"|"===e:"separator"===e.type,DM={type:"separator"},FM=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return DM;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:P(t,FM)}};default:return{type:"menuitem",...t(e),onAction:(o=e.onAction,()=>o())}}var o},IM=(e,t)=>{if(0===t.length)return e;const o=ne(e).filter((e=>!AM(e))).fold((()=>[]),(e=>[DM]));return e.concat(o).concat(t).concat([DM])},VM=(e,t)=>"longpress"!==t.type&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),RM=(e,t)=>VM(e,t)?e.selection.getStart(!0):t.target,zM=(e,t,o)=>{const n=wo().deviceType.isTouch,s=ja(wh.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:ll([Fp("dismissContextMenu",[Er(ur(),((t,o)=>{Ad.close(t),e.focus()}))])])})),a=e=>wh.hide(s),i=t=>{if(yM(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!yM(e))(e,t)||(e=>0===wM(e).length)(e))return;const a=((e,t)=>{const o=xM(e),n=VM(e,t)?"selection":"point";if(Te(o)){const s=RM(e,t);return dx(Fe(s),o)?"node":n}return n})(e,t);(n()?MM:TM)(e,t,(()=>{const o=RM(e,t),n=e.ui.registry.getAll(),s=wM(e);return((e,t,o)=>{const n=j(t,((t,n)=>fe(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n))return IM(t,n.split(" "));if(n.length>0){const e=P(n,FM);return IM(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&AM(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},HM=fs([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),PM=e=>t=>t.translate(-e.left,-e.top),NM=e=>t=>t.translate(e.left,e.top),LM=e=>(t,o)=>j(e,((e,t)=>t(e)),zt(t,o)),WM=(e,t,o)=>e.fold(LM([NM(o),PM(t)]),LM([PM(t)]),LM([])),UM=(e,t,o)=>e.fold(LM([NM(o)]),LM([]),LM([NM(t)])),jM=(e,t,o)=>e.fold(LM([]),LM([PM(o)]),LM([NM(t),PM(o)])),GM=(e,t,o)=>{const n=e.fold(((e,t)=>({position:B.some("absolute"),left:B.some(e+"px"),top:B.some(t+"px")})),((e,t)=>({position:B.some("absolute"),left:B.some(e-o.left+"px"),top:B.some(t-o.top+"px")})),((e,t)=>({position:B.some("fixed"),left:B.some(e+"px"),top:B.some(t+"px")})));return{right:B.none(),bottom:B.none(),...n}},$M=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s(jM,qM),s(UM,XM),s(WM,KM))},qM=HM.offset,XM=HM.absolute,KM=HM.fixed,YM=(e,t)=>{const o=ft(e,t);return u(o)?NaN:parseInt(o,10)},JM=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=YM(o,t.leftAttr),s=YM(o,t.topAttr);return isNaN(n)||isNaN(s)?B.none():B.some(zt(n,s))})(e,t).fold((()=>o),(e=>KM(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?QM(e,t,a,s,r):eA(e,t,a,s,r),l=WM(a,s,r);return((e,t,o)=>{const n=e.element;pt(n,t.leftAttr,o.left+"px"),pt(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:KM(l.left,l.top),extra:B.none()})),(e=>({coord:e.output,extra:e.extra})))},ZM=(e,t,o,n)=>re(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=UM(e,s,r),i=UM(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?B.some({output:$M(e.output,t,o,n),extra:e.extra}):B.none()})),QM=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return ZM(r,o,n,s).orThunk((()=>{const e=j(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=UM(e,s,r),i=UM(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return zt(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:B.some(a),snap:B.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:B.some(a),snap:B.some(t)}:e))}),{deltas:B.none(),snap:B.none()});return e.snap.map((e=>({output:$M(e.output,o,n,s),extra:e.extra})))}))},eA=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return ZM(r,o,n,s)};var tA=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=$e(e.element),o=Do(t),r=T_(s),a=((e,t,o)=>({coord:$M(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=GM(a.coord,0,r);Ct(s,i)}}});const oA="data-initial-z-index",nA=(e,t)=>{e.getSystem().addToGui(t),(e=>{Ye(e.element).filter(ze).each((t=>{Tt(t,"z-index").each((e=>{pt(t,oA,e)})),St(t,"z-index",Ot(e.element,"z-index"))}))})(t)},sA=e=>{(e=>{Ye(e.element).filter(ze).each((e=>{bt(e,oA).fold((()=>Mt(e,"z-index")),(t=>St(e,"z-index",t))),yt(e,oA)}))})(e),e.getSystem().removeFromGui(e)},rA=(e,t,o)=>e.getSystem().build(xx.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var aA=rs("snaps",[Un("getSnapPoints"),fi("onSensor"),Un("leftAttr"),Un("topAttr"),as("lazyViewport",Uo),as("mustSnap",!1)]);const iA=[as("useFixed",_),Un("blockerClass"),as("getTarget",x),as("onDrag",b),as("repositionTarget",!0),as("onDrop",b),ms("getBounds",Uo),aA],lA=(e,t)=>({bounds:e.getBounds(),height:Vt(t.element),width:Ut(t.element)}),cA=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>lA(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=$e(e.element),a=Do(r),i=T_(s),l=(e=>{return(t=Tt(e,"left"),o=Tt(e,"top"),n=Tt(e,"position"),s=(e,t,o)=>("fixed"===o?KM:qM)(parseInt(e,10),parseInt(t,10)),t.isSome()&&o.isSome()&&n.isSome()?B.some(s(t.getOrDie(),o.getOrDie(),n.getOrDie())):B.none()).getOrThunk((()=>{const t=Pt(e);return XM(t.left,t.top)}));var t,o,n,s})(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=UM(t,o,n),i=Ii(a.left,r.x,r.x+r.width-s.width),l=Ii(a.top,r.y,r.y+r.height-s.height),c=XM(i,l);return t.fold((()=>{const e=jM(c,o,n);return qM(e.left,e.top)}),y(c),(()=>{const e=WM(c,o,n);return KM(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>qM(e+a,t+i)),((e,t)=>XM(e+a,t+i)),((e,t)=>KM(e+a,t+i))));var t,a,i;const l=WM(e,n,s);return KM(l.left,l.top)}),(t=>{const a=JM(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=GM(c,0,i);Ct(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},dA=(e,t,o,n)=>{t.each(sA),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;yt(o,t.leftAttr),yt(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},uA=e=>(t,o)=>{const n=e=>{o.setStartData(lA(t,e))};return Or([Er(ir(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var mA=Object.freeze({__proto__:null,getData:e=>B.from(zt(e.x,e.y)),getDelta:(e,t)=>zt(t.left-e.left,t.top-e.top)});const gA=(e,t,o)=>[Er(Ms(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>dA(n,B.some(l),e,t),a=ux(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),cA(n,e,t,mA,o)}},l=rA(n,e.blockerClass,(e=>Or([Er(Ms(),e.forceDrop),Er(Fs(),e.drop),Er(As(),((t,o)=>{e.move(o.event)})),Er(Ds(),e.delayDrop)]))(i));o(n),nA(n,l)}))],pA=[...iA,xi("dragger",{handlers:uA(gA)})];var hA=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return B.some(zt(t.clientX,t.clientY))})(t):B.none()},getDelta:(e,t)=>zt(t.left-e.left,t.top-e.top)});const fA=(e,t,o)=>{const n=zl(),s=o=>{dA(o,n.get(),e,t),n.clear()};return[Er(_s(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{cA(r,e,t,hA,o)}},c=rA(r,e.blockerClass,(e=>Or([Er(_s(),e.forceDrop),Er(Es(),e.drop),Er(Bs(),e.drop),Er(Ts(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),nA(r,c)})),Er(Ts(),((o,n)=>{n.stop(),cA(o,e,t,hA,n.event)})),Er(Es(),((e,t)=>{t.stop(),s(e)})),Er(Bs(),s)]},bA=pA,vA=[...iA,xi("dragger",{handlers:uA(fA)})],yA=[...iA,xi("dragger",{handlers:uA(((e,t,o)=>[...gA(e,t,o),...fA(e,t,o)]))})];var xA=Object.freeze({__proto__:null,mouse:bA,touch:vA,mouseOrTouch:yA}),wA=Object.freeze({__proto__:null,init:()=>{let e=B.none(),t=B.none();const o=y({});return ma({readState:o,reset:()=>{e=B.none(),t=B.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=B.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=B.some(e)}})}});const SA=ml({branchKey:"mode",branches:xA,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:B.from(e.extra)})},state:wA,apis:tA}),kA=(e,t,o,n,s,r)=>e.fold((()=>SA.snap({sensor:XM(o-20,n-20),range:zt(s,r),output:XM(B.some(o),B.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return SA.snap({sensor:XM(s,r),range:zt(40,40),output:XM(B.some(o-a.width/2),B.some(n-a.height/2)),extra:{td:t}})})),CA=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>je(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),OA=e=>Ch(kh.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:ll([SA.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),Ew.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),_A=(e,t)=>{const o=hs([]),n=hs([]),s=hs(!1),r=zl(),a=zl(),i=e=>{const o=Wo(e);return kA(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=Wo(e);return kA(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=CA((()=>P(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=CA((()=>P(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=OA(c),m=OA(d),g=ja(u.asSpec()),p=ja(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);SA.snapTo(t,r),((t,o,n,r)=>{const a=o.dom.getBoundingClientRect();Mt(t.element,"display");const i=Ke(Fe(e.getBody())).dom.innerHeight,l=a[s]<0,c=((e,t)=>e[s]>t)(a,i);(l||c)&&St(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");wo().deviceType.isTouch()&&(e.on("TableSelectionChange",(e=>{s.get()||(gd(t,g),gd(t,p),s.set(!0)),r.set(e.start),a.set(e.finish),e.otherCells.each((t=>{o.set(t.upOrLeftCells),n.set(t.downOrRightCells),f(e.start),b(e.finish)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(fd(g),fd(p),s.set(!1)),r.clear(),a.clear()})))},TA=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:ll([kp.config({mode:"flow",selector:"div[role=button]"}),vm.config({disabled:o.isDisabled}),Iv(),Tx.config({}),Dp.config({}),Fp("elementPathEvents",[Rr(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>kp.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=e.dispatch("ResolveName",{name:r.nodeName.toLowerCase(),target:r});if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?j(r,((t,n,r)=>{const a=((t,n,s)=>kh.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s,"aria-level":s+1}},components:[Na(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:ll([Vv(o.isDisabled),Iv()])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[Na(` ${s} `)]},a])}),[]):[];Dp.set(t,a)}))}))])]),components:[]}};var EA;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(EA||(EA={}));const BA=(e,t,o)=>{const n=Fe(e.getContainer()),s=((e,t,o,n,s)=>{const r={};return r.height=VB(n+t.top,Kh(e),Jh(e)),o===EA.Both&&(r.width=VB(s+t.left,Xh(e),Yh(e))),r})(e,t,o,It(n),Wt(n));le(s,((e,t)=>St(n,t,IB(e)))),(e=>{e.dispatch("ResizeEditor")})(e)},MA=(e,t,o,n)=>{const s=zt(20*o,20*n);return BA(e,s,t),B.some(!0)},AA=(e,t)=>({dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const o=(()=>{const o=[];return wf(e)&&o.push(TA(e,{},t)),e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>Dp.set(e,[Na(t.translate(["{0} "+n,o[n]]))]);return kh.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:ll([Vv(t.isDisabled),Iv(),Tx.config({}),Dp.config({}),Jd.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Fp("wordcount-events",[Pr((e=>{const t=Jd.getValue(e),n="words"===t.mode?"characters":"words";Jd.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Rr((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=Jd.getValue(t);Jd.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[Zs()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),Sf(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=editor_referral&utm_medium=poweredby&utm_source=tinymce&utm_content=v6",rel:"noopener",target:"_blank","aria-label":Oh.translate(["Powered by {0}","Tiny"])},innerHtml:'<svg width="50px" height="16px" viewBox="0 0 50 16" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.143 0c2.608.015 5.186 2.178 5.186 5.331 0 0 .077 3.812-.084 4.87-.361 2.41-2.164 4.074-4.65 4.496-1.453.284-2.523.49-3.212.623-.373.071-.634.122-.785.152-.184.038-.997.145-1.35.145-2.732 0-5.21-2.04-5.248-5.33 0 0 0-3.514.03-4.442.093-2.4 1.758-4.342 4.926-4.963 0 0 3.875-.752 4.036-.782.368-.07.775-.1 1.15-.1Zm1.826 2.8L5.83 3.989v2.393l-2.455.475v5.968l6.137-1.189V9.243l2.456-.476V2.8ZM5.83 6.382l3.682-.713v3.574l-3.682.713V6.382Zm27.173-1.64-.084-1.066h-2.226v9.132h2.456V7.743c-.008-1.151.998-2.064 2.149-2.072 1.15-.008 1.987.92 1.995 2.072v5.065h2.455V7.359c-.015-2.18-1.657-3.929-3.837-3.913a3.993 3.993 0 0 0-2.908 1.296Zm-6.3-4.266L29.16 0v2.387l-2.456.475V.476Zm0 3.2v9.132h2.456V3.676h-2.456Zm18.179 11.787L49.11 3.676H46.58l-1.612 4.527-.46 1.382-.384-1.382-1.611-4.527H39.98l3.3 9.132L42.15 16l2.732-.537ZM22.867 9.738c0 .752.568 1.075.921 1.075.353 0 .668-.047.998-.154l.537 1.765c-.23.154-.92.537-2.225.537-1.305 0-2.655-.997-2.686-2.686a136.877 136.877 0 0 1 0-4.374H18.8V3.676h1.612v-1.98l2.455-.476v2.456h2.302V5.9h-2.302v3.837Z"/>\n</svg>\n'.trim()},behaviours:ll([Hp.config({})])}]}),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:o}]:[]})(),n=((e,t)=>{const o=(e=>{const t=kf(e);return!1===t?EA.None:"both"===t?EA.Both:EA.Vertical})(e);return o===EA.None?B.none():B.some(Ih("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize")},behaviours:[SA.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>BA(e,s,o),blockerClass:"tox-blocker"}),kp.config({mode:"special",onLeft:()=>MA(e,o,-1,0),onRight:()=>MA(e,o,1,0),onUp:()=>MA(e,o,0,-1),onDown:()=>MA(e,o,0,1)}),Tx.config({}),Hp.config({})]},t.icons))})(e,t);return o.concat(n.toArray())})()}),DA=e=>e.get().getOrDie("UI has not been rendered"),FA=e=>{const t=e.inline,o=t?NB:DB,n=zf(e)?Q_:O_,s=zl(),r=zl(),a=zl(),i=zl(),l=wo().deviceType.isTouch()?["tox-platform-touch"]:[],c=Df(e),d=nf(e),u=Ch({dom:{tag:"div",classes:["tox-anchorbar"]}}),m=()=>r.get().bind(TE.getHeader),g=()=>$o.fromOption(s.get(),"UI has not been rendered"),p=()=>r.get().bind((e=>TE.getToolbar(e))).getOrDie("Could not find more toolbar element"),h=()=>r.get().bind((e=>TE.getThrobber(e))).getOrDie("Could not find throbber element"),f=((e,t,o)=>{const n=hs(!1),s=(e=>{const t=hs(Df(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),r={shared:{providers:{icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:Oh.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get},interpreter:e=>((e,t,o)=>oO(eO,e,{},o))(e,0,r),anchors:TO(t,o,s.isPositionedAtTop),header:s,getSink:e},urlinput:u_(t),styles:NO(t),colorinput:DO(t),dialog:IO(t),isContextMenuOpen:()=>n.get(),setContextMenuState:e=>n.set(e)};return r})(g,e,(()=>r.get().bind((e=>u.getOpt(e))).getOrDie("Could not find a anchor bar element"))),b=t=>{const o=IB((e=>{const t=(e=>{const t=$h(e),o=Kh(e),n=Jh(e);return FB(t).map((e=>VB(e,o,n)))})(e);return t.getOr($h(e))})(e)),n=IB((e=>RB(e).getOr(qh(e)))(e));return e.inline||(Bt("div","width",n)&&St(t.element,"width",n),Bt("div","height",o)?St(t.element,"height",o):St(t.element,"height","400px")),o};return{getMothership:()=>DA(a),getUiMothership:()=>DA(i),backstage:f,renderUI:()=>{const{mothership:v,outerContainer:y}=(()=>{const o=(()=>{const t={attributes:{[nc]:c?oc.BottomToTop:oc.TopToBottom}},o=TE.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:f,onEscape:()=>{e.focus()}}),n=TE.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:g,providers:f.shared.providers,onEscape:()=>{e.focus()},type:d,lazyToolbar:p,lazyHeader:()=>m().getOrDie("Could not find header element"),...t}),s=TE.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:f.shared.providers,onEscape:()=>{e.focus()},type:d}),r=Af(e),a=Bf(e),i=_f(e);return TE.parts.header({dom:{tag:"div",classes:["tox-editor-header"],...t},components:q([i?[o]:[],r?[s]:a?[n]:[],If(e)?[]:[u.asSpec()]]),sticky:zf(e),editor:e,sharedBackstage:f.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[TE.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),TE.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=TE.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:f}),i=xf(e)&&!t?B.some(AA(e,f.shared.providers)):B.none(),h=q([c?[]:[o],t?[]:[n],c?[o]:[]]),b=q([[{dom:{tag:"div",classes:["tox-editor-container"]},components:h}],t?[]:i.toArray(),[s]]),v=Rf(e),y={role:"application",...Oh.isRtl()?{dir:"rtl"}:{},...v?{"aria-hidden":"true"}:{}},x=ja(TE.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(t?["tox-tinymce-inline"]:[]).concat(c?["tox-tinymce--toolbar-bottom"]:[]).concat(l),styles:{visibility:"hidden",...v?{opacity:"0",border:"0"}:{}},attributes:y},components:b,behaviours:ll([Iv(),vm.config({disableClass:"tox-tinymce--disabled"}),kp.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=wx(x);return r.set(x),a.set(w),{mothership:w,outerContainer:x}})(),{uiMothership:x,sink:w}=(()=>{const t=Vf(e),o=je(ut(),t)&&"grid"===Ot(t,"display"),r={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(l),attributes:{...Oh.isRtl()?{dir:"rtl"}:{}}},behaviours:ll([ad.config({useFixed:()=>n.isDocked(m)})])},a={dom:{styles:{width:document.body.clientWidth+"px"}},events:Or([Er(lr(),(e=>{St(e.element,"width",document.body.clientWidth+"px")}))])},c=ja(nn(r,o?a:{})),d=wx(c);return s.set(c),i.set(d),{sink:c,uiMothership:d}})();ce(sf(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:S,menuItems:k,contextToolbars:C,sidebars:O}=e.ui.registry.getAll(),_=Mf(e),T={menuItems:k,menus:Hf(e),menubar:uf(e),toolbar:_.getOrThunk((()=>mf(e))),allowToolbarGroups:d===zh.floating,buttons:S,sidebar:O};(t=>{e.addShortcut("alt+F9","focus menubar",(()=>{TE.focusMenubar(t)})),e.addShortcut("alt+F10","focus toolbar",(()=>{TE.focusToolbar(t)})),e.addCommand("ToggleToolbarDrawer",(()=>{TE.toggleToolbarDrawer(t)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>TE.isToolbarDrawerToggled(t)))})(y),((e,t,o)=>{const n=(e,n)=>{N([t,o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{N([t,o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(Dd(),{target:e.target}),a=Ro(),i=Pl(a,"touchstart",r),l=Pl(a,"touchmove",(e=>n(rr(),e))),c=Pl(a,"touchend",(e=>n(ar(),e))),d=Pl(a,"mousedown",r),u=Pl(a,"mouseup",(e=>{0===e.raw.button&&s(Id(),{target:e.target})})),m=e=>s(Dd(),{target:Fe(e.target)}),g=e=>{0===e.button&&s(Id(),{target:Fe(e.target)})},p=()=>{N(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(ir(),Ll(e)),f=e=>{s(Fd(),{}),n(lr(),Ll(e))},b=()=>s(Fd(),{}),v=t=>{t.state&&s(Dd(),{target:Fe(e.getContainer())})},y=e=>{s(Dd(),{target:Fe(e.relatedTarget.getContainer())})};e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",b),e.on("AfterProgressState",v),e.on("DismissPopups",y)})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",b),e.off("AfterProgressState",v),e.off("DismissPopups",y),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind()})),e.on("detach",(()=>{xd(t),xd(o),t.destroy(),o.destroy()}))})(e,v,x),n.setup(e,f.shared,m),fM(e,f),zM(e,g,f),(e=>{const{sidebars:t}=e.ui.registry.getAll();N(ae(t),(o=>{const n=t[o],s=()=>ye(B.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),NT(e,h,f.shared),rM(e,C,w,{backstage:f}),_A(e,w);const E={mothership:v,uiMothership:x,outerContainer:y,sink:w},M={targetNode:e.getElement(),height:b(y)};return o.render(e,E,T,f,M)}}},IA=y([Un("lazySink"),Zn("dragBlockClass"),ms("getBounds",Uo),as("useTabstopAt",T),as("eventOrder",{}),Zd("modalBehaviours",[kp]),bi("onExecute"),yi("onEscape")]),VA={sketch:x},RA=y([Tu({name:"draghandle",overrides:(e,t)=>({behaviours:ll([SA.config({mode:"mouse",getTarget:e=>Ja(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),Ou({schema:[Un("dom")],name:"title"}),Ou({factory:VA,schema:[Un("dom")],name:"close"}),Ou({factory:VA,schema:[Un("dom")],name:"body"}),Tu({factory:VA,schema:[Un("dom")],name:"footer"}),_u({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[as("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),as("components",[])],name:"blocker"})]),zA=em({name:"ModalDialog",configFields:IA(),partFields:RA(),factory:(e,t,o,n)=>{const s=zl(),r=Xr("modal-events"),a={...e.eventOrder,[cr()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])};return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([Ga(t)]),behaviours:ll([Hp.config({}),Fp("dialog-blocker-events",[Vr(Vs(),(()=>{kp.focusIn(t)}))])])});gd(o,a),kp.focusIn(t)},hide:e=>{s.clear(),Ye(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{fd(e)}))}))},getBody:t=>Nu(t,e,"body"),getFooter:t=>Nu(t,e,"footer"),setIdle:e=>{zT.unblock(e)},setBusy:(e,t)=>{zT.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:eu(e.modalBehaviours,[Dp.config({}),kp.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt}),zT.config({getRoot:s.get}),Fp(r,[Rr((t=>{((e,t)=>{const o=bt(e,"id").fold((()=>{const e=Xr("dialog-label");return pt(t,"id",e),e}),x);pt(e,"aria-labelledby",o)})(t.element,Nu(t,e,"title").element),((e,t)=>{const o=B.from(ft(e,"id")).fold((()=>{const e=Xr("dialog-describe");return pt(t,"id",e),e}),x);pt(e,"aria-describedby",o)})(t.element,Nu(t,e,"body").element)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),HA=yn([pb,hb].concat(dv)),PA=En,NA=[Lb("button"),Eb,ds("align","end",["start","end"]),Rb,Vb,os("buttonType",["primary","secondary"])],LA=[...NA,bb],WA=[qn("type",["submit","cancel","custom"]),...LA],UA=[qn("type",["menu"]),Tb,Bb,Eb,Jn("items",HA),...NA],jA=Pn("type",{submit:WA,cancel:WA,custom:WA,menu:UA}),GA=[pb,bb,qn("level",["info","warn","error","success"]),yb,as("url","")],$A=yn(GA),qA=[pb,bb,Vb,Lb("button"),Eb,Ib,os("buttonType",["primary","secondary","toolbar"]),Rb],XA=yn(qA),KA=[pb,hb],YA=KA.concat([Mb]),JA=KA.concat([fb,Vb]),ZA=yn(JA),QA=En,eD=YA.concat([zb("auto")]),tD=yn(eD),oD=kn([xb,bb,yb]),nD=yn(YA),sD=Tn,rD=yn(YA),aD=Tn,iD=KA.concat([cs("tag","textarea"),$n("scriptId"),$n("scriptUrl"),is("settings",void 0,An)]),lD=KA.concat([cs("tag","textarea"),Xn("init")]),cD=Fn((e=>Vn("customeditor.old",vn(lD),e).orThunk((()=>Vn("customeditor.new",vn(iD),e))))),dD=Tn,uD=yn(YA),mD=xn(gn),gD=e=>[pb,Gn("columns"),e],pD=[pb,$n("html"),ds("presets","presentation",["presentation","document"])],hD=yn(pD),fD=YA.concat([us("sandboxed",!0)]),bD=yn(fD),vD=Tn,yD=yn(KA.concat([ts("height")])),xD=yn([$n("url"),es("zoom"),es("cachedWidth"),es("cachedHeight")]),wD=YA.concat([ts("inputMode"),ts("placeholder"),us("maximized",!1),Vb]),SD=yn(wD),kD=Tn,CD=e=>[pb,fb,e],OD=[bb,xb],_D=[bb,Jn("items",((e,t)=>{const o=jt(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,(()=>TD)))],TD=wn([yn(OD),yn(_D)]),ED=YA.concat([Jn("items",TD),Vb]),BD=yn(ED),MD=Tn,AD=YA.concat([Yn("items",[bb,xb]),ls("size",1),Vb]),DD=yn(AD),FD=Tn,ID=YA.concat([us("constrain",!0),Vb]),VD=yn(ID),RD=yn([$n("width"),$n("height")]),zD=KA.concat([fb,ls("min",0),ls("max",0)]),HD=yn(zD),PD=_n,ND=[pb,Jn("header",Tn),Jn("cells",xn(Tn))],LD=yn(ND),WD=YA.concat([ts("placeholder"),us("maximized",!1),Vb]),UD=yn(WD),jD=Tn,GD=YA.concat([ds("filetype","file",["image","media","file"]),Vb]),$D=yn(GD),qD=yn([xb,Hb]),XD=e=>Nn("items","items",{tag:"required",process:{}},xn(Fn((t=>Vn(`Checking item of ${e}`,KD,t).fold((e=>$o.error(Hn(e))),(e=>$o.value(e))))))),KD=fn((()=>{return Dn("type",{alertbanner:$A,bar:yn((e=XD("bar"),[pb,e])),button:XA,checkbox:ZA,colorinput:nD,colorpicker:rD,dropzone:uD,grid:yn(gD(XD("grid"))),iframe:bD,input:SD,listbox:BD,selectbox:DD,sizeinput:VD,slider:HD,textarea:UD,urlinput:$D,customeditor:cD,htmlpanel:hD,imagepreview:yD,collection:tD,label:yn(CD(XD("label"))),table:LD,panel:JD});var e})),YD=[pb,as("classes",[]),Jn("items",KD)],JD=yn(YD),ZD=[Lb("tab"),vb,Jn("items",KD)],QD=[pb,Yn("tabs",ZD)],eF=yn(QD),tF=LA,oF=jA,nF=yn([$n("title"),jn("body",Dn("type",{panel:JD,tabpanel:eF})),cs("size","normal"),Jn("buttons",oF),as("initialData",{}),ms("onAction",b),ms("onChange",b),ms("onSubmit",b),ms("onClose",b),ms("onCancel",b),ms("onTabChange",b)]),sF=yn([qn("type",["cancel","custom"]),...tF]),rF=yn([$n("title"),$n("url"),es("height"),es("width"),ss("buttons",sF),ms("onAction",b),ms("onCancel",b),ms("onClose",b),ms("onMessage",b)]),aF=e=>a(e)?[e].concat(X(he(e),aF)):l(e)?X(e,aF):[],iF=e=>r(e.type)&&r(e.name),lF={checkbox:QA,colorinput:sD,colorpicker:aD,dropzone:mD,input:kD,iframe:vD,imagepreview:xD,selectbox:FD,sizeinput:RD,slider:PD,listbox:MD,size:RD,textarea:jD,urlinput:qD,customeditor:dD,collection:oD,togglemenuitem:PA},cF=e=>{const t=(e=>W(aF(e),iF))(e),o=X(t,(e=>(e=>B.from(lF[e.type]))(e).fold((()=>[]),(t=>[jn(e.name,t)]))));return yn(o)},dF=e=>({internalDialog:Rn(Vn("dialog",nF,e)),dataValidator:cF(e),initialData:e.initialData}),uF={open:(e,t)=>{const o=dF(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Rn(Vn("dialog",rF,t))),redial:e=>dF(e)},mF=e=>{const t=[],o={};return le(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?$o.error(t):$o.value(o)},gF=(e,t,o)=>{const n=Ch(vk.sketch((n=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:P(e.items,(e=>tO(n,e,t,o)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[n.asSpec()]}],behaviours:ll([kp.config({mode:"acyclic",useTabstopAt:k(Uk)}),(s=n,sm.config({find:s.getOpt})),Mk(n,{postprocess:e=>mF(e).fold((e=>(console.error(e),{})),x)})])};var s},pF=Qu({name:"TabButton",configFields:[as("uid",void 0),Un("value"),Nn("dom","dom",ln((()=>({attributes:{role:"tab",id:Xr("aria"),"aria-selected":"false"}}))),Cn()),Zn("action"),as("domModification",{}),Zd("tabButtonBehaviours",[Hp,kp,Jd]),Un("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:Zp(e.action),behaviours:eu(e.tabButtonBehaviours,[Hp.config({}),kp.config({mode:"execution",useSpace:!0,useEnter:!0}),Jd.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),hF=y([Un("tabs"),Un("dom"),as("clickToDismiss",!1),Zd("tabbarBehaviours",[Em,kp]),pi(["tabClass","selectedClass"])]),fF=Eu({factory:pF,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Em.dehighlight(e,t),xr(e,fr(),{tabbar:e,button:t})},o=(e,t)=>{Em.highlight(e,t),xr(e,hr(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Em.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),bF=y([fF]),vF=em({name:"Tabbar",configFields:hF(),partFields:bF(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:eu(e.tabbarBehaviours,[Em.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{pt(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{pt(t.element,"aria-selected","false")}}),kp.config({mode:"flow",getInitial:e=>Em.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),yF=Qu({name:"Tabview",configFields:[Zd("tabviewBehaviours",[Dp])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:eu(e.tabviewBehaviours,[Dp.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),xF=y([as("selectFirst",!0),fi("onChangeTab"),fi("onDismissTab"),as("tabs",[]),Zd("tabSectionBehaviours",[])]),wF=Ou({factory:vF,schema:[Un("dom"),Kn("markers",[Un("tabClass"),Un("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),SF=Ou({factory:yF,name:"tabview"}),kF=y([wF,SF]),CF=em({name:"TabSection",configFields:xF(),partFields:kF(),factory:(e,t,o,n)=>{const s=(t,o)=>{Pu(t,e,"tabbar").each((e=>{o(e).each(wr)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:Qd(e.tabSectionBehaviours),events:Or(q([e.selectFirst?[Rr(((e,t)=>{s(e,Em.getFirst)}))]:[],[Er(hr(),((t,o)=>{(t=>{const o=Jd.getValue(t);Pu(t,e,"tabview").each((n=>{G(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();bt(t.element,"id").each((e=>{pt(n.element,"aria-labelledby",e)})),Dp.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),Er(fr(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>Pu(t,e,"tabview").map((e=>Dp.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Em.getCandidates(e);return G(o,(e=>Jd.getValue(e)===t)).filter((t=>!Em.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),OF=(e,t)=>{St(e,"height",t+"px"),St(e,"flex-basis",t+"px")},_F=(e,t,o)=>{Ja(e,'[role="dialog"]').each((e=>{Qa(e,'[role="tablist"]').each((n=>{o.get().map((o=>(St(t,"height","0"),St(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=Xe(e).dom,s=Ja(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===Ot(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=It(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Wt(o)?Math.max(It(o),a):a,l=parseInt(Ot(e,"margin-top"),10)||0,c=parseInt(Ot(e,"margin-bottom"),10)||0;return r-(It(e)+l+c-i)})(e,t,n))))).each((e=>{OF(t,e)}))}))}))},TF=e=>Qa(e,'[role="tabpanel"]'),EF="send-data-to-section",BF="send-data-to-view",MF=(e,t,o)=>{const n=hs({}),s=e=>{const t=Jd.getValue(e),o=mF(t).getOr({}),s=n.get(),r=nn(s,o);n.set(r)},r=e=>{const t=n.get();Jd.setValue(e,t)},a=hs(null),i=P(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[Na(o.shared.providers.translate(e.title))],view:()=>[vk.sketch((n=>({dom:{tag:"div",classes:["tox-form"]},components:P(e.items,(e=>tO(n,e,t,o))),formBehaviours:ll([kp.config({mode:"acyclic",useTabstopAt:k(Uk)}),Fp("TabView.form.events",[Rr(r),zr(s)]),pl.config({channels:vs([{key:EF,value:{onReceive:s}},{key:BF,value:{onReceive:r}}])})])})))]}))),l=(e=>{const t=(()=>{const t=zl(),o=[Rr((o=>{const n=o.element;TF(n).each((s=>{St(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>P(e,((n,s)=>{Dp.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return Dp.set(o,[]),r.height})))(e,s,o),r=(e=>oe(ee(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),_F(n,s,t),Mt(s,"visibility"),((e,t)=>{oe(e).each((e=>CF.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{_F(n,s,t)}))}))})),Er(lr(),(e=>{const o=e.element;TF(o).each((e=>{_F(o,e,t)}))})),Er(Lx,((e,o)=>{const n=e.element;TF(n).each((e=>{const o=vl(at(e));St(e,"visibility","hidden");const s=Tt(e,"height").map((e=>parseInt(e,10)));Mt(e,"height"),Mt(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),_F(n,e,t)):s.each((t=>{OF(e,t)})),Mt(e,"visibility"),o.each(fl)}))}))];return{extraEvents:o,selectFirst:!1}})();return{smartTabHeight:t,naiveTabHeight:{extraEvents:[],selectFirst:!0}}})(i).smartTabHeight;return CF.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=Jd.getValue(t);xr(e,Nx,{name:n,oldName:a.get()}),a.set(n)},tabs:i,components:[CF.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[vF.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:ll([Tx.config({})])}),CF.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:l.selectFirst,tabSectionBehaviours:ll([Fp("tabpanel",l.extraEvents),kp.config({mode:"acyclic"}),sm.config({find:e=>oe(CF.getViewItems(e))}),Dk(B.none(),(e=>(e.getSystem().broadcastOn([EF],{}),n.get())),((e,t)=>{n.set(t),e.getSystem().broadcastOn([BF],{})}))])})},AF=Xr("update-dialog"),DF=Xr("update-title"),FF=Xr("update-body"),IF=Xr("update-footer"),VF=Xr("body-send-message"),RF=(e,t,o,n,s)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:ll([_k(0),cB.config({channel:`${FF}-${t}`,updateState:(e,t)=>B.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[MF(t,e.initialData,n)]:[gF(t,e.initialData,n)]},initialData:e})])}),zF=Lh.deviceType.isTouch(),HF=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),PF=(e,t)=>zA.parts.close(kh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:ll([Tx.config({})])})),NF=()=>zA.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),LF=(e,t)=>zA.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:HT(`<p>${t.translate(e)}</p>`)}]}]}),WF=e=>zA.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),UF=(e,t)=>[xx.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),xx.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],jF=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return zA.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),B.some(!0)),useTabstopAt:e=>!Uk(e),dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:HT(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:zF?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:ll([Hp.config({}),Fp("dialog-events",e.dialogEvents.concat([Vr(Vs(),((e,t)=>{kp.focusIn(e)}))])),Fp("scroll-lock",[Rr((()=>{_a(ut(),s)})),zr((()=>{Ta(ut(),s)}))]),...e.extraBehaviours]),eventOrder:{[Zs()]:["dialog-events"],[cr()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[dr()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},GF=e=>kh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),title:e.translate("Close")}},components:[Ih("close",{tag:"div",classes:["tox-icon"]},e.icons)],action:e=>{yr(e,Vx)}}),$F=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:ll([cB.config({channel:`${DF}-${t}`,initialData:e,renderComponents:e=>[Na(n.translate(e.title))]})])}),qF=()=>({dom:HT('<div class="tox-dialog__draghandle"></div>')}),XF=(e,t,o)=>((e,t,o)=>{const n=zA.parts.title($F(e,t,B.none(),o)),s=zA.parts.draghandle(qF()),r=zA.parts.close(GF(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return xx.sketch({dom:HT('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),KF=(e,t,o)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:t,components:[{dom:HT('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),YF=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{zA.setBusy(e(),((e,n)=>KF(o.message,n,t)))},onUnblock:()=>{zA.setIdle(e())}}),JF=(e,t,o,n)=>ja(jF({...e,lazySink:n.shared.getSink,extraBehaviours:[cB.config({channel:`${AF}-${e.id}`,updateState:(e,t)=>B.some(t),initialData:t}),Fk({}),...e.extraBehaviours],onEscape:e=>{yr(e,Vx)},dialogEvents:o,eventOrder:{[Js()]:[cB.name(),pl.name()],[cr()]:["scroll-lock",cB.name(),"messages","dialog-events","alloy.base.behaviour"],[dr()]:["alloy.base.behaviour","dialog-events","messages",cB.name(),"scroll-lock"]}})),ZF=e=>P(e,(e=>"menu"===e.type?(e=>{const t=P(e.items,(e=>({...e,storage:hs(!1)})));return{...e,items:t}})(e):e)),QF=e=>j(e,((e,t)=>"menu"===t.type?j(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),eI=(e,t)=>[Dr(Vs(),Wk),e(Ix,((e,o)=>{t.onClose(),o.onClose()})),e(Vx,((e,t,o,n)=>{t.onCancel(e),yr(n,Ix)})),Er(Px,((e,o)=>t.onUnblock())),Er(Hx,((e,o)=>t.onBlock(o.event)))],tI=(e,t,o)=>{const n=(t,o)=>Er(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{cB.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...eI(n,t),n(zx,((e,t)=>t.onSubmit(e))),n(Fx,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(Rx,((e,t,n,s)=>{const r=()=>kp.focusIn(s),a=e=>vt(e,"disabled")||bt(e,"aria-disabled").exists((e=>"true"===e)),i=at(s.element),l=vl(i);t.onAction(e,{name:n.name,value:n.value}),vl(i).fold(r,(e=>{a(e)||l.exists((t=>Ge(e,t)&&a(t)))?r():o().toOptional().filter((t=>!Ge(t.element,e))).each(r)}))})),n(Nx,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),zr((t=>{const o=e();Jd.setValue(t,o.getData())}))]},oI=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=L(o,(e=>"start"===e.align)),s=(e,t)=>xx.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:P(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},nI=(e,t,o)=>({dom:HT('<div class="tox-dialog__footer"></div>'),components:[],behaviours:ll([cB.config({channel:`${IF}-${t}`,initialData:e,updateState:(e,t)=>{const n=P(t.buttons,(e=>{const t=Ch(((e,t)=>PC(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return B.some({lookupByName:t=>((e,t,o)=>G(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:oI})])}),sI=(e,t,o)=>zA.parts.footer(nI(e,t,o)),rI=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=sm.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return vk.getField(o,t).orThunk((()=>{const o=e.getFooter();return cB.getState(o).get().bind((e=>e.lookupByName(t)))}))}return B.none()},aI=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...Jd.getValue(n),...ce(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=nn(r,t),i=((e,t)=>{const o=e.getRoot();return cB.getState(o).get().map((e=>Rn(Vn("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();Jd.setValue(l,i),le(o,((e,t)=>{be(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{rI(e,t).each(o?vm.enable:vm.disable)},focus:t=>{rI(e,t).each(Hp.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{xr(t,Hx,{message:e})}))},unblock:()=>{n((e=>{yr(e,Px)}))},showTab:t=>{n((o=>{const n=e.getBody();cB.getState(n).get().exists((e=>e.isTabPanel()))&&sm.getCurrent(n).each((e=>{CF.showTab(e,t)}))}))},redial:o=>{n((n=>{const r=e.getId(),a=t(o);n.getSystem().broadcastOn([`${AF}-${r}`],a),n.getSystem().broadcastOn([`${DF}-${r}`],a.internalDialog),n.getSystem().broadcastOn([`${FF}-${r}`],a.internalDialog),n.getSystem().broadcastOn([`${IF}-${r}`],a.internalDialog),s.setData(a.initialData)}))},close:()=>{n((e=>{yr(e,Ix)}))}};return s};var iI=tinymce.util.Tools.resolve("tinymce.util.URI");const lI=["insertContent","setContent","execCommand","close","block","unblock"],cI=e=>a(e)&&-1!==lI.indexOf(e.mceAction),dI=(e,t,o,n)=>{const s=Xr("dialog"),i=XF(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[Nk({dom:{tag:"iframe",attributes:{src:e.url}},behaviours:ll([Tx.config({}),Hp.config({})])})]}],behaviours:ll([kp.config({mode:"acyclic",useTabstopAt:k(Uk)})])};return zA.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?B.none():B.some(sI({buttons:e},s,n)))),u=((e,t)=>{const o=(t,o)=>Er(t,((t,s)=>{n(t,((n,r)=>{o(e(),n,s.event,t)}))})),n=(e,t)=>{cB.getState(e).get().each((o=>{t(o,e)}))};return[...eI(o,t),o(Rx,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})((()=>y),YF((()=>v),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},g=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],p=new iI(e.url,{base_uri:new iI(window.location.href)}),h=`${p.protocol}://${p.host}${p.port?":"+p.port:""}`,f=Rl(),b=[Fp("messages",[Rr((()=>{const t=Pl(Fe(window),"message",(t=>{if(p.isSameOrigin(new iI(t.raw.origin))){const n=t.raw.data;cI(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,y,n):(e=>!cI(e)&&a(e)&&be(e,"mceAction"))(n)&&e.onMessage(y,n)}}));f.set(t)})),zr(f.clear)]),pl.config({channels:{[VF]:{onReceive:(e,t)=>{Qa(e.element,"iframe").each((e=>{e.dom.contentWindow.postMessage(t,h)}))}}}})],v=JF({id:s,header:i,body:l,footer:c,extraClasses:g,extraBehaviours:b,extraStyles:m},e,u,n),y=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{xr(t,Hx,{message:e})}))},unblock:()=>{t((e=>{yr(e,Px)}))},close:()=>{t((e=>{yr(e,Ix)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([VF],e)}))}}})(v);return{dialog:v,instanceApi:y}},uI=(e,t,o)=>t&&o?[]:[U_.config({contextual:{lazyContext:()=>B.some(Lo(Fe(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})],mI=e=>{const t=e.backstage,o=e.editor,n=zf(o),s=(e=>{const t=e.backstage.shared;return{open:(o,n)=>{const s=()=>{zA.hide(l),n()},r=Ch(PC({name:"close-alert",text:"OK",primary:!0,buttonType:B.some("primary"),align:"end",enabled:!0,icon:B.none()},"cancel",e.backstage)),a=NF(),i=PF(s,t.providers),l=ja(jF({lazySink:()=>t.getSink(),header:HF(a,i),body:LF(o,t.providers),footer:B.some(WF(UF([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Er(Vx,s)],eventOrder:{}}));zA.show(l);const c=r.get(l);Hp.focus(c)}}})(e),r=(e=>{const t=e.backstage.shared;return{open:(o,n)=>{const s=e=>{zA.hide(c),n(e)},r=Ch(PC({name:"yes",text:"Yes",primary:!0,buttonType:B.some("primary"),align:"end",enabled:!0,icon:B.none()},"submit",e.backstage)),a=PC({name:"no",text:"No",primary:!1,buttonType:B.some("secondary"),align:"end",enabled:!0,icon:B.none()},"cancel",e.backstage),i=NF(),l=PF((()=>s(!1)),t.providers),c=ja(jF({lazySink:()=>t.getSink(),header:HF(i,l),body:LF(o,t.providers),footer:B.some(WF(UF([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Er(Vx,(()=>s(!1))),Er(zx,(()=>s(!0)))],eventOrder:{}}));zA.show(c);const d=r.get(c);Hp.focus(d)}}})(e),a=(e,o)=>uF.open(((e,n,s)=>{const r=n,a=((e,t,o)=>{const n=Xr("dialog"),s=e.internalDialog,r=XF(s.title,n,o),a=((e,t,o)=>{const n=RF(e,t,B.none(),o,!1);return zA.parts.body(n)})({body:s.body,initialData:s.initialData},n,o),i=ZF(s.buttons),l=QF(i),c=sI({buttons:i},n,o),d=tI((()=>h),YF((()=>g),o.shared.providers,t),o.shared.getSink),u=(e=>{switch(e){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}})(s.size),m={id:n,header:r,body:a,footer:B.some(c),extraClasses:u,extraBehaviours:[],extraStyles:{}},g=JF(m,e,d,o),p={getId:y(n),getRoot:y(g),getBody:()=>zA.getBody(g),getFooter:()=>zA.getFooter(g),getFormWrapper:()=>{const e=zA.getBody(g);return sm.getCurrent(e).getOr(e)}},h=aI(p,t.redial,l);return{dialog:g,instanceApi:h}})({dataValidator:s,initialData:r,internalDialog:e},{redial:uF.redial,closeWindow:()=>{zA.hide(a.dialog),o(a.instanceApi)}},t);return zA.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),e),i=(e,s,r,a)=>uF.open(((e,i,l)=>{const c=Rn(Vn("data",l,i)),d=zl(),u=t.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{wh.reposition(e),U_.refresh(e)})),g=((e,t,o,n)=>{const s=Xr("dialog"),r=Xr("dialog-label"),a=Xr("dialog-content"),i=e.internalDialog,l=Ch(((e,t,o,n)=>xx.sketch({dom:HT('<div class="tox-dialog__header"></div>'),components:[$F(e,t,B.some(o),n),qF(),GF(n)],containerBehaviours:ll([SA.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>ei(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:i.title,draggable:!0},s,r,o.shared.providers)),c=Ch(((e,t,o,n,s)=>RF(e,t,B.some(o),n,s))({body:i.body,initialData:i.initialData},s,a,o,n)),d=ZF(i.buttons),u=QF(d),m=Ch(((e,t,o)=>nI(e,t,o))({buttons:d},s,o)),g=tI((()=>h),{onBlock:e=>{zT.block(p,((t,n)=>KF(e.message,n,o.shared.providers)))},onUnblock:()=>{zT.unblock(p)},onClose:()=>t.closeWindow()},o.shared.getSink),p=ja({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:{role:"dialog","aria-labelledby":r,"aria-describedby":a}},eventOrder:{[Js()]:[cB.name(),pl.name()],[Zs()]:["execute-on-form"],[cr()]:["reflecting","execute-on-form"]},behaviours:ll([kp.config({mode:"cyclic",onEscape:e=>(yr(e,Ix),B.some(!0)),useTabstopAt:e=>!Uk(e)&&("button"!==Ve(e)||"disabled"!==ft(e,"disabled"))}),cB.config({channel:`${AF}-${s}`,updateState:(e,t)=>B.some(t),initialData:e}),Hp.config({}),Fp("execute-on-form",g.concat([Vr(Vs(),((e,t)=>{kp.focusIn(e)}))])),zT.config({getRoot:()=>B.some(p)}),Dp.config({}),Fk({})]),components:[l.asSpec(),c.asSpec(),m.asSpec()]}),h=aI({getId:y(s),getRoot:y(p),getFooter:()=>m.get(p),getBody:()=>c.get(p),getFormWrapper:()=>{const e=c.get(p);return sm.getCurrent(e).getOr(e)}},t.redial,u);return{dialog:p,instanceApi:h}})({dataValidator:l,initialData:c,internalDialog:e},{redial:uF.redial,closeWindow:()=>{d.on(wh.hide),o.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},t,a),p=ja(wh.sketch({lazySink:t.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:ll([Fp("window-manager-inline-events",[Er(ur(),((e,t)=>{yr(g.dialog,Vx)}))]),...uI(o,n,u)]),isExtraPart:(e,t)=>(e=>dx(e,".tox-alert-dialog")||dx(e,".tox-confirm-dialog"))(t)}));return d.set(p),wh.showWithin(p,Ga(g.dialog),{anchor:s},B.some(ut())),n&&u||(U_.refresh(p),o.on("ResizeEditor",m)),g.instanceApi.setData(c),kp.focusIn(g.dialog),g.instanceApi}),e);return{open:(e,o,n)=>void 0!==o&&"toolbar"===o.inline?i(e,t.shared.anchors.inlineDialog(),n,o.ariaAttrs):void 0!==o&&"cursor"===o.inline?i(e,t.shared.anchors.cursor(),n,o.ariaAttrs):a(e,n),openUrl:(e,n)=>((e,n)=>uF.openUrl((e=>{const s=dI(e,{closeWindow:()=>{zA.hide(s.dialog),n(s.instanceApi)}},o,t);return zA.show(s.dialog),s.instanceApi}),e))(e,n),alert:(e,t)=>{s.open(e,(()=>{t()}))},close:e=>{e.close()},confirm:(e,t)=>{r.open(e,(e=>{t(e)}))}}};E.add("silver",(e=>{(e=>{jh(e),(e=>{const t=e.options.register;var o;t("color_map",{processor:e=>f(e,r)?{value:Fy(e),valid:!0}:{valid:!1,message:"Must be an array of strings."},default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_cols",{processor:"number",default:(o=zy(e).length,Math.max(5,Math.ceil(Math.sqrt(o))))}),t("custom_colors",{processor:"boolean",default:!0})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:bM(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);const{getUiMothership:t,backstage:o,renderUI:n}=FA(e);cx(e,o.shared);const s=mI({editor:e,backstage:o});return{renderUI:n,getWindowManagerImpl:y(s),getNotificationManagerImpl:()=>((e,t,o)=>{const n=t.backstage.shared,s=()=>{const t=Lo(Fe(e.getContentAreaContainer())),o=Uo(),n=Ii(o.x,t.x,t.right),s=Ii(o.y,t.y,t.bottom),r=Math.max(t.right,o.right),a=Math.max(t.bottom,o.bottom);return B.some(No(n,s,r-n,a-s))};return{open:(t,r)=>{const a=()=>{r(),wh.hide(l)},i=ja(Rh.sketch({text:t.text,level:V(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:B.from(t.icon),closeButton:t.closeButton,onAction:a,iconProvider:n.providers.icons,translationProvider:n.providers.translate})),l=ja(wh.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:n.getSink,fireDismissalEventInstead:{},...n.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}}}));o.add(l),t.timeout>0&&Sh.setEditorTimeout(e,(()=>{a()}),t.timeout);const c={close:a,reposition:()=>{const t=Ga(i),o={maxHeightFunction:ql()},r=e.notificationManager.getNotifications();if(r[0]===c){const e={...n.anchors.banner(),overrides:o};wh.showWithinBounds(l,t,{anchor:e},s)}else I(r,c).each((e=>{const n=r[e-1].getEl(),a={type:"node",root:ut(),node:B.some(Fe(n)),overrides:o,layouts:{onRtl:()=>[Xi],onLtr:()=>[Xi]}};wh.showWithinBounds(l,t,{anchor:a},s)}))},text:e=>{Rh.updateText(i,e)},settings:t,getEl:()=>i.element.dom,progressBar:{value:e=>{Rh.updateProgress(i,e)}}};return c},close:e=>{e.close()},getArgs:e=>e.settings}})(e,{backstage:o},t())}}))}();