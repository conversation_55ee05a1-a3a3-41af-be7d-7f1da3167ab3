import "./styles/cover.less"
// import "./styles/common.less"
// import 'systemjs/dist/system.src';

require("./assets/iconfont/iconfont")
import "./assets/iconfont/iconfont.css"


// 因为ant design 默认的是en,所以需要全局设置zh-cn;
import moment from "moment";
import 'moment/locale/zh-cn';
moment.locale('zh-cn');

import "react-zmage/lib/zmage.css";
import GC from '@grapecity/spread-sheets';
// import '@grapecity/spread-sheets/styles/gc.spread.sheets.excel2013white.css';
import '@grapecity/spread-sheets-resources-zh';
GC.Spread.Common.CultureManager.culture('zh-cn')


// import 'public/systemjs.config'
// import '/public/systemjs.config';


//Please provide valid license key.

// GC.Spread.Sheets.LicenseKey = "E197797523415716#B1bdfxWULRzaJRkbxZmbaFjaBRmdJNmUEZlT8ZlT8BlbzBVbx36LR9UO88kUPd6RxRzVLR4NJNmdOhnbEl5YIJUVtdmQXhzTtF6QEtiR8lWVrBXd8AlTzh6RvhnUYFHek3yM83mNaZVeW9mMLNDR6cHULNlbzE6Ly3GSt9mZ9omUkVTeWlmMNBDWohzNa56Vv4UdiBVYHVFa8c6Muh5bKJFaWFFN8YzRHRXY8dXa4VUMRl6YMplbMB7axh4dv3GM8BVWydzKSJDNrBTdxFUblVVejV4UEpWQhpHcsNlRTtiRr3mQ7MDb6gWOyY6dDVzblJnbxIUQyUXcvgjb5MTM7U5dHt4QEJWZvxWR8sUaGZUSKN4c4w6KuBlRCZldDlVbnZHWyQEORZzRNh7QMlzZ5g5bwU5ZtFkeRdUastCTENUaMhkcoxUMoRmQmVzTvolI0IyUiwiIDlzQxQEN6cjI0ICSiwyMxYTMxYTM8cTM0IicfJye#4Xfd5nIXR5QaJiOiMkIsIiNx8idgMlSgQWYlJHcTJiOi8kI1tlOiQmcQJCLikTM4ETMwAiNxATMzIDMyIiOiQncDJCLiMXduMXdpN6cl5mLqwCcq9yc5l6YzVWbuoCLvlmLzVXajNXZt9iKs46bj9yc5l6YzVWbuoCLwpmLvNmLzVXajNXZt9iKs46bj9idlRWe4l6YlBXYydmLqwibj9SbvNmL9RXajVGchJ7ZuoCLt36YukHdpNWZwFmcn9iKsI7au26YukHdpNWZwFmcn9iKsAnau26YukHdpNWZwFmcn9iKiojIz5GRiwiITVVSDNVRNJiOiEmTDJCLlVnc4pjIsZXRiwiI6EzN5EDNzITN7kzN7kTMiojIklkIs4XXiQXZlh6U4J7bwVmUiwiI4VWZoNFd49WYHJCLiUGbiFGV43mdpBlIbpjInxmZiwSZzxWYmpjIyNHZisnOiwmbBJye0ICRiwiI34TQqNTbhF5ULA";



