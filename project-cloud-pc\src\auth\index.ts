import {REDIRECT_LOGIN, TOKEN_KEY} from '@/constants/token';
import Message from 'bmc-app-react-component/es/util/Message';

interface TokenInfo {
  access_token: string;
  token_type: string;
  refresh_token: string;
  expires_in: number;
  scope: string;
  companyCode: string;
  id: string;
  jti: string;
  expireAt: number;
}

let tokenInfo: TokenInfo | null = null;

export function getToken() {
  if (!tokenInfo) {
    const storage = window.localStorage.getItem(TOKEN_KEY);
    if (storage) {
      tokenInfo = JSON.parse(storage);
    } else {
      // 无token信息或token验证失败跳转登录
      // redirectToLogin();
    }
  }
  return tokenInfo!;
}

function redirectToLogin() {
  Message.error('当前登录状态已过期，请重新登录');
  const doc = top || window;
  doc.location.href = REDIRECT_LOGIN;
}


