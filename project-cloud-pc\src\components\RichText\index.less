
.tox-dialog__body-nav-item.tox-tab {
  visibility: hidden;
  width: 0;
}

.tox-dialog__body-nav {
  padding: 0 !important;
}

#rich-text-img-container {
  height: 100px;
  width: 350px;

  img {
    max-width: 100px;
    max-height: 100px;
    text-align: center;
    object-fit: cover;
    color: transparent;
    text-indent: 10000px;
  }
}


///*滚动条的设置*/
//.tox-tinymce .tox-edit-area iframe::-webkit-scrollbar {
//  width: 6px;
//  height: 6px;
//}
//
///* 滚动条的滑块 */
//.tox-tinymce .tox-edit-area iframe::-webkit-scrollbar-thumb {
//  box-shadow: inset 0 0 5px transparent;
//  border-radius: 10px;
//  background-color: transparent;
//}
//
//.tox-tinymce .tox-edit-area iframe:hover::-webkit-scrollbar-thumb {
//  box-shadow: inset 0 0 5px #e3e7ef;
//  background-color: #c1c1c1;
//}
//
///* 滚动条轨道背景色 */
//.tox-tinymce .tox-edit-area iframe::-webkit-scrollbar-track{
//  box-shadow: inset 0 0 5px transparent;
//  background-color: transparent;
//  border-radius: 10px;
//}
//
//.tox-tinymce .tox-edit-area iframe:hover::-webkit-scrollbar-track {
//  box-shadow: inset 0 0 5px #E1E1E1;
//  background-color: #E1E1E1;
//}
//
//.tox-tinymce .tox-edit-area iframe::-webkit-scrollbar-thumb:hover {
//  background-color: rgba(0, 0, 0, 0.5);
//}
//
///*滚动条的设置*/
//.tox-editor-container .tox-sidebar-wrap .tox-edit-area iframe::-webkit-scrollbar {
//  width: 6px;
//  height: 6px;
//}
//
///* 滚动条的滑块 */
//.tox-editor-container .tox-sidebar-wrap .tox-edit-area iframe::-webkit-scrollbar-thumb {
//  box-shadow: inset 0 0 5px transparent;
//  border-radius: 10px;
//  background-color: transparent;
//}
//
//.tox-editor-container .tox-sidebar-wrap .tox-edit-area iframe:hover::-webkit-scrollbar-thumb {
//  box-shadow: inset 0 0 5px #e3e7ef;
//  background-color: #c1c1c1;
//}
//
///* 滚动条轨道背景色 */
//.tox-editor-container .tox-sidebar-wrap .tox-edit-area iframe::-webkit-scrollbar-track{
//  box-shadow: inset 0 0 5px transparent;
//  background-color: transparent;
//  border-radius: 10px;
//}
//
//.tox-editor-container .tox-sidebar-wrap .tox-edit-area iframe:hover::-webkit-scrollbar-track {
//  box-shadow: inset 0 0 5px #E1E1E1;
//  background-color: #E1E1E1;
//}
//
//.tox-editor-container .tox-sidebar-wrap .tox-edit-area iframe::-webkit-scrollbar-thumb:hover {
//  background-color: rgba(0, 0, 0, 0.5);
//}


