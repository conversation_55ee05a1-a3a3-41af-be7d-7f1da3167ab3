/**
 * @description 富文本框
 * <AUTHOR>
 * @date 2022/05/23
 * @modified zhangLei 2022/6/7
 */

import React from 'react';
import { FormHelperText, Stack, Typography } from '@mui/material';
import { Editor, IAllProps } from '@tinymce/tinymce-react';
import Request from 'bmc-app-react-component/es/util/Request';
import { TOKEN_PREFIX, COMPANY_CODE } from '@/constants/token';
import { getToken } from '@/auth';
import { Editor as TinyMCEEditor } from 'tinymce';
import { isObject } from 'lodash';
import "./index.less"

interface valueType { value?: string, fileList?: any[] }

interface RichTextProps extends Omit<any, 'children' | "onChange" | "onBlur">, Omit<IAllProps, 'value' | 'onChange'> {
  onChange?: (text: valueType, editor: TinyMCEEditor) => void;
  hideTool?: boolean;
  value?: valueType | null;
  minHeight?: number
  maxHeight?: number
  height?: number
  enterKeypress?: any
}

function RichText(props: RichTextProps) {
  //内容显示区域的样式设置
  const contentStyle = 'body {font-size:12px} ol {padding:0px} ul {padding:0px} .mce-content-body[data-mce-placeholder]:not(.mce-visualblocks)::before { color: #bbbbbb}';
  //工具栏展示是项
  const toolBarShow = 'undo redo | styles | link image| bullist  numlist | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify  |  outdent indent ';
  //插件配置
  const pluginsShow = ['advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview', 'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen', 'insertdatetime', 'media', 'help', 'wordcount',];
  // @ts-ignore
  const { uploadUrl } = process.env.uploadConfig;

  const {
    hideTool = false,
    label,
    sx,
    required = false,
    placeholder,
    tips,
    value,
    onChange,
    error,
    helperText,
    minHeight = 15,
    maxHeight = 1000,
    height = 250,
    enterKeypress,
    ...otherProps
  } = props;

  function imageUploadHandler(blob: any) {
    return new Promise<string>((resolve, reject) => {
      const formData = new FormData();
      formData.append('file', blob.blob(), blob.filename());
      // @ts-ignore
      formData.append('isPublic', true);
      Request.post(uploadUrl, {
        requestType: 'form',
        data: formData,
        headers: {
          Authorization: TOKEN_PREFIX + getToken()?.access_token,
        }
      }).then(res => {
        if (res?.code === 0) {
          if (res.data) {
            setTimeout(() => { //之所以写在setTimeout是因为这个函数返回值返回出去图片路径，imgage插件的组件会更新state（状态），即使先设置了innerHTML也会被重置掉，恢复成原来的样子，所以需要写setTimeout保证是最后才设置，不会被组件内部的state重置掉
              const temp = document.querySelector('.tox-form__group')
              if (temp) {
                temp.innerHTML = `<div id="rich-text-img-container"><img src='${res.data.url}' /></div>`
              }
            }, 0)
            resolve(res.data.url);
            return;
          }
        }
        reject({ message: '失败', remove: true });
      }).catch(() => {
        reject({ message: '失败', remove: true });
      });
    });
  }

  /**
   * @description 處理按鍵事件
   * @param e
   * @param editor
   */
  const handleEditorKeyChange = (e: any, editor: any) => {
    if (e.shiftKey && e.keyCode === 13) {
      editor.execCommand('mceInsertContent', false, '<br>')
      return
    }
    if (enterKeypress && e.keyCode === 13) {
      enterKeypress?.(e)
    }
  }


  /**
    * @description 處理鼠標時間
   * @param e
   * @param editor
    */
  const handleEditorMouseChange = (e: any, editor: any) => {
    console.log(e)
    // e.preventDefault()
  }

  //获取value
  const getFormatValue = (value?: valueType | null) => {
    if (isObject(value)) {
      if (value.value) {
        return value.value;
      } else {
        return ''
      }
    }else{
      return ''
    }
  }

  const renderEditor = () => {
    // @ts-ignore
    return <Editor
      tinymceScriptSrc={process.env.NODE_ENV == 'production' ? "/project-management/tinymce/tinymce.min.js" : "/tinymce/tinymce.min.js"}
      {...otherProps}
      init={{
        statusbar: false,
        width: '100%',
        language: 'zh-Hans',
        // min_height: 160,
        menubar: false,
        // image_title:true,
        // automatic_uploads:true,
        fontsize_formats: '12px 14px 16px 18px 20px',
        // file_picker_types: 'image',
        placeholder,
        toolbar: !hideTool && toolBarShow,
        // images_upload_handler: imageUploadHandler,
        // selector:'#textarea',
        plugins: 'table link emoticons lists insertdatetime charmap help preview autoresize',
        // file_picker_types: 'image',
        // file_picker_callback: (cb, value, meta) => {
        //   const input = document.createElement('input');
        //   input.setAttribute('type', 'file');
        //   input.setAttribute('accept', 'image/*');
        //   input.addEventListener('change', (e: Event) => {
        //       const target = e.target as HTMLInputElement
        //       const file: File = (target.files as FileList)[0]
        //       const reader = new FileReader();
        //       reader.addEventListener('load', () => {
        //         const id = 'blobid' + new Date().getTime();
        //         const blobCache = tinymce.activeEditor.editorUpload.blobCache;
        //         const base64 = (reader.result as string).split(',')[1];
        //         const blobInfo = blobCache.create(id, file, base64);
        //         blobCache.add(blobInfo);
        //         cb(blobInfo.blobUri(), { title: file.name });
        //       });
        //       reader.readAsDataURL(file);
        //   });
        //   input.click();
        // },
        content_style: 'body{font-family:Helvetical,Arial,sans-serif;font-size:12px;overflow-y:auto!important;}',
        setup: (editor) => {
          editor.on('init',() => {
            editor.on('paste postprocess',(e) => {
              const images = editor.dom.select('img');
              images.forEach((img) => {
                editor.dom.setAttrib(img,'width',100);
                editor.dom.setAttrib(img,'height',100);
                editor.dom.setAttrib(img,'style','margin-left:10px');
              })
            })
          })
        },
        ...otherProps?.init,
      }}
      value={getFormatValue(value)}
      onEditorChange={(e,editor) => {
        onChange?.({
          value: e, 
          fileList: []
        },editor)
      }}
    />;
  };

  return <Stack sx={{
    '.tox-tinymce': {
      borderRadius: 0,
      borderWidth: '1px',
    },
    '.tox .tox-edit-area__iframe': {
      backgroundColor: '#ffffff',
      cursor: 'text',
    },
    '.tox-editor-header': {
      py: '0 !important',
    },
    '& img': {
      objectFit: 'contain'
    },
    // height: '100%',
    ...sx,
  }}>
    {
      label && <Stack direction={'row'} sx={{ alignItems: 'center', color: '#666666', fontSize: 12, height: '2rem' }}>
        {label}
        {required && <Typography sx={{ ml: 0.3, color: '#0275d8' }}>*</Typography>}
      </Stack>
    }
    {renderEditor()}
    {error && <FormHelperText error={error}>{helperText}</FormHelperText>}
  </Stack>;
}

export default RichText;
