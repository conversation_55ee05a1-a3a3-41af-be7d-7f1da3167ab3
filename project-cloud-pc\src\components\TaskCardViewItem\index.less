.card_col {
    width: 300px;
    float: left;
    margin-right: 16px;
    &:nth-last-child(1) {
        margin-right: 0;
    }
    .card_item {
        cursor: pointer;
        overflow: hidden;
        width: 100%;
        padding: 12px 16px;
        box-shadow: 0px 0px 5px rgba(0, 0, 0, .1);
        border-radius: 2px;
        background-color: #FFF;
        margin: 16px 0;
        transition: all .2s;
        &:hover {
            transition: all .2s;
            box-shadow: 0px 0px 10px rgba(0, 0, 0, .3);
        }

        .title_wrapper {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            font-size: 13px;
            color: #000;
            .title_info {
                width: 208px;
                padding-top: 2px;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
            }
            .user {
                width: 28px;
                height: 28px;
                text-align: center;
                line-height: 28px;
                font-size: 10px;
                border-radius: 50%;
                color: #fff;
                background-color: #428bca;
            }

        }

        .project_content_wrapper {
            float: right;
            width: 240px;
            display: flex;
            align-items: flex-start;
            margin: 6px 0;
            justify-content: start;
            color: #657599;
            font-size: 12px;
            .message_icon {
                margin-top: 5px;
                margin-right: 10px;
            }

            .project_text {
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
            }
        }

        .desc_wrapper {
            float: right;
            width: 240px;
            display: flex;
            align-items: center;
            font-size: 12px;
            .ant-tag {
                border-color: transparent;
                border-radius: 3px;
            }
            .desc_content {
                display: flex;
                align-items: center;
                .desc_text, .icon {
                    color: #9F9F9F;
                }
                .icon {
                    margin-right: 10px;
                }
                .long_desc {
                    width: 223px!important;
                }
                .desc_text {
                    width: 160px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
    .empty_wrapper {
        color: #9F9F9F;
        text-align: center;
        line-height: 35vh;
    }
}