import React from 'react';
import { Tag, Checkbox } from 'antd';
import IconTitle from "@/components/icon-title";
import { history } from 'umi';
import { MessageFilled, ApartmentOutlined } from "@ant-design/icons";
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { ItemProps } from '@/pages/myTask/cardView';
import './index.less';

interface ITaskCardViewProps {
	itemInfo: {
		code: string
		name: string
		icon: React.ReactNode
		dataList: ItemProps[]
	},
	handleOnCheckedChange: (checkedFlag:boolean, checkedItem: ItemProps) => void
}

// TODO （待回复，待提报，已驳回）状态待替换
// 项目状态tag 颜色及背景色
const statusTagColorObj = {
	CREATE: '#0275d8',
	waitReply: '#23c6c8',
	waitSubmit: '#5c5c5c',
	reject: '#d9534f',
}
const statusTagBGColorObj = {
	CREATE: '#e6f1fb',
	waitReply: '#e9f9fa',
	waitSubmit: '#efefef',
	reject: '#fbeeee',
}

// 看板视图 item
const TaskCardViewItem: React.FC<ITaskCardViewProps> = ({
	itemInfo,
	handleOnCheckedChange
}) => {

const handleCheckTaskItem = (e: CheckboxChangeEvent, item: ItemProps) => {
	handleOnCheckedChange(e.target.checked, item)
}

	return (
		<div className="card_col" key={itemInfo.code}>
			{/* project status title & icon */}
			<IconTitle isImportant icon={itemInfo?.icon as any}>{itemInfo.name}</IconTitle>
			
			{
				itemInfo.dataList?.length ? itemInfo.dataList.map((item: ItemProps, index: number) => (
					<div 
						key={item.projectId} 
						className="card_item" 
						title="查看任务详情" 
						onClick={() => history.push(`/my-project-detail/${item.projectId}?taskId=${item.taskId}`)}
						
					>
						{/* item title */}
						<div className="title_wrapper">
							<Checkbox className="project_checkbox" onChange={(e: CheckboxChangeEvent) => handleCheckTaskItem(e, item)} />
							<div className="title_info">{item.projectName}</div>
							{/* 随机显示颜色 */}
							<span className="user" style={{ backgroundColor: ['#f0ad4e', '#428bca', '#23c6c8'][Math.floor(Math.random() * 4)] }}>{item.projectManager || '--'}</span>
						</div>
						{/* item.content */}
						<div className="project_content_wrapper">
							{/* icon */}
							<MessageFilled className='message_icon'/>
							<div className="project_text" title={item.projectText}>
								{item.projectText}
							</div>
						</div>
						{/* item desc */}
						<div className="desc_wrapper">
							{/* 项目状态 */}
							{itemInfo.code == 'WAIT_DEAL' && <Tag style={{ color: statusTagColorObj[item.projectStatus] }} color={statusTagBGColorObj[item.projectStatus]}>{item.projectStatusText}</Tag>}
							<div className="desc_content">
								<ApartmentOutlined className="icon" />
								<span 
									title={item.projectDesc}
									className={`desc_text ${itemInfo.code !== 'WAIT_DEAL' && 'long_desc'}`}   // 只有待处理的数据显示项目状态
								>项目：{item.projectDesc}</span>
							</div>
						</div>
					</div>
				)) : <div className='empty_wrapper'>-暂无数据-</div>
			}
		</div>
	)
} 

export default TaskCardViewItem;
