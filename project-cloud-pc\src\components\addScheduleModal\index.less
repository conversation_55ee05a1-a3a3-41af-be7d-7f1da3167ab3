@import "../../styles/base.less";

.add-new-schedule-modal {
  .add-new-schedule-form-button {
    width: 280px;
    padding: 0 16px;
  }
  .add-new-schedule-form-content {
    border-right:1px solid @divider;
    padding-right: 16px;
    width: 567px;
    .input_dom {
      font-size: 18px;
      padding: 0;
      color: #000;
      font-weight: bold;
      position: relative;
      border-bottom: 1px solid #c0c0c0;
      padding-left: 0;
    }
    .ant-form-item {
      margin-bottom: 12px;
      .ant-row.ant-form-item-row .ant-col.ant-form-item-label label::before {
        position: absolute;
        top: 50%;
        transform: translate(0, -50%);
        right: -14px;
        font-family: unset;
      }
  
    }
    .ant-form-item-label {
      padding: 0;
    }
    .date_picker_wrapper {
      border-bottom: 1px solid #c0c0c0;
    }
    .ant-picker-active-bar {
      display: none;
    }
    .schedule-need-remind {
      display: flex;
      align-items: center;
      margin-left: 10px;
      .help-title {
          display: flex;
          justify-content: space-between;
          .input_number_dom {
            width: 30px;
            border-bottom: 1px solid #c0c0c0;
            .ant-input-number-handler-wrap {
              display: none;
            }
            input {
              padding: 0 5px;
              height: 20px;
              width: 30px;
            }
          }
      }
    }
    .remind-input {
      width: 80px;
    }
  }
  .remind-input {
    text-indent: 6px !important;
  }
}
