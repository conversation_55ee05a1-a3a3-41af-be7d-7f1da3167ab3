import React, { useState } from "react";
import { 
  Button, 
  Checkbox, 
  Form, 
  message, 
  Modal, 
  Radio,
  Input,
  DatePicker,
  InputNumber
} from "antd";
import CustomRadio from '@/pages/myProject/createNewProject/CustomRadio';
import { BdwUploadBtn, BdwRow, BdwTextarea,  BdwChooseCompanyStaff } from "@/components";
import { listBusinessAssociationType } from "@/service/projectDos/myProjectApi";
import { listEmpByParams, getCurrentUser } from '@/service/projectDos/commonApi';
import { useRequest } from 'ahooks';
import moment from "moment";
import "./index.less";

export interface IAddScheduleModalProps {
  visible: boolean
  onCancel: () => void
  onSubmit: () => void
}

const RangePicker: any = DatePicker.RangePicker;

// 3种关联类型: 无关联 客户关联 订单关联
export type ScheduleRelevance = "Independent" | "Customer" | "Order"

const AddScheduleModal: React.FC<IAddScheduleModalProps> = (props) => {
  const { data: businessAssociationType } = useRequest(listBusinessAssociationType);//项目业务关联
  const {visible, onCancel, onSubmit} = props;
  const [needRemind, setNeedRemind] = useState<Array<string>>(["need"])

  const [form] = Form.useForm();

  const submitData = () => {
    try {
      const formValues = form.getFieldsValue();

      console.log(formValues, '<<<formValues');
      return

      const {
        name = "",
        introduction = "",
        scheduleTime = [moment(), moment()],
        reminderTime = 2,
        association = "",
        usersArray = [],
        attach = []
      } = formValues;
      // 因为通过了校验，所以scheduleTime一定是有值得
      const [startTime, endTime] = scheduleTime;
      const users = usersArray?.map((item: any) => item.id);

      const period = endTime.diff(startTime, "day") + 1;

      const submitInfo = {
        name,
        introduction,
        startTime: startTime.format("YYYY-MM-DD hh:mm:ss"),
        endTime: endTime.format("YYYY-MM-DD hh:mm:ss"),
        attach,
        id: "",
        period,
        reminder: needRemind.length > 0,
        reminderTime: needRemind.length > 0 ? reminderTime : 0,
        users
      }
      message.success("信息保存成功");
      // onSubmit?.()
    } catch (e: any) {
      message.error(e?.message);
    }
  }

  // cancel click
  const handleCancel = () => {
    form.setFieldsValue({
      name: "",
      introduction: "",
      scheduleTime: null,
      reminderTime: 2,
      association: "",
      usersArray: [],
      attach: []
    })
    onCancel()
  }

  return (
    <Modal 
      title='新增日程' 
      getContainer={false} 
      footer={null} 
      open={visible} 
      width={950}
      destroyOnClose
      onCancel={handleCancel}
    >
      <Form 
        form={form} 
        colon={false}
        layout="vertical"
        onFinish={() => submitData()}
        className='add-new-schedule-modal' 
      >
        <BdwRow type='flex'>
          <div className='add-new-schedule-form-content'>
            <Form.Item label='日程名称' required name='name'>
              <Input maxLength={128} bordered={false} className="input_dom" />
            </Form.Item>
            
            <Form.Item label='日程说明' required name='introduction'>
              <BdwTextarea autoSize  maxLength={1024} />
            </Form.Item>

            <BdwRow type='flex'>
              <Form.Item label='日程持续时间' required name='scheduleTime' className="date_picker_wrapper">
                <RangePicker 
                  showTime
                  bordered={false}
                  style={{ width: 320 }}
                  className="date_picker_dom"
                  placeholder={["日程开始时间", "日程结束时间"]}
                />
              </Form.Item>
              <div className='schedule-need-remind'>
                <Checkbox.Group 
                  value={needRemind} 
                  style={{ marginTop: 25 }}
                  onChange={(checkedValue) => setNeedRemind(checkedValue as string[])}
                >
                  <Checkbox value='need'>
                    <div className='help-title'>
                      <span>任务开始之前</span>
                        {needRemind.length ? 
                          <>
                            <InputNumber maxLength={128} bordered={false} className="input_number_dom" />
                            <span>小时</span>
                          </>
                        : null} 
                      <span>需要提醒我</span>
                    </div>
                  </Checkbox>
                </Checkbox.Group>
              </div>
            </BdwRow>

            {/* 项目业务关联 */}
            <Form.Item label="项目业务关联" name='businessAssociationType' initialValue={"noAssociation"}>
              <CustomRadio businessAssociationType={businessAssociationType as any[]} form={form} />
            </Form.Item>
            {/* 已选项目业务id */}
            <Form.Item hidden name='projectBusinessId'>
              <Input disabled />
            </Form.Item>

            {/* 日程参与人员 */}
            <Form.Item
              required
              label="日程参与人员"
              name='projectMembers'
              rules={[{ required: true, message: "请选择日程参与人员" }]}
            >
              <BdwChooseCompanyStaff apiSrc={listEmpByParams} type='multiple' />
            </Form.Item>

            <Form.Item label="日程附件资料" name='attach'>
              <BdwUploadBtn name="上传" width="78px" />
            </Form.Item>
          </div>
          
          <div className='add-new-schedule-form-button'>
            <div className='width-100'>
              <Button htmlType='submit' className='width-100' type='primary'>保存</Button>
            </div>
            <div className='width-100 mt-16'>
              <Button className='width-100' onClick={handleCancel}>取消</Button>
            </div>
          </div>
        </BdwRow>
      </Form>
    </Modal>
  )
}

export default AddScheduleModal;
