/**
 * @description 按钮
 * <AUTHOR>
 * @date 2023-11-07 16:46:01
*/
import React,{ forwardRef } from 'react';
import {Button} from 'antd';
import './index.less';


interface BdwButtonProps{
    width?: string,
    height?: string,
    mr?: string,
    fs?: string
    mb?: string
    bgColor?: string
    style?: object
}
const WrapComponent = <T extends {}>(WrappedComponent: React.FC<T>) => {
    const ContentComponent = (props: BdwButtonProps & T) => {
        const { width='60px', height = '28px' ,mr='',mb='',fs='12px',bgColor,style,...others} = props;
        return (
            <WrappedComponent style={{
                width,
                height,
                marginRight:mr,
                fontSize:fs,
                marginBottom:mb,
                background:bgColor,
                ...style
            }}  {...others as T}/>
        )
    }
    return ContentComponent;
}

const BdwButton = WrapComponent(Button);
export default BdwButton;