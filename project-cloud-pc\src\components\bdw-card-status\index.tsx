import React from "react";
import "./index.less";

// card显示状态的组件
interface BdwCardStatusProps {
  // 卡片显示状态的主题颜色
  theme?: "default" | "blue" | "red" | "orange" | "green"
  // 卡片样式
  className?: string
  // 文字10px
  f10?: boolean
}

const BdwCardStatus:React.FC<BdwCardStatusProps> = (props) => {
  const {className = "",theme = "default",f10=false} = props
  const f = f10?'f-10':''
  return (
    <span className={`bdw-card-status ${className} ${theme}`}>
      <span className={`${f}`}>{props.children}</span>
    </span>
  )
}

export default BdwCardStatus
