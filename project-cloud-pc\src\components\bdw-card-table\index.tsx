import React, { useMemo, useState } from "react";
import { Pagination, Empty } from "antd";
import { useRequest } from "ahooks";

import "./index.less";
import { PageLoading } from "@ant-design/pro-layout";
import BdwRow from "../bdw-row";

interface BdwCardTableProps {
  // 传入需要搜索的url
  url?: string,
  // table总页数
  pageSize?: number
  // table默认页数
  defaultPage?: number
  // 样式名
  className?: string
  // 列表搜索需要的参数 例如 {name: inputValue}
  extraParams?: object
  // 放快捷操作的区域
  fastButtonRender?: () => React.ReactNode
  // 右侧分页那里可以插入的槽
  rightButtonRender?: () => React.ReactNode
  // 全局搜索留的槽
  globalSearchRender?: () => React.ReactNode
  // 单个卡片渲染
  singCardRender: (requestDataItem: any) => React.ReactNode,
  //api
  api: (data: any) => any
}

// 显示card形式的table,获取到数据，然后渲染由外面的组件来渲染
const BdwCardTable: React.FC<BdwCardTableProps> = (props) => {
  const {
    url,
    fastButtonRender,
    rightButtonRender,
    globalSearchRender,
    pageSize = 10,
    defaultPage = 1,
    className,
    extraParams,
    singCardRender,
    api
  } = props;

  const [currentPage, setCurrentPage] = useState(defaultPage);

  const { loading, refresh, error, data: dataList } = useRequest(() => api({
    page:currentPage,
    count:pageSize,
    ...extraParams,
  }), {
    refreshDeps: [
      api, extraParams, currentPage
    ],
    // 需要url准备好,不为空
    ready: !!api,
    // 这些数据发生变化的时候，才会进行请求
    cacheKey: JSON.stringify({ api, extraParams, pageSize, currentPage }),
    throttleInterval: 200,
    debounceInterval: 200
  });
  const handleResData = useMemo(() => {
    if (dataList && typeof dataList === 'object') {
      return dataList.items
    }
    return []
  }, [dataList])

  // if (loading) {
  //   return (
  //     <PageLoading />
  //   )
  // }

  if (error) {
    return (
      <div>发生了不知情的错误</div>
    )
  }


  const showTotalPaging = (total: string | number): string => {

    return `${(currentPage - 1) * pageSize + 1} 至 ${currentPage * pageSize > total! ? total : currentPage * pageSize} 共计: ${total}`

    // return `共计: ${total}`;
  }

  const pagingChange = (page: number) => {
    setCurrentPage(page)
  }

  // @ts-ignore
  const tableRefresh = async () => {
    await refresh();
  }

  const showPagination = <Pagination total={dataList?.total}
    current={currentPage}
    defaultCurrent={defaultPage}
    onChange={pagingChange}
    size='small'
    showTotal={showTotalPaging} />;

  const cardTableShow = handleResData?.map((item: any, index: number) => {
    return (
      <div key={index}>
        {singCardRender?.(item)}
      </div>
    )
  })

  // @ts-ignore
  return (
    <div className={`bdw-table ${className} bdw-card-table-container`}>
      <BdwRow type='flex' className='bdw-table-header bottom-divider'>
        <div className='bdw-table-control-content'>
          {fastButtonRender?.()}
        </div>
        <div className='bdw-table-search flex-1'>
          {globalSearchRender?.()}
        </div>
        <BdwRow className='bdw-table-paging' type='flex'>
          <div className='bdw-table-extra'>
            {rightButtonRender?.()}
          </div>
          <div className='bdw-table-paging-content'>
            {showPagination}
          </div>
        </BdwRow>
      </BdwRow>
      <BdwRow type='flex' className='bdw-card-table-content flex-wrap'>
        {handleResData.length > 0 ? cardTableShow : <div className='width-100 mt-16'><Empty description='没有数据' /></div>}
      </BdwRow>
    </div>
  )
}

export default BdwCardTable
