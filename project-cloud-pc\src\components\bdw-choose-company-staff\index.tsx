import React, { useState, Key } from "react";
import { Input, ConfigProvider, Empty } from "antd";
import {
  BdwCustomSelectProps,
  BdwCustomSelectSetDataEventProps,
  withBdwCustomSelect
} from "@/components/bdw-custom-select"
import { BdwTable, BdwTableHeaderSearchComponent, BdwRow, BdwIcon, BdwTableButton } from "../index";
import { BdwTableColumn } from '../bdw-table';
import "./index.less"


export interface IHeader {
  id: Key
  phone?: string
  name?: string,
  roleName?: string,
  orgName?: string
}


// 选择公司用户的组件
interface BdwChooseCompanyStaffTableProps {
  // 列表搜索需要的参数 例如 {name: inputValue}
  extraParams?: object
  // 定义要显示的列
  showColumns?: string[]
  // 被选中的项的keys
  selectedRowKeys?: number[]
  // 设置被选中的行的key
  setSelectedRowKeys?: (selectedRowKeys: number[]) => void
  //请求api
  apiSrc: (data: any) => void
  //
  placeholder?: string
}

const BdwChooseCompanyStaffTable: React.FC<BdwCustomSelectSetDataEventProps & BdwChooseCompanyStaffTableProps> = (props) => {
  const { type = "single", setDataEvent, selectedRowKeys, setSelectedRowKeys, extraParams = {}, showColumns = ["name", "phone"],apiSrc } = props;
  const [name, setName] = useState<string>("");
  const [phone, setPhone] = useState<string>("");

  // 保存选中的项
  const [selectDataArrPlus, setSelectDataArrPlus] = useState<any[]>([]);

  // 是否存在已选择的值
  //  useEffect(()=>{
  //    if(filterArrNew){
  //      setSelectDataArrPlus?.(filterArrNew)
  //    }
  //  },[filterArrNew])

  const onRowClick = (record: any) => {
    return {
      onClick: () => {
        setDataEvent?.(record);
      }
    }
  }

  const multiSelectOnRowClick = (record: any) => {
    return {
      onClick: () => {
        // 判断当前点击行是否已经选择过
        const isSelected = selectDataArrPlus?.some((item: any) => {
          return item.id === record.id
        })

        // 没有被选择过
        if (!isSelected) {
          const copyArrSelect = selectDataArrPlus;
          copyArrSelect?.push(record);
          setSelectDataArrPlus(copyArrSelect)
          setDataEvent?.(selectDataArrPlus);
          const ids = copyArrSelect.map((item: any) => item.id)
          setSelectedRowKeys?.(ids)
        }
        // 已经选择过，就取消选择
        if (isSelected) {
          const copyArrSelect = selectDataArrPlus;
          const filterSelectDataArrPlus = copyArrSelect?.filter((item: any) => item.id !== record?.id)
          setSelectDataArrPlus(filterSelectDataArrPlus)
          setDataEvent?.(filterSelectDataArrPlus);
        }
      }
    }
  }

  const checkboxChange = {
    selectedRowKeys,
    // onChange:(id:any, items:any)=>{
    //
    //   setSelectedRowKeys?.(id)
    // },

    onSelect: (record: any, selected: boolean) => {
      if (selected) {
        // 如果选中，则储存起来
        const copyArrSelect = selectDataArrPlus;
        copyArrSelect?.push(record);
        setSelectDataArrPlus(copyArrSelect)
        setDataEvent?.(selectDataArrPlus);
        const ids = copyArrSelect.map((item: any) => item.id)
        setSelectedRowKeys?.(ids)
      }
      if (!selected) {
        // 如果取消选中，则从数组中过滤掉当前数据
        const copyArrSelect = selectDataArrPlus;
        const filterSelectDataArrPlus = copyArrSelect?.filter((item: any) => item.id !== record?.id)
        setSelectDataArrPlus(filterSelectDataArrPlus)
        setDataEvent?.(filterSelectDataArrPlus);
      }
    },
    onSelectAll: (selected: boolean, changeRows: any) => {
      if (selected) {
        // 问题：翻页后，selectedRows 会返回前页的数据，但是前页的数据会变成 undefined
        // 解决：过滤一下，过滤掉 undefined  值
        const copyChangeRows = changeRows?.filter((item: any) => {
          return item !== undefined
        })
        const newCopyArrSelect = selectDataArrPlus.concat(copyChangeRows);

        // 点击全选之前，进行了单个点击选择时，需进行去重
        // 去重

        const obj = {}
        const filterSameRows = newCopyArrSelect.reduce((item, next) => {
          obj[next.id] ? '' : obj[next.id] = true && item.push(next)
          return item
        }, []);

        setSelectDataArrPlus(filterSameRows)
        setDataEvent?.(filterSameRows);
        const ids = filterSameRows.map((item: any) => item.id)
        setSelectedRowKeys?.(ids)
      }
      if (!selected) {
        const allSelectArr = changeRows;
        const newCopyArrSelect = allSelectArr?.filter((item: any) => {
          return item !== undefined
        })
        setSelectDataArrPlus(newCopyArrSelect);
        setDataEvent?.(newCopyArrSelect);
        const ids = newCopyArrSelect?.map((item: any) => item.id)
        setSelectedRowKeys?.(ids)

      }
    }
  }




  const clearAllSearchCondition = () => {
    setName('')
    setPhone('')
  }

  // 自定义空状态
  const emptyStatus = () => {
    return (
      <Empty description={
        <p>根据你输入的
          （
          {name && `【${name}】姓名 `}
          {phone && `【${phone}】联系电话`}
          ）
          关键字未搜到任何记录，请检查输入的搜索关键字
        </p>
      } />
    )
  }

  return (
    type === "single" ?
      (
        <ConfigProvider renderEmpty={emptyStatus}>
          <BdwTable
            api={apiSrc}
            rowKey={(record: any) => record.id}
            className="cover-bdw-table-search-style"
            fastButtonRender={() => {
              return (
                <BdwRow type='flex'>
                  <BdwTableButton
                    className='singleSelectFontSize'
                    icon={() => <BdwIcon type='class' icon='icondelete' />}
                    onClick={() => clearAllSearchCondition()}
                  >清空搜索条件</BdwTableButton>
                </BdwRow>
              )
            }}
            onRow={onRowClick}
            extraParams={
              {
                name,
                phone,
                ...extraParams
              }
            }
          >
            {
              showColumns.includes("name") &&
              <BdwTableColumn width={80} dataIndex='name' title={
                <BdwTableHeaderSearchComponent title='姓名'>
                  <Input className='commonStyle borderBottom input-search-s' placeholder="搜索姓名..." maxLength={20} value={name} onChange={(e) => setName(e.target.value)} />
                </BdwTableHeaderSearchComponent>
              } />
            }
            {
              showColumns.includes("phone") &&
              <BdwTableColumn width={80} dataIndex='phone' title={
                <BdwTableHeaderSearchComponent title='联系手机'>
                  <Input className='commonStyle borderBottom input-search-s' placeholder="搜索联系手机..." maxLength={20} value={phone} onChange={(e) => setPhone(e.target.value)} />
                </BdwTableHeaderSearchComponent>
              } />
            }
          </BdwTable>
        </ConfigProvider>
      ) :
      (
        <ConfigProvider renderEmpty={emptyStatus}>
          <BdwTable
            api={apiSrc}
            rowKey={(record: any) => record.id}
            fastButtonRender={() => {
              return (
                <BdwRow type='flex'>
                  <BdwTableButton
                    className='ml-35'
                    icon={() => <BdwIcon type='class' icon='icondelete' />}
                    onClick={() => clearAllSearchCondition()}
                  >清空搜索条件</BdwTableButton>
                </BdwRow>
              )
            }}
            onRow={multiSelectOnRowClick}
            rowSelection={
              {
                type: 'checkbox',
                ...checkboxChange
              }
            }
            extraParams={
              {
                name,
                phone,
                ...extraParams
              }
            }
          >
            {
              showColumns.includes("name") &&
              <BdwTableColumn width={140} dataIndex='name' title={
                <BdwTableHeaderSearchComponent title='姓名'>
                  <Input className='borderBottom commonStyle input-search-s' placeholder="搜索姓名..." maxLength={20} value={name} onChange={(e) => setName(e.target.value)} />
                </BdwTableHeaderSearchComponent>
              } />
            }
            {
              showColumns.includes("phone") &&
              <BdwTableColumn width={140} dataIndex='phone' title={
                <BdwTableHeaderSearchComponent title='联系手机'>
                  <Input className='borderBottom commonStyle input-search-s' placeholder="搜索联系手机..." maxLength={20} value={phone} onChange={(e) => setPhone(e.target.value)} />
                </BdwTableHeaderSearchComponent>
              } />
            }
          </BdwTable>
        </ConfigProvider>
      )

  )
}


const BdwChooseCompanyStaffLocal = withBdwCustomSelect(BdwChooseCompanyStaffTable);

const BdwChooseCompanyStaff: React.FC<BdwCustomSelectProps & BdwChooseCompanyStaffTableProps> = (props) => {
 
  const chooseEcho = (item: IHeader) => {
    if (item && item.name) {
      return (
        <span>{item.name}</span>
      )
    }
    return (<span/>)
  }
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([])
  return (
    <BdwChooseCompanyStaffLocal
      {...props}
      selectItemRender={chooseEcho}
      selectedRowKeys={selectedRowKeys}
      setSelectedRowKeys={setSelectedRowKeys}
    />
  )
}

export default BdwChooseCompanyStaff
