import React, { useState } from "react";
import { Input } from "antd";
import { withBdwCustomSelect } from "@/components/bdw-custom-select";
import type { BdwCustomSelectProps, BdwCustomSelectSetDataEventProps } from "@/components/bdw-custom-select";
import { loadCustomListByParams } from "@/service/projectDos/commonApi";
import { BdwTable, BdwTableHeaderSearchComponent } from "../index";
import { BdwTableColumn } from '../bdw-table';
import moment from "moment";


// 选择客户的组件
const BdwChooseCustomStaffTable: React.FC<BdwCustomSelectSetDataEventProps> = (props) => {
    const { setDataEvent } = props;
    const [customerName, setCustomerName] = useState<string>("");
    const [phone, setPhone] = useState<string>("");
    const [buildingName, setBuildingName] = useState<string>("");
    const onRowClick = (record: any) => {
        return {
            onClick: () => {
                setDataEvent?.(record);
            }
        }
    }
    return (
        <BdwTable api={loadCustomListByParams} onRow={onRowClick} apiType="post" extraParams={
            {
                isExistBuilding: true,
                customerName,
                buildingName,
                mobilePhone:phone,
            }
        }
        >
            <BdwTableColumn width={100} dataIndex='customerName' title={
                <BdwTableHeaderSearchComponent title='客户姓名'>
                    <Input className='no-border-input' maxLength={128} onChange={(e) => setCustomerName(e.target.value)} placeholder='搜索姓名' />
                </BdwTableHeaderSearchComponent>
            } />
            <BdwTableColumn width={100} dataIndex='mobilePhone' title={
                <BdwTableHeaderSearchComponent title='联系电话'>
                    <Input className='no-border-input' maxLength={128} onChange={(e) => setPhone(e.target.value)} placeholder='搜索联系手机号' />
                </BdwTableHeaderSearchComponent>
            } />
            <BdwTableColumn width={160} dataIndex='buildingName' title={
                <BdwTableHeaderSearchComponent title='楼盘信息'>
                    <Input className='no-border-input' maxLength={255} onChange={(e) => setBuildingName(e.target.value)} placeholder='搜索楼盘' />
                </BdwTableHeaderSearchComponent>
            } />
            <BdwTableColumn width={100} dataIndex='characteristic' title={
                <BdwTableHeaderSearchComponent title='客户类别' />
            } />
        </BdwTable>
    )
}


const BdwChooseOrderStaffLocal = withBdwCustomSelect(BdwChooseCustomStaffTable);

const BdwChooseCustomStaff: React.FC<BdwCustomSelectProps> = (props) => {
    const chooseEcho = (item: { customerName: string, mobilePhone: string, buildingName: string }) => {
        if (item.customerName) {
            return (
                <span>{item.customerName}-{item.mobilePhone}@{item.buildingName?item.buildingName:'暂无房屋'}</span>
            )
        }
        return (<span/>)
    }
    return (
        <BdwChooseOrderStaffLocal {...props} selectItemRender={chooseEcho} />
    )
}

export default BdwChooseCustomStaff
