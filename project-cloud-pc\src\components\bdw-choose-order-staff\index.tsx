import React, {useState} from "react";
import {Input} from "antd";
import { withBdwCustomSelect } from "@/components/bdw-custom-select";
import type { BdwCustomSelectProps,BdwCustomSelectSetDataEventProps } from "@/components/bdw-custom-select";
import {loadOrderPagination} from "@/service/projectDos/commonApi";
import {BdwTable, BdwTableHeaderSearchComponent} from "../index";
import {BdwTableColumn} from '../bdw-table';
import moment from "moment";


// 选择订单的组件
const BdwChooseOrderStaffTable: React.FC<BdwCustomSelectSetDataEventProps> = (props) => {
  const {setDataEvent} = props;
  const [customerName, setCustomerName] = useState<string>("");
  const [phone, setPhone] = useState<string>("");
  const [orderContractNo, setOrderContractNo] = useState<string>("");
  const onRowClick = (record: any) => {
    return {
      onClick: () => {
        setDataEvent?.(record);
      }
    }
  }
  return (
    <BdwTable api={loadOrderPagination} onRow={onRowClick} apiType="post" extraParams={
      {
        customerName,
        phone,
        orderContractNo,
        dateInfo:{
          startTime: moment().subtract(1,'years').format('YYYY-MM-DD'),
          stopTime: moment().format('YYYY-MM-DD')
        }
      }
    }>
      <BdwTableColumn width={100} dataIndex='customerName' title={
        <BdwTableHeaderSearchComponent title='客户姓名'>
          <Input className='no-border-input' maxLength={128} onChange={(e) => setCustomerName(e.target.value)} placeholder='搜索姓名'/>
        </BdwTableHeaderSearchComponent>
      }/>
      <BdwTableColumn width={100} dataIndex='customerPhone' title={
        <BdwTableHeaderSearchComponent title='联系电话'>
          <Input className='no-border-input' maxLength={128} onChange={(e) => setPhone(e.target.value)} placeholder='搜索联系手机号'/>
        </BdwTableHeaderSearchComponent>
      }/>
      <BdwTableColumn width={160} dataIndex='contractNo' title={
        <BdwTableHeaderSearchComponent title='合同号'>
          <Input className='no-border-input' maxLength={255} onChange={(e) => setOrderContractNo(e.target.value)} placeholder='合同号'/>
        </BdwTableHeaderSearchComponent>
      }/>
      <BdwTableColumn width={100} dataIndex='orderSubject' title={
        <BdwTableHeaderSearchComponent title='订单品项'/>
      }/>
    </BdwTable>
  )
}


const BdwChooseOrderStaffLocal = withBdwCustomSelect(BdwChooseOrderStaffTable);

const BdwChooseOrderStaff: React.FC<BdwCustomSelectProps> = (props) => {
  const chooseEcho = (item: { customerName: string, customerPhone: string, orderSubject: string }) => {
    if (item.customerName) {
      return (
        <span>{item.customerName}-{item.customerPhone} @ {item.orderSubject}</span>
      )
    }
    return (<span/>)
  }
  return (
    <BdwChooseOrderStaffLocal {...props} selectItemRender={chooseEcho}/>
  )
}

export default BdwChooseOrderStaff
