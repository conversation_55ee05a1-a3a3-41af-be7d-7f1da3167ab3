import React, {useState} from "react";
import {Input} from "antd";
import { withBdwCustomSelect } from "@/components/bdw-custom-select";
import type { BdwCustomSelectProps,BdwCustomSelectSetDataEventProps } from "@/components/bdw-custom-select";
import {loadOrderPagination} from "@/service/projectDos/commonApi";
import {BdwTable, BdwTableHeaderSearchComponent} from "../index";
import {BdwTableColumn} from '../bdw-table';
import moment from "moment";


// 选择模板的组件
const BdwChooseOrderStaffTable: React.FC<BdwCustomSelectSetDataEventProps> = (props) => {
  const {setDataEvent,api} = props;
  const [templateName, setTemplateName] = useState<string>("");
  const [templateGrade, setTemplateGrade] = useState<string>("");
  const onRowClick = (record: any) => {
    return {
      onClick: () => {
        setDataEvent?.(record);
      }
    }
  }
  return (
    <BdwTable api={api} onRow={onRowClick}  extraParams={
      {
        name:templateName,
        projectLevel:templateGrade,
      }
    }>
      <BdwTableColumn width={100} dataIndex='projectName' title={
        <BdwTableHeaderSearchComponent title='模板名称'>
          <Input className='no-border-input' maxLength={128} onChange={(e) => setTemplateName(e.target.value)} placeholder='搜索模板名称'/>
        </BdwTableHeaderSearchComponent>
      }/>
      <BdwTableColumn width={100} dataIndex='projectLevelName' title={
        <BdwTableHeaderSearchComponent title='模板等级'>
          <Input className='no-border-input' maxLength={128} onChange={(e) => setTemplateGrade(e.target.value)} placeholder='搜索模板等级'/>
        </BdwTableHeaderSearchComponent>
      }/>
    </BdwTable>
  )
}


const BdwChooseOrderStaffLocal = withBdwCustomSelect(BdwChooseOrderStaffTable);

const BdwChooseOrderStaff: React.FC<BdwCustomSelectProps> = (props) => {
  const chooseEcho = (item: { projectName: string, projectLevelName: string }) => {
    if (item.projectName) {
      return (
        <span>{item.projectName} @ {item.projectLevelName}</span>
      )
    }
    return (<div/>)
  }
  return (
    <BdwChooseOrderStaffLocal {...props} selectItemRender={chooseEcho}/>
  )
}

export default BdwChooseOrderStaff
