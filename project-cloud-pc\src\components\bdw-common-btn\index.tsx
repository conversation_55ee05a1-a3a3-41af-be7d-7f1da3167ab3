/**
 * @description 项目详情-图标组件
 * <AUTHOR>
 * @date 2023-11-01 10:01:23
*/
import React from 'react';
import type { ReactNode } from 'react';
import './index.less';

interface BdwCommonBtnProps {
    name?: ReactNode | string,
    icon?: string,
    disabled?: boolean,
    className?: string,
    onClick?: () => void,
    iconRender?: () => any
}


const BdwCommonBtn = <T extends BdwCommonBtnProps>(props: T) => {
    const { name = '按钮', icon, disabled = false, className = '', onClick, iconRender: Render } = props;
    const onHandle = () => {
        if (disabled) return
        onClick?.();
    }
    return (
        <div onClick={onHandle} className={`bdw-common-icon-container ${disabled ? 'disabled' : ''} ${className}`}>
            <div className='bdw-common-icon-button-icon'>
                {
                    Render? <Render/>:
                    disabled ?
                        <span className={`bdw-class-icon ${icon}`} /> :
                        <svg className={`bdw-icon`} aria-hidden="true">
                            <use xlinkHref={`#${icon}`} />
                        </svg>
                }
            </div>
            <div className='btnName'>{name}</div>
        </div>
    )
}
export default BdwCommonBtn