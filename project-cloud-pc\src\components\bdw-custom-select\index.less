@import "../../styles/base.less";

.bdw-custom-select {
  position: relative;
  .custom-placeholder{
    font-size: 13px;
    color: #bfbfbf;
  }
  .bdw-custom-selected-data-store {
    padding-right: 30px;
    min-height: 26px;
    color: @black;
    line-height: 26px;
    border-bottom: 1px solid @border;
    .pointer();
    .f-13();
    position: relative;
  }
  .select-disabled{
    color: rgba(0,0,0,0.25);
    cursor: not-allowed;
    opacity: 1;
  }
  .isSelectedClass{
    color: rgba(0,0,0,0.25);
  }
  .bdw-custom-select-content {
    position: absolute;
    left: 0;
    z-index: 100;
    padding-top: 6px;
    background-color: @white;
    box-shadow: 0 0 8px rgba(0,0,0,0.1);
  }
  .select-multiple-item {
    box-sizing: border-box;
    user-select: none;
    margin: 5px 15px 5px 0;
    background-color: #f5f5f5;
    .name-width{
      padding: 0 5px;
      min-width: 62px;
      text-align: center;
    }
    .selected-close-item {
      padding: 0 6px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      .pointer();
    }
  }
}
