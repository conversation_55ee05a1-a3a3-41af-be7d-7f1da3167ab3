import React, { Key, useEffect, useState } from "react";
import { withClickOutSide } from "@/components/withClickOutSide";
import { CloseOutlined, DownOutlined, UpOutlined } from "@ant-design/icons";
import { useParams, useSelector, useDispatch } from 'umi';
import { useBoolean } from "ahooks";
import BdwRow from "../bdw-row";
import { isEmpty } from "lodash";
// import {judgeArrayHasObject} from "@/utils/utils";
export interface IHeader {
  id: Key
  phone?: string
  name?: string,
  roleName?: string,
  orgName?: string
}

export interface BdwCustomSelectProps {
  // 选择变化的时候触发事件
  onChange?: (needSaveValue: Array<string | object> | object) => void,
  // 回显
  selectItemRender?: (saveDataItem: any) => React.ReactNode,
  // 设置选中的项的key
  setSelectedRowKeys?: (selectedRowKeys: Array<number>) => void
  // 默认是single
  type?: "single" | "multiple",
  // 外面传进来的值
  value?: object | Array<string | object>
  // 选择模板
  chooseTemplate?: (obj: any) => void,
  // contentWidth 代表弹窗需要的宽度
  contentWidth?: Key

  // 是否禁止弹出选择框
  isSelected?: boolean
  // 已经选中的数据
  selectData?: any[]
  api?: (params?: any) => Promise<unknown>
  disabled?: Boolean
  placeholder?: string

}

const HasClickOutSideRow = withClickOutSide(BdwRow);

export interface BdwCustomSelectSetDataEventProps {
  // 选择类型：单选 or 多选
  type?: string
  // 获取选中数据事件
  setDataEvent?: (record: any) => void
  // 过滤选中的数据，去除重复的值
  filterArrNew?: Array<any>
  // 已经选中的数据
  selectData?: any[]
  api?: (params?: any) => Promise<unknown>
  disabled?: Boolean,

}

// 自定义弹出选择值得组件
export const withBdwCustomSelect = <T extends {}>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
  const WithBdwCustomSelectFun: React.FC<BdwCustomSelectProps & T> = (props) => {
    const { type = "single", onChange, selectItemRender, setSelectedRowKeys, chooseTemplate, value, contentWidth, isSelected = true, selectData, disabled, placeholder } = props;
    const [singleSelectData, setSingleSelectData] = useState<object>({});
    const [multipleSelectData, setMultipleSelectData] = useState<Array<object>>([]);
    const [contentIsShow, { setFalse: contentHide, setTrue: contentShow }] = useBoolean(false);
    const [contentShowWidth, setContentShowWidth] = useState<Key>(0);
    const dispatch = useDispatch();

    const [filterArrNew, setFilterArrNew] = useState<Array<any>>(selectData ?? [])

    // 为multiple 时候才会存在删除
    const deleteSelectItem = (obj: object) => {

      const filterArray = multipleSelectData.filter((item: any) => item !== obj);
      const selectedRowKeysArr = filterArray.map((item: any) => item.id)

      setMultipleSelectData(filterArray)
      setSelectedRowKeys?.(selectedRowKeysArr)
      onChange?.(filterArray)

      setFilterArrNew?.(filterArray)

      // setSelectDataArrPlus?.(filterArray)

    }

    const singleSelectedShow = (
      <div>
        {selectItemRender?.(singleSelectData)}
      </div>
    );
    useEffect(() => {
      dispatch({
        type: 'projectTasks/setShowEmList',
        payload: contentIsShow
    })
    },[contentIsShow])

    const multipleSelectedShow = multipleSelectData.map((item: any, index: number) => {
      return (
        <BdwRow key={index} className='select-multiple-item' type='flex'>
          <div className={`flex-1 name-width ${disabled ? 'select-disabled' : ''}`}>
            {selectItemRender?.(item)}
          </div>
          {
            isSelected && !disabled &&
            <div className='selected-close-item' onClick={() => {
              deleteSelectItem(item)
            }}>
              <CloseOutlined style={{ color: "#000", fontSize: '12px' }}></CloseOutlined>
            </div>
          }

        </BdwRow>
      )
    })

    const setDataEvent = (record: any) => {
      if (type === "single") {
        setSingleSelectData(record)
        onChange?.(record)
        contentHide();
      } else {
        // 如果是多选的话，那么就先做个排重
        // 现在传入的是一个数组
        // const repetitionFlag = judgeArrayHasObject(record,multipleSelectData);
        // if(!repetitionFlag) {

        // 翻页时，前面的数据没保存
        onChange?.(record)
        if (selectData) {
          // @ts-ignore
          const newArr = selectData.concat(record)

          setMultipleSelectData(newArr);
          const selectedRowKeysArr = newArr.map((item: any) => item.id)
          setSelectedRowKeys?.(selectedRowKeysArr)
          onChange?.(newArr)
        } else {
          // @ts-ignore
          setMultipleSelectData(record);
          const selectedRowKeysArr = record.map((item: any) => item.id)
          setSelectedRowKeys?.(selectedRowKeysArr)
          onChange?.(record)
        }

        //   }
        // else {
        //     onChange?.(record)
        //     // @ts-ignore
        //     setMultipleSelectData([multipleSelectData,...record]);
        //   }
      }
      chooseTemplate?.(record)
    }

    const isHide = contentIsShow ? "show" : "hide";


    useEffect(() => {
      if (type === "single" && typeof value !== "undefined") {
        setSingleSelectData(value)
      } else if (type === "multiple" && typeof value !== "undefined") {

        setMultipleSelectData(value as IHeader[])
        // @ts-ignore
        const selectedRowKeysArr = value.map((item: any) => item.id)
        setSelectedRowKeys?.(selectedRowKeysArr)
      }
    }, [value])

    // 泛型未找到支持 currentTarget的，所以暂且event用any
    const showRowClickEvent = (e: any) => {
      setContentShowWidth(contentWidth ? contentWidth : e.currentTarget.offsetWidth)
      contentShow()
    }
    return (
      <HasClickOutSideRow className='bdw-custom-select' onClickOutSide={contentHide}>
        <BdwRow
          type='flex'
          className={`bdw-custom-selected-data-store ${disabled ? 'select-disabled' : ''} flex-wrap ${!isSelected ? 'isSelectedClass' : ''}`}
          // @ts-ignore
          onClick={(e: any) => {
            if (disabled) {
              return
            }
            showRowClickEvent(e)
          }}
        >
          {type === "single" ? singleSelectedShow : multipleSelectedShow}

          {/* @ts-ignore */}
          
          {((isEmpty(singleSelectData)?true:(singleSelectData?.id || singleSelectData?.projectId ||  singleSelectData?.orderInfoId ||  singleSelectData?.customerId || singleSelectData?.estateId)?false:true) && (multipleSelectData.length == 0)) &&
            <span className="custom-placeholder">{placeholder}</span>
          }
          <DownOutlined
          style={{
            position: 'absolute',
            right: '10px',
            top: '50%',
            color: '#c0c0c0',
            transform: 'translate(0,-50%)',
          }}
        ></DownOutlined>
          
          
        </BdwRow>
        {
          isSelected && <div className={`bdw-custom-select-content ${isHide}`} style={{ width: `${contentShowWidth}px` }}>
            <WrappedComponent setDataEvent={setDataEvent} filterArrNew={filterArrNew} {...props as T} />
          </div>
        }
      </HasClickOutSideRow>
    )
  };
  return WithBdwCustomSelectFun;
};
