@import "~antd/lib/date-picker/style/index";
@import "../../styles/base.less";

.bdw-line-content {
  .ant-picker {
    color: @black;
    border: none;
    box-shadow: none;
    width: 100%;
    height: 26px;
    line-height: 26px;
    padding: 0px 1px 0 4px;
    box-sizing: border-box;
    background-image: linear-gradient(@primary, @darkPrimary), linear-gradient(@border, @border);
    background-size: 0px 1px, 100% 1px;
    background-repeat: no-repeat;
    background-position: left bottom, left 100%;
    &.ant-picker-focused {
      background-size: 100% 1px, 100% 1px;
      transition: all 0.5s;
    }
    .ant-picker-input {
      input {
        color: @black;
        &::-webkit-input-placeholder {
          color: @placeHolder;
          line-height: 26px;
          .f-12();
        }
      }
    }
    .ant-picker-active-bar {
      display: none;
      opacity: 0;
    }
  }
}

