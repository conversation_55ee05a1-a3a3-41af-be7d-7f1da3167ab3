/**
 * @description 查看文件
 * <AUTHOR>
 * @date 2023-11-07 09:59:41
*/
import React, { useEffect, useState } from 'react';
import BdwRow from '../bdw-row';
import { getToken } from '@/auth';
import { fileDownload } from '@/utils/utils';
import axios from 'axios';
import {
	CloudDownloadOutlined,
	EyeOutlined,
	LoadingOutlined,
	CheckOutlined,
	DeleteOutlined,
	FilePptOutlined,
	FilePdfOutlined,
	FileMarkdownOutlined,
	FileTextOutlined,
	FileWordOutlined,
	FileZipOutlined,
	FileImageOutlined,
	FileExcelOutlined,
	FileJpgOutlined,
	FileUnknownOutlined
} from "@ant-design/icons/lib";
import { Modal } from 'antd';
import FileViewer from 'react-file-viewer';
import { TOKEN_PREFIX, REDIRECT_LOGIN } from '@/constants/token';
import { useBoolean } from 'ahooks';
import './index.less';
import { cloneDeep } from 'lodash';

interface BdwFileShowProps {
    attachments?: any,
    showIcon?: boolean,
    loading?: boolean,
    enableEdit?: boolean,
    width?: string,
    showStatus?: boolean,
    onChange?: (data: any) => void
}

const BdwFileShow: React.FC<BdwFileShowProps> = (props) => {
	const { attachments, showIcon = true, enableEdit = false, loading = false, width = '100%', showStatus = false, onChange } = props;
	const [currentFile, setCurrentFile] = useState<any>(null);
	const onPreView = (i: any) => {
			setCurrentFile(i);
			// setTrue();
	}
	const onDelete = (i: number) => {
			const arr = cloneDeep(attachments);
			arr.splice(i, 1)
			onChange?.(arr);
	}
	const getIcon = (type: string) => {
			const fileType = type?.split('.')[type?.split('.').length - 1];
			switch (fileType) {
					case 'txt':
							return <FileTextOutlined style={{ color: '#626262' }} />
					case 'pdf':
							return <FilePdfOutlined style={{ color: '#626262' }} />
					case 'doc':
					case 'docx':
							return <FileWordOutlined style={{ color: '#626262' }} />
					case 'xlsx':
					case 'xls':
							return <FileExcelOutlined style={{ color: '#626262' }} />
					case 'md':
					case 'xmind':
							return <FileMarkdownOutlined style={{ color: '#626262' }} />
					case 'ppt':
							return <FilePptOutlined style={{ color: '#626262' }} />
					case 'zip':
							return <FileZipOutlined style={{ color: '#626262' }} />
					case 'jpg':
					case 'jpeg':
							return <FileJpgOutlined style={{ color: '#626262' }} />
					case 'bmp':
					case 'png':
					case 'gif':
							return <FileImageOutlined style={{ color: '#626262' }} />
					default:
							return <FileUnknownOutlined style={{ color: '#626262' }} />

			}
	}

	const ModalContent = () => {
		var reader = new FileReader();
		const fileType = currentFile?.fileName?.split('.')[currentFile?.fileName?.split('.').length - 1];
		// return <FileViewer
		// 	fileType={fileType}
		// 	filePath={currentFile?.url}
		// 	// unsupportedComponent={<div className='f-12'><span className='f-weight'>{'.'+currentFile?.fileName}is not support</span></div>}
		// />

		switch (fileType) {
			case 'pdf':
				return <iframe src={currentFile?.url} style={{  width: '100%',height:'100%' }}>
					This browser does not support PDF. Please download the PDF to view.
				</iframe>
			case 'jpeg':
			case 'jpg':
			case 'png':
			case 'gif':
			case 'ico':
				return <div style={{width:'100%',height:'100%',overflow:'auto',display:'flex',alignItems:'center',justifyContent:'center'}}><img src={currentFile?.url} alt={currentFile?.fileName} /></div>
		
			default:
				return <div className='f-12'><span className='f-weight'>{'.'+currentFile?.fileName}暂不支持预览，请下载至本地打开</span></div>
		}
	}
    
    return (
        <div className='file-show-container' style={{ width }}>
            {
                attachments?.map((item: any, index: number) => {
                    return <BdwRow type='flex-center-between' key={item.id+index} className='file-item'>
                        <BdwRow type="flex-center">
                            {showIcon && getIcon(item?.fileName)}
                            <div className='file-name'>{item.fileName}</div>
                        </BdwRow>
                        <BdwRow type="flex" className='operate-btn-icons'>
                            <EyeOutlined style={{ marginLeft: '10px' }} onClick={() => { onPreView(item) }} disabled={loading} />
                            <CloudDownloadOutlined style={{ marginLeft: '10px' }} disabled={loading} onClick={() => { fileDownload(item.url, item.fileName) }} />
                            {
                                enableEdit && <DeleteOutlined onClick={() => { onDelete(index) }} disabled={loading} style={{ marginLeft: '10px' }} />
                            }
                            {
                                (enableEdit && showStatus) ? loading ? <LoadingOutlined style={{ marginLeft: '10px' }} /> : <CheckOutlined style={{ marginLeft: '10px' }}  /> : null
                            }
                        </BdwRow>
                    </BdwRow>
                })
            }
            <Modal
                open={currentFile}
                title={"文件预览：" + currentFile?.fileName}
                footer={null}
                onCancel={() => {
                    setCurrentFile(null);
                }}
				zIndex={1001}
                bodyStyle={{ height:'80vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
				width={1440}
				centered={true}
            >
                <ModalContent />
            </Modal>
        </div>
    )
}
export default BdwFileShow;