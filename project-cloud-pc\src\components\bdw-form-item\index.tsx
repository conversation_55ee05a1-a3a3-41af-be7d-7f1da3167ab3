import React from "react";
import {FormItemProps} from "antd/es/form";
import BdwTitle from "../bdw-title";
import {Form} from "antd";
import BdwRow from "../bdw-row";

interface BdwFormItemProps {
  labelRender?: () => React.ReactNode
  labelAlign?: "top" | "left"
  showRequire?: boolean
}

const BdwFormItem: React.FC<FormItemProps & BdwFormItemProps> = (props) => {
  const {className = "", labelRender,labelAlign = "top" ,label, required,showRequire = true,...other} = props;

  if(labelAlign === "left") {
    return  (
      <BdwRow className={className} type='flex'>
        <div className='bdw-form-item-label mr-16'>
          {label ? <BdwTitle required={required} showRequire={showRequire}>{label}</BdwTitle> : ''}
          {labelRender?.()}
        </div>
        <div className='bdw-form-item-content flex-1'>
          <Form.Item required={required} {...other}>
            {props.children}
          </Form.Item>
        </div>
      </BdwRow>
    )
  }

  return (
    <div className={className}>
      {label ? <BdwTitle required={required} showRequire={showRequire}>{label}</BdwTitle> : ''}
      {labelRender?.()}
      <Form.Item required={required} {...other}>
        {props.children}
      </Form.Item>
    </div>
  )
}

export default BdwFormItem;

const BdwForm = Form;
export {BdwForm};
