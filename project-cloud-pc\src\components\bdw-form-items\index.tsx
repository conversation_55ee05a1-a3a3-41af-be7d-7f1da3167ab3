/**
 * @description 表单组件
 * <AUTHOR>
 * @date 2023-10-27 16:29:58
*/
import React from 'react';
import './index.less';
import { Form } from 'antd';

interface BdwFormItemProps {
    width?: string
    className?: string
}
const { Item } = Form;

const BdwFormItems = <T extends {}>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
    const BdwFormItem: React.FC<BdwFormItemProps & T> = (props) => {
        const { width = 'unset',className = '', ...other } = props;
        return (
            <div style={{ width }} className={`form-Item-container ${className}`} >
                <WrappedComponent {...other as T} colon={false}>
                    {props.children}
                </WrappedComponent>
            </div>

        )
    }
    return BdwFormItem;
}
export default BdwFormItems(Item);