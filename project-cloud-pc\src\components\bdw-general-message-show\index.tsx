import React, { Key, useContext, useState } from "react";
import "./index.less"
// import {BdwPictureShow} from "@/components";
import moment from "moment";
import { BdwFileShow, BdwUploadBtn, BdwFormItems, BdwInput, BdwReadonlySpan, BdwRow } from "../index";
import { DOWNLOAD_FILE, GET_FILE_INFO_URL, UPLOAD_FILE_URL } from "@/service/attachment/upload";
import { Button, Form, message, Popconfirm, Popover } from "antd";
import { deleteTaskReportReply, deleteTaskReport, taskReportReply } from '@/service/projectDos/my-project-detail/projectTasks';
import { useBoolean } from "ahooks";
import { useSelector } from 'umi';
import session from "@/session";
import { isObject } from "lodash";


interface BdwGeneralMessageShowProps {
  // 日报数据集合
  generalMessageList?: DayReportDataItem[]
  // 取消回复
  canReply?: boolean
  replyFun?: (id: string | number) => void
  // 日报id
  dayReportId: string | number
  // 回复成功事件
  sureFun: () => void
  // 会话框宽度
  contentWidth?: Key
}

interface GeneralMessageReportsItem {
  //日志说明
  content: string
  // 日志图片信息id集合类型String
  documents: string[]
}

export interface DayReportDataItem {
  // 日报提交人name
  userName: string
  // 日报数据
  projectTaskReportDetails: GeneralMessageReportsItem[]
  // 日报创建时间
  time: string
  // 回复信息的集合
  reportReplies: ReplyItem[]
  // 日报id
  reportId: string | number
}

interface ReplyItem {
  // 日报id
  replyId?: number
  // 日报提交人name
  userName: string
  // 日报时间
  time: string
  // 回复意见
  content: string
  // 回复附件资料上传文件id集合类型String
  documents: any[]
}
// 已经填写的日报内容展示并且可以回复日报信息
const BdwGeneralMessageShow: React.FC<BdwGeneralMessageShowProps> = (props) => {
  const { generalMessageList = [], canReply, replyFun, dayReportId, sureFun, contentWidth } = props;
  const { editable, taskInfo, isAddTask } = useSelector((state: any) => state.projectTasks);
  // 是否为项目负责人
  const isProjectHeader = true;
  // 是否为项目创建人
  const isProjectCreator = true;

  const taskArr: any = []
  const getParentTasks = function (task?: any) {
    return [];
    if (task) {
      if (task.parent) {
        getParentTasks(task.parent)
        taskArr.push(task.parent)
      }
    }
    return taskArr
  }
  const taskList = getParentTasks();
  const filterHeaderId = taskList?.filter((item: any) => {
    return item.assigned === "Accept"
  })


  const [contentShowWidth, setContentShowWidth] = useState<Key>(0);
  // 定义控制回复弹窗显示的布尔值和记录当前日报ID的变量
  const [replyShowFlag, { setTrue: replyShow, setFalse: replyHide }] = useBoolean(false)
  const [replyShowNum, setReplyShowNum] = useState<string | number>()

  const generalDetailShow = (reports: GeneralMessageReportsItem[]) => {
    return reports && reports.map((item, index) => {
      let content = '';
      try{
        content = isObject(JSON.parse(item.content))?JSON.parse(item.content).value:item.content
      }catch{
        content = item.content
      }
      console.log(content)
      return (
        <div className='bdw-general-detail-item' key={index}>
          <div>
            <span>{index + 1}、</span>
            <span>{content}</span>
          </div>
          {
            item.documents && item.documents.length > 0 &&
            <div>
              {/* <BdwPictureShow fileIds={item.attach}/> */}
              <BdwFileShow attachments={item.documents} />
            </div>
          }
        </div>
      )
    })
  }

  // 日报回复作废
  const reportReplyDelete = async (id: number | string) => {
    try {
      await deleteTaskReportReply(id)
      message.success('日报回复作废成功！')
      // 更新日报展示数据，刷新回复记录
      sureFun();
    } catch (e) {
      message.error(e.message)
    }
  }

  const showReplyMessage = (replys: ReplyItem[]) => {
    return replys && replys.map((item, index: number) => {
      return (
        <div className='reply-message-item' key={index}>
          <BdwRow type='flex' className='reply-message-item-title'>
            <div className='flex-1 reply-message-item-title-name'>
              {item.userName}
            </div>
            <div className='reply-message-item-title-time mr-10'>
              {item.time && moment(item.time).format("YYYY-MM-DD HH:mm")}
            </div>
            {
              true &&
              <div className='reply-message-item-title-delete'>
                {/*  增加日报回复内容作废功能 */}
                <Popconfirm
                  placement="bottomRight"
                  title="是否确认作废此日报回复？"
                  onConfirm={() => reportReplyDelete(item.replyId!)}
                  trigger="click"
                >
                  <span className="can-click">作废</span>
                </Popconfirm>
              </div>
            }
          </BdwRow>
          <div className='reply-message-item-msg'>
            {item.content}
          </div>
          {item.documents && item.documents.length > 0 &&
            <div className='reply-message-file-content'>
              <BdwFileShow attachments={item.documents} />
            </div>
          }
        </div>
      )
    })
  }

  // 泛型未找到支持 currentTarget的，所以暂且event用any
  const replyReportBtnClick = (e: any, id: string | number) => {
    replyHide()
    // 没有传入弹窗宽度值时，设置为回复按钮的爷爷级元素的宽度
    setContentShowWidth(contentWidth ? contentWidth : e.currentTarget.parentElement.parentElement.offsetWidth)
    replyFun?.(id);
    // 更新控制回复弹窗显示的两个变量的值
    replyShow();
    setReplyShowNum(id)
  }

  const [form] = Form.useForm();
  const cancelReply = () => {
    // 还原表单
    form.resetFields();
    replyHide()
    // 更新日报展示数据，刷新回复记录
    sureFun();
  }

  const reply = async () => {
    form.validateFields().then(async () => {
      try {
        const formValues = form.getFieldsValue();
        const { replyContent, document } = formValues;
        const data = await taskReportReply({
          reportId: dayReportId,
          replyContent,
          document: document?.map((item: any) => item.id).join(),
          taskId: taskInfo.taskId
        })
        message.success("日报回复成功")
        // 还原表单
        form.resetFields();
        // 更新日报展示数据，刷新回复记录
        sureFun();
        // 关闭日报回复弹窗
        replyHide()
      } catch (e) {
        message.error(e.message)
      }
    })
  }


  // 日报作废
  const deleteReport = async (id: number | string) => {
    try {
      await deleteTaskReport(id)
      // await taskStore.init(Number(taskStore.project?.id))
      message.success('日报作废成功！')
      // 更新日报展示数据，刷新回复记录
      sureFun();
    } catch (e) {
      message.error(e.message)
    }
  }



  const contentElement = (
    <div>
      <Form form={form} onFinish={() => reply()}>
        <BdwFormItems label='回复意见' name='replyContent' required rules={[{ required: true, message: '请输入回复意见' }]} >
          <BdwInput maxLength={512} autoFocus onKeyDown={(e) => {
            if (e.keyCode === 13) {
              e.nativeEvent.stopImmediatePropagation()
            }
          }} />
        </BdwFormItems>
        <BdwFormItems label='附件资料' name='document'>
          <BdwUploadBtn />
        </BdwFormItems>
        <div className="mt-16 text-right">
          <Button size="small" className="mr-16" onClick={() => cancelReply()}>取消</Button>
          <Button htmlType='submit' size="small" type='primary'>确认回复</Button>
        </div>
      </Form>
    </div>
  )

  const dayReportDataListShow = generalMessageList.map((item, index) => {
    const canInvalid = true;
    return (
      <div className='bdw-general-message-item' key={index}>
        <div className='bdw-general-message-name'>
          {item.userName}
        </div>
        <div className='bdw-general-message-content mt-5'>
          {generalDetailShow(item.projectTaskReportDetails)}
        </div>
        <BdwRow className='bdw-general-message-time-and-button mt-16' type='flex'>
          <div className='flex-1 bdw-general-message-time'>
            {item.time}
          </div>
          <div className='bdw-general-message-reply-button'>
            {
              canReply &&
              <Popover
                open={replyShowNum === item.reportId && replyShowFlag}
                title={<BdwReadonlySpan importantLevel="important">日报回复</BdwReadonlySpan>}
                content={contentElement}
                placement="bottomRight"
                overlayStyle={{ width: `${contentShowWidth}px` }}
                trigger="click"
              >
                <span className="mr-10" onClick={(e) => replyReportBtnClick(e, item.reportId)}>回复</span>
              </Popover>
            }
            {/* 增加日报作废功能 ：权限：1.当前日报提交人 2. 当前日报还未回复过 */}
            {
              // canInvalid &&
              <Popconfirm
                placement="bottomRight"
                title="是否确认作废此日报？"
                onConfirm={() => deleteReport(item.reportId)}
                trigger="click"
              >
                <span>作废</span>
              </Popconfirm>
            }
          </div>
        </BdwRow>
        {item.reportReplies && item.reportReplies.length > 0 &&
          <div className='bdw-general-reply-message-content'>
            {showReplyMessage(item.reportReplies)}
          </div>
        }
      </div>
    )
  })

  return (
    <div>
      {dayReportDataListShow}
    </div>
  )
}

export default BdwGeneralMessageShow
