import React from 'react';

interface BdwIconProps {
  icon: string;
  disabled?: boolean;
  type?: 'class' | 'symbol' | 'img';
  className?: string;
  style?: object;
}

import './index.less';

const BdwIcon: React.FC<BdwIconProps> = props => {
  const {
    icon,
    disabled = false,
    type = 'symbol',
    className = '',
    style,
  } = props;

  if (disabled) return null;
  return (
    <>
      {type === 'symbol' && (
        <svg className={`bdw-icon ${className}`} aria-hidden="true">
          <use xlinkHref={`#${icon}`} />
        </svg>
      )}
      {type === 'class' && (
        <span className={`bdw-class-icon ${icon} ${className}`} />
      )}
      {type === 'img' && (
        <span
          className={`bdw-img-icon ${className}`}
          style={style as React.CSSProperties}
        />
      )}
    </>
  );
};

export default BdwIcon;
