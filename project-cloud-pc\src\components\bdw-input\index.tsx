/**
 * @description input框
 * <AUTHOR>
 * @date 2023-10-27 15:10:22
*/
import React,{forwardRef} from 'react';
import './index.less';
import BdwRow from '@/components/bdw-row';
import { Input } from 'antd';


const BdwInput = <T extends any>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
    const BdwInputCustom: React.FC<T> = (props) => {
        // @ts-ignore
        const {cref,...others} = props;
        return <div className='bdw-input-container'>
            {/* @ts-ignore */}
            <WrappedComponent bordered={false} ref={cref} autoComplete='off' {...others as T} />
        </div>
    }
    return forwardRef((props: T,ref)=>{
        //@ts-ignore
        return <BdwInputCustom {...props} cref={ref}></BdwInputCustom>
    });
}
export default BdwInput(Input);