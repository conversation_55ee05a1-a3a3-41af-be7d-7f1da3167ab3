import React from "react";

import "./index.less"
import ReactZ<PERSON> from "react-zmage";
import { CloseOutlined } from "@ant-design/icons/lib";
import { Popconfirm } from "antd";

interface BdwLeaveMessageReadonlyProps {
  // 附件id集合
  fileList: any[]
  value: string
  className?: string
  // 当前数据标识
  itemIndex?: number
  // 任务信息
  task?: any
  // 更新当前任务信息的方法
  updateTask?: (data: []) => void
  // 能否删除信息
  canDelete?: boolean
}
// 日报、相关资料等信息的只读组件
const BdwLeaveMessageReadonly: React.FC<BdwLeaveMessageReadonlyProps> = (props) => {
  const { fileList, value, className = "", itemIndex, task, updateTask, canDelete = true } = props;

  const taskRemarkList = task?.taskExplain ?? [];
  const fileListShow = fileList?.map((item: any, index: number) => {
    return (
      <ReactZmage key={index} backdrop='rgba(0,0,0,0.7)' src={item.url} alt={item.name} />
    )
  })

  // 删除任务说明
  const deleteTaskRemark = async () => {
    if (!task?.taskId) {
      return;
    }
    // 由于没有每条任务信息没有id,暂时使用 index下标进行过滤
    const filterTaskRemark = taskRemarkList?.filter((item: any, index: number) => {
      return index !== itemIndex
    })

    updateTask?.(filterTaskRemark);
  }

  return (
    <div className={`bdw-leave-message-readonly-box ${className}`}>
      <div className='bdw-leave-message-content-show'>
        {value}
      </div>
      <div className='bdw-leave-message-picture-show'>
        {fileListShow}
      </div>

      {
        canDelete &&
        <Popconfirm
          title="确认删除此任务说明？"
          okText="确认"
          cancelText="取消"
          onConfirm={deleteTaskRemark}
        >
          <CloseOutlined style={{ 'fontSize': '10px' }} className="delete-has-upload-textarea-btn" title="删除" />
        </Popconfirm>
      }

    </div>
  )
}

export default BdwLeaveMessageReadonly
