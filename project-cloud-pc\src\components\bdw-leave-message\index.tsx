import React, { useCallback, useEffect, useRef, useState } from "react";


import { withClickOutSide } from "@/components/withClickOutSide";
import { useBoolean } from "ahooks";
import { saveBatch } from '@/service/projectDos/commonApi';
import TextArea from "antd/es/input/TextArea";
import ReactZmage from "react-zmage";
import BdwRow from "../bdw-row";
import "./index.less";

interface BdwPastePictureProps {
  // 获取值
  setValue?: any
  onChange?: (value: any) => void
  // 任务说明数据
  value?: BdwLeaveMessageValue
  // 更新当前任务的回调方法
  updateTask?: any
  // 当前任务信息
  task?: any
  // 保存时间
  saveFun?: any
}

interface BdwLeaveMessageValue {
  // 任务说明：说明内容
  value?: string
  // 附件（图片）集合
  fileList?: string[]
}

const ClickOutsideRow = withClickOutSide(BdwRow)
// 高阶组件，传入textarea，得到带粘贴图片功能的组件。
export const withBdwPastePicture = <T extends {}>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
  const WithBdwPastePictureFun: React.FC<BdwPastePictureProps & T> = (props) => {
    const { onChange, value, setValue, updateTask, task, ...other } = props;
    const [fileList, setFileList] = useState<any[]>([])
    const [active, { setFalse, setTrue }] = useBoolean(false);
    const [textAreaValue, setTextAreaValue] = useState<string>("");
    const component = useRef<any>();

    const addFile = useCallback((fileUploadResult: object) => {
      setFileList([...fileList, fileUploadResult])
      onChange?.({
        value: textAreaValue,
        fileList: [...fileList, fileUploadResult].map((item: any) => ({
          url:item.url,
          name: item.fileName,
          id: item.id
        }))
      })
    }, [fileList, textAreaValue])

    const pastePicture = useCallback(async (e: any) => {
      const { clipboardData } = e;
      const items = clipboardData.items || [];
      const saveFileList = [];
      if (items && items.length) {
        for (let i = 0; i < items.length; i += 1) {
          saveFileList.push(items[i].getAsFile());
        }
      }
      const savePictureListFormData = new FormData();
      if (saveFileList && saveFileList.length > 0) {
        saveFileList.forEach((item) => {
          if (item) {
            savePictureListFormData.set('files', item);
            savePictureListFormData.set('isPublic', 'true');
          }
        })
      } else {
        // 剪切板没得图片就算了，不执行后面了
        return
      }

      if (savePictureListFormData.getAll("files").length === 0) {
        return
      }

      const resData = await saveBatch(savePictureListFormData);
      addFile(resData[0])
    }, [addFile])
    // 删除已经上传的图片
    const deleteHasUploadPicture = (index: number) => {
      const copyFileList = [...fileList];
      copyFileList.splice(index, 1);
      setFileList(copyFileList);
      onChange?.({
        value: textAreaValue,
        fileList: copyFileList
      })
    }
    // 已经上传的图片展示
    const showPasteArray = fileList.map((item: any, index: number) => {
      return (
        <div className='leave-message-paste-image' key={index}>
          <ReactZmage backdrop='rgba(0,0,0,0.7)' key={index} alt='粘贴图片' src={item.url} />
          <span className='image-delete-icon' title='删除图片' onClick={() => deleteHasUploadPicture(index)}>X</span>
        </div>
      )
    })

    const textAreaValueChange = useCallback((e: any) => {
      setTextAreaValue(e.target.value);
      onChange?.({
        value: e.target.value,
        fileList: fileList.map((item: any) => ({
          url:item.url,
          name: item.fileName,
          id: item.id
        }))
      })
    }, [textAreaValue, fileList])

    useEffect(() => {
      if (active) {
        document.addEventListener("paste", pastePicture)
        return () => {
          document.removeEventListener('paste', pastePicture)
        }
      }
      return () => {
      };
    }, [active, pastePicture])

    useEffect(() => {
      if(value) {
        if(value.value || value.fileList?.length) {
          setTextAreaValue(value.value ?? "");
          setFileList(value.fileList ?? []);
          onChange?.({
            value: value.value ?? "",
            fileList: value.fileList ?? []
          })
        }
      }
    },[JSON.stringify(value)])

    return (
      <div className='bdw-paste-picture-content' ref={component}>
        <div>
          <ClickOutsideRow className='bdw-paste-picture-wrapper-content' onClickOutSide={setFalse} onClick={setTrue}>
            <WrappedComponent value={textAreaValue} onChange={textAreaValueChange} autoSize {...other as T} />
          </ClickOutsideRow>
          {
            showPasteArray.length > 0 &&
            <BdwRow type='flex' className='bdw-paste-picture-show-content flex-wrap'>
              {showPasteArray}
            </BdwRow>
          }
        </div>
      </div>
    )
  };
  return WithBdwPastePictureFun;
};

// 可通过formItem获取值
// 返回值为对象， {value: ,fileList}
export default withBdwPastePicture(TextArea)
