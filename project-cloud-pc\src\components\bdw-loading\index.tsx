/**
 * @description 加载
 * <AUTHOR>
 * @date 2023-10-31 16:15:25
*/
import React from 'react';
import './index.less';
import { Spin } from 'antd';
 
interface BdwLoadingType{
    tip?: string
}


const BdwLoading: React.FC<BdwLoadingType> = (props: BdwLoadingType) => {
    const {tip='保存中~'} = props
    return (
        <div className='create-loading'>
            <div className='create-loading-save'>
                <Spin />
                <span>{tip}</span>
            </div>
        </div>
    )
}
export default BdwLoading;