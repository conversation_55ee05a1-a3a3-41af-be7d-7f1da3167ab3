import React from "react";
// @ts-ignore
import LogoImg from "@/assets/image/wego_Software_logo_blue.png"

import "./index.less";

interface BdwLogoParams {
  // 公司名称
  companyName?: string
}
const BdwLogo: React.FC<BdwLogoParams> = () => {
  return (
    <div className='logo-content'>
      <div className='logo-image'>
        <img src={LogoImg} alt=""/>
      </div>
    </div>
  )
}

export default BdwLogo


// 注释掉显示公司名称
// <div className='company-name'>
//   {companyName}
// </div>


