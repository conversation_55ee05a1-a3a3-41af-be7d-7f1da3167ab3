import React from "react";
import {Pop<PERSON>,<PERSON><PERSON>} from "antd";

import "./index.less"

const withBdwSelectParams = <T extends {}> (WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
  const withBdwParamsFun:React.FC<T> = (props) => {
    return (
      <div className='bdw-popover'>
        <WrappedComponent {...(props as T)}>
          <Button className='bdw-popover-button'>
            {props.children}
          </Button>
        </WrappedComponent>
      </div>
    )
  };
  return withBdwParamsFun;
};

const BdwPopover = withBdwSelectParams(Popover)

export default BdwPopover
