import React from "react";
import "./index.less";

interface ReadonlySpanProps {
  importantLevel?: "normal" | "important" | "veryImportant"
  className?: string
  needBorder?: boolean
}

const BdwReadonlySpan:React.FC<ReadonlySpanProps> = (props) => {
  const {className,importantLevel = 'normal', needBorder = false} = props;
  const hasBorder = needBorder ? "has-border" : "no-border";
  return (
    <div className={`bdw-readonly-span ${className} ${importantLevel} ${hasBorder}`}>
      {props.children}
    </div>
  )
}

export default BdwReadonlySpan
