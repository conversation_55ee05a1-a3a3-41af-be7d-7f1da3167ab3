import React from 'react';
import './index.less';

interface BdwRowProps {
  type?: 'flex' | 'flex-between' | 'flex-center-between' | 'flex-center';
  className?: string;
  onClick?: () => void;
}

const BdwRow: React.FC<BdwRowProps> = props => {
  const { type, className, ...other } = props;
  const getClassName = (t?: string) => {
    if(t){
      return 'bdw-'+t;
    }else{
      return '';
    }
  }
  return (
    <div className={`${className || ''} ${getClassName(type)}`} {...other}>
      {props.children}
    </div>
  );
};

export default BdwRow;
