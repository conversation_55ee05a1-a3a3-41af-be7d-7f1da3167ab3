import React from 'react';
import { Button } from 'antd';

import './index.less';
import { ButtonProps } from 'antd/es/button';

export interface BdwTableButtonProps extends ButtonProps {
  icon?: () => React.ReactNode
  className?: string
}

const BdwTableButton: React.FC<BdwTableButtonProps> = (props) => {
  const { className = '', icon, ...other } = props;
  return (
    <div className={`bdw-table-button-content ${className}`}>
      <Button {...other} className='bdw-table-button'>
        <span className='bdw-table-button-icon'>
          {props.icon?.()}
        </span>
        <span className='bdw-table-button-content'>
          {props.children}
        </span>
      </Button>
    </div>
  );
};

export default BdwTableButton;

