

@import '../../styles/base.less';
.bdw-table-search-box {
  .bdw-table-search-title {
    box-sizing: border-box;
    height: 30px;
    padding: 4px 0 5px 0;
    border-bottom: 1px solid @divider;
  }

  .bdw-table-search-frame {
    box-sizing: border-box;
    height: 28px !important;
    position: relative;
    .ant-select-selector {
      padding-left: 0;
      text-indent: 0;
      .ant-select-selection-item {
        font-size: 13px;
      }
    }
    .ant-input{
      position: absolute;
      left: 0;
      top: 0;
      border: none;
      border-bottom: 1px solid @divider;
    }
  }

  .no-border {
    border: none;
    outline: none;
    box-shadow: none;
    text-indent: 0;
    &.ant-input {
      padding-left: 0;
    }
    &:focus {
      box-shadow: none;
    }
  }
}
