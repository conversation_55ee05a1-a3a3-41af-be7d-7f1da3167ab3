import React from 'react';
import './index.less';

interface BdwTableHeaderSearchProps {
  title?: string,
  customStyle?: any
}

const BdwTableHeaderSearchComponent: React.FC<BdwTableHeaderSearchProps> = (props) => {
  const {customStyle={}} = props;
  return (
    <div className='bdw-table-search-box'>
      <div className='bdw-table-search-title'>{props.title}</div>
      <div className='bdw-table-search-frame' style={{...customStyle}}>
        {
          props.children ? props.children : <div />
        }
      </div>
    </div>
  );
};

export default BdwTableHeaderSearchComponent;
