import React from "react";

import "./index.less";
import BdwRow from "../bdw-row";

interface BdwTableSearchWrapperInterface {
  label?: string
}

const BdwTableSearchWrapper: React.FC<BdwTableSearchWrapperInterface> = (props) => {
  return (
    <BdwRow type='flex' className='bdw-table-search-wrapper'>
      <div className='bdw-table-search-label'>
        {props.label} ：
      </div>
      <div className='bdw-table-search-input'>
        {props.children}
      </div>
    </BdwRow>
  )
}

export default BdwTableSearchWrapper
