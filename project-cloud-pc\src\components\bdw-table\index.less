@import "../../styles/base.less";

.bdw-table-header {
  padding-bottom: 10px;

  .ant-pagination-item {
    display: none;
  }

  .ant-pagination-jump-next {
    display: none;
  }

  .ant-pagination-options {
    display: none;
  }

  .ant-pagination-jump-prev {
    display: none;
  }
}
.occur-error-tips{
  text-align: center;
  margin-top: 20px;
}

.bdw-table-search {
  width: 300px;
  margin-right: 20px;
  margin-left: 60px;
}

.bdw-table {
  .ant-table-content ,.ant-table-header ,.ant-table-body{
    padding: 5px;
    table {
      border-collapse: collapse;

      .ant-table-thead{
        tr {
          th {
            color: @help;
            padding: 4px 6px !important;
            // text-indent: 6px;
            background-color: #fff;
            border-top: 1px solid @divider;
            border-bottom: 1px solid @divider;
            .f-13();

            .ant-table-selection {
              text-indent: 0;
            }
          }
        }
      }

      .ant-table-tbody {
        tr {
          &:hover {
            background-color: @crosswalkHover;
          }

          td {
            padding: 6px !important;
            color: @content;
            background-color: #fff;
            border-top: 1px solid @divider;
            border-bottom: 1px solid @divider;
            .f-13();
          }
        }
      }
    }
  }

  .bdw-table-extra {
    line-height: 26px;
  }

  .bdw-table-current-show-data {
    line-height: 27px;
    font-size: 13px;
  }

  .bdw-table-paging-content {
    .ant-pagination-total-text {
      line-height: 26px;
      font-size: 13px;
    }

    .ant-pagination-prev {
      line-height: 28px;
    }

    .ant-pagination-next {
      line-height: 28px;
    }
  }

  .ant-table-row {
    cursor: pointer;

    &:hover {
      td {
        background-color: @crosswalkHover !important;
      }
    }

  }
}
