import React, { useMemo, useState, useEffect } from "react";
import { Pagination, Table } from "antd";
import BdwRow from "../bdw-row";
import { useRequest } from "ahooks";

import "./index.less";

interface BdwTableProps {
  url?: string,
  useLocalData?: boolean
  pageSize?: number
  defaultPage?: number
  className?: string
  extraParams?: any
  fastButtonRender?: () => React.ReactNode
  rightButtonRender?: () => React.ReactNode
  globalSearchRender?: () => React.ReactNode
  dataSource?: object[]
  transformDataFun?: (item: any) => any
  pagination?: boolean,
  api?: (data: any) => any
  apiType?: 'get' | 'post',
  showPages?: boolean,
  configPage?: boolean
}

const widthBdwParams = <T extends {}>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
  const WidthBdwParamsFun: React.FC<BdwTableProps & T> = (props) => {
    const {
      url,
      dataSource,
      fastButtonRender,
      rightButtonRender,
      globalSearchRender,
      useLocalData = false,
      pageSize = 10,
      defaultPage = 1,
      className = "",
      extraParams,
      transformDataFun,
      pagination = true,
      api,
      showPages = true,
      apiType = 'get',
      configPage = false,
      ...other
    } = props;

    const [currentPage, setCurrentPage] = useState(defaultPage);

    const pData = apiType == 'get' ? { ...extraParams } : { data: extraParams }
    // @ts-ignore
    const { loading, refresh, error, data: getTableData } = useRequest(() => {
      if (pData.name && pData.statusCode) {
        if(!configPage){
          // 有参数设置为请求第一页
          for(let key in pData){
            if(pData[key]){
              setCurrentPage(1);
              break;
            }
          }
        }
      }
      return api?.({
        page: currentPage,
        count: pageSize,
        ...pData
      })
    }, {
      refreshDeps: [
        currentPage, JSON.stringify(extraParams)
      ],
      // 需要url准备好,不为空
      ready: !!api,
      // 这些数据发生变化的时候，才会进行请求
      cacheKey: JSON.stringify({ api, extraParams, pageSize, currentPage }),
      throttleInterval: 200,
      debounceInterval: 200
    });

    const handleTableData: object[] = useMemo(() => {
      if (getTableData && typeof getTableData === "object") {
        // if(transformDataFun && typeof transformDataFun === "function") {
        //   return getTableData.gridModel.map(transformDataFun)
        // }
        return getTableData.items
      }
      return []
    }, [getTableData])

    if (error) {
      console.error(error)
      return (
        <div className="occur-error-tips">发生了不知情的错误，请刷新重试！</div>
      )
    }
    // 如果是使用localData,那么就不请求。直接处理传进来的dataSource
    const tableData = (useLocalData ? dataSource : handleTableData) || []

    const handleDataSource = tableData.map((item: any, index: number) => {
      return { ...item, key: index }
    })

    const showTotalPaging = (total: string | number): string => {
      return `共计: ${total}`;
    }

    const showCurrentPageData = (
      <span className='mr-5'>{`${(currentPage - 1) * pageSize + 1} 至 ${currentPage * pageSize > getTableData?.total! ? getTableData?.total : currentPage * pageSize}`}</span>
    )

    const pagingChange = (page: number) => {
      setCurrentPage(page);
    }

    // @ts-ignore
    const tableRefresh = async () => {
      await refresh();
    }
    const showPagination = (useLocalData || !pagination) ? "" :
      <Pagination total={getTableData?.total}
        current={currentPage}
        defaultCurrent={defaultPage}
        onChange={pagingChange}
        size='small'
        showTotal={showTotalPaging} />;

    return (
      <div className={`bdw-table ${className}`}>
        <BdwRow type='flex-between' className='bdw-table-header'>
          <div className='bdw-table-control-content'>
            {fastButtonRender?.()}
          </div>
          {
            globalSearchRender && <div className='bdw-table-search flex-1'>
              {globalSearchRender?.()}
            </div>
          }
          <BdwRow className='bdw-table-paging' type='flex'>
            <div className='bdw-table-extra'>
              {rightButtonRender?.()}
            </div>
            <div className='bdw-table-current-show-data'>
              {showPages && showCurrentPageData}
            </div>
            <div className='bdw-table-paging-content'>
              {showPagination}
            </div>
          </BdwRow>
        </BdwRow>
        {/* @ts-ignore */}
        <WrappedComponent loading={loading} dataSource={handleDataSource} pagination={false} {...(other as T)} />
      </div>
    )
  };
  return WidthBdwParamsFun;
};

const { Column } = Table;
export const BdwTableColumn = Column;

const BdwTable = widthBdwParams(Table);
export default BdwTable

