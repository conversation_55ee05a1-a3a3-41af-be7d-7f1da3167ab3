import request from "umi-request"

interface TableGetDataParams {
  url?: string
  pageSize: number
  currentPage: number
  extraParams?: object
}

export interface OldTableResult {
  gridModel: object[],
  page: number,
  record: number,
  rows: number,
  total: number
}

export const getOldTableDataByUrl = async (params: TableGetDataParams): Promise<OldTableResult> => {
  const {url, pageSize, currentPage, extraParams = {}} = params;
  return request(url!, {
    method: "GET",
    params: {
      rows: pageSize,
      page: currentPage,
      ...extraParams,
    }
  })
}