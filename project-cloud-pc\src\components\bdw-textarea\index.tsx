/**
 * @description 文本域
 * <AUTHOR>
 * @date 2023-11-01 13:34:46
*/
import React from 'react';
import './index.less';
import { Input } from 'antd';


const {TextArea} = Input;
const BdwTextarea = <T extends {}>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
    const BdwTextareaCustom = (props: T) => {
        return (
            <div className='bdw-textarea-container'>
                    <WrappedComponent style={{fontSize:'13px'}} autoSize={true} bordered={false} {...props as T} />
                    <div className='input-textArea-icon'>
                        <div className='input-textArea-icon-line' />
                    </div>
            </div>
        )
    }
    return BdwTextareaCustom;
}
export default BdwTextarea(TextArea);