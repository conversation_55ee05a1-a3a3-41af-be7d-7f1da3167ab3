import React from "react";
import "./index.less"

interface BdwTitleProps {
  required?: boolean
  showRequire?: boolean
  className?: string
  importantLevel?: "normal" | "important" | "veryImportant"
}

const BdwTitle: React.FC<BdwTitleProps> = (props) => {
  const {required = false,showRequire = true,className = "", importantLevel= "normal"} = props;
  const isRequired = required && showRequire ? "required" : ""
  return (
    <span className={`bdw-title ${isRequired} ${className} ${importantLevel}`}>
      {props.children}
    </span>
  )
};

export default BdwTitle
