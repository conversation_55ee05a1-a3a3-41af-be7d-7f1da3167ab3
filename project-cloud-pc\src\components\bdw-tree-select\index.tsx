/**
 * @description 树选择
 * <AUTHOR>
 * @date 2023-11-21 15:29:51
*/
import React from 'react';
import './index.less';
import { TreeSelect } from 'antd';


const BdwTreeSelect = <T extends {}>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
    const Content = (props: T) => {
        return (
            <WrappedComponent {...props as T} />
        )
    }
    return Content;
}
export default BdwTreeSelect(TreeSelect);
