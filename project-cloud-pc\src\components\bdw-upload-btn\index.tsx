/**
 * @description 
 * <AUTHOR>
 * @date 2023-11-03 09:26:51
*/
import React, { useState } from 'react';
import { Button, Upload } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { TOKEN_PREFIX, REDIRECT_LOGIN } from '@/constants/token';
import { getToken } from '@/auth';
import BdwFileShow from '../bdw-file-show';
// import { saveBatch } from '@/service/projectDos/commonApi';
import { UPLOAD_FILE_URL } from '@/constants/Enum';
import UploadImg from '@/assets/image/shangchuanshangchuan.svg';
import './index.less';
import { cloneDeep } from 'lodash';

interface BdwUploadBtnProps {
    name?: string,
    onChange?: (data: any) => void,
    value?: any,
    icon?: boolean,
    color?: string
    fontSize?: string
    width?: string
    height?: string
    mt?: string
    mb?: string
    uploadType?: 'multiple' | 'single'
    getType?: 'id' | 'obj'
}
const headers = {
    companyCode: '20150323',
    Authorization: TOKEN_PREFIX + getToken()?.access_token,
}

const BdwUploadBtn = <T extends {}>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
    const UploadBtn: React.FC<BdwUploadBtnProps & T> = (props) => {
        const [loading, setLoading] = useState(false);
        const [file, setFile] = useState<any>(null);
        const { name = '本地上传', onChange, getType = 'obj', value = [], icon = true, children, color = '#5c5c5c', uploadType = 'multiple', fontSize = '13px', width = '108px', height = '28px', mt = '6px', mb = '0px', ...other } = props;
        const uploadChange = (info: any) => {
            const temValue = cloneDeep(value);
            if (info.file.status === 'uploading') {
                setLoading(true);
                setFile([{
                    fileName: info.file.name,
                    id: '1',
                    url: ''
                }])
            } else {
                setLoading(false);
                setFile(null);
            }
            if (info.file.status === 'done') {
                if (uploadType == 'multiple') {
                    if (getType == 'id') {
                        temValue.push(info.file.response.data[0].id)
                    } else {
                        temValue.push(info.file.response.data[0])
                    }
                    onChange?.(temValue);

                } else {
                    if (getType == 'id') {
                        onChange?.(info.file.response.data[0].id);
                    } else {
                        onChange?.(info.file.response.data[0]);
                    }
                }
            }
            if (info.file.status === 'removed') {
                if (uploadType == 'multiple') {
                    let rv: any = null;
                    if (getType == 'id') {
                        rv = temValue.filter((item: any) => item != info.file.response.data[0].id);
                    } else {
                        rv = temValue.filter((item: any) => item.id != info.file.response.data[0].id);
                    }
                    onChange?.(rv);
                } else {
                    onChange?.(undefined);
                }
            }
        }
        const onDelete = (data: any) => {
            if (getType == 'id') {
                onChange?.(data.map((i: any) => i.id));
            } else {
                onChange?.(data);
            }
        }
        return (
            <div className='bdw-upload-btn-container'>
                <>
                    {children}
                    <WrappedComponent action={UPLOAD_FILE_URL} headers={headers} maxCount={uploadType == 'single' ? 1 : null} name='files' onChange={uploadChange} showUploadList={false} {...other as T}>
                        <Button icon={icon && <div style={{overflow:'hidden'}}><img
                            src={UploadImg}
                            style={{
                                width: '16px',
                                height: '14px',
                                marginRight:'8px',
                                transform: 'translateX(-100px)',
                                filter: `drop-shadow(100px 0px ${color})`,
                                overflow: 'hidden',
                            }}
                            alt=""
                        /></div>} style={{ color, fontSize, width, height, marginTop: mt, marginBottom: mb, display: 'flex', alignItems: 'center',boxShadow:'unset' }} >{name}</Button>
                    </WrappedComponent>
                    {value && value.length >= 1 && <BdwFileShow width={'350px'} showStatus={true} onChange={onDelete} enableEdit={true} attachments={value} />}
                    {file && loading && <BdwFileShow width={'350px'} showStatus={true} loading={loading} enableEdit={true} attachments={file} />}
                </>
            </div>
        )
    }
    return UploadBtn;
}
export default BdwUploadBtn(Upload);