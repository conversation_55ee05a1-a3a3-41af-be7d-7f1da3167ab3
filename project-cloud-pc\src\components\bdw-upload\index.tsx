import React, { useEffect, useState } from "react";

import "./index.less";
import { beforeUploadExamineFormat, getBase64 } from "@/utils/utils";
import { useBoolean, useUpdateEffect } from "ahooks";
import { LoadingOutlined, PlusOutlined, CloseOutlined } from "@ant-design/icons";
import { saveBatch } from '@/service/projectDos/commonApi';
import { message, Upload } from "antd";
import { UPLOAD_FILE_URL } from "@/service/attachment/upload";
import { cloneDeep } from 'lodash';
import BdwFileShow from '../bdw-file-show';

interface BdwSinglePictureUploadProps {
  // 文件上传地址
  action?: string
  // 上传文件变化是的回调函数
  onChange?: (fileIds: string) => void
  // 允许上传的文件格式
  accept?: string
  // 上传按钮的名字
  buttonName?: string
  // 默认显示的文件
  defaultFile?: BdwUploadFile
  value?: string,
  uploadType?: 'picture' | 'other'
  uploadMore?: boolean
}

interface BdwUploadFile {
  id: string | number
  uid: string
  name: string
  url: string
}

const withSinglePictureUploadParams = <T extends {}>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
  const WithSinglePictureUploadParamsFun: React.FC<BdwSinglePictureUploadProps & T> = (props) => {
    const { value, defaultFile, action = UPLOAD_FILE_URL, onChange, buttonName = "", accept = ".png,.jpg,.jpeg,.gif,.bmp", uploadType = 'picture', uploadMore = false, ...other } = props;
    const [imageUrl, setImageUrl] = useState<any[]>([]);
    const [loading, { setFalse: setLoadingFalse, setTrue: setLoadingTrue }] = useBoolean(false);
    const [file, setFile] = useState<any>(null);
    // 执行格式检查
    const beforeUpload = (file: any) => {
      if (accept == '.') {
        return true;
      }
      return new Promise((resolve, reject) => {
        const examineResult = beforeUploadExamineFormat(file, accept);
        if (examineResult) {
          resolve(file)
        }
        reject(new Error("上传的文件格式有问题，请检查"));
      })
    }


    const uploadButton = (
      <div>
        {loading ? <LoadingOutlined /> : <PlusOutlined style={{ fontSize: '24px', fontWeight: 'bold', color: '#ddd' }} />}
        <div className='ant-upload-text'>{buttonName}</div>
      </div>
    )
    // 自定义上传
    const customRequest = ({ file }: any) => {
      setFile([{
        fileName: file.name,
        id: '1',
        url: ''
      }])
      setLoadingTrue()
      const formData = new FormData();
      formData.set('files', file);
      saveBatch(formData).then((res) => {
        setFile(null);
        setLoadingFalse();
        if (uploadType == 'picture') {
          getBase64(file, (url: any) => {
            const obj = { ...res[0], base64Url: url }
            if (uploadMore) {
              const imageUrls = cloneDeep(imageUrl);
              imageUrls.push(obj);
              setImageUrl(imageUrls);
            } else {
              setImageUrl([obj]);
            }
          })
        } else {
          if (uploadMore) {
            const imageUrls = cloneDeep(imageUrl);
            imageUrls.push(res[0]);
            setImageUrl(imageUrls);
          } else {
            setImageUrl([res[0]]);
          }
        }
        message.success('上传成功！')
      }).catch((err) => {
        setLoadingFalse();
        setFile(null);
        message.error('上传失败！')
      })
    }

    useEffect(() => {
      if (defaultFile && defaultFile.url && defaultFile.id) {
        setImageUrl(defaultFile.url)
        onChange?.((defaultFile.id).toString())
      }
    }, [JSON.stringify(defaultFile)])
    useUpdateEffect(() => {
      onChange?.(imageUrl.map(i => i.id).join());
    }, [imageUrl])

    const onDelete = (i: any) => {
      setImageUrl(i);
    }


    return (
      <div className='bdw-single-picture-upload'>
        {
          (imageUrl.length > 0 ? uploadMore : true) && <WrappedComponent accept={accept} customRequest={customRequest} listType='picture-card' className='avator-uploader' showUploadList={false} {...other as T} beforeUpload={beforeUpload} >
            {uploadButton}
          </WrappedComponent>
        }
        <div>
          {
            imageUrl.length > 0 && <BdwFileShow width={'350px'} showStatus={true} onChange={onDelete} enableEdit={true} attachments={imageUrl} />
          }
          {file && loading && <BdwFileShow width={'350px'} showStatus={true} loading={loading} enableEdit={true} attachments={file} />}
        </div>
      </div>
    )
  }
  return WithSinglePictureUploadParamsFun
}


export default withSinglePictureUploadParams(Upload)
