.customTable {
    .ant-table-thead > tr > th {
        background-color: #eee;
        color: #616a6f;
        padding: 6px 16px;
    }
    .ant-table-tbody .ant-table-cell {
        padding: 6px 16px;
    }
    .ant-table-tbody > tr.ant-table-row:hover > td, .ant-table-tbody > tr > td.ant-table-cell-row-hover {
        background: #ebf4fc!important;
    }
    // 由于第一行为过滤条件行，故需对第一行的复选框和展开按钮做隐藏
    // 隐藏全选与第一行的checkbox
    .ant-table-thead > tr > th .ant-table-selection, .ant-table-tbody tr:nth-child(1) td .ant-checkbox-wrapper {
        // TODO 样式待调整
        // display: none;
    }
    // 隐藏列表第一行的展开按钮
    .ant-table-tbody tr:nth-child(1) td:nth-child(2) .ant-table-row-expand-icon {
        display: none;
    }
    // 当前激活行
    .active_row {
        background-color: #ebf4fc;
    }
}