import React from "react";
import { Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import type { TableRowSelection } from 'antd/es/table/interface';
import './index.less';

interface ICustomTableProps {
	rowKey: string
	tableLoading?: any // 列表loading
	isRowSelection: boolean  // 是否开启复选框
	columns: ColumnsType<any>  // 表格列
	currentTableRowInfo?: any
	onRowClick?: (clickItem: any) => void // 列是否点击可选
	onSelectRow?: (record: Object, selected: boolean, selectedRows: any[]) => void; // 列选择时
	data: any[]   // 表格数据
	selectedRowKeys?: React.Key[]
}


const CustomTable: React.FC<ICustomTableProps> = ({
	rowKey,
	tableLoading,
	isRowSelection,
	columns,
	data,
	currentTableRowInfo,
	onRowClick,
	onSelectRow,
	selectedRowKeys
}) => {
	// 列选择
	const rowSelection: TableRowSelection<any> = {
		checkStrictly: false,  // 选中父节点时，是否同时选中该节点下的所有子节点
		selectedRowKeys,
		onChange: (selectedRowKeys, selectedRows) => {
			console.log(selectedRowKeys, '<<<选中的列key', selectedRows, '<<<选中的列信息');
		},
		onSelect: (record, selected, selectedRows) => {
			onSelectRow?.(record, selected, selectedRows)
			// console.log(record, '<<<record', selected ,'<<<<selected', selectedRows, '<<<<selectedRows');
		},
		onSelectAll: (selected, selectedRows, changeRows) => {
			console.log(selected, '<<<selected', selectedRows, '<<<selectedRows', changeRows, '<<<changeRows');
		}
	}

	return (
		<Table
			loading={tableLoading}
			className="customTable"
			columns={columns}
			dataSource={data}
			pagination={false}
			rowKey={rowKey}
			// 设置选中行背景色
			rowClassName={(record) => ((currentTableRowInfo && record.taskId == currentTableRowInfo?.taskId) ? 'active_row' : '' )}
			onRow={(record, index) => {
				if (onRowClick) {
					return {
						onClick: event => {
							// 第0行搜索行不可选中
							if (index) {
								onRowClick(record)
							}
						}
					}
				} else {
					return {}
				}
			}}
			rowSelection={isRowSelection && rowSelection || undefined}
		/>
	)
}

export default CustomTable;
