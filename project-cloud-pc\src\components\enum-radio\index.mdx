---
name: 【form】表单 - 传入对象，生成单选的radio
route: /components/enum-radio
order: -1
sidebar: true
---

import EnumRadio from "./";
import {Props, Playground} from 'docz';
import BdwFormItem,{BdwForm} from "../bdw-form-item";
import enumTestData from "./testData"

## 【form】表单 - 根据工作日类型选择时间

### 引入

```
import {EnumRadio} from "bdw-react-components"
```

### 基本使用
<Playground>
    <BdwForm>
        <BdwFormItem label="测试" required name="test">
            <EnumRadio enumObj={enumTestData} />
        </BdwFormItem>
    </BdwForm>
</Playground>