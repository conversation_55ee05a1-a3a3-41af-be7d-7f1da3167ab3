import React from "react";
import {Radio} from "antd";

const {Group} = Radio


interface EnumRadioProps {
  enumObj: {
    [key: string]: any
  }
}

const withEnumRadio = <T extends {}>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
  const WithEnumRadiosFun: React.FC<EnumRadioProps & T> = (props) => {
    const {enumObj, ...other} = props;
    const enumRadios = Object.keys(enumObj).map((key) => {
      return (
        <Radio key={key} value={key}>{(enumObj[key]).toString()}</Radio>
      )
    })
    return (
      <WrappedComponent {...other as T}>
        {enumRadios}
      </WrappedComponent>
    )
  };
  return WithEnumRadiosFun
}

export default withEnumRadio(Group)
