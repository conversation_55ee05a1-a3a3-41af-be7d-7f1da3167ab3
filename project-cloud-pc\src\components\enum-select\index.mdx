---
name: 【form】表单 - 传入对象，生成select
route: /components/enum-select
order: -1
sidebar: true
---

import EnumSelect,{BdwEnumSelect} from "./";
import {Props, Playground} from 'docz';
import BdwFormItem,{BdwForm} from "../bdw-form-item";
import selectTestData from "./testData"

## 【form】表单 - 传入对象，生成select

### 引入

```
import {EnumSelect} from "bdw-react-components"
import {BdwEnumSelect} from "bdw-react-components"
```

### 基本使用
<Playground>
    <BdwForm>
        <BdwFormItem label="基于ant-design的select封装" required name="test">
            <EnumSelect style={{width: "100%"}} enumList={selectTestData} />
        </BdwFormItem>
        <BdwFormItem label="基于bdw-select 封装" required name="name">
            <BdwEnumSelect enumList={selectTestData} />
        </BdwFormItem>
    </BdwForm>
</Playground>