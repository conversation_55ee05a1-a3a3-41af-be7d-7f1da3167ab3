import React from "react";
import BdwSelect from "../bdw-select";
import {Select} from "antd";

interface EnumSelectProps {
  enumList: {
    [key: string]: any
  }
}

const withEnumOptions = <T extends {}>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
  const WithEnumOptionsFun: React.FC<EnumSelectProps & T> = (props) => {
    const {enumList = {}, ...other} = props;
    const enumOptions = Object.keys(enumList).map((key: string) => {
      return (
        <Select.Option key={key} value={key}>{(enumList[key]).toString()}</Select.Option>
      )
    })
    return (
      <WrappedComponent {...other as T}>
        {enumOptions}
      </WrappedComponent>
    )
  };
  return WithEnumOptionsFun
}

export default withEnumOptions(Select)

const BdwEnumSelect = withEnumOptions(BdwSelect)
export {BdwEnumSelect}
