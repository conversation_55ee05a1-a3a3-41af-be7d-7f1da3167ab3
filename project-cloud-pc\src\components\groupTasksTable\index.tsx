import React, { useState } from 'react';
import { DatePicker, Input, Menu, Tooltip, Select } from 'antd';
import { withKeydown } from '@/components/withKeydown';
import { withClickOutSide } from '@/components/withClickOutSide';
import TableRowInformation from '@/pages/my-project-detail/projectTasks';
import styled from 'styled-components';
import Highlighter from "react-highlight-words";
import { useParams, useSelector, useDispatch } from 'umi';
import { CaretDownOutlined} from '@ant-design/icons';
import { 
	moveDownApi,
	moveUpApi, 
	deleteTasksApi, 
	listTaskFunctionality, 
	downgradeTask, 
	upgradeTask,
	getTaskInfo
} from "@/service/projectDos/my-project-detail/projectTasks";
import { 
	BdwTable, 
	BdwTableHeaderSearchComponent, 
	BdwRow, 
	EditableContent 
} from "@/components";
import { disabledFlag } from '@/pages/my-project-detail/projectTasks'
import moment from 'moment';
import { useBoolean } from 'ahooks';
import { RightMenuBtn } from '@/constants/Enum'
import { isObject, filter } from 'lodash';
import './index.less';

const IndexSpan = styled.span`
 font-weight: bold;
 font-size: 13px;
 margin-right: 10px;
`;
const NotSupport = styled.div`
  cursor: not-allowed;
  width: 100%;
  height: 100%;
`
const FixedMenu = withClickOutSide(styled(Menu)`
  position: fixed;
  z-index: 1000;
  box-shadow: 0 0 5px rgba(0,0,0,0.1) !important;
`);
const WrapperComponent = withKeydown(BdwRow);

interface Position {
    x: number,
    y: number
}
interface GroupTasksTableProps {

}

const ContextMenu: React.FC<{ top: number, left: number, visible: boolean, hide: () => void }> = (props) => {
	const dispatch = useDispatch();
	const { projectId } = useParams<{ projectId: string }>();
	const { functionality, taskInfo, editable } = useSelector((state: any) => state.projectTasks);
	
	// 点击事件
	const onHandleMenuItem = (type: string) => {
		switch (type) {
			case 'ADD_NEW_CHILD_TASK'://添加子任务
				dispatch({
					type: 'projectTasks/addProjectTask',
					payload: true
				})
				listTaskFunctionality({ projectId }).then((res: any) => {
					dispatch({
						type: 'projectTasks/setFunctionality',
						payload: res
					})
				})
				break;
			case 'ADD_NEW_TASK'://添加同级任务
				dispatch({
					type: 'projectTasks/addProjectTask',
				})
				listTaskFunctionality({ projectId }).then((res: any) => {
					dispatch({
						type: 'projectTasks/setFunctionality',
						payload: res
					})
				})
				break;
			case 'EDIT'://编辑
				dispatch({
					type: 'projectTasks/setEditable',
					payload: true
				})
				break;
			case 'MOVE_UP'://上移
				moveUpApi(taskInfo.taskId).then(() => {
						getTaskInfo(taskInfo.taskId).then((res)=>{
							dispatch({
								type: 'projectTasks/fetchTaskList',
								payload: projectId,
								changeType: 'move',
								taskId: taskInfo.taskId,
								successRes: res
							})
						})
				})
					break;
			case 'MOVE_DOWN'://下移
					moveDownApi(taskInfo.taskId).then(() => {
						getTaskInfo(taskInfo.taskId).then((res)=>{
							dispatch({
								type: 'projectTasks/fetchTaskList',
								payload: projectId,
								changeType: 'move',
								taskId: taskInfo.taskId,
								successRes: res
							})
						})
					})
					break;
			case 'UPGRADE'://升级
					upgradeTask(taskInfo.taskId).then(() => {
						dispatch({
							type: 'projectTasks/fetchTaskList',
							payload: projectId,
							changeType: 'move',
							taskId: taskInfo.taskId
						})
					})
					break;
			case 'DOWNGRADE'://降级
					downgradeTask(taskInfo.taskId).then(() => {
						dispatch({
							type: 'projectTasks/fetchTaskList',
							payload: projectId,
							changeType: 'move',
							taskId: taskInfo.taskId
						})
					})
					break;
			case 'DELETE'://删除任务
				deleteTasksApi(taskInfo.taskId).then(() => {
					dispatch({
						type: 'projectTasks/fetchTaskList',
						payload: projectId,
						changeType: 'delete'
					})
				})
				break;
		}
		props.hide();
	}

	const FixedMenuItem = filter(RightMenuBtn.map((item: any) => {
		if (disabledFlag(functionality, item.key)) {
			return null
		} else {
			return {
				key: item.key,
				label: <div
					className='bdw-menu-item'
					onClick={() => {
						onHandleMenuItem(item.key)
					}}
				>{item.name}</div>
			}
		}
	}), v => v)
		
	return <FixedMenu
		onClickOutSide={props.hide}
		style={{
			top: `${props.top > 400 ? props.top - 130 : props.top}px`,
			left: `${props.left}px`,
			display: props.visible ? 'block' : 'none'
		}}
		items={FixedMenuItem}
	/>
}

const GroupTasksTable: React.FC<GroupTasksTableProps> = ({

}) => {
	const dispatch = useDispatch();
	const { taskLists, taskInfo, isAddTask, editable, expandedRowKeys, filter: filterObj, functionality } = useSelector((state: any) => state.projectTasks);
	const { projectId } = useParams<{ projectId: string }>();

	const [showToolTip, setShowToolTip] = useState<boolean>(false);
	const [contextPosition, setContextMenuPosition] = useState<Position>({ x: -1000, y: -1000});
	const [contextMenuVisible, { setTrue: showContextMenu, setFalse: hideContextMenu }] = useBoolean(false);

	//是否有编辑权限
	const getEditAuthNew = () => {
		return disabledFlag(functionality, 'EDIT')
	}

	// 任务表格Columns
	const TaskTableColumns = [
		{
			title: <BdwTableHeaderSearchComponent title="进程标题">
				<Input placeholder="搜索..."
					className='no-border-input ant-input-cover-style wordPt'
					value={filterObj?.title}
					onChange={(e) => {
						// dispatch({
						// 	type: 'projectTasks/setFilterTableData',
						// 	typeKey: e.target.value,
						// 	typeName: "title",
						// 	status: e.target.value
						// })
					}}
				/>
			</BdwTableHeaderSearchComponent>,
			dataIndex: 'title',
			ellipsis: true,
			render: function TitleColumn(value: string, record: any) {
				const indexSpan = <span className='task-index-num'><IndexSpan>{record.index?.map((it: any) => it + 1).join('.')}</IndexSpan></span>;
				const renderShow = <span className='task-name-show'>
						<Highlighter
							highlightClassName="title-highlight"
							searchWords={[filterObj?.title]}
							autoEscape
							textToHighlight={record.title ?? ""}
						/>
				</span>
				const renderEditor = (
					<BdwRow type='flex' className='flex-1'>
						<div className='flex-1'>
							{
								isAddTask ?
								<Tooltip title='提示：仅限输入64个字符！' open={showToolTip}>
									<Input
										tabIndex={-1}
										size="small"
										className='task-name-input paddingStyle'
										maxLength={32}
										autoFocus
										onBlur={(e) => { }}
										value={taskInfo?.title}
										onChange={(e) => {
											if (e.target.value.length > 32) {
												setShowToolTip?.(true)
											} else {
												setShowToolTip?.(false)
											}
											dispatch({
												type: 'projectTasks/renewTaskInfo',
												payload: {
													title: e.target.value
												}
											})
										}}
									/></Tooltip> :
								<span>{taskInfo?.title}</span>
							}
							</div>
					</BdwRow>)
				return <BdwRow type='flex'>
					<div>{indexSpan}</div>
					<EditableContent
						editable={editable && record.taskId === taskInfo?.taskId}
						renderShow={renderShow}
						renderEditor={renderEditor}
					/>
				</BdwRow>;
			},
		},
		{
			title: <BdwTableHeaderSearchComponent title="项目名称">
				<Select  
					options={[]}  
					bordered={false}  
					style={{ width: '100%', height: 23 }}  
					suffixIcon={<CaretDownOutlined />} 
					onChange={(val) => console.log(val.target.value, '<<<<val')} 
				/>
			</BdwTableHeaderSearchComponent>,
			dataIndex: 'title',
			width: 300,
			ellipsis: true,
			render: (value: any, record: any) => {
				return <span className='color-5c5c5c f-12 status-span' >{value}</span>
			}
		},
		{
			title: <BdwTableHeaderSearchComponent title="工期(天)">
				{/* <NotSupport /> */}
			</BdwTableHeaderSearchComponent>,
			dataIndex: 'executionCycle',
			width: 180,
			ellipsis: true,
		},
		{
			title: <BdwTableHeaderSearchComponent title="开始时间">
				{/* <DatePicker
					className='datePicker'
					bordered={false}
					suffixIcon={null}
					onChange={date => {
						console.log(moment(date).format('YYYY-MM-DD'))
					}}
				/> */}
			</BdwTableHeaderSearchComponent>,
			dataIndex: 'startTime',
			width: 180,
			ellipsis: true,
			render: (value: any, record: any) => {
				const date = (isObject(value) || (value && value?.indexOf('时') == '-1')) ? moment(value).format("MM-DD HH时") : value;
				const renderEditor = <div>{(isObject(taskInfo?.startTime) || (taskInfo?.startTime && taskInfo?.startTime?.indexOf('时') == '-1')) ? moment(taskInfo?.startTime).format("MM-DD HH时") : taskInfo?.startTime}</div>;
				const renderShow = <div>{date}</div>
				return <EditableContent
					editable={editable && record.taskId === taskInfo?.taskId}
					renderShow={renderShow}
					renderEditor={renderEditor}
				/>
			}
		},
		{
			title: <BdwTableHeaderSearchComponent title="截止日期">
				{/* <DatePicker
					className='datePicker'
					bordered={false}
					suffixIcon={null}
					onChange={date => {

					}}
				/> */}
			</BdwTableHeaderSearchComponent>,
			dataIndex: 'endTime',
			width: 180,
			ellipsis: true,
			render: (value: any, record: any) => {
				const date = (isObject(value) || (value && value?.indexOf('时') == '-1')) ? moment(value).format("MM-DD HH时") : value;
				const renderEditor = <div>{(isObject(taskInfo?.endTime) || (taskInfo?.endTime && taskInfo?.endTime?.indexOf('时') == '-1')) ? moment(taskInfo?.endTime).format("MM-DD HH时") : taskInfo?.endTime}</div>;
				const renderShow = <div>{date}</div>
				return <EditableContent
					editable={editable && record.taskId === taskInfo?.taskId}
					renderShow={renderShow}
					renderEditor={renderEditor}
				/>
			}
		},
		{
			title: <BdwTableHeaderSearchComponent title="状态">
				<Select  
					options={[]}  
					bordered={false}  
					style={{ width: '100%', height: 23 }}  
					suffixIcon={<CaretDownOutlined />} 
					onChange={(val) => console.log(val.target.value, '<<<<val')} 
				/>
				{/* <NotSupport /> */}
			</BdwTableHeaderSearchComponent>,
			width: 180,
			ellipsis: true,
			dataIndex: 'statusName',
			render: (value: any, record: any) => {
				return <span className='color-5c5c5c f-12 status-span' >{value}</span>
			}
		},
		{
			title: <BdwTableHeaderSearchComponent title="日报">
				{/* <NotSupport /> */}
			</BdwTableHeaderSearchComponent>,
			dataIndex: 'process',
			width: 180,
			ellipsis: true,
		},
	];

	return (
		<BdwRow type='flex' className='project-task-table-container'>
			<div className={`table-details ${taskInfo?.taskId ? 'table-details-w' : ''}`}>
				<WrapperComponent>
					<ContextMenu 
						top={contextPosition.y} 
						left={contextPosition.x} 
						visible={contextMenuVisible}
						hide={hideContextMenu} 
					/>
					<BdwTable
						className='project-task-table'
						pagination={false}
						loading={{
							spinning: false
						}}
						expandable={{
							defaultExpandAllRows: true,
							expandedRowKeys,
							onExpandedRowsChange: (e) => {
								// dispatch({
								// 	type: 'projectTasks/setExpandedRowKeys',
								// 	payload: e
								// })
							},
						}}
						size="small"
						rowKey="taskId"
						// @ts-ignore
						columns={TaskTableColumns}
						dataSource={taskLists ?? []}
						useLocalData
						sticky
						scroll={{ x: true }}
						showPages={false}
						rowSelection={{
							selectedRowKeys: taskInfo?.taskId && [taskInfo?.taskId] || [],
							type: 'radio',
							columnWidth: 1,
							renderCell: () => {
								return null;
							},
							checkStrictly: true,
						}}
						// @ts-ignore
						onRow={(task: ITask) => ({
							onClick: async () => {
								// 打开详情

								// if (task.taskId !== taskInfo?.taskId) {
								// 	if ((isAddTask || editable) && taskInfo.title) {
								// 		await dispatch({
								// 			type: 'projectTasks/setSubmitStatus',
								// 			payload: task,
								// 		})
								// 	} else {
								// 		dispatch({
								// 			type: 'projectTasks/fetchTaskInfo',
								// 			payload: task,
								// 			projectId
								// 		})
								// 	}
								// }
							},
							onDoubleClick() {
								// 有编辑权限时 双击才进入编辑状态
								if (getEditAuthNew()) return
								dispatch({
									type: 'projectTasks/setEditable',
									payload: true
								})
							},
							onContextMenu(e) {
								// 有编辑权限时 鼠标右键单击才弹出相应菜单操作栏
								// 禁用鼠标右键默认行为
								e.preventDefault();
								// safe(taskStore.selectTask)(task.uid);
								showContextMenu();
								let y = e.clientY
								let x = e.clientX
								if (document.body.offsetHeight - e.clientY < 300) {
									y = document.body.offsetHeight - 200;
								}
								// @ts-ignore
								if (window.clientWidth - e.clientX < 100) {
									// @ts-ignore
									x = window.clientWidth - 100;
								}
								setContextMenuPosition({
									x,
									y
								});
							},
						})}
					/>
				</WrapperComponent>
			</div>
			
			{/* 列表右侧详情 */}
			{	taskInfo?.taskId && <TableRowInformation />}
		</BdwRow>
	)
}

export default GroupTasksTable;
