import React, { useState } from 'react';
import { 
	BarsOutlined, 
	CaretDownOutlined, 
	AppstoreOutlined
} from '@ant-design/icons';
import { Switch } from 'antd';
import { BdwPopover, IconTitle } from "@/components";
import './index.less';

interface IHeaderBarProps {
	relatedToMeFlag?: boolean  // 是否显示 与我相关 按钮
	relatedMeIsChecked?: boolean // 与我相关显示时，是否选中
	handleRelatedToMeChange?: (checked: boolean) => void
	projectList: any[]
	viewList: any[]
	onViewChange: (checkedView: string) => void
	onProjectChange: (checkedProject: string) => void
}

// TODO icon待替换
const viewIconObj = {
	"TABLE": <BarsOutlined />,
	"CARD": <AppstoreOutlined />,
	"GANTT": <AppstoreOutlined />
}
const viewTitleObj = {
	"TABLE": "列表视图",
	"CARD": "看板视图",
	"GANTT": "甘特图",
}

// 头部项目切换、只看与我相关、视图切换
const HeaderBar: React.FC<IHeaderBarProps> = ({
	relatedToMeFlag,
	relatedMeIsChecked,
	handleRelatedToMeChange,
	projectList,
	viewList,
	onViewChange,
	onProjectChange
}) => {
	
	const [projectTitle, setProjectTitle] = useState<string | null>();  // 项目标题
	const [viewMethod, setViewMethod] = useState<string>("TABLE");  // 视图 TABLE：列表 CARD：卡片

	// 项目下拉框dom渲染
	const projectDropContent = (
		<>
			{
				projectList.map((item) => (
					<div 
						key={item.key}
						title={item.title}
						style={{ cursor: 'pointer', padding: '3px 0' }}
						onClick={() => {
							onProjectChange(item.key)
							setProjectTitle(item.title)
						}}
					>{item.title}</div>
				))
			}
		</>
	)

	// 页面视图下拉框dom渲染
	const viewDropContent = (
		<>
			{
				viewList.map((item) => (
					<div 
						key={item.key} 
						title={item.title}
						onClick={() => {
							onViewChange(item.key)
							setViewMethod(item.key)
						}}
					>
						<IconTitle className='mb-10 pointer' icon={() => item?.icon && item?.icon()}>
							{item.title}
						</IconTitle>
					</div>
				))
			}
		</>
	)

	return (
		<div className="header_bar_wrapper">
			<div className="header_wrapper flex-w">
				{/* 头部项目、视图切换 */}
				{
					projectList.length &&
					<div className='left_wrapper flex_layout'>
						<BdwPopover 
							trigger='hover'
							placement='bottomLeft' 
							content={projectDropContent} 
						>
							<IconTitle 
								className='mr-20' 
								rightIcon={() => <CaretDownOutlined style={{ color: '#9F9F9F' }} />}
							>{projectTitle || '所有项目'}</IconTitle>
						</BdwPopover>

						{/* 只看与我相关 */}
						{
							relatedToMeFlag && 
							<div className='related_wrapper flex_layout'>
								<Switch
									loading={false}
									className='mr-5'
									checked={relatedMeIsChecked} 
									checkedChildren="开启"
									unCheckedChildren="关闭"
									onChange={(checked: boolean) => handleRelatedToMeChange && handleRelatedToMeChange(checked)} />
								<span>只看与我相关</span>
							</div>
						}
					</div> || null
				}


				{/* 右侧下拉框 */}
				{
					viewList.length && 
					<BdwPopover 
						trigger='hover'
						placement='bottomLeft' 
						content={viewDropContent} 
					>
						<IconTitle 
							className='mr-20' 
							icon={() => viewIconObj[viewMethod]}
							rightIcon={() => <CaretDownOutlined style={{ color: '#9F9F9F' }} />}
						>{viewTitleObj[viewMethod]}
						</IconTitle>
					</BdwPopover> || null
				}
			</div>
		</div>
	)
}

export default HeaderBar;
