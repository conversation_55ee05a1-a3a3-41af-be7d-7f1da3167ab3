import React from "react";
import "./index.less";

interface IconTitleProps {
  // 图标，
  icon?: () => React.ReactNode  // 左侧icon
  rightIcon?: () => React.ReactNode  // 右侧icon
  className?: string
  isImportant?: boolean
}

const IconTitle:React.FC<IconTitleProps> = (props) => {
  const {icon, rightIcon, className = "", isImportant = false} = props;
  const isImportantClass = isImportant ? "is-important" : "";
  return (
    <div className={`bdw-icon-title ${className} ${isImportantClass}`}>
      <span className='bdw-icon-title-icon'>
        {icon?.()}
      </span>
      <span className='bdw-icon-title-content'>
        {props.children}
      </span>
      <span className='bdw-icon-title-icon'>
        {rightIcon?.()}
      </span>
    </div>
  )
}

export default IconTitle
