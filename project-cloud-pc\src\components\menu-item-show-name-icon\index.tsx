import React from "react";
import "./index.less";

interface MenuItemShowNameIconProps {
  // 样式名
  className?: string
  // 名称
  name: string
}

const MenuItemShowNameIcon:React.FC<MenuItemShowNameIconProps> = (props) => {
  const {className} = props;
  const colorArray = ["ffa500","C025aa5","fa8072","C23c6c8","C5cb85c","C8a2be2","d9534f","C107a4a","C8b4513","f0e68c","transparent"];
  const colorIndex = props.name ? parseInt(
    props.name.charCodeAt(props.name.length - 1).toString(16)
    .replace(/[^\d.]/g,"")
  ) % 10 : 10
  const color = colorArray[colorIndex]
  return (
    <div className={`menu-item-show-name-icon-content ${className || ""} ${color}`}>
      <span className='menu-item-show-name-icon'>{props.name?.length > 2 ? props.name?.substring(props.name.length - 2,props.name.length) : props.name}</span>
    </div>
  )
}

export default MenuItemShowNameIcon
