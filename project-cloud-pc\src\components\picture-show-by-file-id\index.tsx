import React from "react";
import { useRequest } from "ahooks";
import { getFileById } from "@/service/attachment/upload";

interface PictureGetByIdProps {
  fileId: string
}

const PictureGetById: React.FC<PictureGetByIdProps> = (props) => {
  const { fileId } = props;
  // 根据文件ID请求相应的文件数据进行渲染
  const { data: imgData } = useRequest(() => getFileById(fileId), {
    refreshDeps: [fileId]
  })
  return (
    <img src={imgData && imgData?.url} />
  )
}

export default PictureGetById
