import React, { useEffect, useState } from "react";
import { Button, Form, message, Modal } from "antd";
import { useDispatch } from 'umi'
import { BdwFormItem, BdwInput, BdwUploadBtn, BdwFileShow } from "@/components";
import { DOWNLOAD_FILE, UPLOAD_FILE_URL } from "@/service/attachment/upload";
import { taskReportReply } from '@/service/projectDos/my-project-detail/projectTasks';
import "./index.less";

interface TaskReplyProps {
  taskInfo: any
  visible: boolean
  onCancel: () => void
  onSubmit: () => void
}
interface GeneralMessageReportsItem {
  //日志说明
  content: string
  // 日志图片信息id集合类型String
  documents: string[]
}

// 回复
const TaskReplyModal: React.FC<TaskReplyProps> = ({
  taskInfo,
  visible,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [applyInfo, setApplyInfo] = useState<any>(null)

  useEffect(() => {
    if (taskInfo) {
      dispatch({
        type: 'myTask/fetchTaskApplyInfo',
        payload: {
          taskId: taskInfo.taskId
        },
        onSuccess: (res: any) => {
          setApplyInfo(res)
        }
      })
    }
  }, [taskInfo])

  // 取消
  const cancelReply = () => {
    form.resetFields(); // 还原表单
    onCancel?.();
  }

  // 确认回复
  const reply = async () => {
    form.validateFields().then( async () => {
      try {
        const formValues = form.getFieldsValue();
        const { replyRemark, attachmentFileList } = formValues;

        // 派发回复dispatch
        await taskReportReply({
          reportId: applyInfo.reportId,
          taskId: taskInfo.taskId,
          replyContent: replyRemark,
          document: attachmentFileList?.map((item: any) => item.id).join(),
        })

        // 重置表单
        message.success("日报回复成功")
        form.resetFields();
        onSubmit();
      } catch (e: any) {
        // message.error(e.message)
      }
    })
  }

  const generalDetailShow = (reports: GeneralMessageReportsItem[]) => {
    return reports && reports.map((item, index) => {
      return (
        <div className='bdw-general-detail-item' key={index}>
          <div>
            <span>{index + 1}、</span>
            <span>{item.content}</span>
          </div>
          {
            item.documents && item.documents.length > 0 &&
            <div>
              {/* <BdwPictureShow fileIds={item.attach}/> */}
              <BdwFileShow attachments={item.documents} />
            </div>
          }
        </div>
      )
    })
  }


  return (
    <Modal 
      width={750} 
      title='日报回复' 
      open={visible} 
      getContainer={false} 
      onCancel={cancelReply} 
      footer={[
        <Button key='cancel' onClick={cancelReply}>取消</Button>,
        <Button key='sure' type='primary' onClick={()=>reply()}>确认回复</Button>,
      ]}
    >
      <div className='reply_wrapper'>
        <div className='reply_info'>
          <span className="reply-user-name">{applyInfo?.userName}</span>
          <span className="reply-time">{applyInfo?.time}</span>
        </div>
        <div className='reply_content mt-5'>
          {generalDetailShow(applyInfo?.projectTaskReportDetails)}
        </div>
      </div>
      <Form form={form} onFinish={()=>reply()}>
        <BdwFormItem 
          required 
          label='回复意见' 
          name='replyRemark' 
          style={{ marginBottom: 12 }}
          rules={[{required:true,message:'请输入回复意见'}]} 
        >
          <BdwInput maxLength={512} autoFocus />
        </BdwFormItem>
        <BdwFormItem label='附件资料' name='attachmentFileList'>
          <BdwUploadBtn 
            name="本地上传"
            action={UPLOAD_FILE_URL}  
            // maxSize={200} 
            // downLoadEvent={DOWNLOAD_FILE} 
          />
        </BdwFormItem>
      </Form>
    </Modal>
  )
}

export default TaskReplyModal
