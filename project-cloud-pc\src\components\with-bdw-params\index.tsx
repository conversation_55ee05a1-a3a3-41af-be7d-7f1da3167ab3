import React from 'react';

export interface BdwParamsProps {
  /**
   *  等级
   * */
  importantLevel?: "normal" | "important" | "veryImportant"
}

export const withBdwParams = <T extends {}>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
  const withBdwParamsFun: React.FC<BdwParamsProps & T> = (props) => {
    const { importantLevel = "normal", ...other } = props
    const inputClassName = importantLevel || "";
    return (
      <div className={`bdw-line-content ${inputClassName}`}>
        <WrappedComponent {...(other as T)} autoComplete="off" />
      </div>
    )
  };
  return withBdwParamsFun;
};





