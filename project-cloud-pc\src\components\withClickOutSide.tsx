import React, { useEffect, useRef } from 'react';

interface KeydownProps {
  onClickOutSide?: (e: MouseEvent) => void
}

export function withClickOutSide<T>(Component: React.ComponentType<T>) {

  const ClickOutSideComponent: React.FC<T & KeydownProps> = (props) => {

    const { onClickOutSide, ...otherProps } = props

    const componentRef = useRef<any>();

    useEffect(() => {
      if (componentRef.current) {
        const listener = (e: MouseEvent) => {
          if (!componentRef.current.contains(e.target) && onClickOutSide) {
            onClickOutSide(e)
          }
        }
        document.addEventListener("click", listener)
        return () => {
          document.removeEventListener("click", listener)
        }
      }

      return () => {

      }

    }, [componentRef.current, onClickOutSide])

    return <div className='click-out-side-box' ref={componentRef}><Component {...(otherProps as T)} /></div>;
  };
  return ClickOutSideComponent;
}
