import React, { useEffect, useRef } from 'react';
import { useBoolean } from 'ahooks';
import { withClickOutSide } from '@/components/withClickOutSide';

interface KeydownProps {
  onKeydown?: (e: KeyboardEvent) => void
}

export function withKeydown<T>(Component: React.ComponentType) {

  const ClickOutSideComponent = withClickOutSide(Component);

  const WithKeyDownComponent: React.FC<T & KeydownProps> = ({ onKeydown, ...other }) => {
    const component = useRef<any>();
    const [active, { setFalse, setTrue }] = useBoolean(false);
    useEffect(() => {
      if (onKeydown && active) {
        const listener = (e: KeyboardEvent) => {
          onKeydown(e);
        };

        document.addEventListener('keydown', listener);
        return () => {
          document.removeEventListener('keydown', listener);
        };
      }
      return () => {
      };
    }, [onKeydown, active]);

    return <div className='with-click-out-side-component' ref={component}><ClickOutSideComponent onClickOutSide={setFalse} onClick={setTrue} {...other} /></div>;
  };
  return WithKeyDownComponent;
}
