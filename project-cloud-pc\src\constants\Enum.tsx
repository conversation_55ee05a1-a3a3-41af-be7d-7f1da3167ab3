//项目详情tab
export const myProjectDetailTabs = [
    {
        label: '项目任务',
        key: 'PROJECT_TASKS'
    },
    {
        label: '团队任务',
        key: 'TEAM_TASKS'
    },
    // {
    //     label: '项目日程',
    //     key: 'TEAM_HISTORY'
    // },
    {
        label: '价值评审',
        key: 'VALUE_REVIEW'
    },
    {
        label: '项目概览',
        key: 'PROJECT_OVERVIEW'
    },
    // {
    //     label: '里程碑',
    //     key: 'MILESTONE'
    // }
]
export enum myProjectDetailTabKey{
    PROJECT_TASKS = 'PROJECT_TASKS',
    TEAM_TASKS = 'TEAM_TASKS',
    TEAM_HISTORY = 'TEAM_HISTORY',
    VALUE_REVIEW = 'VALUE_REVIEW',
    PROJECT_OVERVIEW = 'PROJECT_OVERVIEW',
    MILESTONE = 'MILESTONE'
}
//项目详情->项目任务行详情tab
export const projectTasksRowTab = [
    {
        label: '任务信息',
        key: 'TASK_INFORMATION'
    },
    {
        label: '任务历程',
        key: 'TASK_HISTORY'
    },
    {
        label: '进程汇报',
        key: 'PROGRESS_REPORT'
    },
    {
        label: '标准参考',
        key: 'STANDARD_REFERENCE'
    },
    {
        label: '结果评价',
        key: 'RESULT_EVALUATION'
    }
]
//项目管理核算tab
export const projectAccountingTasksRowTab = [
    {
        label: '任务信息',
        key: 'TASK_INFORMATION'
    },
    {
        label: '任务历程',
        key: 'TASK_HISTORY'
    },
]
export enum projectTasksRowTabKey{
    TASK_INFORMATION = 'TASK_INFORMATION',
    TASK_HISTORY = 'TASK_HISTORY',
    PROGRESS_REPORT = 'PROGRESS_REPORT',
    RESULT_EVALUATION = 'RESULT_EVALUATION',
    STANDARD_REFERENCE = 'STANDARD_REFERENCE'
}
//价值评分 tab 
export const valueScoreTabs = [
    {
        label: '基本信息',
        key: 'BASIC_MES'
    },
    {
        label: '任务说明',
        key: 'TASK_INTRODUCTION'
    },
    {
        label: '相关资料',
        key: 'DOCUMENT'
    },
    {
        label: '评价标准',
        key: 'EVALUATE_CRITERION'
    },
]
export enum valueScoreTabsKey{
    BASIC_MES = "BASIC_MES",
    TASK_INTRODUCTION = 'TASK_INTRODUCTION',
    DOCUMENT = 'DOCUMENT',
    EVALUATE_CRITERION = 'EVALUATE_CRITERION'
}
//审核提交枚举
export enum AuditRelease {
    PASSED = 'PASSED',//通过
    REJECTED = 'REJECTED',//驳回
    UN_AUDITED = 'UN_AUDITED'//取消
}
//上传路径
export const UPLOAD_FILE_URL = '/cloud-base-service/common/attachment/save-batch';
// 文件类型对应的只能上传文件的类型枚举
export enum ProjectFileTypeAccepts {
    "IMG" = ".jpg,.jpeg,.png,.gif",
    "ZIP" = ".zip",
    "RAR" = ".rar",
    "WORD" = ".doc,.docx",
    "EXCEL" = ".xls,.xlsx",
    "PPT" = ".ppt,.pptx",
    "TXT" = ".txt",
    "PDF" = ".pdf",
    "CAD" = ".dwg,.dxf,.dws,.dwt,.bak",
    "XMIND" = ".xmind"
}
// 评审结果枚举对象
export enum AuditResult {
    /**
     * 未评审
     */
     WAIT_DEAL ,
    /**
     * 合格
     */
     APPROVED,
    /**
     * 只评审内容
     */
    RemarkOnly,
    /**
     * 返工
     */
     REJECTED,
}
// 审核项目的时候，通过还是拒绝的选择枚举
export enum ProjectAuditTaskEnum {
    APPROVED = "通过",
    REJECTED = "拒绝"
}
//项目任务 右键快捷菜单
export const  RightMenuBtn = [
    {
        key:'ADD_NEW_CHILD_TASK',
        name:'添加子级任务'
    },
    {
        key:'ADD_NEW_TASK',
        name:'添加同级任务'
    },
    {
        key:'EDIT',
        name:'编辑'
    },
    {
        key:'MOVE_UP',
        name:'上移'
    },
    {
        key:'MOVE_DOWN',
        name:'下移'
    },
    {
        key:'UPGRADE',
        name:'升级'
    },
    {
        key:'DOWNGRADE',
        name:'降级'
    },
    {
        key:'DELETE',
        name:'删除任务'
    }
]
// 项目管理核算右键菜单
export const RightMenuAccountingBtn = [
    {
        key:'ADD_NEW_CHILD_TASK',
        name:'添加子级任务'
    },
    {
        key:'ADD_NEW_TASK',
        name:'添加同级任务'
    },
    {
        key:'DELETE',
        name:'删除任务'
    }
]
//操作按钮操作类型编码
export enum BtnOperateType{
    ASSIGNMENT='ASSIGNMENT',//分派任务
    REASSIGN='REASSIGN',//改派任务
    RECEIVE='RECEIVE',//接收任务
    RETURN='RETURN',//退回任务
    TASK_DELAY='TASK_DELAY',//申请延期
    TASK_PAUSE='TASK_PAUSE',//申请暂停
    TASK_RECOVERY='TASK_RECOVERY',//申请恢复
    TASK_HANDOVER='TASK_HANDOVER',//移交任务
}
//审核类型
export enum AuditType{
    APPROVED='APPROVED',//通过
    REJECTED='REJECTED',//拒绝
}
//任务可操作按钮权限
export enum TaskFunctionCode{
    ADD_NEW_TASK = 'ADD_NEW_TASK',//新增任务
    ADD_NEW_CHILD_TASK = 'ADD_NEW_CHILD_TASK',//新增子任务
    MOVE_UP = 'MOVE_UP',//上移
    MOVE_DOWN = 'MOVE_DOWN',//下移
    UPGRADE = 'UPGRADE',//升级
    DOWNGRADE = 'DOWNGRADE',//降级
    EDIT = 'EDIT',//编辑
    CLOSE = 'CLOSE',//关闭
    DELETE = 'DELETE',//删除
    ASSIGNMENT = 'ASSIGNMENT',//分派任务
    REASSIGN = 'REASSIGN',//改派任务
    RECEIVE = 'RECEIVE',//接收任务
    RETURN = 'RETURN',//退回任务
    DELAY_APPLY = 'DELAY_APPLY',//申请延期
    PAUSE_APPLY = 'PAUSE_APPLY',//暂停申请
    RECOVERY_APPLY = 'RECOVERY_APPLY',//恢复申请
    HANDOVER_APPLY = 'HANDOVER_APPLY',//移交任务
    AUDIT_APPLY = 'AUDIT_APPLY',//审核申请
    PROCESS_REPORT = 'PROCESS_REPORT',//进程汇报
    PROCESS_REPLY = 'PROCESS_REPLY',//进程回复
    EDIT_RESULT_EVALUATION_CRITERIA = 'EDIT_RESULT_EVALUATION_CRITERIA',//编辑任务结果评价标准
    SUBMIT_EVALUATION_MATERIALS = 'SUBMIT_EVALUATION_MATERIALS',//汇报结果资料
    REVIEW = 'REVIEW',//评审汇报结果
}

//确认方类型
export enum ConfirmPersonType{
    CUSTOMER = 'CUSTOMER',
    EMPLOYEE = 'EMPLOYEE',
    SUPPLIER = 'SUPPLIER',
}

//项目概览操作按钮权限
export enum ProjectOverviewFunctionCode{
    APPLY_RELEASE = 'APPLY_RELEASE',//申请发布
    AUDIT_APPLY = 'AUDIT_APPLY',//项目审核
    STARTUP = 'STARTUP',//项目启动
    PAUSE = 'PAUSE',//项目暂停
    SETTING = 'SETTING',//项目设置
    DELETE = 'DELETE',//删除项目
    COPY = 'COPY',//复制项目
    IMPORT_EXCEL_TEMPLATE="IMPORT_EXCEL_TEMPLATE",//导入Excel模板
    SAVE_AS_TEMPLATE = 'SAVE_AS_TEMPLATE'//另存为项目模板

}

//按钮操作key值
export enum BtnKey{
    taskFunctionCode = 'taskFunctionCode',//我的任务操作按钮权限
    projectFunctionCode = 'projectFunctionCode',//项目概览操作按钮权限
}
// 项目管理核算右键头部行菜单
export const RightMenuProjectRowBtn = [
    {
        key:ProjectOverviewFunctionCode.DELETE,
        name:'删除项目'
    },
    {
        key:ProjectOverviewFunctionCode.SETTING,
        name:'更改项目负责人'
    }
]
  