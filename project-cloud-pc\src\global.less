@import "~antd/es/tree-select/style/index";
@import "./styles/task-table-cover";

.ant-tabs-top{
  overflow-x: auto !important;
}
.ant-badge{
  font-size: 13px !important;
}
.ant-table-measure-row{
  visibility: collapse;
}
.ant-btn-sm{
  height: 20px !important;
  padding: 0 5px !important;
  font-size: 13px !important;
}

.ant-popover-message{
  font-size: 13px !important;
}
.ant-tabs-content-holder{
  overflow: auto;
}
.bdw-table-extra,.bdw-table-current-show-data{
  display: flex;
  align-items: center;
  line-height: 26px;
}
html,
body,
#root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

// 兼容IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}
* ::-webkit-scrollbar{
  height: 4px;
}

