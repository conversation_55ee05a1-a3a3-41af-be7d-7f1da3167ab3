import { Effect, Reducer } from 'umi';
import { getCurrentUser } from '@/service/projectDos/commonApi';
import {listProjectBaseInfo} from '@/service/projectDos/myProjectApi';
import {CurrentUserInfoType,projectListInfoType} from '@/type'


export interface HomeModelState {
    userInfo: CurrentUserInfoType,
    projectListInfo: any[];
    projectListOptions: projectListInfoType[]
}

export interface HomeModelType {
  namespace: 'commonTask';
  state: HomeModelState;
  effects: {
    fetchUserInfo: Effect;
    fetProjectListInfo: Effect;
  };
  reducers: {
    saveCustomerInfo: Reducer<HomeModelState>;
    saveFetchUserInfo: Reducer<HomeModelState>;
  };
}

const HomeModel: HomeModelType = {
  namespace: 'commonTask',

  state: {
    userInfo: {},
    projectListInfo: [],
    projectListOptions: []
  },

  effects: {
    *fetchUserInfo({ payload, onSuccess, onFailed }, { call, put }) {
      const res = yield call(getCurrentUser, payload);
      if (res) {
        yield put({
  
          type: 'saveCustomerInfo',
          payload: res,
        });
        // onSuccess && onSuccess(res.data, null);
      } else {
        yield put({
          type: 'saveCustomerInfo',
          payload: [],
        });
        // onFailed && onFailed(null, res);
      }
    },
    *fetProjectListInfo({ payload, onSuccess, onFailed }, { call, put }) {
      const res = yield call(listProjectBaseInfo, payload);
      if (res) {
        const options = res.map((item: projectListInfoType) => ({ label: item.name, value: item.projectId }));
        yield put({
          type: 'saveFetchUserInfo',
          payload: res,
          options
        });
        // onSuccess && onSuccess(res.data, null);
      } else {
        yield put({
          type: 'saveFetchUserInfo',
          payload: [],
        });
        // onFailed && onFailed(null, res);
      }
    },
  },

  reducers: {
    saveCustomerInfo(state: any, action: any) {
      return {
        ...state,
        userInfo: action.payload || [],
      };
    },
    saveFetchUserInfo(state: any, action: any) {
      return {
        ...state,
        projectListInfo: action.payload || [],
        projectListOptions: action.options || [],
      };
    },
  },
};

export default HomeModel;
