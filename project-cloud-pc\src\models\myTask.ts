import { Effect, Reducer } from 'umi';
import {
  queryTaskStatusList,
  queryWaitDealTaskList,
  queryInProgressTaskList,
  queryFinishedTaskList,
  queryTaskApplyInfo,
  queryTeamProjectList,
  queryTeamTaskList
} from '@/service/myTask';
import { rebuildTeamTaskData, handleGetMineEmployeesData } from '@/utils/utils';
import { filterTaskItem } from "@/utils/utils";

import cloneDeep from 'lodash/cloneDeep';

// /* eslint-disable */ 
export interface TaskModelState {
  viewMethod: string
  taskInfo: object
  taskStatusList: any[] // tab栏数据
  paginationInfo: object // 分页数据
  tableData: any[]  // table data
  projectList: any[]  // 项目信息
  filterInfo: any  // 列表过滤条件
  teamTaskList: any[]  // 项目信息
  expandedRowKeys: string[] // 团队任务列表展开的keys
}

export interface TaskModelType {
  namespace: 'myTask';
  state: TaskModelState;
  effects: {
    fetchTaskStatusList: Effect;
    fetchTaskTableData: Effect;
    fetchTaskApplyInfo: Effect;
    fetchTeamProjectList: Effect;
    fetchTeamTaskList: Effect;
    handleSetTeamTaskList: Effect;
  };
  reducers: {
    saveTaskStatusList: Reducer<TaskModelState>;
    saveTaskTableData: Reducer<TaskModelState>;
    saveTaskTablePageData: Reducer<TaskModelState>;
    saveTeamProjectList: Reducer<TaskModelState>;
    setViewMethod: Reducer<TaskModelState>;
    setTeamTaskList: Reducer<TaskModelState>;
    setExpandedRowKeys: Reducer<TaskModelState>;
    setFilterTableData: Reducer<TaskModelState>;
  };
}

const TaskModel: TaskModelType = {
  namespace: 'myTask',

  state: {
    viewMethod: 'TABLE',
    taskInfo: {},
    taskStatusList: [], // 任务状态栏
    tableData: [],
    paginationInfo: {
      current: 1, // 当前页
      size: 10, // 每页条数
      pages: 0,   // 总的页数
      total: 0, // 当前页条数
    },
    projectList: [], // 所有项目数据
    filterInfo: {}, // 列表过滤条件
    teamTaskList: [],  // 团队任务列表
    expandedRowKeys: [], // 团队任务列表展开的keys
  },

  effects: {
    // 获取任务状态
    *fetchTaskStatusList({ payload, onSuccess, onFailed }, { call, put }) {
      const res = yield call(queryTaskStatusList, payload);
      if (res) {
        yield put({
          type: 'saveTaskStatusList',
          payload: res,
        });
        
        onSuccess && onSuccess(res);
      } else {
        yield put({
          type: 'saveTaskStatusList',
          payload: [],
        });
        onFailed && onFailed(res);
      }
    },
    // 获取任务列表数据（待处理，处理中，已完成）
    *fetchTaskTableData({ tableType, payload, onSuccess, onFailed }, { call, put }) {
      const queryFuncObj = {
        'WAIT_DEAL': queryWaitDealTaskList,
        'ON_GOING': queryInProgressTaskList,
        'FINISHED': queryFinishedTaskList,
      }
      const res = yield call(queryFuncObj[tableType], payload);
      if (res) {
        yield put({
          type: 'saveTaskTableData',
          payload: res.items,
        })
        // 分页数据
        yield put({
          type: 'saveTaskTablePageData',
          payload: {
            pageNum: Number(res.page),// 当前页
            pageSize: Number(res.count),// 每页条数
            total: Number(res.total),// 总的条数
            pages: Number(res.totalPage),// 总的页数
          }
        })
        onSuccess && onSuccess(res);
      } else {
        yield put({
          type: 'saveTaskTableData',
          payload: [],
        })
        yield put({
          type: 'saveTaskTablePageData',
          payload: {
            pageNum: 1,// 当前页
            pageSize: 10,// 每页条数
            pages: 0,// 总的页数
            total: 0,// 总的条数
          }
        })
        onFailed && onFailed(res);
      }
    },
    // 查询最新的待回复的进程汇报
    *fetchTaskApplyInfo({ payload, onSuccess, onFailed }, { call, put }) {
      const res = yield call(queryTaskApplyInfo, payload);
      
      if (res) {
        onSuccess && onSuccess(res);
      } else {
        onFailed && onFailed(res);
      }
    },
    // 获取任务状态
    *fetchTeamProjectList({ payload, onSuccess, onFailed }, { call, put }) {
      const res = yield call(queryTeamProjectList, payload);
      
      if (res && res.length) {
        yield put({
          type: 'saveTeamProjectList',
          payload: [{
            key: '',
            title: '所有项目'
          }].concat(res.map((item: any) => ({
            ...item,
            title: item.name,
            key: item.projectId,
          }))),
        });
        
        onSuccess && onSuccess(res);
      } else {
        yield put({
          type: 'saveTeamProjectList',
          payload: [{
            key: '',
            title: '所有项目'
          }],
        });
        onFailed && onFailed(res);
      }
    },
    // 获取团队任务数据
    *fetchTeamTaskList({ payload, onSuccess, onFailed }, { call, put }) {
      const res = yield call(queryTeamTaskList, payload);

      // 数据待处理
      const teamTaskList = res ? rebuildTeamTaskData(res) : []
      if (teamTaskList) {
        yield put({
          type: 'setTeamTaskList',
          payload: teamTaskList,
        });
        
        onSuccess && onSuccess(teamTaskList);
      } else {
        yield put({
          type: 'setTeamTaskList',
          payload: [],
        });
        onFailed && onFailed(res);
      }
    },
    // 处理团队任务数据与我相关
    *handleSetTeamTaskList({ payload, onSuccess, onFailed }, { call, put }) {
      const { projectId, userInfo, relatedMeFlag } = payload;
      const res = yield call(queryTeamTaskList, { projectId });

      if (res) {
        if (relatedMeFlag) {  // 打开与我相关
          const resData = {
            organizations: res.organizations,
            employees: handleGetMineEmployeesData(userInfo.userId, res.employees) // 过滤数据
          }
          const teamTaskList = rebuildTeamTaskData(resData)  // 处理数据结构

          yield put({
            type: 'setTeamTaskList',
            payload: teamTaskList,
          });
        } else {  // 关闭
          const teamTaskList = rebuildTeamTaskData(res) // 数据待处理
          yield put({
            type: 'setTeamTaskList',
            payload: teamTaskList,
          })
        }
      } else {
        yield put({
          type: 'setTeamTaskList',
          payload: [],
        })
      }
    }
  },

  reducers: {
    saveTaskStatusList(state: any, action: any) {
      return {
        ...state,
        taskStatusList: action.payload || [],
      };
    },
    saveTaskTableData(state: any, action: any) {
      return {
        ...state,
        tableData: action.payload || [],
      };
    },
    saveTaskTablePageData(state: any, action: any) {
      return {
        ...state,
        paginationInfo: action.payload || [],
      };
    },
    saveTeamProjectList(state: any, action: any) {
      return {
        ...state,
        projectList: action.payload || [],
      };
    },
    setViewMethod(state: any, action: any) {
      return {
        ...state,
        viewMethod: action.payload || [],
      };
    },
    setTeamTaskList(state: any, action: any) {
      return {
        ...state,
        teamTaskList: action.payload || [],
      };
    },
    // 设置展开栏
    setExpandedRowKeys(state: any, action: any) {
      return {
        ...state,
        expandedRowKeys: action.payload,
      };
    },
    // 表格过滤
    setFilterTableData(state: any, action: any) {
      const filterObj = cloneDeep(state.filterInfo);
      const teamTaskList = cloneDeep(state.teamTaskList);
      const expandedRowKeys: any[] = [];

      // console.log(teamTaskList, '<<<filterObj');

      return {
        ...state,
        filterInfo: Object.assign(filterObj, action.payload)
      };

      // if (action.status) {
      //   filterObj[action.typeName] = action.typeKey;
      //   length = Object.getOwnPropertyNames(filterObj).length;
      //   let temTaskLists: any = cloneDeep(state.taskListsBackUp);

      //   if (length == 1) {
      //     // 一种过滤条件
      //     temTaskLists = filterTaskItem(temTaskLists, action.typeKey, action.typeName, (id: string) => {
      //       expandedRowKeys.push(id);
      //     });
      //   } else {
      //     //多种过滤条件
      //     for (const i in filterObj) {
      //       temTaskLists = filterTaskItem(temTaskLists, filterObj[i], i, (id: string) => {
      //         expandedRowKeys.push(id);
      //       });
      //     }
      //   }

      //   return {
      //     ...state,
      //     taskLists: temTaskLists,
      //     expandedRowKeys,
      //     onlyShowMe: action.onlyShowMe ? action.status : state.onlyShowMe,
      //     filter: filterObj
      //   };
      // }
    },
  },
};

export default TaskModel;
