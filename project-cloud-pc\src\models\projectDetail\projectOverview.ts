import { Effect, Reducer } from 'umi';
import { projectOverviewApi } from "@/service/projectDos/my-project-detail/projectOverview";


export interface HomeModelState {
    projectOverviewDetails: any,
    currentCustomerId: string
}

export interface HomeModelType {
  namespace: 'projectOverview';
  state: HomeModelState;
  effects: {
    fetchProjectOverview: Effect;
  };
  reducers: {
    saveProjectOverview: Reducer<HomeModelState>;
  };
}

const HomeModel: HomeModelType = {
  namespace: 'projectOverview',

  state: {
    projectOverviewDetails:null,
    currentCustomerId: ''
  },

  effects: {
    *fetchProjectOverview({ payload, onSuccess, onFailed }, { call, put }) {
        const res = yield call(projectOverviewApi, payload);
        if (res ) {
          yield put({
            type: 'saveProjectOverview',
            payload: res,
          });
          // onSuccess && onSuccess(res.data, null);
        } else {
          yield put({
            type: 'saveProjectOverview',
            payload: [],
          });
          // onFailed && onFailed(null, res);
        }
      },
  },

  reducers: {
    saveProjectOverview(state: any, action: any) {
      let currentCustomerId = '';
      if(action.payload.businessAssociationType && action.payload.businessAssociationType == 'CUSTOMER_ASSOCIATION'){
        currentCustomerId = action.payload.projectBusinessId
      }else{
        currentCustomerId = ''
      }
        return {
          ...state,
          projectOverviewDetails: action.payload || [],
          currentCustomerId
        };
      },
  },
};

export default HomeModel;
