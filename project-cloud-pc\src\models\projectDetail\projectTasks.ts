import { Effect, Reducer } from 'umi';
import { getListTask, getTaskInfo, listAttachmentFormat, listTaskFunctionality } from '@/service/projectDos/my-project-detail/projectTasks';
import { initTaskIndex, addTaskItem, deleteTaskItem, filterTaskItem, findTaskItem, flatten, initIndex } from "@/utils/utils";
import { cloneDeep, isEmpty } from 'lodash';
import { message } from 'antd';
import { currentTaskType, optionType, projectFileType, currentTaskInfoListType } from '@/type';
import { listProjectBaseInfoByProjectId } from "@/service/projectDos/myProjectApi";


export interface HomeModelState {
  taskLists: currentTaskInfoListType[] | null;
  editable: boolean;
  taskInfo: currentTaskType | null;
  isAddTask: boolean;
  expandedRowKeys: any[],
  taskListsBackUp: currentTaskInfoListType[] | null,
  onlyShowMe: boolean,
  taskInfoBackUp: currentTaskType | null;
  attachmentFormatOptions: optionType | null,
  ProjectUploadFileType: projectFileType
  filter: any
  functionality: any[]
  submitStatus: number
  taskInfoFlag: any,
  associatedTasksFilter: any,
  associatedTasksTableData: any,
  associatedTasksTableDataBackUp: any,
  ATExpandedRowKeys: any[],
  cTaskId: string
  saveStatus: number,
  basicProjectInfo: any,
  loading: boolean,
  willSetTaskInfo: any
  showEmList?: boolean;
  newProjectList: any[];  //项目清单数据
  customBaseInfo: any,//客户基本信息
}

export interface HomeModelType {
  namespace: 'projectTasks';
  state: HomeModelState;
  effects: {
    fetchTaskList: Effect;
    fetchTaskInfo: Effect;
    fetchAttachmentFormat: Effect
    addProjectTask: Effect
    fetchProjectInfo: Effect
  };
  reducers: {
    getTaskLists: Reducer<HomeModelState>;
    setEditable: Reducer<HomeModelState>;
    getTaskInfo: Reducer<HomeModelState>;
    setIsAddTask: Reducer<HomeModelState>;
    addTask: Reducer<HomeModelState>;
    setExpandedRowKeys: Reducer<HomeModelState>;
    clickRow: Reducer<HomeModelState>;
    setFilterTableData: Reducer<HomeModelState>;
    renewTaskInfo: Reducer<HomeModelState>;
    setAttachmentFormat: Reducer<HomeModelState>;
    setFunctionality: Reducer<HomeModelState>;
    setSubmitStatus: Reducer<HomeModelState>;
    setSaveStatus: Reducer<HomeModelState>;
    setAssociatedTasksFilter: Reducer<HomeModelState>;
    setATExpandedRowKeys: Reducer<HomeModelState>;
    setAssociatedTasksTableData: Reducer<HomeModelState>;
    setCTaskId: Reducer<HomeModelState>;
    setResetState: Reducer<HomeModelState>;
    setBasicProjectInfo: Reducer<HomeModelState>;
    setLoading: Reducer<HomeModelState>;
    renewTaskList: Reducer<HomeModelState>;
    resetProjectTask: Reducer<HomeModelState>;
    setShowEmList: Reducer<HomeModelState>;
    setNewProjectList: Reducer<HomeModelState>;
    setCustomBaseInfo: Reducer<HomeModelState>;
  };
}

const HomeModel: HomeModelType = {
  namespace: 'projectTasks',

  state: {
    taskLists: null,//当前项目全部任务
    taskListsBackUp: null,//备份全部任务
    editable: false,//是否编辑状态
    taskInfo: null,//当前的任务信息
    taskInfoBackUp: null,//备份编辑前数据
    isAddTask: false,//是否是新增任务
    expandedRowKeys: [],//默认展开
    onlyShowMe: false,//只看我
    attachmentFormatOptions: null,
    ProjectUploadFileType: {},
    filter: {},//过滤字段
    functionality: [],//按钮是否可用
    submitStatus: 0,//触发保存
    saveStatus: 0,//触发保存
    taskInfoFlag: null,//是否重新请求按钮权限
    associatedTasksFilter: {},//关联任务过滤字段
    associatedTasksTableData: null,//关联任务表格数据
    associatedTasksTableDataBackUp: null,//关联任务表格数据备份
    ATExpandedRowKeys: [],//默认展开
    cTaskId: '',
    basicProjectInfo: {},
    loading: false,//加载状态
    willSetTaskInfo: null,//点击切换行时，保存正在编辑的行，存储点击当前的行数据
    showEmList: false,
    newProjectList: [], //项目清单数据
    customBaseInfo: null
  },

  effects: {
    // 获取全部任务
    *fetchTaskList({ payload, changeType, successRes, taskInfoFlag, taskId, onSuccess, onFailed, loading }, { call, put }) {
      if (loading) {
        yield put({
          type: 'setLoading',
          payload: true,
        })
      }
      const res = yield call(getListTask, payload);
      let newTaskInfo = null;
      if (successRes) {
        newTaskInfo = { ...successRes, beforeDocument: successRes.document, taskExplain: successRes.taskExplain ? JSON.parse(successRes.taskExplain) : null }
      }
      let resBtn: any = null;
      if (changeType !== 'delete' && !taskInfoFlag) {
        resBtn = yield call(listTaskFunctionality, { taskId, projectId: payload });
      }
      // initTaskIndex(res);
      yield put({
        type: 'setLoading',
        payload: false,
      })
      yield put({
        type: 'getTaskLists',
        payload: res ?? [],
        changeType,
        successRes: newTaskInfo,
        resBtn,
        taskId,
        taskInfoFlag
      });
      if (taskInfoFlag) {
        yield put({
          type: 'addTask',
          payload: taskInfoFlag.flag,
        })
      }
    },
    // 获取任务信息
    *fetchTaskInfo({ payload, projectId, onSuccess, onFailed }, { call, put }) {
      const res = yield call(getTaskInfo, payload.taskId);
      const resBtn = yield call(listTaskFunctionality, { taskId: payload.taskId, projectId });
      let taskExplain = null;
      if (res) {
        if (res.taskExplain) {
          try {
            taskExplain = JSON.parse(res.taskExplain);
          } catch {
            taskExplain = [{
              value: res.taskExplain,
              fileList: []
            }]
          }
        }

        const taskInfo = { ...payload, ...res, beforeDocument: res?.document, taskExplain };
        yield put({
          type: 'clickRow',
          payload: { taskInfo, resBtn },
        });
      } else {
        yield put({
          type: 'clickRow',
          payload: null,
        });
      }
    },
    //查询所有附件格式
    *fetchAttachmentFormat({ payload, onSuccess, onFailed }, { call, put }) {
      const res = yield call(listAttachmentFormat);
      if (res) {
        yield put({
          type: 'setAttachmentFormat',
          payload: res,
        });
      } else {
        yield put({
          type: 'setAttachmentFormat',
          payload: null,
        });
      }
    },
    //添加任务
    *addProjectTask({ payload, onSuccess, onFailed }, { call, put, select }) {
      const state = yield select((state: any) => state.projectTasks);
      if (state.editable) {
        if (state.taskInfo && !state.taskInfo.name) {
          // message.error('未填写标题');
          return
        } else {
          yield put({
            type: 'setSubmitStatus',
            flag: {
              flag: payload
            },
          })
          return
        }
      }
      yield put({
        type: 'addTask',
        payload,
      })
    },
    //项目基本信息
    *fetchProjectInfo({ payload, onSuccess, onFailed }, { call, put, select }) {
      const res = yield call(listProjectBaseInfoByProjectId, payload);
      if (res) {
        const data = {
          ...res,
          leaderInfo: {
            leaderName: res.leaderName,
            leaderId: res.leaderId,
          }
        }
        yield put({
          type: 'setBasicProjectInfo',
          payload: data,
        });
      } else {
        yield put({
          type: 'setBasicProjectInfo',
          payload: {},
        });
      }
    },
  },

  reducers: {
    getTaskLists(state: any, action: any) {
      let taskInfo: any = action.successRes ?? {};
      const functionality: any = action.resBtn;
      const expandedRowKeys = cloneDeep(state.expandedRowKeys);
      const fExpandedRowKeys: any[] = [];
      switch (action.changeType) {
        case 'move':
        case 'upgrade':
        case 'downgrade':
          findTaskItem(action.payload, state.taskInfo.taskId, (data: any) => {
            taskInfo = { ...data, ...taskInfo };
            if (action.changeType == 'downgrade') {
              expandedRowKeys.push(data.parentId);
            }
          });
          break;
        case 'save':
          if (!isEmpty(taskInfo)) {
            findTaskItem(action.payload, action.taskId, (data: any) => { taskInfo = { ...data, ...taskInfo } });
          }
          break;
        case 'delete':
          taskInfo = null;
          break;
      }
      const filterObj = cloneDeep(state.filter);
      const fLength = Object.getOwnPropertyNames(filterObj).length;
      let temTaskLists = cloneDeep(action.payload);
      let key = false;
      if (fLength) {
        // 多种过滤条件
        for (const i in filterObj) {
          if (i == 'taskId') key = true;
          temTaskLists = filterTaskItem(temTaskLists, filterObj[i], i, (id: string) => {
            fExpandedRowKeys.push(id);
          });
        }
      }
      let rTaskLists: any;
      if (key) {
        rTaskLists = action.payload || [];
      } else {
        if (fLength) {
          rTaskLists = temTaskLists;
        } else {
          rTaskLists = action.payload || [];
        }
      }
      return {
        ...state,
        taskLists: rTaskLists,
        taskListsBackUp: action.payload || [],
        taskInfo: action.payload.length ? action.changeType ? isEmpty(taskInfo) ? state.taskInfo : taskInfo : state.taskInfo : null,
        isAddTask: false,
        editable: false,
        functionality: functionality ?? state.functionality,
        cTaskId: action?.taskId ?? state?.taskInfo?.taskId,
        expandedRowKeys: fExpandedRowKeys?.length ? fExpandedRowKeys : expandedRowKeys,
        willSetTaskInfo: null,
        // onlyShowMe:false
      };
    },
    renewTaskList(state: any, action: any) {
      return {
        ...state,
        taskLists: action.payload || [],
        taskListsBackUp: action.payload || [],
      }
    },
    setAssociatedTasksTableData(state: any, action: any) {
      const taskId = state.taskInfo.taskId;
      const dataSouse = cloneDeep(state.taskListsBackUp);
      deleteTaskItem(dataSouse, taskId);
      const ATExpandedRowKeys = flatten(cloneDeep(dataSouse)).map((item: any) => item.taskId);
      return {
        ...state,
        associatedTasksTableData: dataSouse,//关联任务表格数据
        associatedTasksTableDataBackUp: dataSouse,//关联任务表格数据备份
        ATExpandedRowKeys
      }
    },
    setEditable(state: any, action: any) {
      if (action.payload) {
        return {
          ...state,
          editable: action.payload,
          taskLists: state.taskLists,
          taskInfoBackUp: state.taskInfo
        }
      } else {
        return {
          ...state,
          editable: action.payload,
          taskLists: cloneDeep(state.taskListsBackUp),
          taskInfo: state.taskInfoBackUp,
          isAddTask: false
        };
      }
    },
    getTaskInfo(state: any, action: any) {
      return {
        ...state,
        taskInfo: action.payload,
        cTaskId: action.payload ? action.payload.taskId : null
      };
    },
    setIsAddTask(state: any, action: any) {
      return {
        ...state,
        isAddTask: action.payload,
      };
    },
    // 添加任务
    addTask(state: any, action: any) {
      const expandedRowKeys = cloneDeep(state.expandedRowKeys);
      const temTaskLists = cloneDeep(state.taskLists) ?? [];
      const newChildItem: any = {
        children: null,
        editorId: null,
        editorName: null,
        endTime: "",
        executionCycle: null,
        leaderId: null,
        leaderName: null,
        parentId: null,
        process: null,
        startTime: null,
        status: null,
        taskId: 'newAdd',
        title: null,
        parentTitle: null,
        beforeDocument: [],
        index: [0]
      }
      if (state.taskInfo) {
        //添加任务&&一级任务
        if (state.taskInfo.index?.length == 1 && !action.payload) {
          newChildItem.index = [temTaskLists.length]
          temTaskLists?.splice(state.taskInfo.index[0] + 1, 0, newChildItem);
        } else {
          if (action.payload) {
            //新增子任务 父id是自己
            newChildItem.parentId = state.taskInfo.taskId;
            newChildItem.parentTitle = state.taskInfo.name;
            addTaskItem(state.taskInfo.taskId, temTaskLists, newChildItem);
            const temIndex = cloneDeep(state.taskInfo?.index);
            temIndex?.push(state.taskInfo.childrenSize);
            newChildItem.index = temIndex;
            expandedRowKeys?.push(state.taskInfo.taskId);
          } else {
            //新增任务&&非一级任务 父id是父亲
            newChildItem.parentId = state.taskInfo.parentId;
            newChildItem.parentTitle = state.taskInfo.parentTitle;
            const temIndex = cloneDeep(state.taskInfo.parent.index);
            temIndex?.push(state.taskInfo.parent.childrenSize);
            // temIndex.splice(state.taskInfo.parent.childrenSize,1,state.taskInfo.parent.childrenSize + 1);
            newChildItem.index = temIndex;
            addTaskItem(state.taskInfo.parentId, temTaskLists, newChildItem, state.taskInfo.index[state.taskInfo.index?.length - 1]);
          }
        }
      } else {
        temTaskLists.push(newChildItem);
      }
      // initIndex(temTaskLists);
      return {
        ...state,
        isAddTask: true,
        editable: true,
        taskInfo: newChildItem,
        taskLists: temTaskLists,
        expandedRowKeys,
        cTaskId: 'newAdd',
        taskInfoFlag: null,
      };
    },
    // 设置展开栏
    setExpandedRowKeys(state: any, action: any) {
      return {
        ...state,
        expandedRowKeys: action.payload,
      };
    },
    setATExpandedRowKeys(state: any, action: any) {
      return {
        ...state,
        ATExpandedRowKeys: action.payload,
      };
    },
    // 点击当前行
    clickRow(state: any, action: any) {
      if (state.taskInfo?.taskId && !state.taskInfo?.name) {
        // message.error('未填写标题');
        const temTaskLists = cloneDeep(state.taskLists);
        deleteTaskItem(temTaskLists, state.taskInfo.taskId);
        return {
          ...state,
          taskInfo: action.payload.taskInfo,
          taskLists: cloneDeep(state.taskListsBackUp),
          isAddTask: false,
          editable: false,
          functionality: action.payload.resBtn,
        };
      }
      return {
        ...state,
        taskInfo: action.payload.taskInfo,
        functionality: action.payload.resBtn,
        isAddTask: false,
        editable: false,
      };


    },
    // 表格过滤
    setFilterTableData(state: any, action: any) {
      const filterObj = cloneDeep(state.filter);
      const expandedRowKeys: any[] = [];
      let length = null;
      if (action.status) {
        // 过滤条件不是任务id时删除任务id 的过滤条件
        if (action.typeName != 'taskId') {
          if (filterObj.hasOwnProperty('taskId')) {
            delete filterObj.taskId
          }
        }
        filterObj[action.typeName] = action.typeKey;
        length = Object.getOwnPropertyNames(filterObj).length;
        let temTaskLists: any = cloneDeep(state.taskListsBackUp);
        if (length == 1) {
          // 一种过滤条件
          temTaskLists = filterTaskItem(temTaskLists, action.typeKey, action.typeName, (id: string, obj?: any) => {
            expandedRowKeys.push(id);
            if (action.typeName == 'taskId' && obj.taskId == action.typeKey) {
              action.onSuccess(obj)
            }
          });
        } else {
          // 多种过滤条件
          for (const i in filterObj) {
            temTaskLists = filterTaskItem(temTaskLists, filterObj[i], i, (id: string) => {
              expandedRowKeys.push(id);
            });
          }
        }
        // 携带任务id时定位当前携带的任务
        if (action.typeName == 'taskId') {
          return {
            ...state,
            expandedRowKeys,
            onlyShowMe: action.onlyShowMe ? action.status : state.onlyShowMe,
            filter: filterObj,
            cTaskId: action.typeKey,
          }
        }
        return {
          ...state,
          taskLists: temTaskLists,
          expandedRowKeys,
          onlyShowMe: action.onlyShowMe ? action.status : state.onlyShowMe,
          filter: filterObj
        };
      } else {
        // 过滤条件不是任务id时删除任务id 的过滤条件
        if (action.typeName != 'taskId') {
          if (filterObj.hasOwnProperty('taskId')) {
            delete filterObj.taskId
          }
        }
        delete filterObj[action.typeName];
        length = Object.getOwnPropertyNames(filterObj).length;
        let list = cloneDeep(state.taskListsBackUp);
        //存在还在过滤的项 过滤显示
        if (length) {
          for (const i in filterObj) {
            list = filterTaskItem(list, filterObj[i], i, (id: string) => {
              expandedRowKeys.push(id);
            });
          }
        }
        return {
          ...state,
          taskLists: list,
          onlyShowMe: action.onlyShowMe ? action.status : state.onlyShowMe,
          filter: filterObj
        }
      }
    },
    setAssociatedTasksFilter(state: any, action: any) {
      const filterObj = cloneDeep(state.associatedTasksFilter);
      let expandedRowKeys: any[] = [];
      let length = null;
      if (action.status) {
        filterObj[action.typeName] = action.typeKey;
        length = Object.getOwnPropertyNames(filterObj).length;
        let temTaskLists: any = cloneDeep(state.associatedTasksTableDataBackUp);
        if (length == 1) {
          // 一种过滤条件
          temTaskLists = filterTaskItem(temTaskLists, action.typeKey, action.typeName, (id: string) => {
            // expandedRowKeys.push(id);
          });
          expandedRowKeys = flatten(cloneDeep(temTaskLists)).map((item: any) => item.taskId);
        } else {
          //多种过滤条件
          for (const i in filterObj) {
            temTaskLists = filterTaskItem(temTaskLists, filterObj[i], i, (id: string) => {
              // expandedRowKeys.push(id);
            });
            expandedRowKeys = flatten(cloneDeep(temTaskLists)).map((item: any) => item.taskId);
          }
        }

        return {
          ...state,
          associatedTasksTableData: temTaskLists,
          ATExpandedRowKeys: expandedRowKeys,
          associatedTasksFilter: filterObj
        };
      } else {
        delete filterObj[action.typeName];
        length = Object.getOwnPropertyNames(filterObj).length;
        let list = cloneDeep(state.associatedTasksTableDataBackUp);
        //存在还在过滤的项 过滤显示
        if (length) {
          for (const i in filterObj) {
            list = filterTaskItem(list, filterObj[i], i, (id: string) => {
              expandedRowKeys.push(id);
            });
          }
        }
        expandedRowKeys = flatten(cloneDeep(list)).map((item: any) => item.taskId);
        return {
          ...state,
          associatedTasksTableData: list,
          associatedTasksFilter: filterObj,
          ATExpandedRowKeys: expandedRowKeys,
        }
      }
    },
    //更新任务信息
    renewTaskInfo(state: any, action: any) {
      // action.payload为空时，无需合并taskInfo
      const taskInfo = action.payload ? { ...state.taskInfo, ...action.payload } : action.payload;
      return {
        ...state,
        taskInfo: taskInfo,
        cTaskId: action.payload ? taskInfo.taskId : null
      };
    },
    setAttachmentFormat(state: any, action: any) {
      const ProjectUploadFileType = {}
      if (action.payload) {
        action.payload.forEach((item: any) => {
          ProjectUploadFileType[item.name] = item.value
        })
      }
      return {
        ...state,
        attachmentFormatOptions: action.payload,
        ProjectUploadFileType
      };
    },
    setFunctionality(state: any, action: any) {
      return {
        ...state,
        functionality: action.payload,
      }
    },
    setSubmitStatus(state: any, action: any) {
      return {
        ...state,
        submitStatus: state.submitStatus + 1,
        willSetTaskInfo: action.payload,
        taskInfoFlag: action.flag
      }
    },
    setSaveStatus(state: any, action: any) {
      return {
        ...state,
        saveStatus: state.saveStatus + 1,
      }
    },
    setCTaskId(state: any, action: any) {
      return {
        ...state,
        cTaskId: action.payload
      }
    },
    setResetState(state: any, action: any) {
      return state
    },
    setBasicProjectInfo(state: any, action: any) {
      return {
        ...state,
        basicProjectInfo: action.payload
      }
    },
    setLoading(state: any, action: any) {
      return {
        ...state,
        loading: action.payload
      }
    },
    // 重置项目详情数据
    resetProjectTask(state: any) {
      return {
        ...state,
        taskInfo: null,
        cTaskId: '',
        taskListsBackUp: null,
        taskLists: null,
        filterObj: {}
      }
    },
    //项目人员下拉显示 
    setShowEmList(state: any, action: any) {
      return {
        ...state,
        showEmList: action.payload
      }
    },
    // 保存项目清单数据
    setNewProjectList(state: any, action: any) {
      return {
        ...state,
        newProjectList: action.payload
      }
    },
    // 项目派单客户项目信息
    setCustomBaseInfo(state: any, action: any) {
      return {
        ...state,
        customBaseInfo: action.payload
      }
    },
  },
};

export default HomeModel;
