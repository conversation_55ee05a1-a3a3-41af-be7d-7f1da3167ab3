import { Effect, Reducer } from 'umi';
import { listScoreVersionInfo, listTaskScoreInfo, listScoreVersionTaskInfo } from '@/service/projectDos/my-project-detail/valueReview';
import { initTaskIndex, filterTaskItem } from "@/utils/utils";
import { cloneDeep } from 'lodash';
import  type {currentVersionInfoType} from '@/type'

export interface HomeModelState {
  projectScoreList: any
  projectBackUpScoreList: any
  expandedRowKeys: any
  taskInfo: any
  filter: any,
  projectScoreVersionList: any
  projectScoreBackUpVersionList: any
  versionFilter: any
  currentVersionInfo: currentVersionInfoType
  reviewTaskId: string
}

export interface HomeModelType {
  namespace: 'valueReview';
  state: HomeModelState;
  effects: {
    fetchCurrentProjectScoreList: Effect;
    fetchProjectScore: Effect;
  };
  reducers: {
    setInitProjectScore: Reducer<HomeModelState>
    setExpandedRowKeys: Reducer<HomeModelState>
    setTaskInfo: Reducer<HomeModelState>
    setFilterTableData: Reducer<HomeModelState>
    setFilterVersionTableData: Reducer<HomeModelState>
    setProjectScore: Reducer<HomeModelState>
    setCurrentVersionInfo: Reducer<HomeModelState>
    setProjectScoreVersionList: Reducer<HomeModelState>
    setReviewTaskId: Reducer<HomeModelState>
  };
}

const HomeModel: HomeModelType = {
  namespace: 'valueReview',

  state: {
    projectScoreList: null,//当前项目所有任务分值信息
    projectBackUpScoreList: null,//存储未过滤的值
    expandedRowKeys: [],
    taskInfo: null,
    filter: {},//过滤
    projectScoreVersionList: null,//版本记录
    projectScoreBackUpVersionList: null,//存储版本记录未过滤的值
    versionFilter: {},//版本记录过滤
    currentVersionInfo:{isNew:false,versionId:null},
    reviewTaskId: ''
  },

  effects: {
    *fetchCurrentProjectScoreList({ payload, callback, onSuccess, onFailed }, { call, put }) {
      const res = yield call(listTaskScoreInfo, payload);
      const resVersion = yield call(listScoreVersionInfo, payload);
      if (callback) {
        callback();
      }
      // initTaskIndex(res);
      if (res) {
        yield put({
          type: 'setInitProjectScore',
          payload: {
            list: res,
            resVersion
          },
        });
      } else {
        yield put({
          type: 'setInitProjectScore',
          payload: {
            list: [],
            resVersion: []
          },
        });
      }
    },
    *fetchProjectScore({ payload, refresh, onSuccess, onFailed }, { call, put }) {
      const res = yield call(listScoreVersionTaskInfo, payload);
      // initTaskIndex(res);
      if (res) {
        yield put({
          type: 'setProjectScore',
          payload: res
        });
      } else {
        yield put({
          type: 'setProjectScore',
          payload: [],
        });
      }
    },
  },

  reducers: {
    //初始化 || 刷新
    setInitProjectScore(state: any, action: any) {
      return {
        ...state,
        projectScoreList: action.payload.list || [],
        projectBackUpScoreList: action.payload.list || [],
        projectScoreVersionList: action.payload.resVersion || [],
        projectScoreBackUpVersionList: action.payload.resVersion || [],
        currentVersionInfo:{isNew:false,versionId:null}
      };
    },
    //设置当前的版本详情数据
    setProjectScore(state: any, action: any) {
      return {
        ...state,
        projectScoreList: action.payload || [],
        projectBackUpScoreList: action.payload || [],
      };
    },
    //设置当前的分值版本列表
    setProjectScoreVersionList(state: any, action: any) {
      return {
        ...state,
        projectScoreVersionList: action.payload || [],
        projectScoreBackUpVersionList: action.payload || [],
      };
    },
    // 设置展开栏
    setExpandedRowKeys(state: any, action: any) {
      return {
        ...state,
        expandedRowKeys: action.payload,
      };
    },
    setTaskInfo(state: any, action: any) {
      return {
        ...state,
        taskInfo: action.payload,
      };
    },
    //任务表格过滤
    setFilterTableData(state: any, action: any) {
      const filterObj = cloneDeep(state.filter);
      let temTaskLists = cloneDeep(state.projectBackUpScoreList);
      if (action.status) {
        filterObj[action.typeName] = action.typeKey;
        const expandedRowKeys: any[] = [];
        temTaskLists = filterTaskItem(temTaskLists, action.typeKey, action.typeName, (id: string) => {
          expandedRowKeys.push(id);
        });
        return {
          ...state,
          projectScoreList: temTaskLists,
          expandedRowKeys,
          filter: filterObj
        };
      } else {
        return {
          ...state,
          projectScoreList: temTaskLists,
          filter: {}
        }
      }
    },
    //版本记录表格过滤
    setFilterVersionTableData(state: any, action: any) {
      const filterObj = cloneDeep(state.versionFilter);
      let List = cloneDeep(state.projectScoreBackUpVersionList);
      if (action.status) {
        filterObj[action.typeName] = action.typeKey;
        List = List.filter((item: any) => item.versionName.indexOf(action.typeKey) != -1)
        return {
          ...state,
          projectScoreList: List,
          versionFilter: filterObj
        };
      } else {
        return {
          ...state,
          projectScoreList: List,
          versionFilter: {}
        }
      }
    },
    //评分版本id
    setCurrentVersionInfo(state: any, action: any){
      return {
        ...state,
        currentVersionInfo: {...state.currentVersionInfo,...action.payload},
      }
    },
    setReviewTaskId(state: any, action: any){
      return {
        ...state,
        reviewTaskId: action.payload,
      }
    },

  },
};

export default HomeModel;
