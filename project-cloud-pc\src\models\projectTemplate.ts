import { Effect, Reducer,Subscription } from 'umi';
import { listProjectFunction, projectOverviewApi } from '@/service/projectDos/my-project-detail/projectOverview';
import { SelectSubmitEvaluationMaterials } from '@/service/projectDos/my-project-detail/projectTasks';
import { initTaskIndex,filterTaskItem } from "@/utils/utils";
import { templateListTask } from '@/service/projectDos/myProjectApi';
import {cloneDeep} from 'lodash'


export interface HomeModelState {
  templateBtnUse: any[],
  projectTemplateData: any,
  templateListTasks: any,
  templateListTasksBackUp: any,
  expandedRowKeys: any[],
  filter: any,
  taskInfo: any
  cTaskId: any
}

export interface HomeModelType {
  namespace: 'projectTemplate';
  state: HomeModelState;
  effects: {
    fetchTemplateInit: Effect,
    fetchProjectTemplateData: Effect,
    fetchTemplateListTasks: Effect,
    fetTaskInfo: Effect,
  };
  reducers: {
    saveTemplateBtnUse: Reducer<HomeModelState>;
    saveProjectTemplateData: Reducer<HomeModelState>;
    setTemplateListTasks: Reducer<HomeModelState>;
    setFilterTableData: Reducer<HomeModelState>;
    setExpandedRowKeys: Reducer<HomeModelState>;
    setTaskInfo: Reducer<HomeModelState>;
    setCTaskId: Reducer<HomeModelState>;
  };
  subscriptions: {
    setup: Subscription 
  }
}

const HomeModel: HomeModelType = {
  namespace: 'projectTemplate',

  state: {
    templateBtnUse: [],
    projectTemplateData: {},
    templateListTasks: null,
    templateListTasksBackUp: null,
    filter: {},
    expandedRowKeys:[],
    taskInfo: null,
    cTaskId: null
  },

  effects: {
    *fetchTemplateInit({ payload, init,onSuccess, onFailed }, { call, put }) {
      const res = yield call(listProjectFunction, payload);
      yield put({
        type: 'saveTemplateBtnUse',
        payload: res,
      });
    },
    *fetchProjectTemplateData({ payload, init,onSuccess, onFailed }, { call, put }) {
      const res = yield call(projectOverviewApi, payload);
      yield put({
        type: 'saveProjectTemplateData',
        payload: res,
      });
    },
    *fetchTemplateListTasks({ payload, init,onSuccess, onFailed }, { call, put }) {
      const res = yield call(templateListTask, payload);
      yield put({
        type: 'setTemplateListTasks',
        payload: res,
      });
    },
    *fetTaskInfo({ payload, init,onSuccess, onFailed }, { call, put }) {
      const res = yield call(SelectSubmitEvaluationMaterials, payload.taskId);
      yield put({
        type: 'setTaskInfo',
        payload: {...res,...payload},
      });
    },

  },

  reducers: {
    saveTemplateBtnUse(state: any, action: any) {
      return {
        ...state,
        templateBtnUse: action.payload,
      }
    },
    saveProjectTemplateData(state: any, action: any) {
      return {
        ...state,
        projectTemplateData: action.payload,
      }
    },
    setTemplateListTasks(state: any, action: any) {
      return {
        ...state,
        templateListTasks: action.payload,
        templateListTasksBackUp: action.payload,
      }
    },
    //任务表格过滤
    setFilterTableData(state: any, action: any) {
      const filterObj = cloneDeep(state.filter);
      let temTaskLists = cloneDeep(state.templateListTasksBackUp);
      if (action.status) {
        filterObj[action.typeName] = action.typeKey;
        const expandedRowKeys: any[] = [];
        temTaskLists = filterTaskItem(temTaskLists, action.typeKey, action.typeName, (id: string) => {
          expandedRowKeys.push(id);
        });
        return {
          ...state,
          templateListTasks: temTaskLists,
          expandedRowKeys,
          filter: filterObj
        };
      } else {
        return {
          ...state,
          templateListTasks: temTaskLists,
          filter: {}
        }
      }
    },
    // 设置展开栏
    setExpandedRowKeys(state: any, action: any) {
      return {
        ...state,
        expandedRowKeys: action.payload,
      };
    },
    //
    setTaskInfo(state: any, action: any) {
      return {
        ...state,
        taskInfo: action.payload,
      };
    },
    setCTaskId(state: any, action: any) {
      return {
        ...state,
        cTaskId: action.payload,
      };
    },
  },
  subscriptions: {
    setup({dispatch,history}){
      return history.listen(
        async ({pathname,search}) => {
          if(pathname === "/project-template"){
            dispatch({
              type:'setTemplateListTasks',
              payload: []
            })
          }
        } 
      )
    }
  }
};

export default HomeModel;
