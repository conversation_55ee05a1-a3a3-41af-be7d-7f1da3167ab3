@import "../../../styles/base";
.homepage-left-container{
    width: calc(100% - 336px);
    height: 100%;
    padding: 16px;
    .head-operate{
        height: 40px;
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .index-my-project-statistic{
            display: flex;
            align-items: center;
            user-select: none;
        }
    }
    .information{
        padding: 0 12px  0 54px;
        height: 26px;
        border-top: 1px solid @divider;
        border-bottom: 1px solid @divider;
        color: #5c5c5c;
        font-size: 13px;
    }
    .index-my-project-table-tbody {
        width: 100%;
        overflow: auto;
        height: calc(100vh - 150px);
        .project-tbody-item {
          cursor: pointer;
          padding: 13px 0 0px 0;
          width: 100%;
          box-sizing: border-box;
          overflow: hidden;
          &:hover {
            background-color: @crosswalkHover;
          }
          .project-image {
            margin-right: 14px;
            img {
              width: 40px;
              height: 40px;
            }
          }
          .project-tbody-item-content {
            border-bottom: 1px solid @divider;
            width: 100%;
            overflow: hidden;
            padding-bottom: 13px;
          }
          .tbody-project-name {
            overflow: hidden;
            .project-content {
              width: 100%;
              overflow: hidden;
              .project-title {
                .f-13();
                width: 95%;
                color: @importantTitle;
                font-weight: 500;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                line-height: 20px;
              }
              .project-help {
                margin-top: 4px;
                .f-12();
                width: 95%;
                color: @help;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }
          }
          .tbody-project-duty-person {
            width: 180px;
            padding-left: 12px;
            line-height: 20px;
            text-align: right;
            padding-right: 16px;
            .f-13();
            .tbody-project-finish-time {
              color: @help;
              margin-top: 4px;
            }
          }
        }
      }
      .index-my-project-table-tbody::-webkit-scrollbar {
        width: 2px;
        height: 2px;
      }
    
      .index-my-project-table-tbody::-webkit-scrollbar-track {
        box-shadow: inset 0 0 5px #E1E1E1;
        background-color: #E1E1E1;
        border-radius: 2px;
      }
    
      .index-my-project-table-tbody::-webkit-scrollbar-thumb {
        box-shadow: inset 0 0 5px #8c8c8c;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 2px;
      }
}