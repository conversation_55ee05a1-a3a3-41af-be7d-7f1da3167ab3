/**
 * @description 首页左边部分
 * <AUTHOR>
 * @date 2023-10-26 17:02:36
*/
import React, { useEffect } from 'react';
import './index.less';
import BdwRow from "@/components/bdw-row";
import PictureGetById from "@/components/picture-show-by-file-id";
import { Radio, Empty } from "antd";
import {useRequest} from "ahooks";
import {history} from "@@/core/history";
import IndexTitle from "../index-title";
import { HistoryOutlined } from "@ant-design/icons";
// @ts-ignore
import ProjectDefault from "@/assets/image/project-default.png";
import {statisticsCodeType} from '../index';

interface Statistics{
    quantity: number,
    quickStatus: string,
    quickStatusName: string
}
interface HomepageLeftProps{
    recentProjectStatistics?: Statistics[]
    codeChange: (type: statisticsCodeType) => void,
    recentProjects: any
}
const HomepageLeft: React.FC<HomepageLeftProps> = ({recentProjectStatistics,codeChange,recentProjects}) => {
    useEffect(()=>{
        // run()
    },[])
    const typeChange = (e: any) => {
        if(e.target.value == 'ALL'){
            codeChange('')
        }else{
            codeChange(e.target.value);
        }
    }
    const dataTypeRadios = recentProjectStatistics?.map((item) => {
        return (
            <Radio.Button key={item.quickStatus} value={item.quickStatus}>{item.quickStatusName} {item.quantity}</Radio.Button>
        )
    });
    // 头像数据
    const projectImgRender = (text: string) => {
          return (
            <div className='table-show-image-box'>
              <img src={text?text:ProjectDefault} />
            </div>
          )
      }
    // 跳转查看项目详情
    const toDetail = (id: string | number) => {
        history.push(`/my-project-detail/${id}`)
    }
    //近期项目数据
    const tableShow = recentProjects?.map((item: any, index: number) => {
        return (
            <BdwRow type='flex' key={item.projectId} className='project-tbody-item' onClick={() => toDetail(item.projectId)}>
                <div className='project-image'>
                    {projectImgRender(item.coverPictureUrl)}
                </div>
                <BdwRow className='project-tbody-item-content' type='flex'>
                    <BdwRow className='flex-1 tbody-project-name' type='flex'>
                        <div className='project-content'>
                            <div className='project-title'>
                                {item.name}
                            </div>
                            <div className='project-help'>
                                当前进程: {item.process}
                            </div>
                        </div>
                    </BdwRow>
                    <div className='tbody-project-duty-person'>
                        <div className='tbody-project-duty-person-content'>{item.leaderName}</div>
                        <div className='tbody-project-finish-time'>截止日期: {item.endDate}</div>
                    </div>
                </BdwRow>
            </BdwRow>
        )
    })
    return (
        <div className='homepage-left-container'>
            <BdwRow type='flex' className='head-operate'>
                <IndexTitle className='flex-1' icon={() => <HistoryOutlined />}>近期项目</IndexTitle>
                <div className='index-my-project-statistic'>
                    <Radio.Group buttonStyle="solid" defaultValue='ALL' onChange={typeChange}>
                        {dataTypeRadios}
                    </Radio.Group>
                </div>
            </BdwRow>
            <BdwRow type='flex-center-between' className='information'>
                <div>项目名称</div>
                <div>负责人</div>
            </BdwRow>
            <div className='index-my-project-table-tbody'>
                {recentProjects?.length > 0 ? tableShow : <Empty className='mt-16' description="暂无数据" />}
            </div>
        </div>
    )
}
export default HomepageLeft