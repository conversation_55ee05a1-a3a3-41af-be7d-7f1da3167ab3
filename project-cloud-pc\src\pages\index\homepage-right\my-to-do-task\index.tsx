import React, { useMemo } from "react";
import "./index.less";
import IndexTitle from "../../index-title";
import { ClockCircleOutlined } from "@ant-design/icons";
import TaskItem from "../task-item";
import { useRequest } from "ahooks";
import { Checkbox, Spin, Empty } from "antd";

export interface TaskItemInterface {
  date: string
  endDate: string
  executionCycle: string
  leaderId: string
  leaderName: string
  message: string
  name: string
  passedDays: string
  projectId: string
  projectName: string
  startDate: string
  statusCode: string
  statusName: string
  taskId: string
}
export enum RecentlyProjectStatusColorEnum {
  REPORT = "blue",   // 待回复
  SUBMIT_EVALUATION = "green",  // 待评审
  REJECT_EVALUATION = "red",      // 已驳回
  RETURN = "red",      // 已退回
}

interface MyToDoTaskProps{
  waitDealTasks: any
}
const MyToDoTask: React.FC<MyToDoTaskProps> = ({waitDealTasks}) => {

  const showTaskItem = waitDealTasks?.map((item: TaskItemInterface, index: number) => {

    return (
      <TaskItem
        projectId={item.projectId}
        key={index}
        color={RecentlyProjectStatusColorEnum[item.statusCode] || 'default'}
        projectName={item.projectName}
        taskId={item.taskId} 
        title={item.name}
        statusDesc={item.statusName}
        message={item.message}
        name={item.leaderName}
      />
    )
  })
  const onChange = (e: any) => {
    console.log(e);
  }
  return (
    <div className='index-my-to-do-task'>
      <IndexTitle className="my-to-do-task-title-wrapper" icon={() => <ClockCircleOutlined />} number={waitDealTasks?.length || 0}>待处理任务</IndexTitle>
      <div className='to-do-task-list width-100'>
        <Checkbox.Group className='width-100' onChange={onChange}>
          {waitDealTasks?.length > 0 ? showTaskItem : <Empty className="mt-16" description="暂无数据" />}
        </Checkbox.Group>
      </div>
    </div>
  )
}

export default React.memo(MyToDoTask)
