@import "../../../../styles/base";

.task-item-box {
  border-radius: 2px;
  padding: 10px;
  margin-top: 10px;
  width: 100%;
  box-shadow: #0000001a 0px 0px 4px ;
  .pointer();
  &:hover {
    box-shadow: 0 0 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
  }
  .task-item-checkbox {
    margin-right: 8px;
  }
  .task-item-title {
    .f-13();
    color: @content;
  }
  .task-item-latest-message {
    .f-12();
    color: @info;
    .task-item-latest-message-icon {
      .f-13();
      line-height: 20px;
      margin-right: 5px;
    }
  }
  .task-item-status {
    margin-top: 8px;
    align-items: flex-end;
    .custom-class{
      height: 20px;
      display: inline-block;
    }
    .task-item-status-box {
      padding: 4px 8px;
      .f-12();
      border-radius: 5px;
      line-height: 1;
      &.blue {
        background-color: rgba(90,165,229,0.1);
        color: @primary;
      }
    }
    .task-belong-project {
      overflow: hidden;
      .f-12();
      color: @help;
      .task-belong-project-title {
        width: 56px;
        text-align: center;
      }
      .task-belong-project-box {
        .overflow-show-ellipsis {
          width: 140px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          height: 20px;
        }
      }
    }
  }
}
