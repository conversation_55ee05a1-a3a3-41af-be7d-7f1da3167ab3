import React from "react";
import { Checkbox } from "antd";
import { MenuItemShowNameIcon, BdwCardStatus } from "@/components";
import { TagOutlined, MessageOutlined } from "@ant-design/icons";

import "./index.less";
import { history } from "@@/core/history";
import BdwRow from "@/components/bdw-row";


interface TaskItemProps {
  // 任务id
  taskId: string,
  // 标题
  title: string,
  // 样式名
  className?: string,
  // 任务信息
  message?: string,
  // 任务状态
  status?: string,
  // 项目的状态name
  statusDesc?: string,
  // 名称
  name: string,
  // 项目名称
  projectName: string,
  // 状态标签颜色
  color?: "default" | "blue" | "red" | "orange" | "green"
  // 项目id
  projectId: string | number
}

const TaskItem: React.FC<TaskItemProps> = (props) => {
  const { taskId, projectId, color = 'default' } = props;

  const toDetail = () => {
    history.push(`/my-project-detail/${projectId}?taskId=${taskId}`)
  }

  return (
    <BdwRow type='flex' className={`task-item-box ${props.className || ''}`} onClick={() => toDetail()}>
      <div className='task-item-checkbox'>
        <Checkbox value={projectId} />
      </div>
      <div className='task-item-content flex-1'>
        <BdwRow type='flex' className='task-item-title'>
          <div className='task-item-title-left flex-1'>
            {props.title}
          </div>
          <MenuItemShowNameIcon name={props.name} />
        </BdwRow>
        <div className='task-item-latest-message'>
          <span className='task-item-latest-message-icon'>
            <MessageOutlined />
          </span>
          <span>
            {props.message}
          </span>
        </div>
        <BdwRow className='task-item-status width-100' type='flex'>
          <div>
            <BdwCardStatus theme={color} className="custom-class" f10={true}>
              {props.statusDesc}
            </BdwCardStatus>
          </div>
          <BdwRow className='task-belong-project flex-1' type='flex'>
            <div className='task-belong-project-title'>
              <TagOutlined className='mr-5' />
              <span>项目:</span>
            </div>
            <div className='task-belong-project-box flex-1'>
              <div className='overflow-show-ellipsis'>
                {props.projectName}
              </div>
            </div>
          </BdwRow>
        </BdwRow>
      </div>
    </BdwRow>
  )
}

export default TaskItem
