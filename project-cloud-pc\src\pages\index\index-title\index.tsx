import React from "react";

import "./index.less"
import BdwRow from "@/components/bdw-row";

interface IndexTitleProps {
  // 图标
  icon?: () => React.ReactNode
  // 数量
  number?: number
  // 样式名
  className?: string
}

const IndexTitle: React.FC<IndexTitleProps> = (props) => {
  const {className = ""} = props
  return (
    <BdwRow type='flex' className={`index-title ${className}`}>
      <div className='index-title-icon f-16'>
        {props.icon?.()}
      </div>
      <div className='index-title-content f-13'>
        {props.children}
      </div>
      <div className='index-title-statistic-number'>
        {props.number}
      </div>
    </BdwRow>
  )
}
export default IndexTitle
