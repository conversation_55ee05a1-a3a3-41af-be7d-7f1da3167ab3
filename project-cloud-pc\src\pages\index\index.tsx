/**
 * @description 首页
 * <AUTHOR>
 * @date 2023-10-26 17:04:39
*/
import React, { useEffect, useState } from "react";
import HomepageRight from "./homepage-right";
import HomepageLeft from "./homepage-left";
import { Notification } from 'bmc-app-react-component';
import { listRecentProjectApi,recentProjectStatisticsApi } from "@/service/homepage"; 
import {useRequest} from 'ahooks';

import "./index.less";
export type statisticsCodeType  =  'OVERDUE' | 'GO_ON_NORMALLY' | 'PAUSE' | 'ALL' | '';

const HomePage: React.FC = () => {
  const [statisticsCode,setStatisticsCode] = useState<statisticsCodeType>('')
  const { data: recentProjectStatistics,  } = useRequest<any>(recentProjectStatisticsApi);
  //项目业务关联
  const { data: listRecentProject  } = useRequest<any>(()=>listRecentProjectApi({status:statisticsCode}),{
    refreshDeps: [
      statisticsCode
    ],
  });
  const codeChange = (e: statisticsCodeType) => {
    setStatisticsCode(e);
  }
  return (
    <div className="homepage-container">
      <HomepageLeft recentProjects={listRecentProject?.recentProjects} codeChange={codeChange} recentProjectStatistics={recentProjectStatistics} />
      <HomepageRight waitDealTasks={listRecentProject?.waitDealTasks}/>
      <Notification/>
    </div>
  )
}
export default HomePage;
