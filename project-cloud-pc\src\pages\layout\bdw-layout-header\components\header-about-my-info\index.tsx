import React from "react";
import {MessageOutlined,CalendarOutlined} from "@ant-design/icons";

// @ts-ignore
import HeaderImage from "@/assets/image/portrait_06.png";
import "./index.less"

interface HeaderAboutMyInfoProps {

}

const HeaderAboutMyInfo: React.FC<HeaderAboutMyInfoProps> = (props) => {
  return (
    <div className='header-about-my-info-content'>
      <div className='my-message mr-20'>
        <MessageOutlined />
      </div>
      <div className='my-time mr-20'>
        <CalendarOutlined />
      </div>
      <div className='about-me mr-16'>
        <img src={HeaderImage} alt=""/>
      </div>
    </div>
  )
}

export default HeaderAboutMyInfo
