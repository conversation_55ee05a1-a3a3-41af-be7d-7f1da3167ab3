@import "../../../../../styles/base";

.header-global-search {
  // margin-left: 20px;

  .header-global-search-input {
    width: 320px;
    margin-top: 8px;
    background-color: @backGround;
    border: none !important;
    outline: none !important;
    border-radius: 32px;
    padding: 0 12px;
    &.ant-select-focused {
      .ant-select-selector {
        outline: none !important;
        box-shadow: none;
        border: none !important;
      }
    }

    .ant-select-selector {
      border: none !important;
      outline: none !important;
      background-color: transparent;
      .ant-select-selection-search-input {
        .f-13();
        color: @black;
      }
      .ant-select-selection-placeholder {
        color: @placeHolder;
        .f-12();
        opacity: 1;
        user-select: none;
      }
    }

  }
}
