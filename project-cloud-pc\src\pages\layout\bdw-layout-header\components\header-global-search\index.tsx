import React, { useEffect, useMemo, useState } from "react";
import { Select, Spin } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import "./index.less";
import { useDispatch, useSelector, history } from "umi";
import { setLocalStorage } from '@/utils/utils'


const HeaderGlobalSearch: React.FC<{}> = () => {
  const dispatch = useDispatch();
  const { projectListInfo, projectListOptions } = useSelector((state: any) => state.commonTask);

  useEffect(() => {
    dispatch({ type: 'commonTask/fetProjectListInfo' })
  }, [])
  const onSelect = (e: string) => {
    history.push(`/my-project-detail/${e}`)

  }


  return (
    <div className='header-global-search'>
      <Select className='header-global-search-input'
        placeholder="搜索项目"
        allowClear
        autoClearSearchValue
        notFoundContent={null}
        showSearch
        options={projectListOptions}
        // @ts-ignore
        filterOption={(input, option) => (option?.label?.indexOf(input) != -1)}
        suffixIcon={<SearchOutlined />}
        onSelect={onSelect}
      />
    </div>
  )
}

export default HeaderGlobalSearch
