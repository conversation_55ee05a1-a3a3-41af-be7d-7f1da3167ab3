@import "../../../styles/base";

.layout-header {
  background-color: @white;
  border-bottom: 1px solid @divider;
  height: 48px;
  // 顶部固定
  position: fixed;
  width: 100%;
  z-index: 999;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // transition: all .3s;
  >div {
    height: 100%;
  }

  .layout-header-left {
    display: flex;
    .layout-header-switch-button {
      width: 49px;
      font-size: 24px;
      line-height: 48px;
      .pointer();
      color: @primary;
      padding: 0 12px;
      border-right: 1px solid @divider;
      margin: unset;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .ant-select-selection-item{
      font-weight: bold;
    }
  }

  .layout-header-center {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
  }

  .layout-header-right {
    display: flex;
  }
}
