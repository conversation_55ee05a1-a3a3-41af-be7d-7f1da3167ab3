import React, { useState, useEffect } from "react";
import BdwLogo from "@/components/bdw-logo";
import HeaderGlobalSearch from "@/pages/layout/bdw-layout-header/components/header-global-search";
import BdwIcon from "@/components/bdw-icon";
import { BdwRow } from '@/components'
import "./index.less";
import { MenuUnfoldOutlined, MenuFoldOutlined } from "@ant-design/icons";
import { useContainer } from "@/utils/Container";
import { LayoutContainer } from "@/pages/layout/context";
import { history } from "@@/core/history";
import { useSelector,useParams,useDispatch } from 'umi';
import { useBoolean, useRequest } from "ahooks";
import { getLocalStorage ,setLocalStorage} from "@/utils/utils";
import { Select } from "antd";


interface LayoutHeaderProps {
  setCollapsedStatus?: any
}
const LayoutHeader = (props: LayoutHeaderProps) => {
  const { title, setProjectId, collapsed, toggle, setCollapsedShow } = useContainer(LayoutContainer);
  const { setCollapsedStatus } = props;
  const path = history.location.pathname;
  const { projectListInfo ,projectListOptions} = useSelector((state: any) => state.commonTask);
  const { basicProjectInfo } = useSelector((state: any) => state.projectTasks);
  const { projectId } = basicProjectInfo;
  const dispatch = useDispatch();

  const [selectId, setSelectId] = useState('');
  const [showFlag, { setFalse, setTrue }] = useBoolean(false);
  const changeCollapsedStatus = () => {
    setCollapsedStatus(collapsed)
    toggle();
  }
  useEffect(() => {
    if (projectId) {
      setSelectId(projectId);
    }else{
      setSelectId('');
    }
  }, [projectId])
  // 当前路由不是主页，不是折叠的时候。那么跳转需要折叠菜单栏
  useEffect(() => {
    if (path === "/index") {
      setCollapsedShow();
      setCollapsedStatus(true)
    }
    if (path !== "/index" && !collapsed) {
      toggle();
      setCollapsedStatus(false)
    }
    if (path.indexOf('/my-project-detail/') == -1) {
      setFalse();
      // 不是项目详情页面时情况项目详情数据
      dispatch({
        type:'projectTasks/resetProjectTask'
      })
    } else {
      setTrue();
    }
  }, [path])
  const onSelect = (e: string) => {
    setSelectId(e);
    history.push(`/my-project-detail/${e}`)
  }
  // @ts-ignore
  return (
    <div className="layout-header">
      <div className="layout-header-left">
        <div className="layout-header-switch-button" onClick={changeCollapsedStatus}>
          {collapsed ? <BdwIcon icon='iconretract' /> : <BdwIcon icon='iconunwind' />}
        </div>
        <BdwLogo />
        {
          (selectId && showFlag) && <BdwRow type="flex-center">
            <Select
              onSelect={onSelect}
              options={projectListOptions}
              style={{ width: '370px' }}
              value={selectId}
            />
          </BdwRow>
        }


      </div>
      <div className="layout-header-center">
        <HeaderGlobalSearch />
      </div>
      <div className="layout-header-right">

      </div>
    </div>
  )
}

export default LayoutHeader
