import React from "react";
import BdwMenuItem from "@/pages/layout/bdw-layout-menu/components/menu-item";

import "./index.less";
import BdwIcon from "@/components/bdw-icon";

const MenuAboutMy: React.FC = () => {
  return (
    <div className='menu-about-my'>
      <BdwMenuItem url='/index' icon={()  => <BdwIcon icon='iconhomepage' type='class' />}>首页</BdwMenuItem>
      <BdwMenuItem url='/my-project' icon={()  => <BdwIcon icon='iconmy-project' type='class' />}>我的项目</BdwMenuItem>
      <BdwMenuItem url='/my-task' icon={()  => <BdwIcon icon='iconmy-task' type='class' />}>我的任务</BdwMenuItem>
      {/* <BdwMenuItem url='/my-schedule' icon={()  => <BdwIcon icon='iconschedule' type='class' />}>我的日程</BdwMenuItem> */}
      <BdwMenuItem url="/team-task" icon={() => <BdwIcon icon='iconmanage' type='class' />}>团队任务</BdwMenuItem>
      <BdwMenuItem url="/project-template" icon={() => <BdwIcon icon='iconproject-template' type='class' />}>项目模板</BdwMenuItem>
    </div>
  )
}

export default MenuAboutMy;
