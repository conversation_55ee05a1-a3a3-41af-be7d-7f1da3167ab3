import React from "react";
import BdwMenuItem from "@/pages/layout/bdw-layout-menu/components/menu-item";

import "./index.less";
import BdwIcon from "@/components/bdw-icon";

const MenuAssignInvitation = () => {
    return <div className='menu-assign-invitation'>
        <BdwMenuItem url='/index1' icon={() => <BdwIcon icon='iconhomepage' type='class' />}>首页</BdwMenuItem>
        <BdwMenuItem url='/project-management1' icon={() => <BdwIcon icon='iconmy-project' type='class' />}>我的项目</BdwMenuItem>
        <BdwMenuItem url='/my-task1' icon={() => <BdwIcon icon='iconmy-task' type='class' />}>我的任务</BdwMenuItem>
        <BdwMenuItem url='/my-schedule1' icon={() => <BdwIcon icon='iconschedule' type='class' />}>我的日程</BdwMenuItem>
        <BdwMenuItem url="/group-task1" icon={() => <BdwIcon icon='iconmanage' type='class' />}>团队任务</BdwMenuItem>
        <BdwMenuItem url="/project-template1" icon={() => <BdwIcon icon='iconproject-template' type='class' />}>项目模板</BdwMenuItem>
    </div>
}
export default MenuAssignInvitation;