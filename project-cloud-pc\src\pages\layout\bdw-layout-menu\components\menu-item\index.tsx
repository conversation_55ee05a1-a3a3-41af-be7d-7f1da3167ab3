import React from "react";
import {MenuItemShowNumber} from "@/components";
import {history} from "@@/core/history";

import "./index.less";
import BdwRow from "@/components/bdw-row";

interface MenuItemProps {
  // 图标
  icon?: () => React.ReactNode
  // 数量
  number?: string | number
  // 数量颜色
  numberColor?: string | number
  // 路由地址
  url?: string
  // 样式名
  className?: string
}

const BdwMenuItem: React.FC<MenuItemProps> = (props) => {
  const {url} = props;
  const currentUrl = history.location.pathname;

  const isActive = currentUrl.includes(url) ? "active" : "";

  const toPath = () => {
    if(url) history.push(url);
  }
  return (
    <BdwRow type='flex' className={`${isActive} ${props.className || ""} bdw-menu-item`} onClick={toPath}>
      <div className='menu-item-icon'>
        {props.icon?.()}
      </div>
      <div className='menu-item-content flex-1'>
        {props.children}
      </div>
      <div className='menu-item-number'>
        {props.number && props.number > 0
        ? <MenuItemShowNumber>{props.number}</MenuItemShowNumber>
        : <span>{props.number}</span>}
      </div>
    </BdwRow>
  )
}

export default BdwMenuItem
