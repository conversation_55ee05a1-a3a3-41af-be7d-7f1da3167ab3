import React, {useMemo} from "react";
import "./index.less";
import BdwMenuItem from "@/pages/layout/bdw-layout-menu/components/menu-item";
import {MenuItemShowNameIcon} from "@/components";
import {useRequest} from "ahooks";

interface ResDataItem {
  code: string | number
  name: string
  num: string | number
}

const MenuTeamTask: React.FC = () => {
  // const {data: resultData} = useRequest( () => getTeamReportStatisticData(),{
  //   staleTime: 5000
  // })

  // const handleResultData = useMemo(() => {
  //   if(resultData && resultData.length > 0) {
  //     return resultData
  //   }
  //   return []
  // }, [resultData])
  const handleResultData = [
    {
      name:'张三',
      num:'10',
      code:'182'
    },
    {
      name:'李四',
      num:'100',
      code:'1628'
    },
    {
      name:'王二麻子',
      num:'101',
      code:'1323'
    },
  ]

  const showContent = handleResultData.map((item: ResDataItem, index: number) => {
    return (
      <BdwMenuItem key={index} url={`/team-report/${item.code}`} icon={() => <MenuItemShowNameIcon name={item.name}/>} number={item.num}>{item.name}</BdwMenuItem>
    )
  })
  return (
    <div className='menu-team-task'>
      {showContent}
    </div>
  )
}

export default MenuTeamTask
