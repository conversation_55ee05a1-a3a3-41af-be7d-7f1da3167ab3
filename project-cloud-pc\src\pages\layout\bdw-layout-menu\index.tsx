import React, { useEffect } from "react";
import MenuAboutMy from "@/pages/layout/bdw-layout-menu/components/menu-about-my";
import MenuTeamTask from "@/pages/layout/bdw-layout-menu/components/menu-team-task";
import MenuTitle from "@/pages/layout/bdw-layout-menu/components/menu-title";
import MenuAssignInvitation from "@/pages/layout/bdw-layout-menu/components/menu-assign-invitation";
import {LayoutContainer} from "@/pages/layout/context";
import "./index.less";
import {useContainer} from "@/utils/Container";
import { useParams, useSelector, useDispatch } from 'umi';


const LayoutMenu: React.FC<{}> = () => {
  const {collapsed} = useContainer(LayoutContainer);
  const dispatch = useDispatch();
  const menuClassName = collapsed ? "bdw-menu-collapsed" : "";
  useEffect(()=>{
    dispatch({type: 'commonTask/fetchUserInfo'});
    dispatch({ type: 'projectTasks/fetchAttachmentFormat' });
  },[])

  return (
    <div className={`bdw-menu ${menuClassName}`}>
      <MenuAboutMy/>
      {/* <MenuTitle title={collapsed?"团队":"团队汇报"} />  */}
      {/* <MenuTeamTask/> */}
      {/* <MenuTitle title={collapsed?"分配":"分配邀请"} />  */}
      {/* <MenuAssignInvitation/> */}
    </div>
  )
}

export default LayoutMenu
