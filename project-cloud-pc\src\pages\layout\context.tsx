import {useState} from "react";
import {createContainer} from "@/utils/Container";
import {useBoolean} from "ahooks";

interface InitDataInterface {
  collapsed: boolean
  toggle: () => void
  setCollapsedShow: () => void
  title: string
  setTitle: (title: string) => void
  projectId: string
  setProjectId: (projectId: string) => void
}

export const LayoutContainer = createContainer<InitDataInterface>(() => {
  const [collapsed, {setFalse: setCollapsedShow,toggle}] = useBoolean(false)
  const [title, setTitle] = useState<string>("")
  const [projectId, setProjectId] = useState<string>("")

  return {
    collapsed,
    title,
    projectId,
    toggle,
    setTitle,
    setProjectId,
    setCollapsedShow
  }
})



