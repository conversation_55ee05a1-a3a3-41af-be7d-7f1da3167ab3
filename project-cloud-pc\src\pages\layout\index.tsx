import React, { useState } from "react";
import { LayoutContainer } from "@/pages/layout/context";
import zhCN from "antd/es/locale/zh_CN";
import LayoutMenu from "@/pages/layout/bdw-layout-menu";
import LayoutHeader from "@/pages/layout/bdw-layout-header";

import { useLocation } from 'umi';

import "./index.less"
import { ConfigProvider } from "antd";

const BdwPage: React.FC = ({ children }) => {
  // 定义全局变量，储存侧边为收起还是展开
  const [collapsedStatus, setCollapsedStatus] = useState<boolean>(true);
  const paddingLeftClass = collapsedStatus ? "paddingLeftClass" : "";
  const { pathname } = useLocation();


  return (
    //@ts-ignore
    <LayoutContainer.Provider>
      <ConfigProvider locale={zhCN}>
        <div className='bdw-layout'>
          {
            pathname.includes('project-accounting-management') ? children : <>
              <LayoutHeader setCollapsedStatus={setCollapsedStatus} />
              <div className='bdw-layout-main'>
                <LayoutMenu />
                <div className={`bdw-layout-content flex-1 ${paddingLeftClass}`}>
                  {children}
                </div>
              </div>
            </>
          }
        </div>
      </ConfigProvider>
    </LayoutContainer.Provider>
  )
}

export default BdwPage
