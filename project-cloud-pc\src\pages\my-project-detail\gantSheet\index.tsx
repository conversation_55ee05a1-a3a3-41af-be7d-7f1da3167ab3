import React, { useMemo, useState, useEffect } from 'react';
import { SpreadSheets, Worksheet } from '@grapecity/spread-sheets-react';
import * as GC from '@grapecity/spread-sheets';
import '@grapecity/spread-sheets-tablesheet';
import '@grapecity/spread-sheets-ganttsheet';
import { getListTask } from '@/service/projectDos/my-project-detail/projectTasks';
import { Spin } from 'antd';
import { useRequest } from 'ahooks';
import { useParams } from 'umi';
import { initTaskIndex } from "@/utils/utils";
import "@grapecity/spread-sheets-resources-zh"
GC.Spread.Common.CultureManager.culture('ZH-CN')
import './index.less';

let hostStyle = {
  width: '100%',
  height: '600px',
  border: '1px solid darkgray'
}

export default function AppFunc() {
  const { projectId } = useParams<{ projectId: string }>();
  const { data, loading } = useRequest<any>(() => getListTask(projectId))
  const [spreadVal, setSpreadVal] = useState<any>(null)

  let initSpread = function (value: any) {
    let spread = value;
    setSpreadVal(value)
    initGanttSheetWithChildrenData(spread);
    initMenu(spread)
  }

  const dataSouse = useMemo(() => {
    initTaskIndex(data);
    // console.log(data)
    return [
      {
        id: 1,
        taskNumber: 1,
        parentId: null,
        name: '项目1',
        duration: '3 day',
        start: '23/12/23 08:00',
        finish: '23/12/27 17:00',
        predecessors: '',
        mode: 'Manual',
        children: [
          {
            id: 2,
            taskNumber: 2,
            parentId: null,
            name: '项目1-子项目1',
            duration: '10 day',
            start: '23/12/23 08:00',
            finish: '23/12/25 17:00',
            predecessors: '',
            mode: 'Manual',
          },
          {
            id: 3,
            taskNumber: 3,
            parentId: null,
            name: '项目2-子项目2',
            duration: '2 day',
            start: '23/12/26 08:00',
            finish: '23/12/27 08:00',
            predecessors: '2',
            mode: 'Manual',
          }
        ]
      },
      // {
      //   id: 4,
      //   taskNumber: 4,
      //   parentId: null,
      //   name: '项目2',
      //   duration: '4 day',
      //   start: '23/12/24 08:00',
      //   finish: '23/12/29 17:00',
      //   predecessors: '1',
      //   mode: 'Manual'
      // },
    ]
    if (data) {
      initTaskIndex(data);
      console.log(data)
      return data
    } else {
      return []
    }
  }, [data])


  function initGanttSheetWithChildrenData(spread) {

    var tableName = "Gantt_Children";
    var baseApiUrl = getBaseApiUrl();
    var apiUrl = baseApiUrl + "/" + tableName;
    var dataManager = spread.dataManager();
    var myTable1 = dataManager.addTable("myTable1", {
      batch: true,
      data: dataSouse ?? [],
      schema: {
        hierarchy: {
          type: "ChildrenPath",
          column: "children"
        },
      },
    });
    var ganttSheet = spread.addSheetTab(2, "GanttSheet3", GC.Spread.Sheets.SheetType.ganttSheet);
    // setTable(ganttSheet);
    var view = myTable1.addView("myView1", [
      { value: "taskNumber", caption: "NO.", width: 60 },
      { value: "name", caption: "Task Name", width: 200 },
      { value: "duration", caption: "Duration", width: 90 },
      // { value: "start", caption: "Start", width: 200 },
      // { value: "finish", caption: "Finish", width: 200 },
      { value: "predecessors", caption: "Predecessors", width: 120 },
      { value: "leaderName", caption: "leaderName", width: 120 }
    ]);
    view.fetch().then(function () {
      ganttSheet.bindGanttView(view);
      //获取默认的工作周  默认周末不上班
      // let workweek = ganttSheet.project.calendar.defaultWorkWeek;
      //配置不上班时间
      // workweek.workDays[4] = []
      // ganttSheet.project.calendar.defaultWorkWeek = workweek;
      // ganttSheet.project.calendar = GC.Spread.Sheets.GanttSheet.Calendar.standard
    });
    setTimeout(() => { console.log(ganttSheet.getDataView(), '数据源') }, 1000)

  }
  function defaultWorkDay() {
    return [
      {
        start: { hour: 8, minute: 0 },
        end: { hour: 12, minute: 0 }
      },
      {
        start: { hour: 13, minute: 0 },
        end: { hour: 17, minute: 0 }
      }
    ]
  }
  function getBaseApiUrl() {
    return "https://developer.mescius.com/spreadjs/demos/server/api"
  }
  
  window.addEventListener('click', () => {
    console.log(spreadVal?.dataManager().tables.myTable1.getChanges(), '<<click');
  })

  function initMenu(spread){

  }
  function ContextMenu(spread){
    ContextMenu.prototype = new GC.Spread.Sheets.ContextMenu.ContextMenu(spread);
  }
  return (<div className="sample-tutorial">
    <div className="sample-spreadsheets">
      <Spin spinning={loading}>
        <SpreadSheets workbookInitialized={spread => initSpread(spread)} hostStyle={hostStyle}>
        </SpreadSheets>
      </Spin>
    </div>
  </div>);
}
