/**
 * @description 我的项目->详情
 * <AUTHOR>
 * @date 2023-10-31 16:40:41
*/
import React, { useEffect, useState } from 'react';
import { useDispatch, useParams,useLocation } from 'umi';
import { Tabs, Button, Space } from 'antd';
import { BdwIcon } from '@/components';
import milestone from './milestone';
import projectOverview from './projectOverview';
import projectTasks from './projectTasks';
import teamHistory from './teamHistory';
import teamTasks from './teamTasks';
import valueReview from './valueReview';
import GantSheet from './gantSheet';
import { DisplayType } from './Enum';
import { myProjectDetailTabs,myProjectDetailTabKey } from '@/constants/Enum';
import './index.less';


interface ContentComponentProps {
    padding?: string
}

const WrapComponent = <T extends {}>(WrappedComponent: React.FC<T>) => {
    const ContentComponent = (props: ContentComponentProps & T) => {
        const { padding } = props;
        return (
            <div className={`wrap-container ${padding}`}>
                <WrappedComponent  {...props as T} />
            </div>
        )
    }
    return ContentComponent;
}

const MyProjectDetail: React.FC = (props) => {
    const [displayType, setDisplayType] = useState<DisplayType>(DisplayType.TABLE);
    const location: any = useLocation();
    const {taskId} = location.query;
    const dispatch = useDispatch();
    const { projectId } = useParams<{ projectId: string }>();
    useEffect(() => {
        dispatch({
            type: 'projectOverview/fetchProjectOverview',
            payload: projectId
        })
        dispatch({
            type: 'projectTasks/fetchTaskList',
            payload: projectId,
            loading:true,
            taskId
        })
        dispatch({
            type: 'projectTasks/fetchProjectInfo',
            payload: projectId
        })
    }, [projectId])
    const TabsItem: any[] = myProjectDetailTabs.map((item: any, index: number) => {
        let children: any;
        switch (item.key) {
            case myProjectDetailTabKey.PROJECT_TASKS://项目任务
                const ProjectTasks = WrapComponent(projectTasks);
                children = displayType == DisplayType.TABLE ? <ProjectTasks padding={'p-16 p-wrapper'} /> : <GantSheet />;
                break;
            case myProjectDetailTabKey.TEAM_TASKS://团队任务
                const TeamTasks = WrapComponent(teamTasks);
                children = <TeamTasks padding={'pl-16 pr-16'} />
                break;
            case myProjectDetailTabKey.TEAM_HISTORY://项目日程
                const TeamHistory = WrapComponent(teamHistory);
                children = <TeamHistory />
                break;
            case myProjectDetailTabKey.VALUE_REVIEW://价值评审
                const ValueReview = WrapComponent(valueReview);
                children = <ValueReview padding={'p-16'} />
                break;
            case myProjectDetailTabKey.PROJECT_OVERVIEW://项目概览
                const ProjectOverview = WrapComponent(projectOverview);
                children = <ProjectOverview />
                break;
            case myProjectDetailTabKey.MILESTONE://里程碑
                const Milestone = WrapComponent(milestone);
                children = <Milestone />
                break;
        }
        return {
            ...item,
            children
        }
    })
    return (
        <div className='my-project-detail-container'>
            <Tabs
                className='my-project-detail-tabs'
                items={TabsItem}
                defaultActiveKey={myProjectDetailTabKey.PROJECT_TASKS}
                destroyInactiveTabPane={true}
                // 甘特图
                // tabBarExtraContent={{
                //     right: displayType == DisplayType.TABLE ? <Button type='link' className='bdw-icon-button' onClick={() => setDisplayType(DisplayType.GANTT)}>
                //         <Space>
                //             <BdwIcon className='f-16' icon="iconganttchart" />
                //             <span>甘特图</span>
                //         </Space>
                //     </Button> : <Button type='link' className='bdw-icon-button' onClick={() => setDisplayType(DisplayType.TABLE)}>
                //         <Space>
                //             <BdwIcon className='f-16' icon="iconview-board" />
                //             <span>表格</span>
                //         </Space>
                //     </Button>
                // }}
            />
        </div>
    )
}
export default MyProjectDetail;