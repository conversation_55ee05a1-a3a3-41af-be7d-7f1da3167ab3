/**
 * @description 项目概览
 * <AUTHOR>
 * @date 2023-10-31 17:44:35
*/
import React, { useEffect, useState, useMemo, useRef } from 'react';
import { BdwRow, BdwCommonIcon, BdwFormItems, BdwInput } from '@/components';
import ProjectReleaseAudit from './projectReleaseAudit';
import ProjectDynamic from './projectDynamic';
import ProjectDetailReadonlyInfo from './projectDetailReadonlyInfo';
import ProjectSetting from './projectSetting';
import ProjectAllocationSituation from './projectAllocationSituation';
import ProjectScheduling from './projectScheduling';
import ProjectStatistics from './projectStatistics';
import ExportOrPontProjectModal from './exportPaintProjectModal';
import copy from 'copy-to-clipboard';
import { useBoolean, useUpdateEffect } from 'ahooks';
import { message, Modal, Button, Input } from 'antd';
import {
    deleteProjectApi,
    startupProject,
    pauseProject,
    listProjectFunction,
    copyProject,
    projectStatisticsApi
} from "@/service/projectDos/my-project-detail/projectOverview";
import { useParams, useSelector, useDispatch, useRequest, history, useLocation } from 'umi';


import { DividerLine } from '../projectTasks';
import { AuditRelease, BtnKey, ProjectOverviewFunctionCode } from '@/constants/Enum';
import {startType} from './Enum';
import './index.less';
import { disabledFlag } from '../projectTasks';
import { getLocalStorage } from "@/utils/utils";
import moment from 'moment';

const ProjectOverview: React.FC = () => {
    const dispatch = useDispatch();
    const inputRef = useRef();
    const [openRAModal, { setFalse: openRAHide, setTrue: openRAShow }] = useBoolean(false);//申请发布 || 项目审核
    const [openProjectSetting, { setFalse: openProjectSettingHide, setTrue: openProjectSettingShow }] = useBoolean(false);//项目设置
    const [deleteProjectModal, { setFalse: deleteProjectModalHide, setTrue: deleteProjectModalShow }] = useBoolean(false);//删除项目
    const [suspendProjectModal, { setFalse: suspendProjectHide, setTrue: suspendProjectModalShow }] = useBoolean(false);//暂停项目
    const [startProjectModal, { setFalse: startProjectModalHide, setTrue: startProjectModalShow }] = useBoolean(false);//项目启动
    const [timeOutModal,{setTrue:timeOutModalShow,setFalse:timeOutModalHide}]=useBoolean(false);//项目启动异常提示
    const [projectTemplate, { setFalse: projectTemplateHide, setTrue: projectTemplateShow }] = useBoolean(false);//另存为项目模板
    const [copyProjectModal, { setFalse: copyProjectModalHide, setTrue: copyProjectModalShow }] = useBoolean(false);//复制项目
    const [exportOrPointProjectModal, { setFalse: exportOrPointProjectModalHide, setTrue: exportOrPointProjectModalShow }] = useBoolean(false);//导出打印
    const [defaultCopyName, setDefaultCopyName] = useState<string>('');//保存项目副本的项目名
    const [nameDisabled, { setFalse: nameDisabledFalse, setTrue: nameDisabledTrue }] = useBoolean(false);//保存项目是否只读
    const [modalRefresh, setModalRefresh] = useState(0);
    const { projectId } = useParams<{ projectId: string }>();
    const [currentType, setCurrentType] = useState<'release' | 'audit' | ''>('');
    const { projectOverviewDetails } = useSelector((state: any) => state.projectOverview);
    const [copyProjectId, setCopyProjectId] = useState('');
    const location = useLocation();
    const { basicProjectInfo } = useSelector((state: any) => state.projectTasks);
    const { name } = basicProjectInfo;
    const { data: functionality } = useRequest<any>(() => listProjectFunction(projectId), {
        refreshDeps: [
            modalRefresh
        ],
    });
    const { data: projectStatisticsData, run: runStatistics } = useRequest<any>(() => projectStatisticsApi(projectId), {
        manual: true
    });
    useEffect(() => {
        if (projectOverviewDetails && projectOverviewDetails?.started) {
            runStatistics();
        }
    }, [projectOverviewDetails])

    useUpdateEffect(() => {
        dispatch({
            type: 'projectOverview/fetchProjectOverview',
            payload: projectId
        })
    }, [modalRefresh])

    // 关闭项目设置弹窗
    const closeProjectSettingModal = () => {
        openProjectSettingHide();
        setModalRefresh(modalRefresh + 1);
    }

    //项目审核
    const projectReview = () => {
        setCurrentType('audit');
        openRAShow();
    }
    // 删除项目
    const confirmDelete = () => {
        deleteProjectApi(projectId).then(() => {
            message.success('删除成功！')
            deleteProjectModalHide();
            history.push('/my-project')
        })
    }
    // 启动项目
    const confirmStart = () => {
        startupProject(projectId).then(() => {
            message.success('启动成功！');
            setModalRefresh(modalRefresh + 1)
            startProjectModalHide();
        }).catch((error)=>{
            if(error.code == 10190){
                startProjectModalHide();
                timeOutModalShow();
            }
        })
    }
    //启动项目异常
    const onStartType = (type: startType.AUTOMATIC_PROVISIONING | startType.START_NOW) => {
        const data = {startupMethod:type};
        startupProject(projectId,data).then(() => {
            message.success('启动成功！');
            setModalRefresh(modalRefresh + 1);
            timeOutModalHide();
        })
    }
    // 暂停项目
    const confirmSuspend = () => {
        pauseProject(projectId).then(() => {
            message.success('暂停成功！');
            setModalRefresh(modalRefresh + 1)
            suspendProjectHide();
        })
    }
    //复制项目
    const copyProjectHandle = () => {
        setDefaultCopyName(name + '-副本-' + moment().format('YYYY/MM/DD HH:mm'));
        copyProjectModalShow();
    }
    //复制项目链接
    const copyProjectLink = () => {
        copy(window.location.href);
        message.success('项目链接已复制!')
    }
    //审核申请发布完成回调
    const auditReleaseFinish = () => {
        setModalRefresh(modalRefresh + 1);
        openRAHide();
    }
    //确认复制项目
    const confirmCopyProject = () => {
        if (!defaultCopyName) {
            message.error('请填写项目名称');
            return
        }
        copyProject(projectId, { name: defaultCopyName }).then((res: any) => {
            setModalRefresh(modalRefresh + 1);
            setCopyProjectId(res);
            message.success('复制成功!');
            nameDisabledTrue();
        })
    }
    //取消复制项目、关闭复制项目弹窗
    const cancelCopyModal = () => {
        copyProjectModalHide();
        nameDisabledFalse();
    }
    //打开复制项目
    const openCopyProject = () => {
        cancelCopyModal();
        window.open(`/#/my-project-detail/${copyProjectId}`)
    }
    useEffect(() => {
        if (copyProjectModal && inputRef?.current) {
            //@ts-ignore
            inputRef?.current?.focus();
        }
    }, [inputRef, copyProjectModal])

    return (
        <div className='project-overview-container'>
            {
                projectOverviewDetails && <>
                    <BdwRow type='flex' className='btn-overview-wrapper' >
                        <BdwCommonIcon
                            name='返回'
                            icon='iconback'
                            className="mr-10 ml-10"
                            onClick={() => { history.push('/my-project') }}
                        />
                        <BdwCommonIcon
                            name='刷新'
                            icon='iconrefresh'
                            className="mr-10"
                            onClick={() => { history.go(0) }}
                        />
                        <DividerLine />
                        <BdwCommonIcon
                            name='申请发布'
                            icon='iconrelease'
                            className="mr-10"
                            onClick={() => { setCurrentType('release'); openRAShow(); }}
                            disabled={disabledFlag(functionality, ProjectOverviewFunctionCode.APPLY_RELEASE, BtnKey.projectFunctionCode)}
                        />
                        <BdwCommonIcon
                            name='项目审核'
                            icon='iconproject-review'
                            className="mr-10"
                            onClick={projectReview}
                            disabled={disabledFlag(functionality, ProjectOverviewFunctionCode.AUDIT_APPLY, BtnKey.projectFunctionCode)}
                        />
                        <BdwCommonIcon
                            name='项目启动'
                            icon='iconproject-startup'
                            className="mr-10"
                            onClick={startProjectModalShow}
                            disabled={disabledFlag(functionality, ProjectOverviewFunctionCode.STARTUP, BtnKey.projectFunctionCode)}
                        />
                        <BdwCommonIcon
                            name='项目暂停'
                            icon='iconproject-outage'
                            className="mr-10"
                            onClick={suspendProjectModalShow}
                            disabled={disabledFlag(functionality, ProjectOverviewFunctionCode.PAUSE, BtnKey.projectFunctionCode)}
                        />
                        <DividerLine />
                        <BdwCommonIcon
                            name='项目设置'
                            icon='iconsetting'
                            className="mr-10"
                            onClick={openProjectSettingShow}
                            disabled={disabledFlag(functionality, ProjectOverviewFunctionCode.SETTING, BtnKey.projectFunctionCode)}
                        />
                        <BdwCommonIcon
                            name='复制项目链接'
                            icon='iconlink'
                            className="mr-10"
                            onClick={copyProjectLink}
                        />
                        <BdwCommonIcon
                            name='复制项目'
                            icon='iconcopying'
                            className="mr-10"
                            onClick={copyProjectHandle}
                            disabled={disabledFlag(functionality, ProjectOverviewFunctionCode.COPY, BtnKey.projectFunctionCode)}
                        />
                        <BdwCommonIcon
                            name='删除项目'
                            icon='icondelete'
                            className="mr-10"
                            onClick={deleteProjectModalShow}
                            disabled={disabledFlag(functionality, ProjectOverviewFunctionCode.DELETE, BtnKey.projectFunctionCode)}
                        />
                        <BdwCommonIcon
                            name='保存为项目模板'
                            icon='iconsave'
                            className="mr-10"
                            onClick={projectTemplateShow}
                        />
                        {/* <BdwCommonIcon
                            name='查看回收站'
                            icon='iconsave'
                            className="mr-10"
                        /> */}
                        <DividerLine />
                        <BdwCommonIcon
                            name='打印'
                            icon='iconprint'
                            className="mr-10"
                            onClick={() => exportOrPointProjectModalShow()}
                        />
                        <BdwCommonIcon
                            name='导出任务'
                            icon='iconexport'
                            className="mr-10"
                        />
                    </ BdwRow>
                    {/* 项目发布 || 项目审核 */}
                    {
                        openRAModal && <ProjectReleaseAudit currentType={currentType} show={openRAModal} onFinish={auditReleaseFinish} onClose={() => openRAHide()} />
                    }
                    {/* 项目设置 */}
                    {
                        openProjectSetting && <ProjectSetting show={openProjectSetting} updateBaseInfo={true} onClose={closeProjectSettingModal} />
                    }
                    {/* 项目模板 */}
                    {
                        projectTemplate && <ProjectSetting show={projectTemplate} template={true} onClose={projectTemplateHide} />
                    }
                    {/* 删除项目 */}
                    {
                        deleteProjectModal && <Modal
                            open={deleteProjectModal}
                            title="项目删除"
                            onOk={confirmDelete}
                            onCancel={deleteProjectModalHide}
                        >
                            确认删除项目吗？
                        </Modal>
                    }
                    {/* 启动项目 */}
                    {
                        startProjectModal && <Modal
                            open={startProjectModal}
                            title="项目启动"
                            onOk={confirmStart}
                            onCancel={startProjectModalHide}
                        >
                            确认立即启动项目吗？
                        </Modal>
                    }
                    {
                        timeOutModal && <Modal className='start-abnormal-modal' title='提示' width={550} open={timeOutModal} onCancel={() => timeOutModalHide()}
                            footer={[
                                <Button key='not-deploy' onClick={()=>{
                                    onStartType(startType.START_NOW)
                                }}>立即启动</Button>,
                                <Button key='auto-deploy' onClick={() => {onStartType(startType.AUTOMATIC_PROVISIONING)}}>自动调配</Button>,
                                <Button key='manual-deploy' type='primary' onClick={timeOutModalHide}>手动调配</Button>,
                            ]}
                        >
                            <div className='tips-title'>项目中存在任务开始时间是小于当前时间的任务！是否调配？</div>
                            <div>【立即启动】 - 不再调配任务时间进行启动</div>
                            <div>【自动调配】 - 根据系统时间来进行调配任务时间</div>
                            <div>【手动调配】 - 自行手动去调配任务时间</div>
                        </Modal>
                    }
                    {/* 项目暂停 */}
                    {
                        suspendProjectModal && <Modal
                            open={suspendProjectModal}
                            title="项目暂停"
                            onOk={confirmSuspend}
                            onCancel={suspendProjectHide}
                        >
                            确认暂停项目吗？
                        </Modal>
                    }
                    {/* 打印 */}
                    {
                        exportOrPointProjectModal && <ExportOrPontProjectModal
                            visible={exportOrPointProjectModal}
                            cancelEvent={() => exportOrPointProjectModalHide()}
                        />
                    }
                    {/* 复制项目 */}
                    {
                        copyProjectModal && <Modal
                            open={copyProjectModal}
                            title="复制项目"
                            onCancel={cancelCopyModal}
                            footer={
                                <BdwRow>
                                    {
                                        !nameDisabled ? <>
                                            <Button onClick={cancelCopyModal}>取消</Button>
                                            <Button type='primary' onClick={confirmCopyProject}>确认复制</Button>
                                        </> : <>
                                            <Button onClick={cancelCopyModal}>关闭对话框</Button>
                                            <Button type='primary' onClick={openCopyProject}>打开复制项目</Button>
                                        </>
                                    }
                                </BdwRow>
                            }
                            className={`copy-project-name-modal ${nameDisabled ? 'disabled-name' : ''}`}
                        >
                            <div>
                                <div className='f-12 copy-project-label'>项目名称<span>*</span></div>
                                {/* @ts-ignore */}
                                <BdwInput
                                    onChange={(e) => { setDefaultCopyName(e.target.value) }}
                                    disabled={nameDisabled}
                                    ref={inputRef}
                                    value={defaultCopyName}
                                    placeholder='请输入项目名称'
                                />
                            </div>
                        </Modal>
                    }
                    {/* 内容区 */}
                    <div className='project-overview-content-wrapper'>
                        <div className='project-overview-content'>
                            <div className='content-left'>
                                {
                                    projectOverviewDetails?.started ? <ProjectScheduling timeProgressStatus={projectStatisticsData?.timeProgressStatus} /> : <ProjectAllocationSituation />
                                }
                                <ProjectDynamic />
                            </div>
                            <div className='content-right'>
                                <ProjectDetailReadonlyInfo />
                                {
                                    projectOverviewDetails?.started && <ProjectStatistics projectStatisticsInfo={projectStatisticsData?.projectStatisticsInfo} />
                                }
                            </div>
                        </div>
                    </div>
                </>
            }
        </div>



    )
}
export default ProjectOverview;