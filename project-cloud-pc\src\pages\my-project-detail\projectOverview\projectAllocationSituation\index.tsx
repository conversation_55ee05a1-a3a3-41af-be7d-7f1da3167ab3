/**
 * @description 任务分派情况
 * <AUTHOR>
 * @date 2023-12-04 15:04:29
*/
import React, { useMemo } from 'react';
import { BdwRow } from '@/components';
import { useRequest } from 'ahooks';
import { useParams } from 'umi';
import {
    taskAssignmentReceiveStatus
} from "@/service/projectDos/my-project-detail/projectOverview";
import ProjectTaskAssignStatisticsItem from '../projectTaskAssignStatisticsItem';
import { allocationSituation } from './Enum';
import './index.less';

interface statisticsDataType {
    notAssignment?: string | number,
    received?: string | number,
    returned?: string | number,
    waitReceive?: string | number,
}
export interface statisticsDataItem {
    name: string,
    code: string,
    num: number | string
}
type nameType = 'notAssignment' | 'received' | 'returned' | 'waitReceive';

const ProjectAllocationSituation: React.FC = () => {

    const { projectId } = useParams<{ projectId: string }>();

    // 获取项目任务的分配详情数据
    const { data: statisticsData } = useRequest<statisticsDataType>(() => taskAssignmentReceiveStatus(projectId), {
        ready: !!projectId,
        refreshDeps: [projectId]
    })
    const colorObject = {
        returned: {
            strokeColor: "#9F9F9F",
            trailColor: "#E2E2E2"
        },
        received: {
            strokeColor: "#5CB85C",
            trailColor: "#CEEACE"
        },
        notAssignment: {
            strokeColor: "#F0AD4E",
            trailColor: "#FBE7CA"
        },
        waitReceive: {
            strokeColor: "#F0AD4E",
            trailColor: "#FBE7CA"
        },
    }
    const getName = (type: nameType) => {
        switch (type) {
            case allocationSituation.notAssignment:
                return '未分派';
            case allocationSituation.received:
                return '已接收';
            case allocationSituation.returned:
                return '已退回';
            case allocationSituation.waitReceive:
                return '待接收';
            default:
                return '';
        }
    }
    const handleStatisticsData = useMemo(() => {
        if (statisticsData) {
            const arr: statisticsDataItem[] = [];
            for (const i in statisticsData) {
                arr.push({
                    //@ts-ignore
                    name: getName(i),
                    code: i,
                    num: statisticsData[i]
                })
            }
            return arr;
        }
        return []
    }, [statisticsData])

    const taskNumTotal = useMemo(() => {
        if (statisticsData) {
            let sum = 0;
            for (const i in statisticsData) {
                sum += Number(statisticsData[i]);
            }
            return sum;
        } else {
            return 0;
        }
    }, [statisticsData]);
    const showProjectTaskStatistics = handleStatisticsData?.map((item, index) => {
        const hasMargin = index === handleStatisticsData.length - 1 ? "" : "mr-40";
        return (
            <ProjectTaskAssignStatisticsItem
                key={item.code}
                className={`${hasMargin} flex-1`}
                title={item.name}
                number={item.num}
                total={taskNumTotal ?? 0}
                strokeColor={colorObject[item.code].strokeColor}
                trailColor={colorObject[item.code].trailColor} />
        )
    })
    return (
        <div className='task-assignment-acceptance'>
            <div className='title f-16'>任务分派接受情况</div>
            <BdwRow type='flex'>
                {showProjectTaskStatistics}
            </BdwRow>
        </div>
    )
}
export default ProjectAllocationSituation;