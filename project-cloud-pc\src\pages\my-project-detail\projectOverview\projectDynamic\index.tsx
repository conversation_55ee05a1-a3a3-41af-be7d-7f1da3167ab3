import React from "react";

import ProjectDynamicItem from "../projectDynamicItem";
import { bdwShowTimeFunction } from "@/utils/utils";
import {BdwFileShow} from '@/components';
import { useContainer } from "@/utils/Container";
import { useParams, useSelector, useDispatch } from 'umi';
import "./index.less";

const ProjectDynamic: React.FC = () => {
  const { projectOverviewDetails } = useSelector((state: any) => state.projectOverview);
  const showDynamicDetail = projectOverviewDetails.projectTaskNews?.map((item: any, index: number) => {
    return (
      <ProjectDynamicItem
        key={`${index}`}
        className='mt-16'
        personName={item.handlerName}
        title={item.eventMainContent}
        time={item.handleTime}
        attach={item.attachments}
        detail={item.eventSubContent} />
    )
  })

  return (
    <div className='project-dynamic'>
      <div className='project-dynamic-title'>
        项目动态
      </div>
      <div className='project-dynamic-content'>
        {showDynamicDetail}
      </div>
    </div>
  )
}

export default ProjectDynamic
