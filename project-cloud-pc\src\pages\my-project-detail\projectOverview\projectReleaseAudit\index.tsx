/**
 * @description 项目申请发布 || 项目审核
 * <AUTHOR>
 * @date 2023-11-02 15:06:28
*/
import React, { useEffect } from 'react';
import {
    BdwRow, BdwCommonIcon, BdwInput,
    BdwFormItems,
    BdwTextarea,
    BdwUpload,
    BdwLoading,
    BdwChooseCompanyStaff,
    BdwFileShow,
    BdwReadonlySpan
} from '@/components';
import { useRequest } from 'ahooks';
import { Drawer, Form, Button, message } from 'antd';
import { listEmpByParams } from '@/service/projectDos/commonApi';
import {
    applyRelease,
    applyReleaseByProjectId,
    auditApplyInfoApi
} from "@/service/projectDos/my-project-detail/projectOverview";
import { CloseOutlined } from '@ant-design/icons';
import { AuditRelease } from '@/constants/Enum';
import { useParams,useSelector } from 'umi';
import './index.less';

interface ProjectReleaseAuditProps{
    currentType: "release" | "audit" | '',
    show: boolean,
    onClose: () => void,
    onFinish?: () => void
}

const ProjectReleaseAudit: React.FC<ProjectReleaseAuditProps> = (props) => {
    const {currentType,show,onClose,onFinish} = props;
    const { projectId } = useParams<{ projectId: string }>();
    const [releaseForm] = Form.useForm();
    const [auditForm] = Form.useForm();
    const {projectOverviewDetails} = useSelector((state: any) => state.projectOverview);
    
     // 查询项目审核信息
     const { data: releaseData, run: getReleaseData } = useRequest(applyReleaseByProjectId, {manual: true})

     useEffect(()=>{
         if(currentType == 'audit'){
            getReleaseData(projectId);
         }
     },[])
    //申请发布 确认申请
    const confirmSubmit = (data: any) => {
        const rData = { ...data,auditorId:data.auditorId?.id, projectId }
        applyRelease(rData).then(() => {
            message.success('申请发布成功！');
            onFinish?.();
        })
    }
    //确认审核发布
    const auditClick = (type: string) => {
        auditForm.validateFields().then(() => {
            const {attach} = auditForm.getFieldsValue();
            const params = {
                ...auditForm.getFieldsValue(),
                projectAuditStatus: type,
                attach: [...new Set(attach?.split(','))].join(),
                //@ts-ignore
                projectApplyId: releaseData.projectApplyId
            }
            auditApplyInfoApi(params).then(() => {
                message.success('审核发布成功!');
                onFinish?.();
            })
        })

    }
    return (
        <Drawer
            open={show}
            width={450}
            closable={false}
            extra={
                <CloseOutlined color='#9f9f9f' onClick={() => { onClose() }} />
            }
            title={currentType == 'release' ? '项目发布' : '项目申请发布'}
            headerStyle={{
                height: '50px',
                fontSize: '18px',
                color: '#333',
                fontWeight: 'bold'
            }}
        >
            <div className='drawer-release-content'>
                <div className='information mb-5'>
                    项目名称
                </div>
                <div className='project-name mb-14'>
                    {/* @ts-ignore */}
                    {currentType == 'release' ? projectOverviewDetails?.instructions : releaseData?.projectName}
                </div>
                <div className='information mb-5'>
                    项目说明
                </div>
                <div className='f-13 mb-14'>
                    {projectOverviewDetails?.instructions}
                </div>
                <div className='information mb-5'>
                    项目负责人
                </div>
                <div className='f-13 mb-14'>
                    {/* @ts-ignore */}
                    {currentType == 'release' ? projectOverviewDetails?.leaderName : releaseData?.leaderName}
                </div>
                {currentType == 'release' ?
                    <Form onFinish={confirmSubmit} form={releaseForm}>
                        <BdwFormItems label="项目审核人" name='auditorId' rules={[{ required: true, message: "请选择项目审核人" }]}>
                            <BdwChooseCompanyStaff apiSrc={listEmpByParams} />
                        </BdwFormItems>
                        <BdwFormItems label="申请备注" name='applyNote' rules={[{ required: true, message: "请填写申请备注" }]}>
                            <BdwTextarea />
                        </BdwFormItems>
                        <BdwFormItems label="附件资料" name='attach'>
                            <BdwUpload uploadType="other" accept="." />
                        </BdwFormItems>
                        <BdwRow>
                            <Button htmlType='submit' type="primary" style={{ marginRight: '10px' }}>确认申请</Button>
                            <Button onClick={() => { onClose() }}>取消</Button>
                        </BdwRow>
                    </Form> :
                    <>
                        <div className='information mb-5'>
                            项目申请备注
                        </div>
                        <div className='f-13 mb-14'>
                            {/* @ts-ignore */}
                            {releaseData?.applyNote}
                        </div>
                        <div className='information mb-5'>
                            附件资料
                        </div>
                        <div className='f-13 mb-14'>
                            {/* @ts-ignore */}
                        {releaseData?.attach ? <BdwFileShow attachments={releaseData?.attach}></BdwFileShow> : <BdwReadonlySpan>无附件资料</BdwReadonlySpan>}
                        </div>
                        <div className='information f-weight mb-5 c-2'>
                            项目审核信息
                        </div>
                        <Form form={auditForm}>

                            <BdwFormItems label="审核意见" name='auditNote' rules={[{ required: true, message: "请填写审核意见" }]}>
                                <BdwTextarea />
                            </BdwFormItems>
                            <BdwFormItems label="附件资料" name='attach'>
                                <BdwUpload uploadType="other" accept="." uploadMore={true} />
                            </BdwFormItems>
                            <BdwRow>
                                <Button onClick={() => { auditClick(AuditRelease.PASSED) }} type="primary" style={{ marginRight: '10px' }}>审核通过</Button>
                                <Button onClick={() => { auditClick(AuditRelease.REJECTED) }} danger type="primary" style={{ marginRight: '10px' }}>驳回申请</Button>
                                <Button onClick={() => { auditClick(AuditRelease.UN_AUDITED) }}>取消</Button>
                            </BdwRow>
                        </Form>
                    </>

                }

            </div>
        </Drawer>
    )
}
export default ProjectReleaseAudit;