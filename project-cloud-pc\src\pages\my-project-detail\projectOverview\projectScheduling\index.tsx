/**
 * @description 时间进度
 * <AUTHOR>
 * @date 2023-12-04 15:38:36
*/
import React, { useMemo } from 'react';
import { BdwRow } from '@/components';
import ProjectTaskAssignStatisticsItem from '../projectTaskAssignStatisticsItem';
import { allocationSituation } from './Enum';
import { statisticsDataItem } from '../projectAllocationSituation';
import './index.less';

interface timeProgressStatusType {
    completionProgress?: string | number,
    normal?: string | number,
    overdueSeven?: string | number,
    overdueThree?: string | number,
}
type nameType = 'normal' | 'overdueSeven' | 'overdueThree' | 'completionProgress';

interface ProjectSchedulingProps {
    timeProgressStatus: timeProgressStatusType
}

const ProjectScheduling: React.FC<ProjectSchedulingProps> = (props) => {
    const { timeProgressStatus } = props;
    // @ts-ignore
    const completionRate = 20;

    const colorObject = {
        overdueThree: {
            strokeColor: "#9F9F9F",
            trailColor: "#E2E2E2"
        },
        normal: {
            strokeColor: "#5CB85C",
            trailColor: "#CEEACE"
        },
        overdueSeven: {
            strokeColor: "#F0AD4E",
            trailColor: "#FBE7CA"
        },
    }
    const getName = (type: nameType) => {
        switch (type) {
            case allocationSituation.normal:
                return '按进度达成';
            case allocationSituation.overdueSeven:
                return '超期7天';
            case allocationSituation.overdueThree:
                return '超期3天';
            default:
                return '';
        }
    }
    const handleStatisticsData = useMemo(() => {
        if (timeProgressStatus) {
            const arr: statisticsDataItem[] = [];
            for (const i in timeProgressStatus) {
                if (i == allocationSituation.completionProgress) continue
                arr.push({
                    //@ts-ignore
                    name: getName(i),
                    code: i,
                    num: timeProgressStatus[i]
                })
            }
            return arr;
        }
        return []
    }, [timeProgressStatus])

    const taskNumTotal = useMemo(() => {
        if (timeProgressStatus) {
            let sum = 0;
            for (const i in timeProgressStatus) {
                sum += Number(timeProgressStatus[i]);
            }
            return sum;
        } else {
            return 0;
        }
    }, [timeProgressStatus]);

    const showProjectTaskStatistics = handleStatisticsData?.map((item, index) => {
        const hasMargin = index === handleStatisticsData.length - 1 ? "" : "mr-40";
        return (
            <ProjectTaskAssignStatisticsItem
                key={item.code}
                className={`${hasMargin} flex-1`}
                title={item.name}
                number={item.num}
                total={taskNumTotal ?? 0}
                strokeColor={colorObject[item.code].strokeColor}
                trailColor={colorObject[item.code].trailColor} />
        )
    })
    return (
        <BdwRow className='project-task-assign-statistics' type='flex'>
            <div className='flex-2 statisticsBox'>
                <div className='project-task-assign-statistics-title'>
                    时间进度状态
                </div>
                <BdwRow type='flex'>
                    {showProjectTaskStatistics}
                </BdwRow>
            </div>

            <div className='flex-1 ml-20'>
                <div className='project-task-assign-statistics-title'>项目结果状态</div>
                <BdwRow>
                    <div className='project-task-assign-statistics-item-title'>目前项目完成进度</div>
                    <div className='project-task-assign-statistics-item-number'>
                        {timeProgressStatus?.completionProgress}
                    </div>
                </BdwRow>
            </div>
        </BdwRow>
    )
}
export default ProjectScheduling;