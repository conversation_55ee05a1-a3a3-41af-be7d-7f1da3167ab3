import React, { useMemo } from "react";
import { BdwRow } from "@/components";
import ProjectTaskAssignStatisticsItem from '../projectTaskAssignStatisticsItem';

import "./index.less"
import { allocationSituation } from './Enum';
import {statisticsDataItem} from '../projectAllocationSituation';

type nameType = 'expiresToday' | 'finished' | 'notFinish' | 'notStartup' | 'overdue' | 'overdueFinished'

interface projectStatisticsInfoType{
    expiresToday?: string | number,
    finished?: string | number,
    notFinish?: string | number,
    notStartup?: string | number,
    overdue?: string | number,
    overdueFinished	?: string | number,
}

interface ProjectStatisticalResultsProps{
    projectStatisticsInfo:projectStatisticsInfoType
}

const ProjectStatisticalResults: React.FC<ProjectStatisticalResultsProps> = (props) => {

    const {projectStatisticsInfo} = props;

    const getName = (type: nameType) => {
        switch (type) {
            case allocationSituation.finished:
                return '已完成';
            case allocationSituation.notFinish:
                return '未完成';
            case allocationSituation.overdue:
                return '已逾期';
            case allocationSituation.notStartup:
                return '未启动';
            case allocationSituation.expiresToday:
                return '今日到期';
            case allocationSituation.overdueFinished:
                return '逾期完成';
            default:
                return '';
        }
    }
    const handleStatisticsData = useMemo(() => {
        if (projectStatisticsInfo) {
            const arr: statisticsDataItem[] = [];
            for (const i in projectStatisticsInfo) {
                if (i !== 'notAssignment') {
                    arr.push({
                        //@ts-ignore
                        name: getName(i),
                        code: i,
                        num: projectStatisticsInfo[i]
                    })
                }

            }
            return arr;
        }
        return []
    }, [projectStatisticsInfo])

    const taskNumTotal = useMemo(() => {
        if (projectStatisticsInfo) {
            let sum = 0;
            for (const i in projectStatisticsInfo) {
                sum += Number(projectStatisticsInfo[i]);
            }
            return sum;
        } else {
            return 0;
        }
    }, [projectStatisticsInfo]);
    const colorObject = {
        finished: {
            strokeColor: "#5CB85C",
            trailColor: "#CEEACE"
        },
        notFinish: {
            strokeColor: "#F0AD4E",
            trailColor: "#FBE7CA"
        },
        overdue: {
            strokeColor: "#D9534F",
            trailColor: "#F4CCCB"
        },
        notStartup: {
            strokeColor: "#9F9F9F",
            trailColor: "#E2E2E2"
        },
        expiresToday: {
            strokeColor: "#428BCA",
            trailColor: "#C7DCEF"
        },
        overdueFinished: {
            strokeColor: "#D9534F",
            trailColor: "#F4CCCB"
        },
    }

    const showProjectStatisticalResults = handleStatisticsData?.map((item, index) => {
        const hasMargin = index === handleStatisticsData.length - 1 ? "" : "mr-40";
        return (
            <ProjectTaskAssignStatisticsItem
                key={item.code}
                className={hasMargin}
                title={item.name}
                number={item.num}
                total={taskNumTotal ?? 0}
                strokeColor={colorObject[item.code].strokeColor}
                trailColor={colorObject[item.code].trailColor} />
        )
    })

    return (
        <BdwRow className='project-statistical-results'>
            <div className='project-statistical-results-title'>
                项目统计
            </div>
            <BdwRow type='flex' className='project-statistical-results-items'>
                {showProjectStatisticalResults}
            </BdwRow>
        </BdwRow>
    )
}

export default ProjectStatisticalResults
