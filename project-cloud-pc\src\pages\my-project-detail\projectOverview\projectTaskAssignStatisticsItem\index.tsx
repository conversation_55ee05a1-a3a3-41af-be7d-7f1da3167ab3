import React from "react";

import "./index.less";
import {Progress} from "antd";

interface ProjectTaskAssignStatisticsItemProps {
  title: string
  number: number | string
  total: number
  strokeColor: string
  trailColor: string
  className?: string
}

export const ProjectTaskAssignStatisticsItem:React.FC<ProjectTaskAssignStatisticsItemProps> = (props) => {
  const {title,number,total,strokeColor,trailColor,className = ""} = props;
  const percent = total > 0 ? parseFloat(((number / total) * 100).toFixed(2)) : 0;
  return (
    <div className={`project-task-assign-statistics-item ${className}`}>
      <div className='project-task-assign-statistics-item-title'>
        {title}
      </div>
      <div className='project-task-assign-statistics-item-number'>
        {number}
      </div>
      <div className='project-task-assign-statistics-item-progress'>
        <Progress strokeColor={strokeColor} trailColor={trailColor} percent={percent} showInfo={false} />
      </div>
    </div>
  )
}

export default ProjectTaskAssignStatisticsItem
