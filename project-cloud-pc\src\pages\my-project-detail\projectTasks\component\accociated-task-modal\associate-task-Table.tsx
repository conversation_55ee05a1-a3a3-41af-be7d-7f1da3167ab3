import { DatePicker, Input } from 'antd';
import React from 'react';
import styled from 'styled-components';
import moment from "moment";
import { BdwRow, BdwTable, BdwTableHeaderSearchComponent } from "@/components";
import { useParams, useSelector, useDispatch } from 'umi';
import { findTaskItem } from '@/utils/utils';
import Highlighter from "react-highlight-words"

import "./index.less"

const IndexSpan = styled.span`
 font-weight: bold;
 font-size: 13px;
 margin-right: 10px;
`;

interface TaskTableProps {
  selectedRowKeysArr?: Array<any>
  setSelectedRowKeysArr?: (val: Array<any>,name: string) => void
}



const NotSupport = styled.div`
  cursor: not-allowed;
  width: 100%;
  height: 100%;
`

const AssociateTaskTable: React.FC<TaskTableProps> = (props) => {
  const dispatch = useDispatch();
  const { associatedTasksTableData, ATExpandedRowKeys, associatedTasksFilter, taskInfo, isAddTask } = useSelector((state: any) => state.projectTasks);
  const { selectedRowKeysArr, setSelectedRowKeysArr } = props
  // 任务表格表头项
  const TaskTableColumns = [
    {
      title: <BdwTableHeaderSearchComponent title="任务">
        <Input placeholder="请输入任务名称进行检索"
          className='no-border-input ant-input-cover-style'
          value={associatedTasksFilter?.name}
          maxLength={20}
          onChange={event => {
            dispatch({
              type: 'projectTasks/setAssociatedTasksFilter',
              typeKey: event.target.value,
              typeName: "name",
              status: event.target.value
            })
          }}
          onPressEnter={(e) => {
            e.nativeEvent.stopImmediatePropagation()
          }}
        />
      </BdwTableHeaderSearchComponent>,
      dataIndex: 'name',
      // render: (value: string, record: any) =>  {
      //   const indexSpan = <span
      //     className='task-index-num'><IndexSpan>{record.index?.map(it => it + 1).join('.')}</IndexSpan></span>;
      //   return <BdwRow type='flex'>
      //     <div>{indexSpan}{record?.title}</div>
      //   </BdwRow>

      // },
      ellipsis: true
    },
    {
      title: <BdwTableHeaderSearchComponent title="工期"><NotSupport /></BdwTableHeaderSearchComponent>,
      dataIndex: 'executionCycle',
      width: 60,
      // render: (value: string, record: any): any =>{
      //   return <>{record.executionCycle}</>
      // }
    },
    {
      title: <BdwTableHeaderSearchComponent title="负责人">
        <Input className='no-border-input ant-input-cover-style'
          placeholder="请输入"
          value={associatedTasksFilter?.leaderName}
          maxLength={128}
          onChange={event => {
            dispatch({
              type: 'projectTasks/setAssociatedTasksFilter',
              typeKey: event.target.value,
              typeName: "leaderName",
              status: event.target.value
            })
          }}
          onPressEnter={(e) => {
            e.nativeEvent.stopImmediatePropagation()
          }}
        />
      </BdwTableHeaderSearchComponent>,
      dataIndex: 'leaderName',
      width: 80,
      // render: (value: string, record: any): any => {
      //   return <>
      //     <Highlighter
      //       highlightClassName="title-highlight"
      //       searchWords={[associatedTasksFilter?.leaderName]}
      //       autoEscape
      //       textToHighlight={record.leaderName ?? ""}
      //     /></>
      // }
    },
    {
      title: <BdwTableHeaderSearchComponent title="起始日期">
        <DatePicker
          // value={taskStore.filter.startTime ? moment(taskStore.filter.startTime) : undefined}
          className='datePicker'
          bordered={false}
          suffixIcon={null}
          onChange={date => {

            if (date === null) {
              // taskStore.filter.setStartTime(undefined)
            } else {
              // taskStore.filter.setStartTime(moment(date))
            }

          }}

        />
      </BdwTableHeaderSearchComponent>,
      dataIndex: 'startTime',
      width: 170,
    },
    {
      title: <BdwTableHeaderSearchComponent title="截止日期">
        <DatePicker
          // value={taskStore.filter.endTime ? moment(taskStore.filter.endTime) : undefined}
          className='datePicker'
          bordered={false}
          suffixIcon={null}
          onChange={date => {
            if (date === null) {
              // taskStore.filter.setEndTime(undefined)
            } else {
              // taskStore.filter.setEndTime(moment(date))
            }

          }}

        />
      </BdwTableHeaderSearchComponent>,
      dataIndex: 'endTime',
      width: 170,
    },
    {
      title: <BdwTableHeaderSearchComponent title="状态"><NotSupport /></BdwTableHeaderSearchComponent>,
      width: 100,
      dataIndex: 'statusName',
    }
  ];

  return (
    <BdwTable
      className='accociated-task-table'
      pagination={false}
      loading={{
        spinning: false
      }}
      expandable={{
        defaultExpandAllRows: true,
        expandedRowKeys: ATExpandedRowKeys,
        onExpandedRowsChange: (e) => {
          dispatch({
            type: 'projectTasks/setATExpandedRowKeys',
            payload: e
          })
        },
      }}
      size="small"
      rowKey="taskId"
      // @ts-ignore
      columns={TaskTableColumns}
      dataSource={associatedTasksTableData ?? []}
      useLocalData
      sticky
      scroll={{ x: true }}
      showPages={false}
      rowSelection={{
        selectedRowKeys:selectedRowKeysArr,
        type: 'checkbox',
        hideSelectAll: true,
        columnWidth: 30,
        checkStrictly: true,
        onChange: ((selectedRowKeys: any) => {
          // @ts-ignore
          const newSelectedRowKeysArr = selectedRowKeys.filter((item:any) => !selectedRowKeysArr.includes(item));
          findTaskItem(associatedTasksTableData,newSelectedRowKeysArr[0],(data)=>{
            setSelectedRowKeysArr?.(newSelectedRowKeysArr,data.name)
          })
        }),
      }}
      // @ts-ignore
      onRow={(task: ITask) => ({
        onClick: async () => {
          if (task.taskId !== taskInfo.taskId) {
            // @ts-ignore
            setSelectedRowKeysArr([task.taskId],task.name);
          }
        },
      })}
    />
  )

};

export default AssociateTaskTable;
