import React, { useEffect, useState } from "react";
import { Button, Checkbox, Modal, Space } from "antd";
import { flatten } from "@/utils/utils";
import { BdwReadonlySpan } from "@/components";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import AssociateTaskTable from "./associate-task-Table";
import "./index.less"
import { isString } from "lodash";


interface AssociatedTaskModalProps {
  visible: boolean
  cancelFun: () => void
  task?: any
  updateTask?: (task: Partial<any>) => void
  editable?: boolean
  tasks?: any[]
  successEvent?: (data: any,name?: string) => void
}

const AssociatedTaskModal: React.FC<AssociatedTaskModalProps> = (props) => {
  const { visible, cancelFun, task, successEvent } = props;
  const initialPreTaskId = task?.associationTaskId ? isString(task?.associationTaskId) ? task?.associationTaskId?.split(',') : task?.associationTaskId : undefined;
  const [selectedRowKeysArr, setSelectedRowKeysArr] = useState<Array<any>>(initialPreTaskId ? initialPreTaskId : [])
  const [name, setName] = useState('');

  // 确认关联任务,关闭Modal
  const enterAssociatedEvent = async () => {
    // await taskStore.updateTask({
    //   preTask: flattenTasks.find(it => it.id === selectedRowKeysArr[0])
    // })
    successEvent?.(selectedRowKeysArr,name);
  }

  // 取消关联任务，关闭Modal
  const cancelAssociatedEvent = () => {
    cancelFun?.()
  }

  // 是否确认连接关联任务的截止时间选框
  // const LinkPreTask = () => {
  //   const onCheckboxChange = async (e: CheckboxChangeEvent) => {
  //     await taskStore.updateTask({
  //       relationPreTime: e.target.checked
  //     })
  //   }

  //   return useObserver(() => {
  //     return selectedRowKeysArr.length > 0 && <>
  //       <Checkbox checked={taskStore.currentTask?.relationPreTime ?? false} onChange={onCheckboxChange} className='mr-5' />
  //       <span className='f-13'>连接关联任务截止时间</span>
  //     </> || <></>
  //   })
  // }

  return (
    <Modal
      getContainer={false}
      open={visible}
      title={
        <Space className="bottom-align">
          <BdwReadonlySpan importantLevel="veryImportant">关联任务</BdwReadonlySpan>
          <span className="help-title f-weight-normal">(选择关联的任务汇报结果资料会调入本任务参考资料)</span>
        </Space>
      }
      onCancel={cancelAssociatedEvent}
      width={1100}
      bodyStyle={{ height: '60vh', background: "#fff",overflow:'hidden' }}
      footer={
        [
          // <span key='link-preTask-strictly' className="float-left"><LinkPreTask /></span>,
          <Button key='cancel' onClick={cancelAssociatedEvent}>取消</Button>,
          <Button key='sure' type='primary' onClick={() => enterAssociatedEvent()}>确认</Button>,
        ]
      }
    >
      <div style={{width:'100%',height:'100%',overflow:'auto'}}>
        <AssociateTaskTable
          selectedRowKeysArr={selectedRowKeysArr}
          setSelectedRowKeysArr={(id,v) => {
            setSelectedRowKeysArr(id);
            setName(v);
          }}
        />
      </div>

    </Modal>
  )
}

export default AssociatedTaskModal
