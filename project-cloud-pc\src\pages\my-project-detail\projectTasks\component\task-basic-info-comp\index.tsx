import React from "react";
import { BdwReadonlySpan, BdwRow, BdwTitle} from "@/components";
import {
  UserOutlined,
  FileTextOutlined,
  CalendarOutlined
} from "@ant-design/icons/lib";
import moment from "moment";

interface TaskBasicInfoProps {
  task: any
}

const TaskBasicInfo: React.FC<TaskBasicInfoProps> = ({task}) => {
  return (
    <div className='task-basic-info'>
      <BdwRow type='flex' className='mt-16'>
        <div className='task-basic-info-label'>
          <FileTextOutlined className='mr-5'/><BdwTitle>标&emsp;题：</BdwTitle>
        </div>
        <BdwReadonlySpan className='flex-1' importantLevel='important'>
          {task?.name}
        </BdwReadonlySpan>
      </BdwRow>
      <BdwRow type='flex' className='mt-16'>
        <div className='task-basic-info-label'>
          <UserOutlined className='mr-5'/><BdwTitle>负责人：</BdwTitle>
        </div>
        {
          task?.leaderName ? (
            <BdwReadonlySpan className='flex-1'>
              {`${task?.leaderName}`}
            </BdwReadonlySpan>
          ) : (
              <BdwReadonlySpan className='flex-1'>
                无负责人
              </BdwReadonlySpan>
            )
          
        }
      </BdwRow>
      <BdwRow type='flex' className='mt-16'>
        <div className='task-basic-info-label'>
          <CalendarOutlined className='mr-5'/>
          <BdwTitle>
            <span>任务周期：</span>
          </BdwTitle>
        </div>
        <BdwRow type='flex'>
          {
            // @ts-ignore
            (task?.startTime && task?.endTime)  ?
            <BdwReadonlySpan>
              <span>
                {moment(task?.startTime).format("YYYY-MM-DD")}&emsp;至&emsp;
                {moment(task?.endTime).format("YYYY-MM-DD")}
              </span>
                <span className="ml-16">
                执行周期: {task?.executionCycle}天
              </span>
            </BdwReadonlySpan>: <BdwReadonlySpan className='flex-1'>
                未设置任务周期
              </BdwReadonlySpan>
          }
        </BdwRow>
      </BdwRow>
    </div>
  )
}

export default TaskBasicInfo
