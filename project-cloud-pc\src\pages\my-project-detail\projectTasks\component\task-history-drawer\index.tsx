/**
 * @description 任务历程弹窗
 * <AUTHOR>
 * @date 2023-11-28 15:53:56
*/
import React from 'react';
import { Drawer } from 'antd';
import TaskHistory from '../../tableRowInformation/taskHistory';
import './index.less';

interface taskHistoryDrawerProps {
    visible?: boolean
    task?: any
    cancelEvent?: () => void
}

const taskHistoryDrawer: React.FC<taskHistoryDrawerProps> = (props) => {
    const { visible, cancelEvent, task } = props;
    return (
        <Drawer
            open={visible}
            onClose={() => cancelEvent?.()}
            destroyOnClose
            zIndex={1001}
            width={700}
            bodyStyle={{
                padding: '16px'
            }}
            maskClosable={false}
            title={task.name + '-任务历程'}

        >
            <div style={{padding:'4px 11px'}}>
                <TaskHistory />
            </div>

        </Drawer>
    )
}
export default taskHistoryDrawer;