import React from "react";
import TaskBasicInfo from "../task-basic-info-comp";
import TaskProcessInfo from "../task-process-info-comp";
import { Divider } from "antd";
import { BdwRow} from "@/components";
import "./index.less"



const TaskInfo: React.FC<{task?: any}> = (props) => {
  const {task} = props;
  return (
    <BdwRow type='flex' className='task-info flex-column pl-16 pr-16'>
      <div>
        <TaskBasicInfo task={task} />
      </div>
      <div>
        {props.children}
      </div>
      <div className='flex-1 task-process-area'>
        <Divider className='mt-16'  />
        <TaskProcessInfo task={task} />
      </div>
    </BdwRow>
  )
}

export default TaskInfo
