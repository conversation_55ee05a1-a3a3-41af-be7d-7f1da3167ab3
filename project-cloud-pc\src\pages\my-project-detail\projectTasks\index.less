@import '../../../styles/base.less';



.project-task-warper {
  background: white;
  height: 100%;

  .project-tasks-container {
    .btn-project-tasks-wrapper {
      height: 70px;
      align-items: center;
      padding: 12px 0;
    }

    .project-tasks-table {
      width: 100%;
      height: 100%;
      border-top: 1px solid @divider;
     
    }
  }
}
.task-spin-wrapper.ant-spin-nested-loading{
  height: 100%;
  .ant-table-body::-webkit-scrollbar{
      height: 7px;
  }
  .ant-spin-container{
    height: 100%;
    .project-task-warper{
      .with-click-out-side-component{
        height: 100%;
        .project-task-table{
          // overflow: auto;
        }
        .click-out-side-box{
          height: 100%;
          overflow-y: auto;
          overflow-x: hidden;
          .project-task-table{
            border: none!important;
          }
          .project-tasks-container{
            height: 100%;
          }
        }
      }
    }
   
  }
}

