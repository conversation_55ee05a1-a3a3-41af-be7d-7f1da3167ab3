/**
 * @description 项目任务
 * <AUTHOR>
 * @date 2023-10-31 17:45:23
*/
import React, { useEffect, useMemo, useState } from 'react';
import { BdwRow, BdwCommonIcon } from '@/components';
import DispatchTaskDrawer from "./component/dispatch-task-drawer";
import AcceptTaskDrawer from './component/accept-task-drawer';
import ExtendTaskRequestDrawer from './component/extend-task-request-drawer';
import ReviewTaskRequestDrawer from './component/review-task-request-drawer';
import TaskHistoryDrawer from './component/task-history-drawer';
import { withKeydown } from "@/components/withKeydown";
import { Switch, Divider, Popconfirm, Modal, Tooltip, Spin } from 'antd';
import { useBoolean } from 'ahooks';
import ProjectTasksTable from './projectTasksTable';
import { useParams, useSelector, useDispatch, history,useLocation } from 'umi';
import { moveDownApi, moveUpApi, deleteTasksApi, listTaskFunctionality, getTaskInfo, closeTask } from "@/service/projectDos/my-project-detail/projectTasks";
import { cloneDeep } from 'lodash';
import { TaskFunctionCode, BtnKey, BtnOperateType } from '@/constants/Enum';
import {findTaskItem} from "@/utils/utils";
import './index.less';


//按钮功能是否可用
export const disabledFlag = (arr: any[], type: string, functionCode = BtnKey.taskFunctionCode, usable = 'usable') => {
    if (!arr || (arr && arr.length == 0)) return true;
    const available = arr?.filter((item: any) => item[functionCode] == type);
    return !available[0][usable];
}

const { confirm } = Modal;

export const DividerLine = () => <Divider type='vertical' style={{ marginRight: '10px', marginLeft: '0', height: '100%', background: '#eee' }} />
const KeyDownComponent = withKeydown((props) => <div className='project-tasks-container' {...props} />);
const ProjectTasks: React.FC = () => {
    const dispatch = useDispatch();
    const [dispatchTaskVisible, { setFalse: dispatchTaskHide, setTrue: dispatchTaskShow }] = useBoolean(false);
    const [handleTaskVisible, { setFalse: handleTaskVisibleHide, setTrue: handleTaskVisibleShow }] = useBoolean(false);
    const [applyTypeVisible, { setFalse: applyTypeVisibleHide, setTrue: applyTypeVisibleShow }] = useBoolean(false);
    const [auditModalVisible, { setFalse: auditModalVisibleHide, setTrue: auditModalVisibleShow }] = useBoolean(false);
    const [taskHistoryVisible, { setFalse: taskHistoryVisibleHide, setTrue: taskHistoryVisibleShow }] = useBoolean(false);
    const [initFlag,setInitFlag] = useState(true);
    const [assignType, setAssignType] = useState<any>(null);
    const { projectId } = useParams<{ projectId: string }>();
    const { userInfo } = useSelector((state: any) => state.commonTask);
    const [paramsTaskInfo,setParamsTaskInfo] = useState<any>(null);
    const location: any = useLocation();
    const {taskId} = location.query;
    const { editable, taskInfo, taskLists, onlyShowMe, isAddTask, functionality, loading, taskListsBackUp} = useSelector((state: any) => state.projectTasks);
    const { projectOverviewDetails } = useSelector((state: any) => state.projectOverview);
    const onkeydown = useMemo(() => {
        return (e: KeyboardEvent) => {
            if (e.ctrlKey && e.code === 'KeyS') {
                e.preventDefault();
                dispatch({
                    type: 'projectTasks/setSaveStatus',
                })
            }

            if (!disabledFlag(functionality, TaskFunctionCode.ADD_NEW_TASK)) {
                if (e.key === 'Enter') {
                    e.preventDefault()
                    dispatch({
                        type: 'projectTasks/addProjectTask',
                    })
                    listTaskFunctionality({ projectId }).then((res: any) => {
                        dispatch({
                            type: 'projectTasks/setFunctionality',
                            payload: res
                        })
                    })
                }
            }

            if (!disabledFlag(functionality, TaskFunctionCode.ADD_NEW_CHILD_TASK)) {
                if (e.key === 'Tab') {
                    e.preventDefault()
                    dispatch({
                        type: 'projectTasks/addProjectTask',
                        payload: true
                    })
                    listTaskFunctionality({ projectId }).then((res: any) => {
                        dispatch({
                            type: 'projectTasks/setFunctionality',
                            payload: res
                        })
                    })
                }
            }
            if (editable) {
                if (e.key === 'Escape') {
                    e.preventDefault();
                    dispatch({
                        type: 'projectTasks/setEditable',
                        payload: false
                    })
                }
            }

            if (!disabledFlag(functionality, TaskFunctionCode.DELETE)) {
                if (e.key === 'Delete') {
                    e.preventDefault()
                    confirm({
                        title: '系统提示',
                        content: '确定删除任务吗？',
                        onOk() {
                            deleteTasksApi(taskInfo.taskId).then(() => {
                                dispatch({
                                    type: 'projectTasks/fetchTaskList',
                                    payload: projectId,
                                    changeType: 'delete'
                                })
                            })
                        },
                    })

                }
            }
        }
    }, [taskInfo, editable]);
    useEffect(()=> {
        if(taskListsBackUp?.length  &&  initFlag && userInfo?.userId && projectOverviewDetails?.leaderId){
            setInitFlag(false);
            // 如果存在跳转的taskId,默认选中该条任务
            if(taskId){
                dispatch({
                    type: 'projectTasks/setFilterTableData',
                    typeKey: taskId,
                    typeName: "taskId",
                    status: true,
                    onlyShowMe: false,
                    onSuccess:(task: any) => {
                        if(task.taskId == taskId){
                            setParamsTaskInfo(task);
                        }
                    }
                })
                
            }else{
                // 当前是项目负责人默认查看全部 ，不是默认查看与我相关的任务
                if(projectOverviewDetails.leaderId != userInfo.userId){
                    dispatch({
                        type: 'projectTasks/setFilterTableData',
                        typeKey: userInfo.userId,
                        typeName: "leaderId",
                        status: true,
                        onlyShowMe: true,
                    })
                }
            }
           
        }
    },[taskListsBackUp,userInfo,projectOverviewDetails])
    useEffect(() => {
        if(paramsTaskInfo){
            dispatch({
                type: 'projectTasks/setCTaskId',
                payload: taskId,
            })
            dispatch({
                type: 'projectTasks/fetchTaskInfo',
                payload: paramsTaskInfo,
                projectId
            })
        }
       
    },[paramsTaskInfo])
    const PageWrapperTitle = () =>
        <Switch
            className=''
            checked={onlyShowMe}
            onChange={(e) => {
                dispatch({
                    type: 'projectTasks/setFilterTableData',
                    typeKey: userInfo.userId,
                    typeName: "leaderId",
                    status: e,
                    onlyShowMe: true,
                })
            }}
            checkedChildren="打开"
            unCheckedChildren="关闭"
        />
    //新增任务
    const addNewTask = () => {
        dispatch({
            type: 'projectTasks/addProjectTask',
        })
        listTaskFunctionality({ projectId }).then((res: any) => {
            dispatch({
                type: 'projectTasks/setFunctionality',
                payload: res
            })
        })
    }
    //新增子任务
    const addNewChildTask = () => {
        dispatch({
            type: 'projectTasks/addProjectTask',
            payload: true
        })
        listTaskFunctionality({ projectId }).then((res: any) => {
            dispatch({
                type: 'projectTasks/setFunctionality',
                payload: res
            })
        })
    }
    //上移
    const moveUp = () => {
        moveUpApi(taskInfo.taskId).then(() => {
            getTaskInfo(taskInfo.taskId).then((res) => {
                dispatch({
                    type: 'projectTasks/fetchTaskList',
                    payload: projectId,
                    changeType: 'move',
                    taskId: taskInfo.taskId,
                    successRes: res
                })
            })

        })
    }
    //下移
    const moveDown = () => {
        moveDownApi(taskInfo.taskId).then(() => {
            getTaskInfo(taskInfo.taskId).then((res) => {
                dispatch({
                    type: 'projectTasks/fetchTaskList',
                    payload: projectId,
                    changeType: 'move',
                    taskId: taskInfo.taskId,
                    successRes: res
                })
            })
        })
    }
    //关闭任务
    const confirmClose = () => {
        closeTask(taskInfo.taskId).then(() => {
            dispatch({
                type: 'projectTasks/fetchTaskList',
                payload: projectId,
                changeType: 'close'
            })
        })
    }
    //删除任务
    const confirmDelete = () => {
        deleteTasksApi(taskInfo.taskId).then(() => {
            dispatch({
                type: 'projectTasks/fetchTaskList',
                payload: projectId,
                changeType: 'delete'
            })
        })
        listTaskFunctionality({ projectId }).then((res: any) => {
            dispatch({
                type: 'projectTasks/setFunctionality',
                payload: res
            })
        })
    }
    //分派、改派任务成功回调
    const dispatchTaskSuccess = () => {
        dispatch({
            type: 'projectTasks/fetchTaskList',
            payload: projectId,
            changeType: 'save',
            taskId: taskInfo.taskId
        })
        dispatchTaskHide();
        setAssignType(null);
    }
    //接收、退回任务成功回调
    const receiveTaskSuccess = () => {
        dispatch({
            type: 'projectTasks/fetchTaskList',
            payload: projectId,
            changeType: 'save',
            taskId: taskInfo.taskId
        })
        handleTaskVisibleHide();
        setAssignType(null);
    }
    //申请延期、暂停、移交、恢复
    const applyTaskPostponeSuccess = () => {
        dispatch({
            type: 'projectTasks/fetchTaskList',
            payload: projectId,
            changeType: 'save',
            taskId: taskInfo.taskId
        })
        applyTypeVisibleHide();
        setAssignType(null);
    }
    //审核 申请延期、暂停、移交、恢复
    const auditApplySuccess = () => {
        dispatch({
            type: 'projectTasks/fetchTaskList',
            payload: projectId,
            changeType: 'save',
            taskId: taskInfo.taskId
        })
        auditModalVisibleHide();
        setAssignType(null);
    }
    //保存
    const save = () => {
        dispatch({
            type: 'projectTasks/setSaveStatus',
        })
    }
    return (
        <Spin spinning={loading} wrapperClassName='task-spin-wrapper'>
            <div className='project-task-warper'>
                <KeyDownComponent  onKeydown={onkeydown}>
                    <BdwRow type='flex' className='btn-project-tasks-wrapper' >
                        <BdwCommonIcon
                            name='返回'
                            icon='iconback'
                            className="mr-10 ml-10"
                            onClick={() => { history.push('/my-project') }}
                        />
                        <BdwCommonIcon
                            name='刷新'
                            icon='iconrefresh'
                            className="mr-10"
                            onClick={() => history.go(0)}
                        />
                        <DividerLine />
                        <BdwCommonIcon
                            className='mr-10'
                            iconRender={PageWrapperTitle}
                            name='只看与我相关'
                        />
                        <DividerLine />
                        <BdwCommonIcon
                            name={<Tooltip title="新增任务（快捷键：Enter）" placement="bottom">新增任务</Tooltip>}
                            icon='icontask-paren'
                            className="mr-10"
                            onClick={addNewTask}
                            disabled={disabledFlag(functionality, TaskFunctionCode.ADD_NEW_TASK)}
                        />
                        <BdwCommonIcon
                            name={<Tooltip title="新增子任务（快捷键：Tab）" placement="bottom">新增子任务</Tooltip>}
                            icon='icontask-child'
                            className="mr-10"
                            onClick={addNewChildTask}
                            disabled={disabledFlag(functionality, TaskFunctionCode.ADD_NEW_CHILD_TASK)}
                        />
                        <DividerLine />
                        <BdwCommonIcon
                            name='上移'
                            icon='iconpageup'
                            className="mr-10"
                            disabled={disabledFlag(functionality, TaskFunctionCode.MOVE_UP)}
                            onClick={moveUp}
                        />
                        <BdwCommonIcon
                            name='下移'
                            icon='iconpagedown'
                            className="mr-10"
                            disabled={disabledFlag(functionality, TaskFunctionCode.MOVE_DOWN)}
                            onClick={moveDown}
                        />
                        <DividerLine />
                        <BdwCommonIcon
                            name={<Tooltip title="保存（快捷键：Ctrl + S）" placement="bottom">保存</Tooltip>}
                            icon='iconsave'
                            className="mr-10"
                            disabled={!editable}
                            onClick={save}
                        />
                        <BdwCommonIcon
                            name={<Tooltip title="编辑（快捷方式：鼠标左键双击）" placement="bottom">编辑</Tooltip>}
                            icon='iconedit'
                            className="mr-10"
                            disabled={disabledFlag(functionality, TaskFunctionCode.EDIT) ? true : editable}
                            onClick={() => {
                                dispatch({
                                    type: 'projectTasks/setEditable',
                                    payload: true
                                })
                            }}
                        />
                        <Popconfirm title="确定要关闭任务吗?" onConfirm={confirmClose}>
                            <BdwCommonIcon
                                name='关闭'
                                icon='icondelete'
                                className="mr-10"
                                disabled={disabledFlag(functionality, TaskFunctionCode.CLOSE)}
                            />
                        </Popconfirm>
                        <Popconfirm title="确定要删除任务吗?" onConfirm={confirmDelete}>
                            <BdwCommonIcon
                                name={<Tooltip title="删除（快捷键：Delete）" placement="bottom">删除</Tooltip>}
                                icon='icondelete'
                                className="mr-10"
                                disabled={disabledFlag(functionality, TaskFunctionCode.DELETE)}
                            />
                        </Popconfirm>
                        <DividerLine />
                        <BdwCommonIcon
                            name='分派任务'
                            icon='iconassign'
                            className="mr-10"
                            onClick={() => {
                                dispatchTaskShow();
                                setAssignType(BtnOperateType.ASSIGNMENT)
                            }
                            }
                            disabled={disabledFlag(functionality, TaskFunctionCode.ASSIGNMENT)}
                        />
                        <BdwCommonIcon
                            name='改派任务'
                            icon='iconreassign'
                            className="mr-10"
                            onClick={() => {
                                dispatchTaskShow();
                                setAssignType(BtnOperateType.REASSIGN)
                            }}
                            disabled={disabledFlag(functionality, TaskFunctionCode.REASSIGN)}
                        />
                        {/* <BdwCommonIcon
                        name='批量分派'
                        icon='iconmodule'
                        className="mr-10"
                        disabled={disabledFlag(functionality,'ADD_NEW_CHILD_TASK')}
                    /> */}
                        <BdwCommonIcon
                            name='接收任务'
                            icon='iconreceive'
                            className="mr-10"
                            onClick={() => {
                                handleTaskVisibleShow();
                                setAssignType(BtnOperateType.RECEIVE)
                            }}
                            disabled={disabledFlag(functionality, TaskFunctionCode.RECEIVE)}
                        />
                        <BdwCommonIcon
                            name='退回任务'
                            icon='iconreturn'
                            className="mr-10"
                            onClick={() => {
                                handleTaskVisibleShow();
                                setAssignType(BtnOperateType.RETURN)
                            }}
                            disabled={disabledFlag(functionality, TaskFunctionCode.RETURN)}
                        />
                        <DividerLine />
                        <BdwCommonIcon
                            name='申请延期'
                            icon='iconapplication-for-extension'
                            className="mr-10"
                            onClick={() => {
                                applyTypeVisibleShow();
                                setAssignType(BtnOperateType.TASK_DELAY)
                            }}
                            disabled={disabledFlag(functionality, TaskFunctionCode.DELAY_APPLY)}
                        />
                        <BdwCommonIcon
                            name='申请暂停'
                            icon='iconpause'
                            className="mr-10"
                            onClick={() => {
                                applyTypeVisibleShow();
                                setAssignType(BtnOperateType.TASK_PAUSE)
                            }}
                            disabled={disabledFlag(functionality, TaskFunctionCode.PAUSE_APPLY)}
                        />
                        <BdwCommonIcon
                            name='申请恢复'
                            icon='iconrecover'
                            className="mr-10"
                            onClick={() => {
                                applyTypeVisibleShow();
                                setAssignType(BtnOperateType.TASK_RECOVERY)
                            }}
                            disabled={disabledFlag(functionality, TaskFunctionCode.RECOVERY_APPLY)}
                        />
                        <BdwCommonIcon
                            name='移交任务'
                            icon='icontransition'
                            className="mr-10"
                            onClick={() => {
                                applyTypeVisibleShow();
                                setAssignType(BtnOperateType.TASK_HANDOVER)
                            }}
                            disabled={disabledFlag(functionality, TaskFunctionCode.HANDOVER_APPLY)}
                        />
                        <BdwCommonIcon
                            name='审核申请'
                            icon='iconcheck'
                            className="mr-10"
                            onClick={() => {
                                auditModalVisibleShow();
                            }}
                            disabled={disabledFlag(functionality, TaskFunctionCode.AUDIT_APPLY)}
                        />
                        <DividerLine />
                        <BdwCommonIcon
                            name='任务历程'
                            icon='iconrefresh'
                            className="mr-10"
                            disabled={!(taskInfo && taskInfo.taskId !== 'newAdd')}
                            onClick={() => {
                                taskHistoryVisibleShow();
                            }}
                        />
                    </BdwRow>
                    <ProjectTasksTable />

                </KeyDownComponent>
                {/* 任务分派改派 */}
                {
                    taskInfo && dispatchTaskVisible && assignType &&
                    <DispatchTaskDrawer
                        visible={dispatchTaskVisible}
                        task={taskInfo}
                        cancelEvent={() => {
                            dispatchTaskHide();
                            setAssignType(null)
                        }}
                        successEvent={dispatchTaskSuccess}
                        assignType={assignType}
                    />
                }
                {/* 任务接收退回 */}
                {
                    taskInfo && handleTaskVisible && assignType &&
                    <AcceptTaskDrawer
                        visible={handleTaskVisible}
                        task={taskInfo}
                        cancelEvent={() => {
                            handleTaskVisibleHide();
                            setAssignType(null);
                        }}
                        successEvent={receiveTaskSuccess}
                        assignType={assignType}
                    />
                }
                {/* 申请延期、暂停、移交、恢复 */}
                {
                    taskInfo && applyTypeVisible && assignType &&
                    <ExtendTaskRequestDrawer
                        visible={applyTypeVisible}
                        task={taskInfo}
                        cancelEvent={() => {
                            applyTypeVisibleHide();
                            setAssignType(null);
                        }}
                        successEvent={applyTaskPostponeSuccess}
                        assignType={assignType}
                    />
                }
                {/* 审核申请 */}
                {
                    taskInfo && auditModalVisible &&
                    <ReviewTaskRequestDrawer
                        visible={auditModalVisible}
                        task={taskInfo}
                        cancelEvent={() => auditModalVisibleHide()}
                        successEvent={auditApplySuccess}
                    />
                }
                {/* 任务历程 */}
                {
                    taskInfo && taskHistoryVisible &&
                    <TaskHistoryDrawer
                        visible={taskHistoryVisible}
                        task={taskInfo}
                        cancelEvent={() => taskHistoryVisibleHide()}
                    />
                }
            </div>
        </Spin>


    )
}
export default ProjectTasks;