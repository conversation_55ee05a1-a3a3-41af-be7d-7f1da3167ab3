@import '../../../../styles/base.less';
.project-task-table-container {
  .ant-input-cover-style{
    background: white!important;
  }
  .ant-table-expanded-row-fixed{
    margin: -16px -16px!important;
  }
  .ant-table-sticky-scroll-bar,.ant-table-sticky-scroll{
    display: none!important;
  }
  width: 100%;
  height: calc(100% - 70px);
  border-top: 1px solid @divider;
  .table-details{
      height: 100%;
      .p-16()
  }
  .table-details-w{
    width: calc(100% - 550px);
  }
  .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before{
    background-color: unset;
  }
  .datePicker{
    padding-left: unset;
    padding-right: unset;
  }
  .status-span{
    background-color: #efefef;
    padding: 3px 6px;
    border-radius: 2px;
  }
  .task-name-show{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
  }
}
