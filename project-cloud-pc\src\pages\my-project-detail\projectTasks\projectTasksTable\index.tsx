/**
 * @description 项目任务表格
 * <AUTHOR>
 * @date 2023-11-07 11:00:43
*/
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Badge, DatePicker, Input, Menu, Tooltip, Modal } from 'antd';
import TableRowInformation from '../tableRowInformation';
import { withKeydown } from '@/components/withKeydown';
import { withClickOutSide } from '@/components/withClickOutSide';
import styled from 'styled-components';
import Highlighter from "react-highlight-words";
import { useParams, useSelector, useDispatch } from 'umi';
import { moveDownApi, moveUpApi, deleteTasksApi, listTaskFunctionality, downgradeTask, upgradeTask, getTaskInfo } from "@/service/projectDos/my-project-detail/projectTasks";
import { BdwTable, BdwTableHeaderSearchComponent, BdwRow, BdwIcon, BdwTableButton, EditableContent } from "@/components";
import { getListTask } from '@/service/projectDos/my-project-detail/projectTasks';
import { initTaskIndex } from '@/utils/utils';
import { disabledFlag } from '../../projectTasks';
import moment from 'moment';
import { useBoolean, useUpdateEffect } from 'ahooks';
import { RightMenuBtn, TaskFunctionCode } from '@/constants/Enum';
import './index.less';
import { isObject, isString, filter } from 'lodash';
import { useResizeDetector } from 'react-resize-detector';

const IndexSpan = styled.span`
 font-weight: bold;
 font-size: 13px;
 margin-right: 10px;
`;
const NotSupport = styled.div`
  cursor: not-allowed;
  width: 100%;
  height: 100%;
`
// @ts-ignore
const FixedMenu = withClickOutSide(styled(Menu)`
  position: fixed;
  z-index: 1000;
  box-shadow: 0 0 5px rgba(0,0,0,0.1) !important;
`);
const WrapperComponent = withKeydown(BdwRow);

interface Position {
    x: number,
    y: number
}
const { confirm } = Modal;
const ContextMenu: React.FC<{ top: number, left: number, visible: boolean, hide: () => void }> = (props) => {
    const dispatch = useDispatch();
    const { projectId } = useParams<{ projectId: string }>();
    const { functionality, taskInfo, editable } = useSelector((state: any) => state.projectTasks);
    const onHandleMenuItem = (type: string) => {
        switch (type) {
            case TaskFunctionCode.ADD_NEW_CHILD_TASK://添加子任务
                dispatch({
                    type: 'projectTasks/addProjectTask',
                    payload: true
                })
                listTaskFunctionality({ projectId }).then((res: any) => {
                    dispatch({
                        type: 'projectTasks/setFunctionality',
                        payload: res
                    })
                })
                break;
            case TaskFunctionCode.ADD_NEW_TASK://添加同级任务
                dispatch({
                    type: 'projectTasks/addProjectTask',
                })
                listTaskFunctionality({ projectId }).then((res: any) => {
                    dispatch({
                        type: 'projectTasks/setFunctionality',
                        payload: res
                    })
                })
                break;
            case TaskFunctionCode.EDIT://编辑
                dispatch({
                    type: 'projectTasks/setEditable',
                    payload: true
                })
                break;
            case TaskFunctionCode.MOVE_UP://上移
                moveUpApi(taskInfo.taskId).then(() => {
                    getTaskInfo(taskInfo.taskId).then((res) => {
                        dispatch({
                            type: 'projectTasks/fetchTaskList',
                            payload: projectId,
                            changeType: 'move',
                            taskId: taskInfo.taskId,
                            successRes: res
                        })
                    })

                })
                break;
            case TaskFunctionCode.MOVE_DOWN://下移
                moveDownApi(taskInfo.taskId).then(() => {
                    getTaskInfo(taskInfo.taskId).then((res) => {
                        dispatch({
                            type: 'projectTasks/fetchTaskList',
                            payload: projectId,
                            changeType: 'move',
                            taskId: taskInfo.taskId,
                            successRes: res
                        })
                    })
                })
                break;
            case TaskFunctionCode.UPGRADE://升级
                upgradeTask(taskInfo.taskId).then(() => {
                    getTaskInfo(taskInfo.taskId).then((res) => {
                        dispatch({
                            type: 'projectTasks/fetchTaskList',
                            payload: projectId,
                            changeType: 'upgrade',
                            taskId: taskInfo.taskId,
                            successRes: res
                        })
                    })
                })
                break;
            case TaskFunctionCode.DOWNGRADE://降级
                downgradeTask(taskInfo.taskId).then(() => {
                    getTaskInfo(taskInfo.taskId).then((res) => {
                        dispatch({
                            type: 'projectTasks/fetchTaskList',
                            payload: projectId,
                            changeType: 'downgrade',
                            taskId: taskInfo.taskId,
                            successRes: res
                        })
                    })

                }).catch((error) => {
                    //降级时错误处理
                    if (error.code == 10190) {
                        confirm({
                            title: '系统提示',
                            content: error.message,
                            onOk() {
                                let data = {};
                                //根据后端返回的字段判断是调整的是开始时间还是结束时间，再次调用接口传递不同的参数
                                // START_TIME：调整开始时间
                                // END_TIME：调整截至时间
                                if (error.data == 'START_TIME') {
                                    data = { autoAdjustStartTime: true }
                                } else {
                                    data = { useParentTaskEndTime: true }
                                }
                                downgradeTask(taskInfo.taskId, data).then(() => {
                                    dispatch({
                                        type: 'projectTasks/fetchTaskList',
                                        payload: projectId,
                                        changeType: 'move',
                                        taskId: taskInfo.taskId
                                    })
                                })
                            },
                            onCancel() {
                                //不调整截至时间需要再次调用一下接口，开始时间不用调接口
                                if (error.data == 'END_TIME') {
                                    downgradeTask(taskInfo.taskId, { useParentTaskEndTime: false }).then(() => {
                                        dispatch({
                                            type: 'projectTasks/fetchTaskList',
                                            payload: projectId,
                                            changeType: 'move',
                                            taskId: taskInfo.taskId
                                        })
                                    })
                                }
                            }
                        })
                    }
                })
                break;
            case TaskFunctionCode.DELETE://删除任务
                deleteTasksApi(taskInfo.taskId).then(() => {
                    dispatch({
                        type: 'projectTasks/fetchTaskList',
                        payload: projectId,
                        changeType: 'delete'
                    })
                })
                break;
        }
        props.hide();
    }
    const FixedMenuItem = filter(RightMenuBtn.map((item: any) => {
        if (disabledFlag(functionality, item.key)) {
            return null
        } else {
            return {
                key: item.key,
                label: <div
                    className='bdw-menu-item'
                    onClick={() => {
                        onHandleMenuItem(item.key)
                    }}
                >{item.name}</div>
            }
        }
    }), v => v)
    return <FixedMenu
        onClickOutSide={props.hide}
        style={{
            top: `${props.top > 400 ? props.top - 130 : props.top}px`,
            left: `${props.left}px`,
            display: props.visible ? 'block' : 'none'
        }}
        items={FixedMenuItem}
    />

}
const ProjectTasksTable: React.FC = () => {
    const dispatch = useDispatch();
    const [showToolTip, setShowToolTip] = useState<boolean>(false);
    const [contextMenuVisible, { setTrue: showContextMenu, setFalse: hideContextMenu }] = useBoolean(false);
    const { projectId } = useParams<{ projectId: string }>();
    const { taskLists, taskInfo, isAddTask, editable, expandedRowKeys, filter: filterObj, functionality, cTaskId } = useSelector((state: any) => state.projectTasks);
    const [contextPosition, setContextMenuPosition] = useState<Position>({
        x: -1000,
        y: -1000
    });
    //是否有编辑权限
    const getEditAuthNew = () => {
        return disabledFlag(functionality, TaskFunctionCode.EDIT)
    }
    // 表格对象容器
    const {width,ref} = useResizeDetector();

    useEffect(()=>{
        dispatch({
            type: 'projectTasks/renewTaskInfo',
            payload: null
        })
    },[projectId])
    // 任务表格表头项
    const TaskTableColumns = [
        {
            title: <BdwTableHeaderSearchComponent title="任务">
                <Input placeholder="请输入任务名称进行检索"
                    className='no-border-input ant-input-cover-style wordPt'
                    value={filterObj?.name}
                    onChange={(e) => {
                        dispatch({
                            type: 'projectTasks/setFilterTableData',
                            typeKey: e.target.value,
                            typeName: "name",
                            status: e.target.value
                        })
                    }}
                />
            </BdwTableHeaderSearchComponent>,
            dataIndex: 'name',
            ellipsis: true,
            render: function TitleColumn(value: string, record: any) {
                const indexSpan = <span
                    className='task-index-num'><IndexSpan>{record.index?.map((it: any) => it + 1).join('.')}</IndexSpan></span>;
                const renderShow = <div title={record.name} className='task-name-show'>
                    <Highlighter
                        highlightClassName="title-highlight"
                        searchWords={[filterObj?.name]}
                        autoEscape
                        textToHighlight={record.name ?? ""}
                    />
                </div>
                const renderEditor = (
                    <BdwRow type='flex' className='flex-1'>
                        <div className='flex-1'>
                            {
                                isAddTask ?
                                    <Tooltip title='提示：仅限输入64个字符！' open={showToolTip}>
                                        <Input
                                            tabIndex={-1}
                                            size="small"
                                            className='task-name-input paddingStyle'
                                            maxLength={32}
                                            autoFocus
                                            // onBlur={(e) => {

                                            // }}
                                            value={taskInfo?.name}
                                            onChange={(e) => {
                                                if (e.target.value.length > 32) {
                                                    setShowToolTip?.(true)
                                                } else {
                                                    setShowToolTip?.(false)
                                                }
                                                dispatch({
                                                    type: 'projectTasks/renewTaskInfo',
                                                    payload: {
                                                        name: e.target.value
                                                    }
                                                })
                                            }}
                                        /></Tooltip> :
                                    <span>{taskInfo?.name}</span>
                            }
                        </div>
                    </BdwRow>)
                return <BdwRow type='flex'>
                    <div>{indexSpan}</div>
                    <EditableContent
                        editable={editable && record.taskId === taskInfo?.taskId}
                        renderShow={renderShow}
                        renderEditor={renderEditor}
                    />
                </BdwRow>;
            },
        },
        {
            title: <BdwTableHeaderSearchComponent title="工期">
                {/* <NotSupport />s */}
            </BdwTableHeaderSearchComponent>,
            dataIndex: 'executionCycle',
            width: 50,
            ellipsis: true,
            render:(value?: any) => {
                return <div>{value ?? '--'}</div>
            }
        },
        {
            title: <BdwTableHeaderSearchComponent title="负责人">
                <Input className='no-border-input ant-input-cover-style wordPt'
                    placeholder="请输入负责人"
                    value={filterObj?.leaderName}
                    onChange={(e) => {
                        dispatch({
                            type: 'projectTasks/setFilterTableData',
                            typeKey: e.target.value,
                            typeName: "leaderName",
                            status: e.target.value
                        })
                    }}
                />
            </BdwTableHeaderSearchComponent>,
            dataIndex: 'leaderName',
            width: 110,
            ellipsis: true,
            render: (value: any, record: any) => {
                const renderEditor = <div>{taskInfo?.leaderName}</div>;
                const renderShow = <Highlighter
                    highlightClassName="title-highlight"
                    searchWords={[filterObj?.leaderName]}
                    autoEscape
                    textToHighlight={record.leaderName ?? ""}
                />
                return <EditableContent
                    editable={editable && record.taskId === taskInfo?.taskId}
                    renderShow={renderShow}
                    renderEditor={renderEditor}
                />
            }
        },
        {
            title: <BdwTableHeaderSearchComponent title="编辑人">
                <Input className='no-border-input ant-input-cover-style wordPt'
                    placeholder="请输入编辑人"
                    value={filterObj?.editorName}
                    onChange={(e) => {
                        dispatch({
                            type: 'projectTasks/setFilterTableData',
                            typeKey: e.target.value,
                            typeName: "editorName",
                            status: e.target.value
                        })
                    }}
                />
            </BdwTableHeaderSearchComponent>,
            dataIndex: 'editorName',
            width: 110,
            ellipsis: true,
            render: (value: any, record: any) => {
                return <Badge dot count={record.needProcess?1:0}>
                    <Highlighter
                        highlightClassName="title-highlight"
                        searchWords={[filterObj?.editorName]}
                        autoEscape
                        textToHighlight={record.editorName ?? ""}
                    />
                </Badge>
            }
        },
        {
            title: <BdwTableHeaderSearchComponent title="起始日期">
                {/* @ts-ignore */}
                <DatePicker
                    className='datePicker'
                    bordered={false}
                    suffixIcon={null}
                    onChange={date => {
                        dispatch({
                            type: 'projectTasks/setFilterTableData',
                            typeKey: date?.format("MM-DD"),
                            typeName: "startTime",
                            status: date?.format("MM-DD")
                        })
                    }}

                />
            </BdwTableHeaderSearchComponent>,
            dataIndex: 'startTime',
            width: 120,
            ellipsis: true,
            render: (value: any, record: any) => {
                const date = (isObject(value) || (value && value?.indexOf('时') == '-1')) ? moment(value).format("MM-DD HH时") : value;
                const renderEditor = <div>{(isObject(taskInfo?.startTime) || (taskInfo?.startTime && taskInfo?.startTime?.indexOf('时') == '-1')) ? moment(taskInfo?.startTime).format("MM-DD HH时") : taskInfo?.startTime}</div>;
                const renderShow = <div>{date}</div>
                return <EditableContent
                    editable={editable && record.taskId === taskInfo?.taskId}
                    renderShow={renderShow}
                    renderEditor={renderEditor}
                />
            }
        },
        {
            title: <BdwTableHeaderSearchComponent title="截止日期">
                {/* @ts-ignore */}
                <DatePicker
                    className='datePicker'
                    bordered={false}
                    suffixIcon={null}
                    onChange={date => {
                        dispatch({
                            type: 'projectTasks/setFilterTableData',
                            typeKey: date?.format("MM-DD"),
                            typeName: "endTime",
                            status: date?.format("MM-DD")
                        })
                    }}

                />
            </BdwTableHeaderSearchComponent>,
            dataIndex: 'endTime',
            width: 120,
            ellipsis: true,
            render: (value: any, record: any) => {
                const date = (isObject(value) || (value && value?.indexOf('时') == '-1')) ? moment(value).format("MM-DD HH时") : value;
                const renderEditor = <div>{(isObject(taskInfo?.endTime) || (taskInfo?.endTime && taskInfo?.endTime?.indexOf('时') == '-1')) ? moment(taskInfo?.endTime).format("MM-DD HH时") : taskInfo?.endTime}</div>;
                const renderShow = <div>{date}</div>
                return <EditableContent
                    editable={editable && record.taskId === taskInfo?.taskId}
                    renderShow={renderShow}
                    renderEditor={renderEditor}
                />
            }
        },
        {
            title: <BdwTableHeaderSearchComponent title="状态">
                {/* <NotSupport /> */}
                <Input className='no-border-input ant-input-cover-style wordPt'
                    placeholder="请输入状态"
                    value={filterObj?.statusName}
                    onChange={(e) => {
                        dispatch({
                            type: 'projectTasks/setFilterTableData',
                            typeKey: e.target.value,
                            typeName: "statusName",
                            status: e.target.value
                        })
                    }}
                />
            </BdwTableHeaderSearchComponent>,
            width: 100,
            ellipsis: true,
            dataIndex: 'statusName',
            render: (value: any, record: any) => {
                return <span className='color-5c5c5c f-12 status-span' >{value}</span>
            }
        },
        {
            title: <BdwTableHeaderSearchComponent title="进程汇报">
                {/* <NotSupport /> */}
            </BdwTableHeaderSearchComponent>,
            dataIndex: 'process',
            width: 80,
            ellipsis: true,
            render: (value: any, record: any) => {
                return <div style={{minWidth:'60px'}}>{value}</div>
            }
        },
    ];
    const taskTableColumns = useMemo(() => {
        if(width){
            if(width>880){
                return TaskTableColumns
            }else{
                return TaskTableColumns.filter((item: any) => !['editorName','endTime','process'].includes(item.dataIndex))
            }
        }
    },[width,TaskTableColumns])
    return (<BdwRow type='flex' className='project-task-table-container'>
            <div  ref={ref} className={`table-details ${taskInfo?.taskId ? 'table-details-w' : ''}`}>
                {/* <div className={`table-details table-details-w`}> */}
                <WrapperComponent>
                    <ContextMenu top={contextPosition.y} left={contextPosition.x} visible={contextMenuVisible}
                        hide={hideContextMenu} />
                    <BdwTable
                        className='project-task-table'
                        pagination={false}
                        loading={{
                            spinning: false
                        }}
                        expandable={{
                            defaultExpandAllRows: true,
                            expandedRowKeys,
                            onExpandedRowsChange: (e) => {
                                dispatch({
                                    type: 'projectTasks/setExpandedRowKeys',
                                    payload: e
                                })
                            },
                        }}
                        size="small"
                        rowKey="taskId"
                        // @ts-ignore
                        columns={taskTableColumns}
                        dataSource={taskLists ?? []}
                        useLocalData
                        sticky
                        scroll={{ x: true }}
                        showPages={false}
                        rowSelection={{
                            selectedRowKeys: cTaskId && [cTaskId] || [],
                            type: 'radio',
                            columnWidth: 1,
                            renderCell: () => {
                                return null;
                            },
                            checkStrictly: true,
                        }}
                        // @ts-ignore
                        onRow={(task: any) => ({
                            onClick: async () => {
                                dispatch({
                                    type: 'projectTasks/setCTaskId',
                                    payload: task.taskId,
                                })
                                if (task.taskId !== taskInfo?.taskId) {
                                    if ((isAddTask || editable) && taskInfo.name) {
                                        await dispatch({
                                            type: 'projectTasks/setSubmitStatus',
                                            payload: task
                                        })
                                    } else {
                                        dispatch({
                                            type: 'projectTasks/fetchTaskInfo',
                                            payload: task,
                                            projectId
                                        })
                                    }
                                }
                            },
                            onDoubleClick() {
                                // 有编辑权限时 双击才进入编辑状态
                                if (task.taskId !== taskInfo?.taskId) return
                                if (getEditAuthNew()) return
                                dispatch({
                                    type: 'projectTasks/setEditable',
                                    payload: true
                                })
                            },
                            onContextMenu: async (e) => {
                                // 有编辑权限时 鼠标右键单击才弹出相应菜单操作栏
                                // 禁用鼠标右键默认行为
                                e.preventDefault();
                                dispatch({
                                    type: 'projectTasks/setCTaskId',
                                    payload: task.taskId,
                                })
                                if (task.taskId !== taskInfo?.taskId) {
                                    if ((isAddTask || editable) && taskInfo.name) {
                                        await dispatch({
                                            type: 'projectTasks/setSubmitStatus',
                                            payload: task
                                        })
                                    } else {
                                        dispatch({
                                            type: 'projectTasks/fetchTaskInfo',
                                            payload: task,
                                            projectId
                                        })
                                    }
                                }
                                showContextMenu();
                                let y = e.clientY
                                let x = e.clientX
                                if (document.body.offsetHeight - e.clientY < 300) {
                                    y = document.body.offsetHeight - 200;
                                }
                                // @ts-ignore
                                if (window.clientWidth - e.clientX < 100) {
                                    // @ts-ignore
                                    x = window.clientWidth - 100;
                                }
                                setContextMenuPosition({
                                    x,
                                    y
                                });
                            },
                        })}
                    />
                </WrapperComponent>

            </div>
            {
                taskInfo?.taskId && <TableRowInformation />
            }
            {/* <TableRowInformation /> */}

        </BdwRow>
 
        


    )
}

export default ProjectTasksTable;
