/**
 * @description 项目任务表格行-详情
 * <AUTHOR>
 * @date 2023-11-07 11:27:11
*/
import React, { useState, } from 'react';
import { message, Tabs, } from 'antd';
import { projectTasksRowTab, projectTasksRowTabKey } from '@/constants/Enum';
import ProgressReport from './progressReport';
import TaskHistory from './taskHistory';
import ResultEvaluation from './resultEvaluation';
import TaskInformation from './taskInformation';
import StandardReference from './standardReference';
import { CloseOutlined } from '@ant-design/icons';
import { useSelector, useDispatch } from 'umi';
import { useUpdateEffect } from 'ahooks';
import './index.less';

const TableRowInformation: React.FC = () => {
    const dispatch = useDispatch();
    const [activeTabKey, setActiveTabKey] = useState<any>(projectTasksRowTabKey.TASK_INFORMATION);
    const { editable, taskInfo, isAddTask } = useSelector((state: any) => state.projectTasks);
    const TabsItem: any[] = projectTasksRowTab.map((item: any, index: number) => {
        let children: any;
        switch (item.key) {
            case projectTasksRowTabKey.TASK_INFORMATION://任务信息
                children = <TaskInformation />;
                break;
            case projectTasksRowTabKey.TASK_HISTORY://任务历程
                children = <TaskHistory />;
                break;
            case projectTasksRowTabKey.PROGRESS_REPORT://进程汇报
                children = <ProgressReport />;
                break;
            case projectTasksRowTabKey.STANDARD_REFERENCE://标准参考
                children = <StandardReference />;
                break;
            case projectTasksRowTabKey.RESULT_EVALUATION://结果评价
                children = <ResultEvaluation />;
                break;
        }
        return {
            ...item,
            children
        }
    })
    useUpdateEffect(()=>{
        if(isAddTask){
            setActiveTabKey(projectTasksRowTabKey.TASK_INFORMATION);
        }
    },[isAddTask])
    function change<T>(e: T) {
        if ((isAddTask || editable)) {
            if (taskInfo.name) {
                dispatch({type: 'projectTasks/setSubmitStatus'})
            } else {
                // message.error('未填写标题');
                return
            }
        }
        setActiveTabKey(e)
    }
    const closeRightContent = () => {
        dispatch({type: 'projectTasks/getTaskInfo',payload:null})
    }
    return (
        <div className='table-row-information'>
            <CloseOutlined onClick={closeRightContent} className='close-icon'></CloseOutlined>
            <Tabs
                className='table-row-information-tabs'
                items={TabsItem}
                onChange={change}
                activeKey={activeTabKey}
                destroyInactiveTabPane
            />
        </div>
    )
}
export default TableRowInformation;