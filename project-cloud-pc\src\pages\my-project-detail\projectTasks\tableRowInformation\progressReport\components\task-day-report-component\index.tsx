import React, { useEffect, useImperativeHandle, useMemo, useState} from "react";
import {useRequest} from "ahooks";
import {getTaskReportByTaskId} from '@/service/projectDos/my-project-detail/projectTasks';
import {groupBy} from "lodash";
import {Collapse, Empty} from "antd";
import moment from "moment";
import {BdwGeneralMessageShow} from "@/components";
import {DayReportDataItem} from "@/components/bdw-general-message-show"
import "./index.less";

const {Panel} = Collapse;

interface GroupByDayTimeItem {
  // 分组好的时间
  dayTime: string
  // 每一个分组里面的数组
  dataList: DayReportDataItem[]
}

interface TaskDayReportComponentProps {
  taskId: string
  canAdd?: boolean
  canReply?: boolean
}

export const TaskDayReportComponent = React.forwardRef((props: TaskDayReportComponentProps, ref: any) => {
  const {taskId,canAdd = false, canReply} = props;
  const [currentDayReportId, setCurrentDayReportId] = useState("");

  const {data: dayReportData, refresh,run: runDayReportData} = useRequest(() => getTaskReportByTaskId(taskId), {
    manual:true,
    cacheKey:'article',
  })
  useEffect(()=>{
    if(taskId && taskId !== 'newAdd'){
      runDayReportData();
    }
  },[taskId])

  useImperativeHandle(ref, () => ({
    refreshComponent: refresh
  }))

  const handleGetData = useMemo(() => {
    //@ts-ignore
    if (dayReportData && dayReportData.length > 0) {
      //@ts-ignore
      const handleDayReportData = dayReportData.map((item: any) => {
        return {...item,dayTime: moment(item.time).format("YYYY-MM-DD")}
      })
      const groupDataByTime = groupBy(handleDayReportData,"dayTime");
      const handleGroupDataByTime: GroupByDayTimeItem[] = Object.keys(groupDataByTime).map((item) => {
        return {
          dayTime: item,
          dataList: groupDataByTime[item]
        }
      })
      return handleGroupDataByTime
    }
    return []
  }, [JSON.stringify(dayReportData)])

  const clickToReply = (datReportId: string | number) => {
    setCurrentDayReportId(datReportId.toString());
  }

  const replyCallback = async () => {
    await refresh();
  }
  const showHasSaveDayReport = handleGetData.map((item: GroupByDayTimeItem,index) => {
    return (
      <Panel key={index} header={`${item.dayTime} 汇报`}>
        <BdwGeneralMessageShow
          canReply={canReply}
          key={index}
          generalMessageList={item.dataList}
          replyFun={clickToReply}
          dayReportId={currentDayReportId}
          sureFun={() => replyCallback()}
        />
      </Panel>
    )
  })

  return (
    <div style={{overflow:'hidden'}}>
      {
        handleGetData.length > 0 &&
        <div className='task-day-report-component'>
          <Collapse bordered={false} ghost expandIconPosition='end' destroyInactivePanel>
            { showHasSaveDayReport }
          </Collapse>
        </div>
      }
      {
        handleGetData.length === 0 && !canAdd &&
          <Empty className='mt-16' description="暂无进程汇报" />
      }
    </div>
  )
})

export default TaskDayReportComponent
