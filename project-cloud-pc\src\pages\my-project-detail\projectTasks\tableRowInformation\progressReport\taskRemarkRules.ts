export interface TaskRemarkFormItem {
    value: "",
    fileList: string[]
  }
export const taskRemarkRules = {
    "item": [
      () => ({
        validator: async (rule: any, value: TaskRemarkFormItem) => {
          if ((value.value && value.value !== '') || (value.fileList ?? []).length > 0) {
            return Promise.resolve()
          }
          return Promise.reject(new Error('该项不能为空'));
        }
      })
    ],
  
  }