/**
 * @description 结果评价
 * <AUTHOR>
 * @date 2023-11-07 11:51:24
*/
import React, {useMemo,useEffect} from "react";
import {Form, Button, message, Divider} from "antd"
import {useRequest} from "ahooks";
import { SelectSubmitEvaluationMaterials, submitEvaluationMaterials } from '@/service/projectDos/my-project-detail/projectTasks';
import EvaluationCriterionReadonly from "./component/evaluation-criterion-readonly";
import {BdwUploadBtn, BdwFormItems, BdwInput} from "@/components";
import {useSelector} from 'umi';
import {AuditResult,ProjectFileTypeAccepts} from "@/constants/Enum";
import "./index.less";

interface SubmitEvaluationCriterionFileProps {
  taskId: string | number
  saveSuccessCallback?: () => void
  cancelCallback?: () => void
}


const ResultEvaluation: React.FC<SubmitEvaluationCriterionFileProps> = (props) => {
  const {saveSuccessCallback,cancelCallback} = props;
  const { editable, taskInfo, isAddTask } = useSelector((state: any) => state.projectTasks);
  const { taskId } = taskInfo;
  // const {taskStore} = useStore();
  // 需要获取历史已经保存的数据
  const {data: historyData,run:runHistoryData} = useRequest<any>(() => SelectSubmitEvaluationMaterials(taskId), {
    manual:true
  })
  useEffect(()=>{
    if(taskId && taskId != 'newAdd'){
        runHistoryData();
    }
},[taskId])

  const [form] = Form.useForm();

  // 将获取到的已保存的数据处理
  const handleHistoryData = useMemo(() => {
    if (historyData) {
      return historyData;
    }
    return null
  }, [historyData])

  const checkFileRule = [
    () => ({
      validator(rule: any, value:string[]) {
        if(value && value.length > 0) {
          return Promise.resolve()
        }
        return Promise.reject(new Error('必须上传该文件'));
      }
    })
  ]
  // buttonName={`上传${item.des}`}
  const getSubmitFileListForm = handleHistoryData && handleHistoryData.evaluationUploadStandards?.map((item: any,index: number) => {

    return(
      <div key={index + "file"}>
        <div className='mb-10'>
          <span className='help-title'>请上传{item.attachmentDescription},要求文件格式为【{item.attachmentFormat?item.attachmentFormat:'任意格式'}】</span>
        </div>
        <BdwFormItems name="document" validateTrigger="onFinish" rules={checkFileRule} >
          <BdwUploadBtn  />
        </BdwFormItems>
      </div>
    )
  })

  const submitData = async (values: any) => {
    const submitInfo = {
      taskId,
      ...values,
      document:values.document?.map((item: any) => item.id).join(),
    }
    try {
      await submitEvaluationMaterials(submitInfo);
      message.success("信息提交成功");
      saveSuccessCallback?.()
    }catch (e) {
      
    }
  }

  const cancelSubmit = () => {
    cancelCallback?.();
  }

  return (
    <div className='pr-16'>
      <EvaluationCriterionReadonly  taskId={taskId} handleHistoryData={handleHistoryData} />
      <Divider dashed={true} className='mt-16 mb-16' />
      <Form form={form} onFinish={submitData}>
        <BdwFormItems label="汇报内容" name='reportContent' required rules={[{required: true, message: "该信息项不能为空"}]}>
          <BdwInput placeholder='请输入汇报内容信息'  maxLength={1024} onKeyDown={(e)=>{
            if(e.keyCode===13){
              e.nativeEvent.stopImmediatePropagation()
            }
          }}/>
        </BdwFormItems>
        {
          handleHistoryData && handleHistoryData.evaluationUploadStandards.length>0 ?
          getSubmitFileListForm :
          (
            <BdwFormItems label="汇报附件" name='document'>
              <BdwUploadBtn />
            </BdwFormItems>
          )
        }
        <div className='evaluation-criterion-submit-buttons'>
          <Button className='mr-16' type='primary' htmlType='submit'>提交</Button>
          <Button onClick={() => cancelSubmit()}>取消</Button>
        </div>
      </Form>
    </div>
  )
}

export default ResultEvaluation
