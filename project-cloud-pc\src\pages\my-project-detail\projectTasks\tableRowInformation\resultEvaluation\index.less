@import '../../../../../styles/base.less';
.result-evaluation-container{
  padding: 20px 16px;
    .project-task-review-modal{
        .h-32{
            height: 35px;
            line-height: 35px;
        }
        .ant-input-affix-wrapper.ant-input-affix-wrapper-borderless{
            padding-left: unset;
        }
    }
    .evaluation-file-item {
        line-height: 24px;
        align-items: flex-end;
        .evaluation-file-type {
          width: 90px;
          word-break: break-all;
          margin: 0 5px;
        }
        .evaluation-file-readonly-item {
          color: @help;
          font-size: 13px;
        }
        .evaluation-file-control {
          .ant-btn {
            height: 24px;
          }
        }
        .ant-form-item{
            margin-bottom: unset;
        }
      }

}