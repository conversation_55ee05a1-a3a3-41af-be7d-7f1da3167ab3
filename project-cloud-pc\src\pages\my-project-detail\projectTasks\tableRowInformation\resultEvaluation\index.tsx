/**
 * @description 结果评价
 * <AUTHOR>
 * @date 2023-11-07 11:51:24
*/
import React, { useContext, useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, Spin } from "antd";
import { useBoolean, useRequest } from "ahooks";
import EvaluationCriterionReadonly from "./component/evaluation-criterion-readonly";
import TaskAudit from "./TaskAudit";
import { useSelector, useParams ,useDispatch} from 'umi';
import session from "@/session";
import { AuditResult, ProjectFileTypeAccepts,TaskFunctionCode } from "@/constants/Enum";
import { SelectSubmitEvaluationMaterials, listTaskFunctionality } from '@/service/projectDos/my-project-detail/projectTasks';
import EvaluationCriterion from "./EvaluationCriterion";
import { disabledFlag } from '../../../projectTasks';
import SubmitEvaluationCriterionFile
  from "./SubmitEvaluationCriterionFile";
import "./index.less";

interface EvaluationCriterionDetailReadonlyProps {
  task?: any
  tabType?: string
}

export type ActiveModuleType = "initial" | "edit" | "submit";

const ResultEvaluation: React.FC<EvaluationCriterionDetailReadonlyProps> = (props) => {
  const { tabType, task } = props;
  const dispatch = useDispatch();
  const [auditShowVisible, { setFalse: auditDialogHide, setTrue: auditDialogShow }] = useBoolean(false);
  const { projectId } = useParams<{ projectId: string }>();
  const [activeModule, setActiveModule] = useState<ActiveModuleType>("initial");
  const { editable, taskInfo, isAddTask, functionality } = useSelector((state: any) => state.projectTasks);
  const { taskId } = taskInfo;

  // 需要获取历史已经保存的数据
  const { data: historyData, loading, run: runHistoryData } = useRequest(() => SelectSubmitEvaluationMaterials(taskId), {
    manual: true,
  })
  useEffect(() => {
    if(taskId && taskId!= 'newAdd'){
      runHistoryData();
    }

  }, [taskId])

  // 将获取到的已保存的数据处理
  const handleHistoryData: any = useMemo(() => {
    if (historyData) {
      return historyData;
    }
    return null
  }, [historyData])



  // const isProjectHeader = Number(session.getLoginUser()?.id) === Number(project?.header);
  // const isProjectCreator = Number(session.getLoginUser()?.id) === Number(project?.recordPerson)


  const taskArr: any = []
  // const getParentTasks = function (task?: any) {
  //   if (task?.parent) {
  //     getParentTasks(task?.parent)
  //     taskArr.push(task?.parent)
  //   }
  //   return taskArr
  // }
  // const taskList = getParentTasks(task?);
  // const filterHeaderId = taskList?.filter((item: any) => {
  //   return item.assigned === "Accept"
  // })


  /**
   *  编辑权限
   *  1. 项目负责人和创建人可以有任何权限
   *  2. 是任务的上级负责人可以编辑
   *  3. 父级任务需要是已接收
   * */
  const getEditSeriesPermission = () => {
    return true;
  }

  const refresh = () => {
    runHistoryData()
    listTaskFunctionality({ projectId, taskId }).then((res: any) => {
      dispatch({
          type: 'projectTasks/setFunctionality',
          payload: res
      })
  })
  }

  useEffect(() => {
    setActiveModule("initial")
  }, [taskId])

  return (
    <div className='pr-16 result-evaluation-container'>
      {activeModule === "initial" &&
        <Spin spinning={loading} className='pr-16'>
          <EvaluationCriterionReadonly taskId={taskId} task={task} handleHistoryData={handleHistoryData} />
        </Spin>

      }
      {
        (activeModule === "initial" && !disabledFlag(functionality, TaskFunctionCode.REVIEW)) &&
        <div className='mt-16'>
          <Button type='primary' onClick={() => auditDialogShow()}>评审</Button>
          <TaskAudit
            taskId={taskId}
            visible={auditShowVisible}
            cancelFun={() => auditDialogHide()}
            sureFun={() => {
              auditDialogHide();
              refresh();
            }}
            standScore={handleHistoryData?.projectScore}
            reviewId={handleHistoryData?.taskReviewInfo?.reviewId}
          />
        </div>
      }
      {
        (activeModule === "initial" && !disabledFlag(functionality, TaskFunctionCode.EDIT_RESULT_EVALUATION_CRITERIA)) &&
        <div className='mt-16'>
          <Button type='primary' onClick={() => setActiveModule("edit")}>编辑评价标准</Button>
        </div>
      }
      {
        (activeModule === "initial" && !disabledFlag(functionality, TaskFunctionCode.SUBMIT_EVALUATION_MATERIALS)) &&
        <div className='mt-16'>
          <Button type='primary' onClick={() => setActiveModule("submit")}>提交评价资料</Button>
        </div>
      }
      {
        activeModule === "edit"
        &&
        <EvaluationCriterion
          taskId={taskId}
          cancelCallback={() => setActiveModule("initial")

          }
          saveSuccessCallback={() => {
            setActiveModule("initial")
            refresh();
          }} />
      }
      {
        activeModule === "submit" &&
        <SubmitEvaluationCriterionFile
          taskId={taskId}
          cancelCallback={() => setActiveModule("initial")}
          saveSuccessCallback={() => {
            setActiveModule("initial")
            refresh();
          }}

        />

      }
    </div>
  )
}

export default ResultEvaluation
