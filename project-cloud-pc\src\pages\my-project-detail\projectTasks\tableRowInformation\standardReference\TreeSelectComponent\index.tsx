import React, { useEffect, useMemo, useState } from "react"
import { Input, Tree, TreeSelect } from 'antd'
import { UserOutlined } from '@ant-design/icons'
import type { DataNode } from 'antd/es/tree'
// @ts-ignore
import Folder from '@/assets/image/folder.svg'
import './index.less'

interface dadaListProps {
    key: React.Key
    title: string
}

interface TreeSelectProps {
    value?: string[] | null | undefined,
    treeNodeData: {
        key: string
        title: string
        children: {
            key: string
            title: string
        }[]
    }[]
    handleOnTreeCheck: (checkedKeysValue: { name?: string }[]) => void,
    onChange?: (checkedKeysValue: any) => void,
}

const { Search } = Input

const X = 3;
const y = 2;
const z = 1;
const defaultData: DataNode[] = [];

// mock data
const generateData = (_level: number, _preKey?: React.Key, _tns?: DataNode[]) => {
    const preKey = _preKey || '0';
    const tns = _tns || defaultData;

    const children: React.Key[] = [];
    for (let i = 0; i < X; i++) {
        const key = `${preKey}-${i}`;
        tns.push({ title: key, key });
        if (i < y) {
            children.push(key)
        }
    }
    if (_level < 0) {
        return tns
    }
    const level = _level - 1;
    children.forEach((key, index) => {
        tns[index].children = [];
        return generateData(level, key, tns[index].children);
    })
}
generateData(z);

// 扁平化搜索数组
const dataList: dadaListProps[] = [];
const generateList = (data: DataNode[]) => {
    for (let i = 0; i < data.length; i++) {
        const node = data[i];
        const { key, title } = node;
        dataList.push({ key, title: title as string })
        if (node.children) {
            generateList(node.children)
        }
    }
}
// generateList(defaultData)

// 获取parentKey
const getParentKey = (key: React.Key, tree: DataNode[]): React.Key => {
    let parentKey: React.Key;

    for (let i = 0; i < tree.length; i++) {
        const node = tree[i]
        if (node.children) {
            if (node.children.some((item) => item.key == key)) {
                parentKey = node.key
            } else if (getParentKey(key, node.children)) {
                parentKey = getParentKey(key, node.children)
            }
        }
    }
    // 末尾的!为ts的非null和undefined断言
    return parentKey!
}

const TreeSelectComponent: React.FC<TreeSelectProps> = ({
    value,
    treeNodeData,
    handleOnTreeCheck,
    onChange
}: TreeSelectProps) => {
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([])  // 展开的节点
    const [searchValue, setSearchValue] = useState<string>('')
    const [autoExpandParent, setAutoExpandParent] = useState(true)  // 自动展开父节点
    const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);

    useEffect(() => {
        if (treeNodeData) {
            // 扁平化搜索list数据
            generateList(treeNodeData)
        }

    }, [treeNodeData])

    // 重置时清空已选
    // useEffect(() => {
    //     if (value?.length) {
    //         setCheckedKeys(value)
    //     }
    // }, [value])

    // 搜索内容触发
    const handleOnSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target
        const newExpandedKeys = dataList.map((item) => {
            if (item.title.indexOf(value) > -1) {
                return getParentKey(item.key, treeNodeData)
            }
            return null
        }).filter((item, i, self) => item && self.indexOf(item) === i)

        // console.log(treeNodeData, newExpandedKeys, '<<<newExpandedKeys');
        console.log(newExpandedKeys, 'newExpandedKeys')
        setExpandedKeys(newExpandedKeys as React.Key[])
        setSearchValue(value)
        setAutoExpandParent(true)
    }

    // 勾选触发
    const handleOnCheck = (checkedKeysValue: any, e: any) => {
        const { checkedNodes } = e;
        setCheckedKeys(checkedKeysValue);
        let arr = [];
        if (checkedNodes.length) {
            arr = checkedNodes.filter((item: any) => !item.isOrg).map((item: any) => ({ name: item.titleDesc,id: item.key }))
        }
        onChange?.(arr);
    }

    // 展开节点触发
    const handleOnExpand = (newExpandedKeys: React.Key[]) => {
        setExpandedKeys(newExpandedKeys)
        setAutoExpandParent(false)
    }


    // tree组件数据
    const treeData = useMemo(() => {
        const loop = (data: any[]): any => {
            return data.map((item) => {
                const strTitle = item.title as string;
                const index = strTitle.indexOf(searchValue);
                const beforeStr = strTitle.substring(0, index);
                const afterStr = strTitle.slice(index + searchValue.length);
                const title = index > -1 ? (
                    <span>
                        {beforeStr}
                        <span className="site-tree-search-value">{searchValue}</span>
                        {afterStr}
                    </span>
                ) : (
                    <span>{strTitle}</span>
                )

                if (item.children?.length) {
                    return {
                        title,
                        key: item.key,
                        titleDesc: item.title,
                        icon: <img style={{ width: 20 }} src={Folder} />,
                        children: loop(item.children),
                        isOrg: item.isOrg,
                        posName: '/' + item.posName
                    }
                }

                // 最末级节点
                return {
                    title,
                    titleDesc: item.title,
                    icon: item.isOrg ? <img style={{ width: 20 }} src={Folder} /> : <UserOutlined style={{ width: 20 }} />,
                    key: item.key,
                    isOrg: item.isOrg,
                    posName: '/' + item.posName
                }
            })
        }
        // return loop(defaultData)
        return loop(treeNodeData)
    }, [searchValue, treeNodeData])



    return <div className="tree-warper-container">
        <Search
            placeholder="请输入"
            style={{ marginBottom: 8 }}
            onChange={handleOnSearch}
        />
        <div className="tree-warper">
            <Tree
                showIcon
                defaultExpandAll
                onCheck={handleOnCheck}
                checkedKeys={checkedKeys}
                onExpand={handleOnExpand}
                expandedKeys={expandedKeys}
                autoExpandParent={autoExpandParent}
                treeData={treeData}
                checkable
            />
        </div>

    </div>
}

export default TreeSelectComponent
