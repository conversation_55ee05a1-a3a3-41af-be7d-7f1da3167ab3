@import '../../../../../styles/base.less';
@taskBottomColor: #c0c0c0;

.basic-information-container {
  .padding-custom {
    padding: 20px 16px;
  }
  .project-information-add{
    .detail-info-word{
      font-size: 13px;
      margin-bottom: 8px;
      min-height: 27px;
      line-height: 27px;
    }

      img{
        width: 100px;
        height: 100px;
        margin: 0 10px 10px 0;
      }
    
    
  }

  .pt-16();
  padding-bottom: 48px;
  height: 100%;
  overflow-y: auto;
  position: relative;

  .ant-form-item {
    margin-bottom: unset;
  }

  .label-reset {
    font-size: 12px;
    color: #5c5c5c;
    flex: 1;
  }

  .input-style.ant-input {
    background-image: linear-gradient(#0275d8, #025aa5), linear-gradient(@taskBottomColor, @taskBottomColor);
    background-repeat: no-repeat;
    background-position: 0 100%, left 100%;
    background-size: 0 1px, 100% 1px;
    border-radius: unset;
    padding-left: unset;
    color: #333;
    font-size: 13px;
    height: 26px;

    &[value=""] {
      background-image: linear-gradient(#0275d8, #025aa5), linear-gradient(#d9534f, #d9534f);
    }

    &:read-only {
      background-image: unset;
    }

    &:focus {
      background-size: 100% 1px, 100% 1px;
    }

    &::placeholder {
      font-size: 12px;
    }
  }

  .basic-information-btn {
    position: absolute;
    height: 48px;
    display: flex;
    align-items: center;
    box-shadow: 0px -1px 3px #00000014;
    width: 100%;
    left: 0;
    bottom: 0;

    .disabled-save {
      pointer-events: none;
      cursor: not-allowed;
      position: relative;
      border-color: rgba(255, 255, 255, .5);
    }

    .disabled-save::after {
      content: '';
      position: absolute;
      right: 0;
      bottom: 0;
      top: 0;
      left: 0;
      background: rgba(255, 255, 255, .5);
    }
  }

  .h-32 {
    height: 32px;
  }

  .form-label {
    width: 63px;
    font-size: 12px;
    color: #5c5c5c;
    text-align: justify;
    text-align-last: justify;
    position: relative;
    padding-right: 5px;
    margin-right: 5px;
    height: 27px;
    line-height: 27px;

    &::after {
      content: ':';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translate(0, -50%);
      line-height: 27px;
    }
  }

  .form-content {
    min-height: 27px;
    line-height: 27px;
    color: #333;
    font-size: 13px;
    flex: 1;
  }

  .association-task-name {
    color: #0275d8;
    flex: 1;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #c0c0c0;
  }
  .association-task-name-empty{
    flex: 1;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #c0c0c0;
    font-size: 12px;
  }

  .bdw-form-item-container {
    margin-bottom: 10px;
  }

  .bdw-form-item-container.association-task-name-w {
    margin-bottom: unset;
  }

  .project-information-add {
    display: flex;
    flex-direction: column;
    width: 100%;
    flex: 1;

    .position-parent-relative {
      position: relative;

      .delete-has-upload-textarea-btn {
        font-size: 12px;
        position: absolute;
        top: 4px;
        right: 4px;
      }
    }
  }

  .work-time-adjust {
    align-items: unset;

    .input-style.ant-input {
      background-image: unset;
    }

    .work-time-adjust-edit {
      width: 270px;

      .work-time-adjust-edit-item {
        width: 270px;
        height: 27px;

        .ant-checkbox-wrapper {
          width: 100px;
          height: 27px;
        }

        .ant-input {
          border-bottom: 1px solid @taskBottomColor;
          height: 27px !important;
          padding-left: unset;
        }

        .ant-input::placeholder {
          font-size: 13px;
        }
      }
    }

    .select-time {
      margin-left: 0 !important;
      // border-bottom: 1px solid @taskBottomColor !important;
      height: 26px !important;
      display: flex;
      align-items: center;
      height: 26px;
      width: 100px;
      margin-right: 10px;

      .ant-select-selector {
        padding: 0;
        height: 26px;
        align-items: center;

        .ant-select-selection-item {
          font-size: 13px;
          padding: 0;
        }
      }
    }
  }

  .taskInfo-time-setting {
    .ant-picker {
      width: 88px;
      padding-right: unset;
      padding-left: unset;

      input {
        font-size: 14px;
      }

      input::placeholder {
        font-size: 12px;
      }

      .ant-picker-suffix {
        display: none;
      }
    }

    >div {
      width: 50%;
    }

    .select-time {
      width: 60px !important;
      margin-left: 0 !important;
      // border-bottom: 1px solid @taskBottomColor !important;
      height: 26px !important;
      text-align: center;
      display: flex;
      align-items: center;

      .ant-select-selector {
        padding: 0;

        .ant-select-selection-item {
          padding: 0;
        }
      }

      .ant-select-arrow {
        display: none;
      }
    }

  }

  .execution-cycle-setting {
    width: 170px;
    height: 27px;

    .ant-input-number-input-wrap {
      input {
        height: 27px;
        font-size: 12px;
        font-weight: bold;
        padding-left: unset;
      }

      input::placeholder {
        font-weight: normal;
      }
    }
  }

  .back-show-info {
    font-size: 12px;
    color: #c5c8cd;
    height: 27px;
    line-height: 27px;
  }
  .no-files-empty{
    height: 60px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #c5c8cd;
    img{
      width: 40px;
      height: 40px;
    }
  }

  .border-bottom-c0 {
    border-bottom: 1px solid @taskBottomColor;
  }

  .w-170 {
    width: 170px;

    .ant-picker,
    .ant-select {
      // border: unset;
      border-bottom: 1px solid @taskBottomColor  !important;
    }

    .ant-select {
      // margin-left: 10px;
    }

    .ant-picker {
      flex: 1;
      padding-left: unset;

      .ant-picker-input input::placeholder {
        font-size: 12px;
      }
    }
  }

  .upload-btn-w {
    margin-bottom: unset;
    button {
      padding: 0;
      border: unset;
    }
  }

  .w-48 {
    width: 48%;
  }

  .zx-fz-wrapper {
    .choose-responsible-person {
      .bdw-custom-select-content {
        width: 510px !important;
        right: 0;
        left: unset !important;
      }
    }

    .ant-select-single .ant-select-selector .ant-select-selection-search {
      left: unset;
    }
  }

  .choose-position-list,
  .process-choose {
    .ant-select {
      width: 170px;

      .ant-select-selector {
        height: 26px;
        border: none;
        border-bottom: 1px solid #c0c0c0;
        padding-left: unset;

        .ant-select-selection-item {
          font-size: 13px;
          line-height: unset;
          display: flex;
          align-items: center;
        }
      }

    }
  }

  .ant-select-selection-placeholder,
  .custom-placeholder {
    font-size: 12px;
  }

  input::placeholder {
    font-size: 12px;
  }

  .ant-select-selection-placeholder {
    line-height: 25px !important;
  }

  .div-line {
    width: 100%;
    height: 8px;
    background: #f7f7f7;
  }

  .pt-0 {
    padding-top: 0px;
  }

  .px-16 {
    padding: 0 16px;
  }

  .zx-ask-for-leave {

    .work-time-adjust .form-label::after,
    .show-work-time-adjust .form-label::after {
      content: '';
    }

    .work-time-adjust-label {
      width: 84px;
    }
    .click-out-side-box{
      height: 100%;
      overflow-y: unset!important;
      overflow-x: unset!important;;
      .project-task-table{
        border: none!important;
      }
      .project-tasks-container{
        height: 100%;
      }
    }
  }

  .ant-radio {
    .ant-radio-inner {
      border-radius: unset;
    }

    .ant-radio-inner::after {
      position: absolute;
      top: 50%;
      left: 21.5%;
      display: table;
      width: 5.71428571px;
      height: 9.14285714px;
      border: 2px solid #fff;
      border-top: 0;
      border-left: 0;
      transform: rotate(45deg) scale(0) translate(-50%, -50%);
      opacity: 0;
      transition: all 0.1s cubic-bezier(0.71, -0.46, 0.88, 0.6), opacity 0.1s;
      content: ' ';
      margin: unset;
      border-radius: unset;
    }
  }

  .ant-radio-checked .ant-radio-inner::after {
    position: absolute;
    display: table;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    transform: rotate(45deg) scale(1) translate(-50%, -50%);
    opacity: 1;
    transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
    content: ' ';
  }

  .ant-radio-checked .ant-radio-inner {
    border-color: #1890ff;
    background: #1890ff;
  }

  .pl-24 {
    padding-left: 24px;
  }
  .show{
    display: block;
  }
  .hidden{
    display: none;
  }

}
