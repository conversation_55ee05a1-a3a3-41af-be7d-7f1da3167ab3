/**
 * @description 任务信息
 * <AUTHOR>
 * @date 2023-11-07 11:51:00
*/
import React, { useEffect, useMemo, useRef, useState } from 'react';
import type { MutableRefObject } from 'react';
import { Form, Input, Divider, Button, Checkbox, DatePicker, InputNumber, Popconfirm, Select, message, Modal, Radio, Space } from 'antd';
import styled from 'styled-components';
import { BdwRow, BdwChooseCompanyStaff, BdwUploadBtn, BdwButton, EditableContent, BdwReadonlySpan, BdwLeaveMessage, BdwLeaveMessageReadonly, BdwFileShow, BdwRichText } from '@/components';
import { CloseOutlined, NotificationOutlined, PlusOutlined } from "@ant-design/icons/lib";
import { listTaskLeaderInfo } from '@/service/projectDos/my-project-detail/projectTasks';
import { useParams, useSelector, useDispatch } from 'umi';
import {
    editProjectTasksApi,
    calcWorkDays,
    getTaskInfo,
    listPositionSelectOptionsApi,
    listDirectoryProcessApi,
    listMaterialSupplyApi,
    listUserTypeVisibilityApi
} from '@/service/projectDos/my-project-detail/projectTasks';
import AssociatedTaskModal from "../../component/accociated-task-modal";
import moment from 'moment';
import { useBoolean, useRequest, useUpdateEffect } from 'ahooks';
import { hourAndMinuteList, workTimeAdjustOption } from './hourAndMinuteList';
import './index.less';
import { cloneDeep, isString, isArray, isObject, rest } from 'lodash';
import ReactZmage from "react-zmage";
import type { defaultTimeAdjustType } from '@/type';
// @ts-ignore
import COPY_PRODUCT_PRICE_INFO from '@/assets/image/COPY_PRODUCT_PRICE_INFO.svg';
import { getRichImgSrc, getText, getLocalStorage } from '@/utils/utils';
// @ts-ignore
import EMPTY_IMG from '@/assets/image/empty.png';

const { Item } = Form;
const { confirm } = Modal;
const ImgIcon = styled.img`
 width:18px;
 height:18px;
 margin-right:10px;
`;

interface WrapComponentProps {
    labelname?: string
    wrapclassname?: string
}



export interface TaskRemarkFormItem {
    value: "",
    fileList: string[]
}

export const taskRemarkRules = {
    "item": [
        () => ({
            validator: async (rule: any, value: TaskRemarkFormItem) => {
                if ((value.value && value.value !== '')) {
                    return Promise.resolve()
                }
                return Promise.reject(new Error('该项不能为空'));
            }
        })
    ],

}

const WrapComponent = <T extends {}>(WrappedComponent: React.FC<T>) => {
    const ContentComponent = (props: WrapComponentProps & T) => {
        const { labelname, wrapclassname = '' } = props;
        return (
            <BdwRow type='flex' className={`${wrapclassname} bdw-form-item-container`}>
                {
                    labelname && <div className='form-label'>
                        {labelname}
                    </div>
                }
                <WrappedComponent
                    className='label-reset'  {...props as T}
                />
            </BdwRow>
        )
    }
    return ContentComponent;
}
const BdwFormItem = WrapComponent(Item);
const defaultTimeAdjust: defaultTimeAdjustType = {
    askLeaveDays: {//请假
        checked: false,
        day: ''
    },
    compensatoryLeaveDays: {//补休
        checked: false,
        day: ''
    },
    depositRestDays: {//存休
        checked: false,
        day: ''
    }
}



const PeriodEditableContent = <T extends { editable: boolean, onChange: (data: any) => void, value: any }>(props: T) => {
    const { editable, onChange, value } = props;
    return <EditableContent
        editable={editable}
        renderShow={value && <BdwReadonlySpan>{value}天</BdwReadonlySpan> ||
            <span className="back-show-info">未填写</span>}
        renderEditor={
            <span className="flex execution-cycle-setting border-bottom-c0">
                <InputNumber
                    step={0.1}
                    placeholder='输入执行周期天数'
                    maxLength={3}
                    min={0}
                    value={value}
                    style={{
                        width: "100%",
                        fontSize: '14px',
                        border: "none !important",
                        outline: "none !important",
                    }}
                    bordered={false}
                    onChange={onChange}
                />
            </span>}
    />
}
const TaskInformation: React.FC = () => {
    const dispatch = useDispatch();
    const scrollRef = useRef();
    const [form] = Form.useForm();
    const { projectId } = useParams<{ projectId: string }>();
    const { projectOverviewDetails } = useSelector((state: any) => state.projectOverview);//项目概览数据
    const { editable, taskInfo, isAddTask, submitStatus, taskInfoFlag, saveStatus, basicProjectInfo, willSetTaskInfo } = useSelector((state: any) => state.projectTasks);
    const { workSystemCode } = basicProjectInfo;
    const [associatedTaskModal, { setFalse: associatedTaskModalHide, setTrue: associatedTaskModalShow }] = useBoolean(false);
    let remark = taskInfo?.taskExplain;
    const [flag, setFlag] = useState(false);
    const [workTimeAdjust, setWorkTimeAdjust] = useState<any>(defaultTimeAdjust);
    const [listPositionSelectOptions, setListPositionSelectOptions] = useState([]);
    const [listDirectoryProcess, setListDirectoryProcess] = useState([]);
    const [listMaterialSupply, setListMaterialSupply] = useState([]);
    const [listUserTypeVisibility, setListUserTypeVisibility] = useState([]);
    const [checkedInfo, setCheckedInfo] = useState({
        isVacation: false,
        isMaterialSupply: false,
        isVisibility: false,
        isAssociated: false
    })
    const showHasSaveTaskRemark = remark && isArray(remark) && remark?.map((item: any, index: number) => {
        const detailsArr = getRichImgSrc(item.value);
        return <div key={index}>
            <div className='detail-info-word'>{getText(item.value)}</div>
            {
                detailsArr.length > 0 && detailsArr.map((i: any, index: number) => (
                    // @ts-ignore
                    <ReactZmage key={index} backdrop='rgba(0,0,0,0.7)' src={i} />
                ))
            }
        </div>
        // return (
        //     <BdwRichText
        //         hideTool
        //         sx={{ width: '100%', height: '120px', marginBottom: '10px' }}
        //         placeholder='填写任务说明(可直接粘贴图片)'
        //         disabled={true}
        //         value={item}
        //         key={index}
        //     >

        //     </BdwRichText>
        // )
    })
    useEffect(() => {
        listPositionSelectOptionsApi().then((res: any) => {
            const resData = res.map((i: any) => ({
                label: i.name,
                value: i.value,
            }))
            setListPositionSelectOptions(resData);
        })
        listDirectoryProcessApi().then((res: any) => {
            const resData = res.filter((i: any) => i.attribute).map((i: any) => ({
                label: i.name,
                value: i.attribute,
            }))
            setListDirectoryProcess(resData);
        })
        listMaterialSupplyApi().then((res: any) => {
            const resData = res.map((i: any) => ({
                label: i.value,
                value: i.name,
            }))
            setListMaterialSupply(resData);
        })
        listUserTypeVisibilityApi().then((res: any) => {
            const resData = res.map((i: any) => ({
                label: i.value,
                value: i.name,
            }))
            setListUserTypeVisibility(resData);
        })
    }, [])
    //工作时间调整参数
    const getCalcWorkDay = (obj: defaultTimeAdjustType) => {
        const params = {
            askLeaveDays: undefined,//请假
            compensatoryLeaveDays: undefined,//补休
            depositRestDays: undefined,//存休
            projectId,
            parentTaskId: taskInfo.parentId,
        }
        for (const i in obj) {
            if (obj[i].checked == true && obj[i].day) {
                params[i] = obj[i].day;
            }
        }
        return params
    }
    const { data: calcWorkDayData, run: calcWorkDay } = useRequest(calcWorkDays, {
        manual: true,
        onSuccess: (res: any) => {
            if (res.status) {
                dispatch({
                    type: 'projectTasks/renewTaskInfo',
                    payload: {
                        executionCycle: res.executionCycle,
                        startTime: moment(res.startTime),
                        endTime: moment(res.endTime),
                    }
                })
                setFlag(false);
            } else {
                setFlag(true);
                confirm({
                    title: '系统提示',
                    content: res.message,
                    onOk() {
                        calcWorkDay({
                            ...getCalcWorkDay(workTimeAdjust),
                            adjustmentType: res.adjustmentType,
                            executionCycle: res.executionCycle,
                            startTime: res.startTime,
                            endTime: res.endTime
                        })
                    },
                    onCancel() {
                        setFlag(false);
                    }
                })
            }
        },
        debounceInterval: 300
    });
    const submit = (data: any, type?: boolean, callback?: () => void) => {
        const beforeDocument = (taskInfo.beforeDocument && taskInfo.beforeDocument.length) ? taskInfo.beforeDocument?.map((item: any) => item.id) : [];
        const document = [...data.document?.map((item: any) => item.id), ...beforeDocument].join();
        let taskExplain = data.taskExplain.length >= 1 ? JSON.stringify(data.taskExplain) : "";
        const associationTaskId = taskInfo?.associationTaskId ? isString(taskInfo?.associationTaskId) ? taskInfo?.associationTaskId : taskInfo?.associationTaskId.join() : null;
        const params = {
            name: taskInfo.name,
            endTime: taskInfo.endTime ? moment(taskInfo.endTime).format('YYYY-MM-DD HH:mm') : null,
            startTime: taskInfo.startTime ? moment(taskInfo.startTime).format('YYYY-MM-DD HH:mm') : null,
            leaderId: taskInfo.leaderId,
            parentId: taskInfo.parentId,
            taskId: taskInfo.taskId == "newAdd" ? null : taskInfo.taskId,
            ...data,
            taskExplain: taskExplain,
            document: document ? document : null,
            projectId,
            executionCycle: taskInfo.executionCycle,
            askLeaveDays: workTimeAdjust.askLeaveDays.day,//请假
            compensatoryLeaveDays: workTimeAdjust.compensatoryLeaveDays.day,//补休
            depositRestDays: workTimeAdjust.depositRestDays.day,//存休
            associationTaskId,
            processStage: taskInfo?.processStageCode,
            executionPositionId: taskInfo?.executionPositionId,
            materialSupply: data.materialSupply.join(),
            visibilityRange: data.visibilityRange.join(),
        }

        editProjectTasksApi(params).then(async (res: any) => {
            const infoParams = type ? willSetTaskInfo?.taskId ?? res : res;
            await getTaskInfo(infoParams).then((t: any) => {
                dispatch({
                    type: 'projectTasks/fetchTaskList',
                    payload: projectId,
                    changeType: 'save',
                    successRes: t,
                    taskId: t.taskId,
                    taskInfoFlag
                })
                dispatch({
                    type: 'projectTasks/setCTaskId',
                    payload: t.taskId,
                })
            })
            if (!type) { message.success('保存成功!') }
            if (callback) callback();
        })
    }
    useUpdateEffect(() => {
        form.validateFields().then((values) => {
            submit(values, true)
        })
    }, [submitStatus])
    useUpdateEffect(() => {
        form.validateFields().then((values) => {
            submit(values)
        })
    }, [saveStatus])
    // useEffect(()=>{

    // },[taskInfo.taskId])
    //取消编辑
    const cancelEditProjectInformation = () => {
        dispatch({
            type: 'projectTasks/setEditable',
            payload: false
        })
    }
    //清除任务信息
    const clearTaskInfo = () => {
        dispatch({
            type: 'projectTasks/renewTaskInfo',
            payload: {
                executionCycle: null,
                startTime: null,
                endTime: null
            }
        })
        setWorkTimeAdjust(defaultTimeAdjust);
    }
    //获取通知栏title内容
    const getTaskInformationTitle = useMemo(() => {
        let title: string = '';
        if (taskInfo.taskId && !isAddTask) {
            if (taskInfo.parentTitle) {
                title = `你正在编辑：任务【${taskInfo.parentTitle}】的子级任务`
            } else {
                title = `你正在编辑一级任务【${taskInfo.name}】`
            }
        } else {
            if (taskInfo.parentTitle && taskInfo.parentId) {
                title = `你正在新建：任务【${taskInfo.parentTitle}】的子级任务`
            } else {
                title = `你正在新增一级任务`
            }
        }

        return <span className='f-12 color-5c'>{title}</span>;
    }, [taskInfo])
    useEffect(() => {
        if (taskInfo) {
            let materialSupply: any[] = [],visibilityRange: any[] = [];
            if(taskInfo.materialSupplies?.length){
                materialSupply = taskInfo.materialSupplies.map((item: any) => item.name);
            }
            if(taskInfo.visibilityRanges?.length){
                visibilityRange = taskInfo.visibilityRanges.map((item: any) => item.name);
            }
            const formValue = {
                associationTaskEndTime: taskInfo.associationTaskEndTime,
                taskExplain: taskInfo?.taskExplain || [],
                document: [],
                materialSupply,
                visibilityRange
            }
            form.setFieldsValue(formValue);
            const newWorkTimeAdjust = {
                askLeaveDays: {//请假
                    checked: taskInfo?.depositRestDays ? true : false,
                    day: taskInfo?.depositRestDays ?? ''
                },
                compensatoryLeaveDays: {//补休
                    checked: taskInfo?.compensatoryLeaveDays ? true : false,
                    day: taskInfo?.compensatoryLeaveDays ?? ''
                },
                depositRestDays: {//存休
                    checked: taskInfo?.askLeaveDays ? true : false,
                    day: taskInfo?.askLeaveDays ?? ''
                }
            }
            setWorkTimeAdjust(newWorkTimeAdjust);
        }
    }, [taskInfo.taskId])
    //执行周期变化
    const executionCycleChange = (e: any) => {
        if (flag) return
        if (e && (taskInfo?.startTime || taskInfo?.endTime)) {
            calcWorkDay({
                ...getCalcWorkDay(workTimeAdjust),
                adjustmentType: 'EXECUTION_CYCLE',
                executionCycle: e,
                startTime: taskInfo?.startTime ? moment(taskInfo?.startTime).format('YYYY-MM-DD HH:mm') : null,
                endTime: taskInfo?.endTime ? moment(taskInfo?.endTime).format('YYYY-MM-DD HH:mm') : null
            })
        } else {
            dispatch({
                type: 'projectTasks/renewTaskInfo',
                payload: {
                    executionCycle: e,
                }
            })
        }
    }
    //开始时间变化
    const startTimeChange = (e: any) => {
        let time = e;
        if (time && (taskInfo?.executionCycle || taskInfo?.endTime)) {
            calcWorkDay({
                ...getCalcWorkDay(workTimeAdjust),
                adjustmentType: 'START_TIME',
                startTime: moment(time).format('YYYY-MM-DD HH:mm'),
                executionCycle: taskInfo?.executionCycle,
                endTime: taskInfo?.endTime ? moment(taskInfo?.endTime)?.format('YYYY-MM-DD HH:mm') : null
            })
        } else {
            dispatch({
                type: 'projectTasks/renewTaskInfo',
                payload: {
                    startTime: moment(time),
                }
            })
        }
    }
    //结束时间变化
    const endTimeChange = (e: any) => {
        let time = e;
        if (time && (taskInfo?.executionCycle || taskInfo?.startTime)) {
            calcWorkDay({
                ...getCalcWorkDay(workTimeAdjust),
                adjustmentType: 'END_TIME',
                startTime: taskInfo?.startTime ? moment(taskInfo?.startTime)?.format('YYYY-MM-DD HH:mm') : null,
                executionCycle: taskInfo?.executionCycle,
                endTime: moment(time).format('YYYY-MM-DD HH:mm')
            })
        } else {
            dispatch({
                type: 'projectTasks/renewTaskInfo',
                payload: {
                    endTime: moment(time)
                }
            })
        }
    }
    //修改结束小时分钟数
    const endHourChange = (e: any) => {
        if (taskInfo?.executionCycle || taskInfo?.startTime) {
            const endTime = moment(taskInfo?.endTime).set({ 'hour': Number(e.split(':')[0]), 'minute': Number(e.split(':')[1]) }).format('YYYY-MM-DD HH:mm');
            if (taskInfo?.startTime && (moment(endTime).isBefore(moment(taskInfo?.startTime)) || moment(endTime).isSame(moment(taskInfo?.startTime)))) {
                message.error('结束时间不能早于或等于开始时间!')
                return
            }
            calcWorkDay({
                ...getCalcWorkDay(workTimeAdjust),
                adjustmentType: 'END_TIME',
                startTime: moment(taskInfo?.startTime).format('YYYY-MM-DD HH:mm'),
                executionCycle: taskInfo?.executionCycle,
                endTime,
            })
        } else {
            dispatch({
                type: 'projectTasks/renewTaskInfo',
                payload: {
                    endTime: moment(taskInfo?.endTime).set({ 'hour': Number(e.split(':')[0]), 'minute': Number(e.split(':')[1]) })
                }
            })
        }
    }
    //修改开始小时分钟数
    const startHourChange = (e: any) => {
        if (taskInfo?.executionCycle || taskInfo?.endTime) {
            calcWorkDay({
                ...getCalcWorkDay(workTimeAdjust),
                adjustmentType: 'START_TIME',
                startTime: moment(taskInfo?.startTime).set({ 'hour': Number(e.split(':')[0]), 'minute': Number(e.split(':')[1]) }).format('YYYY-MM-DD HH:mm'),
                executionCycle: taskInfo?.executionCycle,
                endTime: moment(taskInfo?.endTime).format('YYYY-MM-DD HH:mm'),
            })
        } else {
            dispatch({
                type: 'projectTasks/renewTaskInfo',
                payload: {
                    startTime: moment(taskInfo?.startTime).set({
                        'hour': Number(e.split(':')[0]),
                        "minute": Number(e.split(':')[1])
                    })
                }
            })
        }
    }
    //工作调整选择
    const workTimeAdjustChange = (e: any) => {
        const workData = cloneDeep(workTimeAdjust);
        if (e.target.checked) {
            workData[e.target.value].checked = e.target.checked;
        } else {
            workData[e.target.value] = {
                checked: false,
                day: ''
            }
        }
        setWorkTimeAdjust(workData);
    }
    //是否存在工作调整
    const isWorkTimeAdjust = (obj: defaultTimeAdjustType) => {
        let f: boolean = false;
        for (const i in obj) {
            if (obj[i].checked == true && obj[i].day) {
                f = true;
                break;
            }
        }
        return f;
    }
    //保存表单数据
    const saveForm = () => {
        form.validateFields().then((data: any) => {
            submit(data);
        })
    }
    return (
        <div className='basic-information-container'>
            {/* @ts-ignore */}
            <div ref={scrollRef} style={{ height: '100%', overflow: 'auto', transition: 'all .3s' }}>
                {
                    editable && <BdwRow type='flex-center' className='mb-10 px-16'>
                        <NotificationOutlined style={{ color: '#5c5c5c', marginRight: '16px' }} />
                        {getTaskInformationTitle}
                    </BdwRow>
                }

                <Form form={form} initialValues={{ document: [], associationTaskEndTime: false, taskExplain: [], materialSupply: [], visibilityRange: [] }}>
                    {/* 任务名称-任务说明 */}
                    <div className='padding-custom pt-0'>
                        <BdwRow type='flex' className='mb-10'>
                            <div className='form-label'>任务名称</div>
                            <EditableContent
                                editable={editable}
                                renderShow={<div className='form-content'>{taskInfo?.name}</div>}
                                renderEditor={
                                    <Input
                                        bordered={false}
                                        className='input-style'
                                        style={{ flex: 1 }}
                                        placeholder='标题限制输入64个字符(必填项)'
                                        value={taskInfo?.name}
                                        onChange={(e) => {
                                            dispatch({
                                                type: 'projectTasks/renewTaskInfo',
                                                payload: {
                                                    name: e.target.value
                                                }
                                            })
                                        }}
                                    />


                                }
                            />

                        </BdwRow>
                        {
                            projectOverviewDetails.type === 'CUSTOMER' && <BdwRow type="flex" className='mb-10'>
                            <div className='form-label'>进程阶段</div>
                            <EditableContent
                                editable={editable}
                                renderShow={taskInfo?.processStageName ? <div className='form-content'>{taskInfo?.processStageName}</div> : <div className='back-show-info'>未选择进程阶段</div>}
                                renderEditor={
                                    <div className='flex-1 process-choose'>
                                        <Select
                                            options={listDirectoryProcess}
                                            value={taskInfo?.processStageCode}
                                            placeholder="选择进程阶段"
                                            showSearch
                                            // @ts-ignore
                                            filterOption={(i, o) => (o?.label ?? '').includes(i)}
                                            onChange={(e) => {
                                                dispatch({
                                                    type: 'projectTasks/renewTaskInfo',
                                                    payload: {
                                                        processStageCode: e,
                                                    }
                                                })
                                            }}
                                        >

                                        </Select>
                                    </div>
                                }
                            />
                        </BdwRow>
                        }
                        
                        <BdwRow type='flex' className='mb-10'>
                            <div className='form-label'>任务说明</div>
                            <div className='project-information-add'>
                                {
                                    editable ? <Form.List name='taskExplain'>
                                        {(fields, { add, remove }) => {
                                            return (
                                                <div>
                                                    {fields.map((field, index) => (
                                                        <div key={field.key} className="position-parent-relative">
                                                            <BdwFormItem {...field} validateTrigger="onChange" initialValue={{ value: '', fileList: [] }} rules={taskRemarkRules.item}>
                                                                <BdwRichText
                                                                    hideTool
                                                                    sx={{ width: '100%', minHeight: '120px' }}
                                                                    placeholder='填写任务说明(可直接粘贴图片)'

                                                                >

                                                                </BdwRichText>
                                                            </BdwFormItem>
                                                            {fields?.length >= 1 ? (
                                                                <Popconfirm
                                                                    title="确认删除此输入域？"
                                                                    onConfirm={() => remove(field.name)}
                                                                    okText="确认"
                                                                    cancelText="取消"
                                                                >
                                                                    <CloseOutlined key={index} className="delete-has-upload-textarea-btn" title="删除" />
                                                                </Popconfirm>
                                                            ) : null}
                                                        </div>
                                                    ))}
                                                    <div style={{ height: '28px', display: 'flex', alignItems: 'center', color: '#0275d8', cursor: 'pointer', fontSize: '12px' }}  ><div onClick={add}><PlusOutlined className='f-12' style={{ marginRight: '8px' }} />添加</div></div>


                                                </div>
                                            )
                                        }}
                                    </Form.List> : <div>{isArray(remark) && remark?.length !== 0 ? showHasSaveTaskRemark : <span className='back-show-info'>暂无任务说明</span>}</div>
                                }
                            </div>

                        </BdwRow>
                    </div>
                    <div className="div-line"></div>
                    {/* 执行岗位-开始截止时间 */}
                    <div className="padding-custom zx-ask-for-leave">
                        <BdwRow type='flex-between' className='mb-10 zx-fz-wrapper'>
                            <BdwRow type="flex" className='w-48'>
                                <div className='form-label'>执行岗位</div>
                                <EditableContent
                                    editable={editable}
                                    renderShow={taskInfo?.executionPositionName ? <div className='form-content'>{taskInfo?.executionPositionName}</div> : <div className='back-show-info'>未选择执行岗位</div>}
                                    renderEditor={
                                        <div className='flex-1 choose-position-list'>
                                            <Select
                                                options={listPositionSelectOptions}
                                                value={taskInfo?.executionPositionId}
                                                placeholder="关键字检索岗位"
                                                showSearch
                                                // @ts-ignore
                                                filterOption={(i, o) => (o?.label ?? '').includes(i)}
                                                onChange={(e) => {
                                                    dispatch({
                                                        type: 'projectTasks/renewTaskInfo',
                                                        payload: {
                                                            executionPositionId: e,
                                                        }
                                                    })
                                                }}
                                            >

                                            </Select>
                                        </div>
                                    }
                                />
                            </BdwRow>
                            <BdwRow type="flex" className='w-48'>
                                <div className='form-label'>负责人员</div>
                                <EditableContent
                                    editable={editable}
                                    renderShow={taskInfo?.leaderName ? <div className='form-content'>{taskInfo?.leaderName}</div> : <div className='back-show-info'>未选择任务负责人</div>}
                                    renderEditor={
                                        <div className='flex-1 choose-responsible-person'>
                                            <BdwChooseCompanyStaff
                                                placeholder="姓名关键字检索"
                                                apiSrc={listTaskLeaderInfo}
                                                extraParams={{ projectId }}
                                                value={{ id: taskInfo.leaderId, name: taskInfo.leaderName }}
                                                onChange={(e: any) => {
                                                    dispatch({
                                                        type: 'projectTasks/renewTaskInfo',
                                                        payload: {
                                                            leaderId: e.id,
                                                            leaderName: e.name
                                                        }
                                                    })
                                                }}
                                            />
                                        </div>
                                    }
                                />
                            </BdwRow>

                        </BdwRow>
                        <BdwRow type='flex' className='mb-10'>
                            <div className='form-label'>执行周期</div>
                            <PeriodEditableContent
                                editable={editable && !taskInfo?.received}
                                onChange={executionCycleChange}
                                value={taskInfo?.executionCycle}
                            />

                        </BdwRow>
                        <BdwRow type='flex-between' className='mb-10'>
                            <BdwRow type='flex-center' className='mb-10  w-48'>
                                <div className='form-label'>开始时间</div>
                                <EditableContent
                                    editable={editable && !taskInfo?.received}
                                    renderShow={taskInfo?.startTime ? <div className='form-content'>{taskInfo?.startTime}</div> : <div className='back-show-info'>未设置</div>}
                                    renderEditor={
                                        <BdwRow type='flex-center-between' className='w-170'>
                                            {/* @ts-ignore */}
                                            <DatePicker
                                                showTime={{ defaultValue: moment('09:00:00', 'HH:mm:ss') }}
                                                onChange={startTimeChange}
                                                value={taskInfo?.startTime ? moment(taskInfo?.startTime) : null}
                                                bordered={false}
                                                allowClear={false}
                                                disabledDate={(cd: any) => {
                                                    const projectEndTime = moment(moment(projectOverviewDetails.endDate).format('YYYY-MM-DD 23:59:59')).endOf('second');//项目截止时间
                                                    if (taskInfo?.endTime) {//存在截止时间
                                                        // if (workSystemCode == 'FIVE_DAY_WORKWEEK') {//标准工作日
                                                        //     if (isWorkTimeAdjust(workTimeAdjust)) {
                                                        //         return cd && cd > moment(taskInfo?.endTime).endOf('day');
                                                        //     } else {
                                                        //         return (cd && cd > moment(taskInfo?.endTime).endOf('day')) || moment(cd).isoWeekday() === 6 || moment(cd).isoWeekday() === 7;;
                                                        //     }
                                                        // } else {//非标准工作日
                                                        //     return cd || cd > moment(taskInfo?.endTime).endOf('day');
                                                        // }
                                                        return cd || cd > moment(taskInfo?.endTime).endOf('second') || cd > projectEndTime;

                                                    } else {//不存在截止时间
                                                        // if (workSystemCode == 'FIVE_DAY_WORKWEEK') {//标准工作
                                                        //     if (isWorkTimeAdjust(workTimeAdjust)) {
                                                        //         return false
                                                        //     } else {
                                                        //         return moment(cd).isoWeekday() === 6 || moment(cd).isoWeekday() === 7;
                                                        //     }
                                                        // } else {//非标准工作日
                                                        //     return false;
                                                        // }
                                                        return cd > projectEndTime;
                                                    }

                                                }}
                                            />
                                        </BdwRow>
                                    }
                                />
                            </BdwRow>
                            <BdwRow type='flex-center' className='mb-10 w-48'>
                                <div className='form-label'>截止时间</div>
                                <EditableContent
                                    editable={editable && !taskInfo?.received}
                                    renderShow={taskInfo?.endTime ? <div className='form-content'>{taskInfo?.endTime}</div> : <div className='back-show-info'>未设置</div>}
                                    renderEditor={
                                        <BdwRow type='flex-center-between' className='w-170'>
                                            {/* @ts-ignore */}
                                            <DatePicker
                                                showTime={{ defaultValue: moment('18:00:00', 'HH:mm:ss') }}
                                                allowClear={false}
                                                onChange={endTimeChange}
                                                bordered={false}
                                                value={taskInfo?.endTime ? moment(taskInfo?.endTime) : null}
                                                disabled={!taskInfo?.startTime}
                                                disabledDate={(cd: any) => {
                                                    // if (workSystemCode == 'FIVE_DAY_WORKWEEK') {//标准工作日
                                                    //     if (isWorkTimeAdjust(workTimeAdjust)) {
                                                    //         return cd && cd < moment(taskInfo?.startTime).endOf('day');
                                                    //     } else {
                                                    //         return (cd && cd < moment(taskInfo?.startTime).subtract(1, 'day').endOf('day')) || moment(cd).isoWeekday() === 6 || moment(cd).isoWeekday() === 7;;
                                                    //     }
                                                    // } else {//非标准工作日
                                                    //     return cd && cd <= moment(taskInfo?.startTime).endOf('day');
                                                    // }
                                                    return cd && cd < moment(taskInfo?.startTime).endOf('second');
                                                }}
                                            />
                                        </BdwRow>
                                    }
                                />


                            </BdwRow>

                        </BdwRow>
                        <BdwRow type='flex-center' className={`${checkedInfo.isVacation ? 'mb-10' : ''} show-work-time-adjust`}>
                            <div className='form-label'></div>
                            <Checkbox
                                checked={checkedInfo.isVacation}
                                onChange={(e) => {
                                    setCheckedInfo({
                                        ...checkedInfo,
                                        isVacation: e.target.checked
                                    })
                                }}
                               
                                className='color-5c'
                                style={{ fontSize: '13px' }}
                            >
                                执行期间是否调休或请假
                            </Checkbox>
                        </BdwRow>

                        <div className={`${checkedInfo.isVacation ? 'show' : 'hidden'}`}>
                            <BdwRow type='flex-center' className='work-time-adjust'>
                                <div className='form-label work-time-adjust-label'></div>
                                <EditableContent
                                    editable={editable && !taskInfo?.received}
                                    renderShow={<div className='back-show-info flex' >
                                        {
                                            taskInfo.askLeaveDays && <div className='form-content mr-5' >请假{taskInfo.askLeaveDays}天</div>
                                        }
                                        {
                                            taskInfo.compensatoryLeaveDays && <div className='form-content mr-5'>补休{taskInfo.compensatoryLeaveDays}天</div>
                                        }
                                        {
                                            taskInfo.depositRestDays && <div className='form-content mr-5' >存休{taskInfo.depositRestDays}天</div>
                                        }
                                        {
                                            (!taskInfo.depositRestDays && !taskInfo.compensatoryLeaveDays && !taskInfo.askLeaveDays) &&
                                            <div className='back-show-info'>暂无工作调整</div>
                                        }
                                    </div>}
                                    renderEditor={
                                        <div className='work-time-adjust-edit'>
                                            <BdwRow type='flex'>
                                                <div>
                                                    {
                                                        workTimeAdjustOption.map((item) => (
                                                            <BdwRow type='flex-center' className='work-time-adjust-edit-item' key={item.value}>
                                                                <Checkbox checked={workTimeAdjust[item.value].checked} value={item.value} onChange={workTimeAdjustChange}>
                                                                    {item.label}
                                                                </Checkbox>
                                                                {
                                                                    workTimeAdjust[item.value].checked && <Input
                                                                        placeholder={`请填写${item.label}天数`}
                                                                        bordered={false}
                                                                        type={'number'}
                                                                        value={workTimeAdjust[item.value].day}
                                                                        style={{
                                                                            fontSize: '12px'
                                                                        }}
                                                                        min={0} // 最小0天
                                                                        onChange={(e) => {
                                                                            const workData = cloneDeep(workTimeAdjust);
                                                                            workData[item.value].day = e.target.value;
                                                                            if (taskInfo?.startTime && taskInfo?.executionCycle && taskInfo?.endTime) {
                                                                                calcWorkDay({
                                                                                    ...getCalcWorkDay(workData),
                                                                                    adjustmentType: 'EXECUTION_CYCLE',
                                                                                    startTime: moment(taskInfo?.startTime).format('YYYY-MM-DD HH:mm'),
                                                                                    executionCycle: taskInfo?.executionCycle,
                                                                                    endTime: moment(taskInfo?.endTime).format('YYYY-MM-DD HH:mm'),
                                                                                })
                                                                            }
                                                                            setWorkTimeAdjust(workData);
                                                                        }}
                                                                    />
                                                                }

                                                            </BdwRow>

                                                        ))
                                                    }
                                                </div>
                                            </BdwRow>
                                        </div>

                                    }
                                />


                            </BdwRow>
                        </div>


                    </div>
                    <div className="div-line"></div>
                    {/* 相关资料 */}
                    <div className="padding-custom">
                        <EditableContent
                            editable={editable}
                            renderShow={<BdwRow type='flex-center'>
                                <div className='form-label'>相关资料</div>

                            </BdwRow>}
                            renderEditor={
                                <BdwFormItem labelname="相关资料" name='document' wrapclassname='upload-btn-w'
                                >
                                    <BdwUploadBtn name="本地上传" mt='0px' color='#0275d8' fontSize='12px' />
                                </BdwFormItem>
                            }
                        />

                        {
                            taskInfo?.beforeDocument?.length >= 1 &&
                            <div style={{ paddingLeft: '68px' }}>
                                <BdwFileShow width={'350px'} attachments={taskInfo?.beforeDocument} onChange={(data: any) => {
                                    dispatch({
                                        type: 'projectTasks/renewTaskInfo',
                                        payload: {
                                            beforeDocument: data,
                                        }
                                    })
                                }} enableEdit={editable} />
                            </div>

                        }
                        <Form.Item noStyle
                            shouldUpdate={(p, c) => p.document != c.document}
                        >
                            {({ getFieldValue }) => {
                                return (getFieldValue('document')?.length >= 1 || taskInfo?.beforeDocument?.length >= 1) ? null :
                                    <div className='no-files-empty'>
                                        <img src={EMPTY_IMG} alt="" />
                                        还没有上传资料
                                    </div>
                            }}
                        </Form.Item>
                    </div>
                    <div className="div-line"></div>
                    {/* 物料供应 */}
                    <div className='padding-custom'>
                        <div className={`${checkedInfo.isMaterialSupply ? 'mb-10' : ''}`}>
                            <Checkbox  onChange={(e) => {
                                setCheckedInfo({
                                    ...checkedInfo,
                                    isMaterialSupply: e.target.checked
                                })
                                if (e.target.checked) {
                                    // @ts-ignore
                                    scrollRef.current.scrollTop = scrollRef.current.scrollTop + 100
                                } else {
                                    // @ts-ignore
                                    scrollRef.current.scrollTop = scrollRef.current.scrollTop - 100
                                }
                            }} checked={checkedInfo.isMaterialSupply} className='color-5c' style={{ fontSize: '13px' }}>
                                涉及物料供应
                            </Checkbox>
                        </div>

                        <BdwFormItem hidden={!checkedInfo.isMaterialSupply} name='materialSupply' className='pl-24'>
                            <Checkbox.Group>
                                <Space direction='vertical'>
                                    {
                                        listMaterialSupply.map((item: any) =>
                                            <Checkbox  disabled={!editable} value={item.value} key={item.value}>{item.label}</Checkbox>
                                        )
                                    }
                                </Space>
                            </Checkbox.Group>
                        </BdwFormItem>


                    </div>
                    <div className="div-line"></div>
                    {/* 可见范围 */}
                    <div className='padding-custom'>
                        <div className={`${checkedInfo.isVisibility ? 'mb-10' : ''}`}>
                            <Checkbox onChange={(e) => {
                                setCheckedInfo({
                                    ...checkedInfo,
                                    isVisibility: e.target.checked
                                })
                                if (e.target.checked) {
                                    // @ts-ignore
                                    scrollRef.current.scrollTop = scrollRef.current.scrollTop + 100
                                } else {
                                    // @ts-ignore
                                    scrollRef.current.scrollTop = scrollRef.current.scrollTop - 100
                                }
                            }} checked={checkedInfo.isVisibility} className='color-5c' style={{ fontSize: '13px' }}>
                                设置可见范围
                            </Checkbox>
                        </div>


                        <BdwFormItem hidden={!checkedInfo.isVisibility} name='visibilityRange' className='pl-24'>
                            <Checkbox.Group>
                                <Space direction='vertical'>
                                    {
                                        listUserTypeVisibility.map((item: any) =>
                                            <Checkbox disabled={!editable} value={item.value} key={item.value}>{item.label}</Checkbox>
                                        )
                                    }
                                </Space>
                            </Checkbox.Group>
                        </BdwFormItem>

                    </div>
                    <div className="div-line"></div>
                    {/* 关联任务 */}
                    <div className='padding-custom' style={{ minHeight: '150px' }}>
                        <div className={`${checkedInfo.isAssociated ? 'mb-10' : ''}`}>
                            <Checkbox  onChange={(e) => {
                                setCheckedInfo({
                                    ...checkedInfo,
                                    isAssociated: e.target.checked
                                })
                                if (e.target.checked) {
                                    // @ts-ignore
                                    scrollRef.current.scrollTop = scrollRef.current.scrollTop + 100
                                } else {
                                    // @ts-ignore
                                    scrollRef.current.scrollTop = scrollRef.current.scrollTop - 100
                                }
                            }} checked={checkedInfo.isAssociated} className='color-5c' style={{ fontSize: '13px' }}>
                                设置关联任务
                            </Checkbox>
                        </div>
                        {
                            <div className={`pl-24 ${checkedInfo.isAssociated ? 'show' : 'hidden'}`} >
                                {
                                    taskInfo.associationTaskId && taskInfo.associationTaskName ?
                                        <div className='form-content association-task-name mb-10'>
                                            {taskInfo?.associationTaskName}
                                            {
                                                editable && <CloseOutlined
                                                    style={{ fontSize: '14px', color: '#8c8c8c' }}
                                                    onClick={() => {
                                                        dispatch({
                                                            type: 'projectTasks/renewTaskInfo',
                                                            payload: {
                                                                associationTaskId: '',
                                                                associationTaskName: ''
                                                            }
                                                        })
                                                    }}
                                                ></CloseOutlined>
                                            }
                                        </div>
                                        : editable ? <div className='association-task-name-empty'
                                            onClick={
                                                () => {
                                                    dispatch({
                                                        type: 'projectTasks/setAssociatedTasksTableData'
                                                    })
                                                    associatedTaskModalShow()

                                                }
                                            }
                                            style={{ height: '28px', display: 'flex', alignItems: 'center', color: "#bfbfbf", cursor: 'pointer' }}  >
                                            选择关联任务
                                            <img src={COPY_PRODUCT_PRICE_INFO} width="16px" height="16px" alt="" />
                                        </div> : <div className='back-show-info'>暂无关联任务</div>
                                }
                                <BdwRow type="flex-center-between">
                                    {
                                        taskInfo?.associationTaskName && <BdwFormItem name='associationTaskEndTime' valuePropName='checked'>
                                            <Checkbox disabled={!editable} className='color-5c' style={{ fontSize: '13px' }}>
                                                连接关联任务截至时间
                                            </Checkbox>
                                        </BdwFormItem>
                                    }
                                </BdwRow>
                            </div>
                        }
                    </div>

                </Form>
            </div>

            {
                editable && <BdwRow className='basic-information-btn'>

                    <BdwButton mr='10px' type='primary' onClick={saveForm} >
                        保存
                    </BdwButton>
                    <BdwButton mr='10px' onClick={cancelEditProjectInformation}>
                        取消
                    </BdwButton>
                    <BdwButton width='104px' onClick={clearTaskInfo}>
                        清除任务信息
                    </BdwButton>
                </BdwRow>
            }
            {
                associatedTaskModal &&
                <AssociatedTaskModal
                    visible={associatedTaskModal}
                    cancelFun={() => associatedTaskModalHide()}
                    successEvent={(e, n) => {
                        dispatch({
                            type: 'projectTasks/renewTaskInfo',
                            payload: {
                                associationTaskId: e,
                                associationTaskName: n
                            }
                        })
                        associatedTaskModalHide()
                    }}
                    task={taskInfo}
                />
            }
        </div>
    )
}
export default TaskInformation