import React, { Key, ReactNode, useEffect, useMemo, useState } from "react";
import { Form, Input, Empty, Button, message } from "antd";
import { BdwFormItems, BdwReadonlySpan, BdwRow, BdwTitle, BdwTreeSelect } from "@/components";
import session from "@/session";
import { useParams, useSelector,useDispatch } from "umi";

import "./index.less"
import { setGroupTaskScore } from '@/service/projectDos/my-project-detail/valueReview';
import { safe } from "@/utils/IgnorError";
import { CheckOutlined } from "@ant-design/icons/lib";
import { cloneDeep } from "lodash";
import {flatten,getLocalStorage} from '@/utils/utils';

interface TreeNode {
  taskId?: Key
  titleText: string
  title: ReactNode
  value: Key
  isLeaf?: boolean
  children?: TreeNode[]
  disabled: boolean
  taskScore?: Key
  totalProportion?: Key
  scoreStr?: string
}

interface OtherLevelValueAssessmentSettingProps {
  // 直接跳转到第三步的方法
  skipSetting?: () => void
  needSkipButton?: boolean
  setSuccess?: () => void
}


const OtherLevelValueAssessmentSetting: React.FC<OtherLevelValueAssessmentSettingProps> = (props) => {
  const [form] = Form.useForm();
  form.setFieldsValue({})
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.commonTask);//当前项目登录人信息
  const { basicProjectInfo } = useSelector((state: any) => state.projectTasks);
  const { leaderInfo } = basicProjectInfo;
  const { projectScoreList,currentVersionInfo } = useSelector((state: any) => state.valueReview);
  const { skipSetting, needSkipButton = true, setSuccess } = props;

  const { projectId } = useParams<{ projectId: string }>();

  const [currentTaskStandardScore, setCurrentTaskStandardScore] = useState<Key>("");
  const [currentTaskChildrenList, setCurrentTaskChildrenList] = useState<TreeNode[]>([]);

  const [currentTaskTitleValue, setCurrentTaskTitleValue] = useState<string>("");
  const [currentTaskId, setCurrentTaskId] = useState<number>();

  const treeDataList = useMemo(() => {
    // setCurrentTaskStandardScore(taskStore.tasks.filter(item => item.id === currentTaskId)[0]?.taskScore!.toString())
    return projectScoreList
  }, [projectScoreList])
  const flattenTreeDataList: any[] =  useMemo(() => {
    return flatten(projectScoreList);
  }, [projectScoreList])

  const getPersonCanSetScore = (taskScore: Key, leaderId: Key, children: any[] = []) => {
    // 判断是否可以选择进行编辑分数
    // 如果是这个项目的负责人，可以对所有任务编辑分数
    // 如果是这个任务的负责人，那他可以对子项任务打分
    // 前提这个任务必须有分.
    if (taskScore && children.length > 0 && Number(leaderInfo.leaderId) === Number(userInfo.userId)) {
      return true
    }
    if (taskScore && children.length > 0 && Number(leaderId) === Number(userInfo.userId)) {
      return true
    }
    return false
  }


  const mapToTreeNode = (curr: any): TreeNode => {
    const canSetScore = getPersonCanSetScore(curr.taskScore ?? 0, curr.leaderId ?? 0, curr.children ?? []);
    let scoreVal: string
    if (curr.taskScore) {
      scoreVal = `标准分：${curr.taskScore}`
    } else {
      scoreVal = '暂未设置标准分'
    }

    let settingFlagStr: ReactNode = ''
    if (curr.children && curr.children.length > 0) {
      const flagBooleanVal = curr.children.every((item: any) => {
        return item.taskScore
      })
      if (flagBooleanVal) {
        settingFlagStr = <CheckOutlined style={{ color: "#5cb85c", fontSize: "16px" }} />
      }
    }

    return {
      taskId: curr.taskId,
      scoreStr: `${scoreVal}`,
      titleText: curr.title,
      title: <span>{curr.title}<span className="ml-20">{settingFlagStr}</span></span>,
      value: curr.taskId!,
      isLeaf: !(curr.children && curr.children.length > 0),
      children: curr.children?.map(mapToTreeNode),
      disabled: !canSetScore,
      taskScore: curr.taskScore,
      totalProportion: curr.totalProportion
    }
  }
  // 公共组件库存在问题，高阶组件封装未继承可以传入的泛型，会导致报错，所以这里暂时都用any
  const chooseTask = (value: any, node: any) => {
    // 记录当前选中的任务的ID
    setCurrentTaskId(value)
    // 记录当前选中的任务的标题，便于回填
    setCurrentTaskTitleValue(node.taskId);
    const { children } = node;
    const taskScore = Number(flattenTreeDataList.filter(item => item.taskId === value)[0]?.taskScore)
    setCurrentTaskStandardScore(taskScore);
    // 选择任务获取到的children数据，自己先做一次处理
    const afterHandleChildrenData = children.map((item: any) => {
      return {
        ...item,
        totalProportion: isNaN(Number(item.taskScore) / Number(taskScore)) ?
          0 :
          parseFloat(((Number(item.taskScore) / Number(taskScore)) * 100).toFixed(2)),
      }
    })
    setCurrentTaskChildrenList(afterHandleChildrenData);
  }
  useEffect(()=>{
    if(currentTaskChildrenList && currentTaskChildrenList.length && currentTaskId){
      const filterData = flatten(treeDataList.map(mapToTreeNode)).filter((i: any)=> i.taskId == currentTaskId);
      chooseTask(currentTaskId,filterData[0])
    }
  },[treeDataList])
  // 任务分值改变的函数
  const taskStandardScoreChange = (value: string, index: number) => {
    let scoreVal: string
    if (parseFloat(value)) {
      scoreVal = `标准分：${parseFloat(value)}`
    } else {
      scoreVal = '暂未设置标准分'
    }
    const copyDataList = [...currentTaskChildrenList];
    const changeObject = {
      ...copyDataList[index],
      taskScore: value?parseFloat(value):0,
      scoreStr: scoreVal,
      totalProportion: isNaN(Number(value) / Number(currentTaskStandardScore)) ?
        0 :
        parseFloat(((Number(value) / Number(currentTaskStandardScore)) * 100).toFixed(2))
    };

    copyDataList.splice(index, 1, changeObject);

    const handleDataArray = copyDataList.map((item) => {
      return {
        ...item,
        totalProportion: isNaN(Number(item.taskScore) / Number(currentTaskStandardScore)) ?
          0 :
          parseFloat(((Number(item.taskScore) / Number(currentTaskStandardScore)) * 100).toFixed(2))
      }
    })
    setCurrentTaskChildrenList(handleDataArray)
  }


  // 调整占比的函数
  const taskRatioChange = (value: string, index: number) => {
    const standardScoreVal = isNaN((Number(value) * 0.01) * Number(currentTaskStandardScore)) ? 0 :
      parseFloat(((Number(value) * 0.01) * Number(currentTaskStandardScore)).toFixed(2))
    let scoreVal: string
    if (parseFloat(value)) {
      scoreVal = `标准分：${standardScoreVal}`
    } else {
      scoreVal = '暂未设置标准分'
    }
    const copyDataList = [...currentTaskChildrenList];
    const changeObject = {
      ...copyDataList[index],
      taskScore: standardScoreVal,
      scoreStr: scoreVal,
      totalProportion: parseFloat(value)
    };
    copyDataList.splice(index, 1, changeObject);

    const handleDataArray = copyDataList.map((item) => {
      return {
        ...item,
        taskScore: isNaN((Number(item.totalProportion) * 0.01) * Number(currentTaskStandardScore)) ?
          0 :
          parseFloat(((Number(item.totalProportion) * 0.01) * Number(currentTaskStandardScore)).toFixed(2)),
      }
    })

    setCurrentTaskChildrenList(handleDataArray)
  }

  const getTotalScore = (data: TreeNode[]) => {
    return data?.reduce((sum, item) => {
      return sum += Number(item.taskScore) ?? 0
    }, 0)
  }
  // 检测保存的数据中是否含有负数的数据，如果有，则抛出来
  const detectionHasMinusData = (data: TreeNode[]) => {
    return data.filter((item) => (item.taskScore && Number(item.taskScore) < 0) || (item.totalProportion && Number(item.totalProportion) < 0))
  }

  // @ts-ignore
  const skipSettingFun = () => {
    skipSetting?.();
  }

  // @ts-ignore
  const setSuccessFun = () => {
    setSuccess?.()
  }

  // @ts-ignore
  // const refreshTaskStore = () => {
  //   safe(taskStore.init)(projectId);
  // }
  // 保存分配的任务分值信息
  const saveTaskScoreListInfo = async () => {
    try {
      // 计算出总分
      const totalScore = getTotalScore(currentTaskChildrenList);

      const hasMinusArray = detectionHasMinusData(currentTaskChildrenList);

      if (hasMinusArray.length > 0) {
        message.error("数据中不能含有负数，请检查");
        return
      }

      if (totalScore !== Number(currentTaskStandardScore)) {
        message.error("子项任务分值总和需要等于父项任务分值");
        return
      }


      const groupTaskScores  = currentTaskChildrenList.map((item) => {
        return {
          taskId: item.taskId,
          taskScore: Number(item.taskScore),
        }
      })

      const submitData = {
        versionId:currentVersionInfo.versionId,
        parentTaskId: currentTaskId,
        groupTaskScores,
        projectId
      }
      const scoreVersionId = await setGroupTaskScore(submitData);
      await dispatch({
        type: 'valueReview/setCurrentVersionInfo',
        payload: {
          versionId:scoreVersionId,
          isNew:true
        }
      })
      await dispatch({
        type: 'valueReview/fetchProjectScore',
        payload: scoreVersionId
      })
      message.success("任务分值保存成功");
    } catch (e) {
      message.error(e.message);
    }
  }

  const resetTaskScoreListInfo = () => {
    const newCurrentTaskChildrenList = currentTaskChildrenList.map((item) => {
      return {
        ...item,
        scoreStr: '暂未设置标准分',
        taskScore: undefined,
        totalProportion: 0,
      }
    })
    setCurrentTaskChildrenList(newCurrentTaskChildrenList)
  }
  return (
    <div className='other-level-value-assessment-setting'>
      <Form form={form}>
        <BdwRow type='flex'>
          <BdwFormItems className='flex-1' label='选择上级任务' required>
            <BdwTreeSelect
              treeData={treeDataList.map(mapToTreeNode)}
              style={{ width: '100%' }}
              value={currentTaskTitleValue}
              onSelect={chooseTask}
            />
          </BdwFormItems>
          <div className='show-detail-value'>
            <BdwTitle>任务分值</BdwTitle>
            <BdwReadonlySpan>{currentTaskStandardScore}</BdwReadonlySpan>
          </div>
        </BdwRow>
      </Form>
      <div className='other-level-value-assessment-setting-table'>
        <table>
          <thead>
            <tr>
              <th style={{ width: "40px" }}>序号</th>
              <th>任务名称</th>
              <th style={{ width: "80px" }}>任务分值</th>
              <th style={{ width: "120px" }}>百分比%</th>
            </tr>
          </thead>
          {
            currentTaskChildrenList && currentTaskChildrenList.length > 0 ?
              <tbody>
                {
                  currentTaskChildrenList?.map((item, index) => {
                    return (
                      <tr key={`${index}`}>
                        <td>
                          {index + 1}
                        </td>
                        <td>
                          {item.titleText} {item.scoreStr}
                        </td>
                        <td>
                          <Input type='number'
                            value={item.taskScore}
                            // disabled={flattenTreeDataList.filter(it => it.taskId === item.taskId)[0]?.status === "Finished"}
                            min={0}
                            onChange={(e) => taskStandardScoreChange(e.target.value, index)} />
                        </td>
                        <td>
                          <Input type='number'
                            value={item.totalProportion}
                            // disabled={flattenTreeDataList.filter(it => it.taskId === item.taskId)[0]?.status === "Finished"}
                            min={0}
                            onChange={(e) => taskRatioChange(e.target.value, index)} suffix='%' />
                        </td>
                      </tr>
                    )
                  })
                }
              </tbody> :
              <tbody>
                <tr>
                  <td colSpan={4}>
                    <Empty className='mb-16' description="暂无子项任务数据" />
                  </td>
                </tr>
              </tbody>
          }
        </table>
      </div>
      <div className='mt-16'>
        <Button type='primary' className='mr-16' onClick={saveTaskScoreListInfo}>保存分值</Button>
        {
          needSkipButton &&
          <Button onClick={resetTaskScoreListInfo}>重置分值</Button>
        }
      </div>
    </div>
  )
}

export default OtherLevelValueAssessmentSetting
