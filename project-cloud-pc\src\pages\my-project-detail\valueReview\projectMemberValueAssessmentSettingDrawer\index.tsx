import React from "react";
import {But<PERSON>, Drawer} from "antd";
import ProjectValueAssessmentSettingSecond from "../projectValueAssessmentSettingSecond"

interface ProjectMemberValueAssessmentSettingDrawerProps {
  visible: boolean
  successEvent: () => void
  cancelEvent: () => void
  setVisibleFun: () => void
}

const ProjectMemberValueAssessmentSettingDrawer:React.FC<ProjectMemberValueAssessmentSettingDrawerProps> = (props) => {
  const {visible = false,cancelEvent,setVisibleFun} = props;
  const closeValueAssessmentSettingDrawer = () => {
    // 保存后,直接关闭抽屉
    setVisibleFun();
  }
  return (
    <Drawer open={visible} width={750} title='评分设定'onClose={() => cancelEvent?.()} footer={
      <div style={{textAlign: 'right'}}>
        <Button onClick={() => cancelEvent?.()}>取消</Button>
      </div>
    }>
      <div className='p-16'>
        <ProjectValueAssessmentSettingSecond needSkipButton={false} setSuccessEvent={closeValueAssessmentSettingDrawer} />
      </div>
    </Drawer>
  )
}

export default ProjectMemberValueAssessmentSettingDrawer
