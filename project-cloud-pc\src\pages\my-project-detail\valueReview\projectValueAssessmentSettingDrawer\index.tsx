import React, { useMemo, useState } from "react";
import { But<PERSON>, Checkbox, Collapse, Drawer, Modal } from "antd";

import ProjectValueAssessmentSettingFirst
  from "../projectValueAssessmentSettingFirst";
import ProjectValueAssessmentSettingSecond
  from "../projectValueAssessmentSettingSecond";
import type { CheckboxValueType } from "antd/lib/checkbox/Group";

import "./index.less"
import ProjectVersionNameInputModal from "../projectVersionNameInputModal";
import { useBoolean } from "ahooks";
import { flatten } from "@/utils/utils";
import { useParams, useSelector } from "umi";
import { safe } from "@/utils/IgnorError";

const { Panel } = Collapse;

interface ProjectValueAssessmentSettingProps {
  visible: boolean
  successEvent: () => void
  cancelEvent: () => void
  setVisibleFun: () => void
}

const ProjectValueAssessmentSettingDrawer: React.FC<ProjectValueAssessmentSettingProps> = (props) => {
  const { visible = false, successEvent, cancelEvent, setVisibleFun } = props;
  const [systemMessage, { setFalse: systemMessageFalse, setTrue: systemMessageTrue }] = useBoolean(false);
  const { projectScoreList, currentVersionInfo } = useSelector((state: any) => state.valueReview);
  const { projectId } = useParams<{ projectId: string }>();


  const [modalVisible, { setFalse: modalHide, setTrue: modalShow }] = useBoolean(false);

  const [currentDefaultOpen, setCurrentDefaultOpen] = useState<string>("1");

  // @ts-ignore
  const [checkedCurrentVersion, setCheckedCurrentVersion] = useState<CheckboxValueType[]>([undefined]);

  // 第一步设置成功，打开第二步;
  const firstLevelSettingSuccess = async () => {
    // const projectTaskNewDataList = await getProjectTaskNewDataList(projectId)
    // const projectTaskNewDataListAfterDeal = flatten(projectTaskNewDataList.taskResult.filter((item) => !item.parent))
    // taskStore.updateTasks(projectTaskNewDataListAfterDeal);
    setCurrentDefaultOpen("2");
  }
  // 第二步设置成功，打开第三步
  const secondLevelSettingSuccess = () => {
    // 版本设置暂时未启用，所以第二部保存成功后,直接关闭抽屉
    modalHide();
    setVisibleFun();
    setCurrentDefaultOpen("3");
  }
  // 第二步跳过设置，直接打开第三步
  const secondLevelSkipSetting = () => {
    setCurrentDefaultOpen("3");
  }
  // 版本新建成功之后的回调
  const saveProjectValueAssessmentVersionSuccess = () => {
    modalHide();
    successEvent();
  }

  // 判断是否可以查看后续的步骤
  const hasVersion = useMemo(() => {
    // if(taskStore?.project?.scoreVersion) {
    //   return true
    // }
    return true
    return false
  }, [projectScoreList])

  const panelChangeEvent = (key: string | string[]) => {
    setCurrentDefaultOpen(key as string);
  }
  const refreshTaskStore = () => {
    // safe(taskStore.init)(projectId);
  }
  const cancelEventDefineBySelf = () => {
    //点击右上角关闭按钮和右下角取消按钮时 存在版本id且是新增加的版本才打开系统提示弹窗，否则直接关闭弹窗
    if (currentVersionInfo.isNew && currentVersionInfo.versionId) {
      systemMessageTrue();
      // cancelEvent?.()
      // setCurrentDefaultOpen("1");
      // refreshTaskStore();
      modalShow();
    } else {
      cancelEvent?.()
      setCurrentDefaultOpen("1");
    }
  }

  return (
    <Drawer
      open={visible}
      width={750}
      title='评分设定'
      maskStyle={{ backgroundColor: "transparent" }}
      maskClosable={false}
      onClose={() => cancelEventDefineBySelf()}
      destroyOnClose={true}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Button type='primary' className='mr-20' onClick={() => modalShow()}>{
            checkedCurrentVersion.length > 0 ? "保存并发送" : "保存"
          }</Button>
          {/* <Button className='mr-20' onClick={() => cancelEvent?.()}>另存为副本</Button> */}
          <Button onClick={() => cancelEventDefineBySelf()}>取消</Button>
        </div>
      }
    >
      <div>
        <div className='mt-8'>
          <Collapse ghost activeKey={currentDefaultOpen} accordion onChange={panelChangeEvent}>
            <Panel key='1' header={
              <div className="step-title">第一步：设定一级任务分值</div>
            }>
              <ProjectValueAssessmentSettingFirst setSuccessEvent={firstLevelSettingSuccess} />
            </Panel>
            <Panel key='2' collapsible={!hasVersion ? 'disabled' : 'header'} header={
              <div className="step-title">第二步：设置下级任务分值</div>
            }>
              <ProjectValueAssessmentSettingSecond setSuccessEvent={secondLevelSettingSuccess} skipSettingEvent={secondLevelSkipSetting} />
            </Panel>
            <Panel key='3' collapsible={!hasVersion ? 'disabled' : 'header'} header={
              <div className="step-title">第三步：评分执行设置</div>
            }>
              <div className='ml-20'>
                <Checkbox.Group value={checkedCurrentVersion} onChange={(value) => setCheckedCurrentVersion(value)}>
                  <Checkbox>选择是否设定当前版本为执行版本</Checkbox>
                </Checkbox.Group>
              </div>
              {
                checkedCurrentVersion && checkedCurrentVersion.length > 0 &&
                <div className='ml-20 mt-16 third-tip-info'>
                  <span>你对本项目的设定过分值的任务，由系统分发通知消息至各执行人!</span>
                </div>
              }
            </Panel>
          </Collapse>
        </div>
      </div>
      <ProjectVersionNameInputModal
        visible={modalVisible}
        successEvent={saveProjectValueAssessmentVersionSuccess}
        cancelEvent={(flag?: boolean) => {
          modalHide();
          systemMessageFalse();
          if (flag) {
            successEvent();
            setCurrentDefaultOpen("1");
          }
        }}
        systemMessage={systemMessage}
        checkedCurrentVersion={checkedCurrentVersion}
      />
    </Drawer>
  )
}

export default ProjectValueAssessmentSettingDrawer
