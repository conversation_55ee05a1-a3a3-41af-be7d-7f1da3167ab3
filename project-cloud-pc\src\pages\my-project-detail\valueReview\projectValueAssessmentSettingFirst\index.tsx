import React, { Key, useEffect, useMemo, useState } from "react";
import { Button, Empty, Input, message, Radio } from "antd";

import "./index.less"
import { useBoolean } from "ahooks";
import { useParams, useSelector, useDispatch } from "umi";
import { setOneLevelTaskScore } from "@/service/projectDos/my-project-detail/valueReview"

interface ProjectValueAssessmentSettingFirstProps {
  setSuccessEvent?: () => void
}

const ProjectValueAssessmentSettingFirst: React.FC<ProjectValueAssessmentSettingFirstProps> = (props) => {
  const { setSuccessEvent } = props;

  const { projectId } = useParams<{ projectId: string }>();

  const { projectScoreList, currentVersionInfo } = useSelector((state: any) => state.valueReview);

  const dispatch = useDispatch();

  // 获取一级任务的数据
  const firstLevelTaskList = useMemo(() => {
    if (projectScoreList) {
      // 第一级菜单就是没有父亲的任务
      return projectScoreList.filter((item: any) => !item.parentId)
    }
    return []
  }, [projectScoreList])

  const [projectValueSettingList, setProjectValueSettingList] = useState<any[]>([]);
  const [isChooseDatum, { setTrue: chooseDatum, setFalse: cancelChooseDatum }] = useBoolean(false);
  const [datumValue, setDatumValue] = useState<Key>();

  useEffect(() => {
    const handleFirstLevelTaskList = firstLevelTaskList?.map((item: any) => {
      // const {coefficient = 1} = item;
      // 初始时，设置过分值时，参照系数取其本身的
      // 没有设置过分值时，将一级任务的参照系数都置为 1
      // 用户选择基准项后，再重新进行系数计算
      if (item.coefficient) {
        return { ...item, taskScore: item.taskScore ?? 0 }
      }
      return { ...item, coefficient: 1, taskScore: item.taskScore ?? 0 }
    })

    // 遍历数据，查找是否存在基准项
    const hasDatumData = handleFirstLevelTaskList?.findIndex((item: any) => item.datum);

    // 如果没有选择过基准项数据,那么默认第一个是被选择的基准项
    if (hasDatumData === -1) {
      setProjectValueSettingList(
        handleFirstLevelTaskList.map((item: any, index: number) => {
          if (index === 0) {
            return { ...item, datum: true }
          }
          return { ...item, datum: false }
        })
      )
      if (handleFirstLevelTaskList && handleFirstLevelTaskList.length > 0) {
        setDatumValue(handleFirstLevelTaskList[0]?.taskId)
      }
    } else {
      setProjectValueSettingList(handleFirstLevelTaskList)
      // 勾选已经设置为默认值的基准项
      setDatumValue(handleFirstLevelTaskList[hasDatumData]?.taskId)
    }
  }, [firstLevelTaskList])

  // 获取总的分数
  const getTotalScore = (data: any[]) => {
    return data?.reduce((sum, item) => {
      return sum += item.taskScore ?? 0
    }, 0)
  }
  // 改变基础分值的时候，重新计算系数
  const calculateCoefficient = (data: any[], chooseDatumValue: Key) => {
    // 抽离出基准项数据
    const chooseDatumObject = data[projectValueSettingList.findIndex((item) => item.taskId === chooseDatumValue)]
    // 抽离出基准项的分值
    const chooseDatumScore = chooseDatumObject.taskScore;

    const copyList = data.map((item) => {
      // 非基准项时
      if (item.taskId !== chooseDatumValue) {
        return {
          ...item,
          coefficient: !isNaN(Number(Number(item.taskScore) / Number(chooseDatumScore!))) ?
            parseFloat(Number(Number(item.taskScore) / Number(chooseDatumScore!)).toFixed(2)) :
            0
        }
      } else {
        return { ...item, coefficient: 1 }
      }
    });
    return copyList
  }

  // 计算占比的函数
  const calculateInfo = (data: any[]) => {
    const totalScore = getTotalScore(data);
    const copyList = data.map((item) => {
      return {
        ...item,
        scoreRatio: !isNaN(Number(Number(item.taskScore) / totalScore)) ?
          parseFloat(Number(Number(item.taskScore) / totalScore * 100).toFixed(2)) :
          0
      }
    });
    setProjectValueSettingList(copyList);
  }

  // 基准分值发生变化的时候
  const standardScoreChange = (value: Key, index: number) => {
    // 拷贝projectValueSettingList的值
    const copyList = projectValueSettingList.map((item) => item);
    // 构造基准分值那一项的新数据
    const changeObject = { ...copyList[index], taskScore: Number(value) }
    // 用新基准分数据替换掉原有数据
    copyList.splice(index, 1, changeObject);
    // 调用分值计算方法，依据基准项重新计算所有一级任务的分值
    const allData = calculateCoefficient(copyList, datumValue ?? "");
    // 如未设置基准项，将数据更新
    if (!isChooseDatum) {
      setProjectValueSettingList(allData);
      return;
    }
    // 已经设置了基准项的话
    calculateInfo(allData)
  }

  // 系数发生变化的时候
  const consultChange = (value: Key, index: number) => {
    // 抽离出基准项数据
    const datumObjectIndex = projectValueSettingList.findIndex((item) => item.taskId === datumValue);
    // 抽离出基准项分数
    const { taskScore } = projectValueSettingList[datumObjectIndex];
    // 拷贝projectValueSettingList
    const copyList = projectValueSettingList.map((item) => item);
    // 构造待更新的数据项
    const changeObject = { ...copyList[index], coefficient: Number(value), taskScore: parseFloat((Number(value) * Number(taskScore)).toFixed(2)) }
    // 替换原有数据项
    copyList.splice(index, 1, changeObject);
    // 重新计算分值占比
    calculateInfo(copyList)
  }

  // 选择基础项的时候changeEvent
  const chooseDatumChangeEvent = (value: string) => {
    setDatumValue(value)
    // 如果选择了这个为基础项，那么系数需要重置为1。保证基础项的系数永远为1
    // 寻找到为这个id的索引
    const objectIndex = projectValueSettingList.findIndex((item) => item.taskId === value);
    const copyList = projectValueSettingList.map((item) => item);
    const changeObject = { ...copyList[objectIndex], coefficient: 1 }
    copyList.splice(objectIndex, 1, changeObject);
    const changeData = calculateCoefficient(copyList, value);
    setProjectValueSettingList(changeData);
  }

  // 检测保存的数据中是否含有负数的数据，如果有，则抛出来
  const detectionHasMinusData = (data: any[]) => {
    return data.filter((item) => (item.taskScore && Number(item.taskScore) < 0) || (item.coefficient && Number(item.coefficient) < 0))
  }

  // 确认选择该项为基础项的事件
  const sureChooseDatumEvent = () => {
    const copyList = projectValueSettingList.map((item) => {
      return { ...item, datum: false }
    });
    const hasMinusArray = detectionHasMinusData(copyList);
    if (hasMinusArray.length > 0) {
      message.error("填入值不能为负数，请检查");
      return;
    }
    if (copyList.length === 0) {
      message.error("请必须选择一个基准项");
      return;
    }
    const objectIndex = projectValueSettingList.findIndex((item) => item.taskId === datumValue);
    copyList.splice(objectIndex, 1, { ...projectValueSettingList[objectIndex], datum: true });
    if (projectValueSettingList[objectIndex].taskScore === 0) {
      message.error("基础项的分值必须大于0");
      return
    }
    chooseDatum();
    calculateInfo(copyList);
  }



  const localSaveTheData = async () => {
    try {
      /**
       *  1、 已经有了数据，做更新
       *  2、 没有保存过数据，不更新
       * */
      const hasMinusArray = detectionHasMinusData(projectValueSettingList);
      if (hasMinusArray.length > 0) {
        message.error("数据中不能出现负数，请检查");
        return
      }

      // const totalScore = getTotalScore(projectValueSettingList);

      // 修改一级任务时，经处理后得到要修改的一级任务信息
      const oneLevelTaskScores = projectValueSettingList.map((item) => {
        return {
          taskId: item.taskId!,
          datum: item.datum!,
          taskScore: Number(item.taskScore),
          coefficient: Number(item.coefficient),
        }
      })

      const submitData = {
        oneLevelTaskScores,
        projectId,
        scoreVersionId: currentVersionInfo.versionId
      }
      const scoreVersionId = await setOneLevelTaskScore(submitData);
      await dispatch({
        type: 'valueReview/setCurrentVersionInfo',
        payload: {
          versionId:scoreVersionId,
          isNew:true
        }
      })
      await dispatch({
        type: 'valueReview/fetchProjectScore',
        payload: scoreVersionId
      })
      message.success("第一级任务分值保存成功");
      setSuccessEvent?.();
    } catch (e) {
      message.error(e.message);
    }
  }

  return (
    <div className='project-value-assessment-setting-first'>
      <div className='project-value-assessment-setting-first-introduce'>
        <p className='font-bolder'>分值设定方法：</p>
        <p>
          ①首先在一级任务列表中，选择一项作为价值评分参考项，设置基准评分，其他同级任务可以参照基准任务评分，通过调配参照系数设定每项任务分值，也可以直接设置每项分值。
        </p>
        <p>
          ②设置完成后，系统会核算任务分值百分比，供你更直观的通过调配分值占比完成每项任务相对分值对比设定。
        </p>
        <p>
          ③完成一级任务分值设定后，每项一级任务的子级任务即可采取快捷的百分占比设置方式，设定子项任务价值评分。
        </p>
      </div>
      {
        !isChooseDatum &&
        <div className='project-value-assessment-base-setting'>
          <Radio.Group className='width-100' value={datumValue} onChange={(e) => chooseDatumChangeEvent(e.target.value)}>
            <table className='project-value-assessment-setting-table'>
              <thead>
                <tr>
                  <th>序号</th>
                  <th>任务名称</th>
                  <th>任务分值</th>
                  <th>参照系数</th>
                  <th>设为基准项</th>
                </tr>
              </thead>
              {
                projectValueSettingList.length > 0 ?
                  <tbody>
                    {
                      projectValueSettingList.map((item, index) => {
                        return (
                          <tr key={`${index}`}>
                            <td style={{ width: "40px" }}>{index + 1}</td>
                            <td>{item.title}</td>
                            <td style={{ width: "80px" }}>
                              <Input
                                type='number'
                                onChange={(e) => standardScoreChange(e.target.value, index)}
                                size='small'
                                min={0}
                                disabled={item?.status === "Finished"}
                                value={item.taskScore}
                              />
                            </td>
                            <td style={{ width: "80px" }}>
                              {
                                item.taskId === datumValue ? item.coefficient :
                                  <Input
                                    type='number'
                                    onChange={(e) => consultChange(e.target.value, index)}
                                    size='small'
                                    min={0}
                                    disabled={item?.status === "Finished"}
                                    value={item.coefficient} />
                              }
                            </td>
                            <td style={{ width: "80px" }} className='text-right'>
                              <Radio value={item.taskId} disabled={item.taskScore === 0} />
                            </td>
                          </tr>
                        )
                      })
                    }
                  </tbody> :
                  <tbody>
                    <tr>
                      <td colSpan={5}>
                        <Empty className='mb-16' description="暂无任务可以设置基准项" />
                      </td>
                    </tr>
                  </tbody>

              }

            </table>
          </Radio.Group>
          <Button className='mt-16' disabled={!datumValue} type='primary' onClick={() => sureChooseDatumEvent()}>确认设置基准项</Button>
        </div>
      }
      {
        isChooseDatum &&
        <div className='project-value-assessment-setting-ratio'>
          <table className='project-value-assessment-setting-table'>
            <thead>
              <tr>
                <th>序号</th>
                <th>任务名称</th>
                <th>任务分值</th>
                <th>参照系数</th>
                <th className='text-right'>百分占比</th>
              </tr>
            </thead>
            <tbody>
              {
                projectValueSettingList.map((item, index) => {
                  if (item.datum) {
                    return (
                      <tr key={`${index}`} className='choose-datum-tr'>
                        <td className='is-datum-tips-row' style={{ width: "40px" }}>{index + 1}</td>
                        <td className='is-datum-tips-row'>{item.title}<span className="ml-8 is-datum-tips">基准项</span></td>
                        <td className='is-datum-tips-row' style={{ width: "80px" }}>
                          {item.taskScore}
                        </td>
                        <td className='is-datum-tips-row' style={{ width: "80px" }}>
                          {item.coefficient}
                        </td>
                        <td style={{ width: "80px" }} className='text-right is-datum-tips-row'>
                          {item.scoreRatio}%
                        </td>
                      </tr>
                    )
                  }
                  return (
                    <tr key={`${index}`}>
                      <td style={{ width: "40px" }}>{index + 1}</td>
                      <td>{item.title}</td>
                      <td style={{ width: "80px" }}>
                        <Input
                          type='number'
                          onChange={(e) => standardScoreChange(e.target.value, index)}
                          size='small'
                          min={0}
                          disabled={item?.status === "Finished"}
                          value={item.taskScore} />
                      </td>
                      <td style={{ width: "80px" }}>
                        <Input
                          type='number'
                          onChange={(e) => consultChange(e.target.value, index)}
                          size='small'
                          min={0}
                          disabled={item?.status === "Finished"}
                          value={item.coefficient} />
                      </td>
                      <td style={{ width: "80px" }} className='text-right'>
                        {item.scoreRatio}%
                      </td>
                    </tr>
                  )
                })
              }
            </tbody>
          </table>
          <div className='mt-10'>
            <Button className='mt-16' type='primary' onClick={() => localSaveTheData()}>确认设置分值</Button>
            <Button className='ml-16' onClick={() => cancelChooseDatum()}>重新设置基准项</Button>
          </div>
        </div>
      }
    </div>
  )
}

export default ProjectValueAssessmentSettingFirst
