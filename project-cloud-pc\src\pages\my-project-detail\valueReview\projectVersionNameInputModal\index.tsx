import React from "react";
import { Button, Form, message, Modal } from "antd";

import "./index.less";
import { BdwFormItems, BdwInput, BdwReadonlySpan, BdwRow, BdwTitle } from "@/components";
import moment from "moment";
import { useParams, useSelector } from "umi";
import { useBoolean } from "ahooks";
import { saveVersionInfo, deleteVersion } from "@/service/projectDos/my-project-detail/valueReview";
import {getLocalStorage} from '@/utils/utils';

interface ProjectVersionNameInputModalProps {
  visible: boolean
  cancelEvent?: (flag?: boolean) => void
  successEvent?: () => void
  systemMessage: boolean,
  checkedCurrentVersion: any
}

const ProjectVersionNameInputModal: React.FC<ProjectVersionNameInputModalProps> = (props) => {
  const { taskInfo } = useSelector((state: any) => state.valueReview);
  const { projectId } = useParams<{ projectId: string }>();

  const { currentVersionInfo } = useSelector((state: any) => state.valueReview);
  const { basicProjectInfo } = useSelector((state: any) => state.projectTasks);
  const { name } = basicProjectInfo;
  const defaultVersionName = name + "-基础版";
  const [showTips, { setFalse: showTipsFalse, setTrue: showTipsTrue }] = useBoolean(true);

  const [form] = Form.useForm();
  const { visible, cancelEvent, successEvent, systemMessage, checkedCurrentVersion } = props;

  // 提交数据并保存
  const submitData = () => {
    form.validateFields().then(async () => {
      try {
        // const {scoreVersion, standardScore} = taskStore.project ?? {};

        const { name } = form.getFieldsValue();

        const submitParams = {
          choose: checkedCurrentVersion.length > 0 ? true : false,
          versionId: currentVersionInfo.versionId,
          versionName: name
        }
        await saveVersionInfo(submitParams);
        message.success("版本创建成功");
        successEvent?.();
      } catch (e) {
        message.error(e.message);
      }
    })
  }
  //确认保存
  const confirmSave = () => {
    if (systemMessage && showTips) {
      showTipsFalse();
    } else {
      submitData();
    }
  }
  //取消
  const cancel = () => {
    console.log((systemMessage && showTips),{systemMessage , showTips})
    if (systemMessage && showTips) {
      //删除并关闭评分弹窗
      deleteVersion(currentVersionInfo.versionId).then((res) => {
        cancelEvent?.(true);
      })
    } else {
      //关闭弹窗
      cancelEvent?.();
    }
  }

  return (
    <Modal getContainer={false} zIndex={1001} footer={[
      <Button key='1' onClick={() => cancel()}>取消</Button>,
      <Button type='primary' style={{ backgroundColor: "#0275d8" }} key='2' onClick={confirmSave}>确认保存</Button>
    ]} open={visible} title={(systemMessage && showTips) ? '系统提示' : '价值评分设定结果保存'} width={650} onCancel={cancel}
      maskClosable={false}
    >
      {
        (systemMessage && showTips) ? <div>您正在进行项目任务评分设定，请确认是否保存?</div> :
          <Form form={form} className='add-new-schedule-modal'>
            <BdwRow type='flex'>
              <div className='add-new-schedule-form-content flex-1'>
                <BdwFormItems initialValue={defaultVersionName} label='版本名称' required name='name' rules={[{ required: true, message: "版本名称必须输入" }]}>
                  <BdwInput maxLength={128} />
                </BdwFormItems>
                <div>
                  <BdwTitle>创建时间</BdwTitle>
                  <BdwReadonlySpan>{moment().format("YYYY-MM-DD HH点mm分ss秒")}</BdwReadonlySpan>
                </div>
              </div>
            </BdwRow>
          </Form>
      }

    </Modal>
  )
}

export default ProjectVersionNameInputModal
