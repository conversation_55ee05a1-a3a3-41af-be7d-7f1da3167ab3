/**
 * @description 基本信息
 * <AUTHOR>
 * @date 2023-11-17 11:37:01
*/
import React from "react";
import { BdwReadonlySpan, BdwRow, BdwTitle} from "@/components";
import { Divider } from "antd";
import {UserOutlined, FileTextOutlined, BlockOutlined, PaperClipOutlined,CalendarOutlined} from "@ant-design/icons/lib";
import moment from "moment";
import './index.less';

interface TaskBasicInfo {
  task: any
}

const BasicMes: React.FC<TaskBasicInfo> = ({task}) => {
    console.log(task)
  return (
    <div className='task-basic-info'>
      <BdwRow type='flex-center' className='mt-16'>
        <div className='task-basic-info-label'>
          <FileTextOutlined className='mr-5'/><BdwTitle>名&emsp;称：</BdwTitle>
        </div>
        <BdwReadonlySpan className='flex-1'>
          {task?.name}
        </BdwReadonlySpan>
      </BdwRow>
      <BdwRow type='flex-center' className='mt-16'>
        <div className='task-basic-info-label'>
          <UserOutlined className='mr-5'/><BdwTitle>负责人：</BdwTitle>
        </div>
        {
          task?.leaderName ?
          <BdwReadonlySpan className='flex-1'>
            {`${task?.leaderName}`}
          </BdwReadonlySpan> :
          <span className="back-show-info readonly-text">未选择任务负责人</span>
        }
      </BdwRow>
      <Divider className='mt-16' dashed={true} />
      <div className='mt-16'>
        <div>
          <BlockOutlined className='mr-5'/>
          <BdwTitle>
            <span>关联任务</span>
          </BdwTitle>
        </div>
        <BdwReadonlySpan>
          {task?.preTask?.title && <span className='can-click'><PaperClipOutlined />{task?.preTask?.title}</span>}
          {!task?.preTask?.title && <span className='help-title'>无关联任务</span>}
        </BdwReadonlySpan>
      </div>
      <div className='mt-16'>
        <div>
          <CalendarOutlined className='mr-5'/>
          <BdwTitle>
            <span>任务周期</span>
          </BdwTitle>
        </div>
        <BdwRow type='flex-center' className="mt-16">
          <div className='task-basic-info-label'>
            <BdwTitle>执行周期</BdwTitle>
          </div>
          <BdwReadonlySpan className='flex-1 ml-16'>
            {task?.executionCycle}
          </BdwReadonlySpan>
        </BdwRow>
        <BdwRow type='flex-center' className="mt-16">
          <div className='task-basic-info-label'>
            <BdwTitle>开始时间</BdwTitle>
          </div>
          {
            task?.startTime ?
            <BdwReadonlySpan className='flex-1 ml-16'>
              {moment(task?.startTime).format("YYYY-MM-DD")}
            </BdwReadonlySpan> :
            <span className="back-show-info ml-16 readonly-text">未设置</span>
          }
        </BdwRow>
        <BdwRow type='flex-center' className="mt-16">
          <div className='task-basic-info-label'>
            <BdwTitle>截止时间</BdwTitle>
          </div>
          {
            task?.endTime ?
              <BdwReadonlySpan className='flex-1 ml-16'>
                {moment(task?.endTime).format("YYYY-MM-DD")}
              </BdwReadonlySpan> :
              <span className="back-show-info ml-16 readonly-text">未设置</span>
          }
        </BdwRow>
      </div>
    </div>
  )
}

export default BasicMes
