/**
 * @description 价值评分
 * <AUTHOR>
 * @date 2023-11-07 11:27:11
*/
import React from 'react';
import { Tabs } from 'antd';
import { valueScoreTabs,valueScoreTabsKey } from '@/constants/Enum';
import BasicMes from './basicMes';
import EvaluateCriterion from './evaluateCriterion';
import TaskIntroduction from './taskIntroduction';
import Document from './document';
import { useSelector } from 'umi';
import './index.less';



const TableRowInformation: React.FC = () => {
    const { taskInfo} = useSelector((state: any) => state.valueReview);
    const TabsItem: any[] = valueScoreTabs.map((item: any, index: number) => {
        let children: any;
        switch (item.key) {
            case valueScoreTabsKey.BASIC_MES://基本信息
                children = <BasicMes task={taskInfo}/>;
                break;
            case valueScoreTabsKey.TASK_INTRODUCTION://任务说明
            children = <TaskIntroduction task={taskInfo} />;
                break;
            case valueScoreTabsKey.DOCUMENT://相关资料
            children = <Document task={taskInfo} />;
                break;
            case valueScoreTabsKey.EVALUATE_CRITERION://评价标准
            children = <EvaluateCriterion task={taskInfo} />;
                break;
        }
        return {
            ...item,
            children
        }
    })
    const tabChange = () => {

    }
    return (
        <div className='table-row-information-score'>
            <Tabs
                className='table-row-information-tabs'
                onChange={tabChange}
                items={TabsItem}
                defaultActiveKey={valueScoreTabsKey.BASIC_MES}
                destroyInactiveTabPane
            />
        </div>
    )
}
export default TableRowInformation;