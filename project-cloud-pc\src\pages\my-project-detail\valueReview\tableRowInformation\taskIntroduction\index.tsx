/**
 * @description 任务说明
 * <AUTHOR>
 * @date 2023-11-17 11:38:53
*/
import React from "react";
import BdwLeaveMessageReadonly from "@/components/bdw-leave-message-readonly";
import { Empty } from "antd";


interface TaskRemarkInfoProps {
    task: any
}

const TaskIntroduction: React.FC<TaskRemarkInfoProps> = ({ task }) => {
    const taskRemarkList = task?.taskExplain;
    const showHasSaveTaskRemark = taskRemarkList && taskRemarkList?.map((item: any, index: number) => {
        return (
            <BdwLeaveMessageReadonly  className="mb-16 mt-8"
            fileList={item.fileList} value={item.value} key={index} itemIndex={index} canDelete={false} />
        )
    })

    return (
        <div className='mt-16 task-remark-info'>
            {taskRemarkList?.length > 0 && showHasSaveTaskRemark}
            {(taskRemarkList?.length === 0 || !taskRemarkList) && <Empty className="mt-16" description="无任务说明" />}
        </div>
    )
}

export default TaskIntroduction
