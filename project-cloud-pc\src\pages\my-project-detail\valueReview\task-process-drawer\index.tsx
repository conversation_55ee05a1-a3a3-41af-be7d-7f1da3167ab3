import React, {useMemo} from "react";
import {Drawer, Space, Empty} from "antd";
import {
  BdwReadonlySpan,
  BdwRow,
} from "@/components";
import "./index.less";
import "@/styles/base.less"
import {bdwShowTimeFunction} from '@/utils/utils'
import {useSelector} from 'umi';
import ProjectDynamicItem from "../../projectOverview/projectDynamicItem";

export interface TaskProcessDrawerProps {
  visible: boolean
  cancelEvent?: () => void
  successEvent?: (data: any) => void
  task: any
}

const TaskProcessDrawer: React.FC<TaskProcessDrawerProps> = (props) => {
  const {visible, cancelEvent, successEvent, task} = props;
  const { projectOverviewDetails } = useSelector((state: any) => state.projectOverview);
  const {title} = task ?? {};


  const processElement = projectOverviewDetails?.projectTaskNews?.map((item: any, index: number) => {
    return <BdwRow key={`${index}`}>
      <ProjectDynamicItem
        key={`${index}`}
        className='mt-16'
        personName={item.submitterName}
        title={item.flowInfo}
        time={item.createTime}
        attach={item.attach}
        detail={item.description}/>
    </BdwRow>
  })

  return (
    <Drawer
      zIndex={1001}
      open={visible}
      className="task-process-drawer"
      title={
        <Space>
          <BdwReadonlySpan importantLevel="veryImportant">任务历程</BdwReadonlySpan>
          <BdwReadonlySpan importantLevel="important" className="task-name">{`( ${title} )`}</BdwReadonlySpan>
        </Space>
      }
      placement="right"
      bodyStyle={{
        borderRadius: 20,
      }}
      width={800}
      onClose={() => cancelEvent?.()}
    >
      <BdwRow type='flex' className='task-process-drawer-content ml-20'>
        <div className='timeLineBox'>
          {projectOverviewDetails?.projectTaskNews?.length > 0 ? processElement : <Empty/>}
        </div>
      </BdwRow>
    </Drawer>
  )
}

export default TaskProcessDrawer;
