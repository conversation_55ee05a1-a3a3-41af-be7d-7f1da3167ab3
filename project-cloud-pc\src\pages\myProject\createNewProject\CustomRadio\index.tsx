/**
 * @description 
 * <AUTHOR>
 * @date 2023-10-30 11:03:12
*/
import React, { useEffect, useMemo, useState } from 'react';
import { Radio } from 'antd';
import { useBoolean, useRequest } from 'ahooks';
import BdwChooseOrderStaff from '@/components/bdw-choose-order-staff';
import BdwChooseCustomStaff from '@/components/bdw-choose-customer-staff';
import { formatSelectOptions } from '@/utils/utils';
import {CustomRadioKey} from './Enum';
import {useSelector} from 'umi';
import './index.less';
interface CustomRadioType {
    businessAssociationType: any[];
    defaultCustomValue?: any;
    [propsName: string]: any
}

const CustomRadio: React.FC<CustomRadioType> = (props: CustomRadioType) => {
    const { onChange, businessAssociationType, value ,form,disabled,defaultCustomValue} = props;
    const [show, { setFalse, setTrue }] = useBoolean(false);
    const [defaultAssValue,setDefaultAssValue] = useState<any>();
    const { projectOverviewDetails } = useSelector((state: any) => state.projectOverview);//项目概览数据
    const options = useMemo(() => {
        if (businessAssociationType) {
            const arr = formatSelectOptions(businessAssociationType);
            return [{
                value: CustomRadioKey.noAssociation,
                label: '无关联'
            }, ...arr];
        } else {
            return [];
        }
    }, [businessAssociationType])
    useEffect(()=>{
        if (value != CustomRadioKey.noAssociation) {
            setTrue();
            setDefaultAssValue(defaultCustomValue);
        } else {
            setFalse();
        }
    },[])
    const onchange = (e: any) => {
        onChange(e.target.value);
        form.setFieldValue('projectBusinessId',undefined);
        form.setFieldValue('estateId',undefined);
        if (e.target.value != CustomRadioKey.noAssociation) {
            setTrue();
            if(defaultCustomValue){
                if(e.target.value == projectOverviewDetails.businessAssociationType){
                    setDefaultAssValue(defaultCustomValue);
                }else{
                    setDefaultAssValue(undefined);
                }
            }
            
        } else {
            setFalse();
        }
    }
    const orderChange = (data: any) => {
        form.setFieldValue('projectBusinessId',data.orderInfoId);
    }
    const customerChange = (data: any) => {
        form.setFieldValue('estateId',data.estateId);
        form.setFieldValue('projectBusinessId',data.customerId);
    }
    return (
        <div className='custom-radio-container'>
            <Radio.Group
                options={options}
                onChange={onchange}
                defaultValue={value}
                disabled={disabled}
            />
            {
                show && <div className='project-reference-template'>
                    {
                        value == CustomRadioKey.CUSTOMER_ASSOCIATION && <BdwChooseCustomStaff  placeholder="请选择关联客户" value={defaultAssValue} onChange={customerChange}/>
                    }
                    {
                        value == CustomRadioKey.ORDER_ASSOCIATION && <BdwChooseOrderStaff  placeholder="请选择关联订单" value={defaultAssValue}  onChange={orderChange}/>
                    }
                </div>
            }
        </div>
    )
}
export default CustomRadio