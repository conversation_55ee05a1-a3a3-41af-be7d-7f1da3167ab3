/**
 * @description 
 * <AUTHOR>
 * @date 2023-10-30 10:44:45
*/
import React, { useEffect, useState } from 'react';
import './index.less';
import BdwRow from '@/components/bdw-row';
import { DatePicker } from 'antd';
import moment from 'moment';
import { calcWorkDays } from "@/service/projectDos/myProjectApi";

const { RangePicker } = DatePicker;

interface TimePickerPicker {
    [propName: string]: any;
}

//时间周期选择
const TimePicker = <T extends {}>(WrappedComponent: React.FC<T> | React.ComponentClass<T>) => {
    const Picker: React.FC<TimePickerPicker & T> = (props) => {
        const { onChange, workSystems, value,...other } = props;
        const [time, setTime] = useState(0);
        useEffect(() => {
            if (value) {
                const startDate = moment(value[0]).format('YYYY-MM-DD');
                const endDate = moment(value[1]).format('YYYY-MM-DD');
                calcWorkDays({ workSystem: workSystems, endDate, startDate }).then((res: any) => {
                    setTime(res);
                })
            }else{
                setTime(0);
            }
        }, [value,workSystems])
        // 计算工作天数
        const onchange = (e: any) => {
            onChange(e);
        }
        return (
            <BdwRow type='flex-between' className='custom-range-picker'>
                <WrappedComponent
                    bordered={false}
                    onChange={onchange}
                    value={value}
                    {...other as T}
                />
                <div className='total-range-time'>{time}天</div>
            </BdwRow>
        )
    }

    return Picker

}
export default TimePicker(RangePicker);