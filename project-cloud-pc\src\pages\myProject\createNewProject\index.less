@import '../../../styles/base.less';
.spin-create-new-project{
  height: 100%;
  overflow: auto;
}
.create-new-project-container {
  width: 100%;
  height: 100%;
  position: relative;
  // 顶部标题栏
  .top-title {
    font-size: 20px;
    font-weight: bold;
    height: 50px;
    padding: 0 16px;
    line-height: 50px;
    border-bottom: 1px solid @divider;
  }

  // 内容栏
  .new-project-content {
    width: 100%;

    >div {
      .p-16();
    }

    .new-project-content-left {
      width: 50%;
      border-right: 1px dashed @divider;

        // 输入框
        .ant-input {
          padding: 2px 0;
          border-radius: unset;
          font-size: 13px;
          min-height: 27px;
        }

        // 文本域图标
        .input-textArea-icon {
          position: absolute;
          bottom: -4px;
          right: -4px;
          width: 20px;
          height: 14px;
          transform: rotate(45deg);
          display: flex;
          align-items: center;
          border-left: 1px solid #a8a8a8;
          padding-left: 2px;

          .input-textArea-icon-line {
            width: 1px;
            height: 50%;
            background: #a8a8a8;

          }
        }
        // 自动补全框
        .ant-select-single .ant-select-selector .ant-select-selection-search{
            left: 0;
            right: 0;
        }

      //单选框
      .ant-radio-group {
        .ant-radio-wrapper.ant-radio-wrapper-in-form-item {
          color: rgb(92, 92, 92);
          margin-right: 30px;
        }
        .ant-radio-wrapper.ant-radio-wrapper-in-form-item:last-child{
            margin-right: unset;
        }

      }


    }

    .new-project-content-right {
      width: 50%;

      .right-head {
        height: 50px;
        padding: 8px 16px;
        color: #333;
        font-size: 13px;
        font-weight: bold;
        display: flex;
        align-items: flex-end;
        border-bottom: 1px solid @divider;
      }

      .right-content {
        padding: 10px 16px;
      }
    }
  }
    //   按钮
    .button-container{
        width: 100%;
        height: 48px;
        border-top: 1px solid @divider;
        display: flex;
        align-items: center;
        .btn{
            width: 50%;
            align-items: center;
            justify-content: flex-end;
            .ant-btn{
                height: 28px;
                margin-left: 10px;
                font-size: 13px;
                line-height: 20px;
            }
            .cancel{
                width: 58px;
                color: #333;
            }
            .confirm{
                width: 188px;
            }
        }
    }
}