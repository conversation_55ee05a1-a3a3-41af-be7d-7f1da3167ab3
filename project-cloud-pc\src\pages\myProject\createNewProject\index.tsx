/**
 * @description 新建项目
 * <AUTHOR>
 * @date 2023-10-27 14:34:17
*/
import React, { useEffect, useMemo, useLayoutEffect, Key, useState } from 'react';

import {
    BdwRow,
    BdwInput,
    BdwFormItems,
    BdwLoading,
    BdwTextarea,
    BdwChooseCompanyStaff
} from '@/components';
import TimePicker from './TimePicker';
import CustomRadio from './CustomRadio';
import TemplateRadio from './templateRadio';
import { Form, Button, Input, Select, DatePicker, Radio, Switch, AutoComplete, Spin, Tree, Empty } from 'antd';
import {
    listBusinessAssociationType,
    listProjectLevel,
    listProjectType,
    listWorkSystem,
    listProjectVisibleAuthorization,
    loadOptionalList,
    projectManagementEdit,
} from "@/service/projectDos/myProjectApi";
import { listEmpByParams } from '@/service/projectDos/commonApi';
import { useRequest } from 'ahooks';
import { history, useSelector, useDispatch } from 'umi';
import moment from 'moment';
import { CustomRadioKey } from './CustomRadio/Enum';

import { formatSelectOptions, flatten } from '@/utils/utils';
import { testData } from './utils';
import './index.less';
import { FileOutlined } from '@ant-design/icons';

interface TreeDataItem {
    title: string
    key: Key
    children: TreeDataItem[]
}
const { RangePicker } = DatePicker;


const CreateNewProject: React.FC = () => {
    const dispatch = useDispatch();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(true);
    const { userInfo } = useSelector((state: any) => state.commonTask);//当前项目登录人信息
    const [projectTreeData,setProjectTreeData] = useState([]);
    const { data: classificationId, run: runClassificationId } = useRequest<any>(loadOptionalList, {
        manual: true,
        onSuccess(res) {
            form.setFieldValue('classification', res[0]?.key)
        }
    });//项目分类
    const { data: projectLevel, run: runProjectLevel } = useRequest(listProjectLevel, {
        manual: true,
        onSuccess(res) {
            form.setFieldValue('level', formatSelectOptions(res)[0]?.value)
        }
    });//项目级别
    const { data: projectType, run: runProjectType } = useRequest(listProjectType, {
        manual: true,
        onSuccess(res) {
            form.setFieldValue('type', formatSelectOptions(res)[0]?.value)
        }

    });//项目类型
    const { data: businessAssociationType, run: runBusinessAssociationType } = useRequest<any>(listBusinessAssociationType, { manual: true });//项目业务关联
    const { data: workSystem, run: runWorkSystem } = useRequest(listWorkSystem,
        {
            manual: true,
            onSuccess(res) {
                form.setFieldValue('workSystem', formatSelectOptions(res)[0]?.value)
            }
        });//工作制度
    const { data: visibleAuthorization, run: runVisibleAuthorization } = useRequest(listProjectVisibleAuthorization, { manual: true });
    const { run: submitCreate, loading: submitLoading } = useRequest(projectManagementEdit, {
        manual: true,
        onSuccess: (res: string, params) => {
            dispatch({
                type: 'projectTasks/fetchProjectInfo',
                payload: res
            })
            dispatch({ type: 'commonTask/fetProjectListInfo' })
            history.push(`/my-project-detail/${res}`);
        }
    })
    useEffect(() => {
        (async () => {
            await runClassificationId();
            await runProjectLevel()
            await runProjectType()
            await runBusinessAssociationType()
            await runWorkSystem()
            await runVisibleAuthorization()
            setLoading(false)
        })()
    }, [])

    // 递归处理数据函数
    const handleGetData = (item: any): TreeDataItem => {
        return {
            title: item.name,
            key: item.taskId,
            children: item.children?.map(handleGetData)
        }
    }
    // 选中模板之后，获取每一个被选中的key
    const treeExpendKeys = useMemo(() => {
        let treeAfterHandleData: any[] = [];
        treeAfterHandleData = flatten(projectTreeData, "children")
        return treeAfterHandleData.map((item) => item.taskId);
    }, [projectTreeData])

    const isFinish = (data: any) => {
        const projectMembers = data.projectMembers?.map((item: any) => ({
            userId: item.id
        }))
        const startDate = moment(data.projectCycle[0]).format('YYYY-MM-DD');
        const endDate = moment(data.projectCycle[1]).format('YYYY-MM-DD');
        const resData = {
            ...data,
            startDate,
            endDate,
            leaderId: data.leaderId.id,
            projectMembers,
            businessAssociationType: data.businessAssociationType == CustomRadioKey.noAssociation ? undefined : data.businessAssociationType,
            template: false
        }
        delete resData.projectCycle;
        delete resData.projectUseTemplate;
        submitCreate(resData);
    }
    const treeDataChange = (e: any) => {
        if(e){
            const data = e.map(handleGetData);
            setProjectTreeData(data);
        }else{
            setProjectTreeData([]);
        }
        
    }
    return (
        <div className='spin-create-new-project'>
             <Spin spinning={loading}>
            <div className='create-new-project-container'>
                {submitLoading && <BdwLoading tip='正在生成项目~' />}
                <div className='top-title'>新建项目</div>
                <BdwRow type='flex' className='new-project-content'>
                    <div className='new-project-content-left'>
                        <Form onFinish={isFinish} form={form} initialValues={{
                            leaderId: { id: userInfo?.userId, name: userInfo?.userName },
                            businessAssociationType: 'noAssociation',
                            needAudit: false,
                            canSimultaneouslyParticipateMultipleTasks: false,
                            visibleAuthorization: 'PARTICIPANT',
                            projectUseTemplate: 'noAssociation',
                        }}>
                            <BdwFormItems label="项目名称" name='name' required rules={[{ required: true, message: "请填写项目名称" }]}>
                                <BdwInput style={{ fontWeight: 'bold', fontSize: '18px' }} placeholder='请输入项目名称' />
                            </BdwFormItems>
                            <BdwFormItems label="项目说明" name='instructions' dependencies={['instructions']} rules={[{ required: true, message: "请填写项目说明" }]}>
                                <BdwTextarea placeholder='请输入项目说明' />
                            </BdwFormItems>
                            <BdwFormItems
                                label="项目负责人"
                                name='leaderId'
                                required
                                rules={[{ required: true, message: "请选择项目负责人" }]}
                            >
                                <BdwChooseCompanyStaff placeholder="请选择项目负责人" apiSrc={listEmpByParams} />
                            </BdwFormItems>

                            <BdwRow type="flex-between">
                                {
                                    <BdwFormItems
                                        label="工作制度"
                                        name='workSystem'
                                        required
                                        rules={[{ required: true, message: "请选择工作制度" }]}
                                        width="48%"
                                    >
                                        <Radio.Group
                                            options={formatSelectOptions(workSystem)}
                                        />
                                    </BdwFormItems>
                                }

                                <Form.Item
                                    noStyle
                                    shouldUpdate={(p: any, c: any) => {
                                        return p.workSystem != c.workSystem
                                    }}
                                >
                                    {
                                        () => {
                                            return <BdwFormItems
                                                label="项目周期"
                                                name='projectCycle'
                                                required
                                                rules={[{ required: true, message: "请选择项目周期" }]}
                                                width="48%"

                                            >
                                                <TimePicker workSystems={form.getFieldValue('workSystem')} />
                                            </BdwFormItems>
                                        }
                                    }
                                </Form.Item>
                            </BdwRow>
                            {

                                <BdwFormItems
                                    label="与此项目关联的人员"
                                    name='projectMembers'
                                    required
                                    rules={[{ required: true, message: "请选择与此项目关联的人员" }]}
                                >
                                    <BdwChooseCompanyStaff placeholder="请选择与此项目关联的人员"  apiSrc={listEmpByParams} type='multiple' />
                                </BdwFormItems>
                            }
                            <BdwFormItems label="项目引用模板" name='projectUseTemplate'>
                                <TemplateRadio form={form} treeDataChange={treeDataChange} />
                            </BdwFormItems>
                            <BdwFormItems label="项目业务关联" name='businessAssociationType'>
                                <CustomRadio businessAssociationType={businessAssociationType} form={form} />
                            </BdwFormItems>
                            <BdwFormItems hidden name='quoteProjectId'>
                                <Input disabled />
                            </BdwFormItems>
                            <BdwFormItems hidden name='projectBusinessId'>
                                <Input disabled />
                            </BdwFormItems>
                            <BdwFormItems hidden name='estateId'>
                                <Input disabled />
                            </BdwFormItems>
                            <BdwRow type="flex-between">
                                <BdwFormItems
                                    label="项目分类"
                                    name='classification'
                                    required
                                    rules={[{ required: true, message: "请选择项目分类" }]}
                                    width="48%"
                                >
                                    <Select
                                        options={classificationId?.map((item: any) =>({label:item.value,value:item.key}))}
                                        bordered={false}

                                    />
                                </BdwFormItems>
                                <BdwFormItems
                                    label="项目级别"
                                    name='level'
                                    required
                                    rules={[{ required: true, message: "请选择项目级别" }]}
                                    width="48%"
                                >
                                    <Select
                                        options={formatSelectOptions(projectLevel)}
                                        bordered={false}
                                    />
                                </BdwFormItems>
                            </BdwRow>
                            <BdwRow type="flex-between">
                                <BdwFormItems
                                    label="项目可见授权"
                                    name='visibleAuthorization'
                                    width="48%"
                                >
                                    <Select
                                        options={formatSelectOptions(visibleAuthorization)}
                                        bordered={false}

                                    />
                                </BdwFormItems>
                                <BdwFormItems
                                    label="项目类型"
                                    name='type'
                                    width="48%"
                                    rules={[{ required: true, message: "请选择项目类型" }]}
                                >
                                    <Select
                                        options={formatSelectOptions(projectType)}
                                        bordered={false}

                                    />
                                </BdwFormItems>
                            </BdwRow>
                            <BdwRow type="flex-between">
                                <BdwFormItems
                                    label="项目任务是否需要报审、审核、发布启用"
                                    name='needAudit'
                                    width="48%"
                                    valuePropName="checked"
                                >
                                    <Switch />
                                </BdwFormItems>
                                <BdwFormItems
                                    label="同一人同一时段是否允许参与多个任务"
                                    name='canSimultaneouslyParticipateMultipleTasks'
                                    width="48%"
                                    valuePropName="checked"
                                >
                                    <Switch />
                                </BdwFormItems>
                            </BdwRow>
                        </Form>
                    </div>
                    {/* -------split------- */}
                    <div className='new-project-content-right'>
                        <div className='right-head'>
                            引用模板预览
                        </div>
                        <div className='right-content'>
                            {
                                projectTreeData.length ? <Tree
                                    //@ts-ignore
                                    treeData={projectTreeData}
                                    showLine
                                    expandedKeys={treeExpendKeys}
                                    showIcon
                                    icon={<FileOutlined />}
                                /> : <Empty description='没有数据' />
                            }
                        </div>
                    </div>
                </BdwRow>
                <div className='button-container'>
                    <BdwRow type='flex' className='btn'>
                        <Button className='cancel' onClick={() => { history.goBack() }}>取消</Button>
                        <Button type='primary' className='confirm' onClick={() => { form.submit() }}>保存，并开始项目节点编辑</Button>
                    </BdwRow>
                </div>
            </div>
        </Spin>
        </div>
       

    )
}
export default CreateNewProject;