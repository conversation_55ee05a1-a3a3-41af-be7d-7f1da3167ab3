export enum templateRadioKey {
    noAssociation = 'noAssociation',
    USE_TEMPLATE = 'USE_TEMPLATE',
    USE_HISTORY_PROJECT = 'USE_HISTORY_PROJECT'
}
export const templateRadioOptions = [
    {
        "value": "noAssociation",
        "label": "新建空白项目"
    },
    {
        "value": "USE_TEMPLATE",
        "label": "引用模板创建项目"
    },
    {
        "value": "USE_HISTORY_PROJECT",
        "label": "引用历史项目"
    }
]