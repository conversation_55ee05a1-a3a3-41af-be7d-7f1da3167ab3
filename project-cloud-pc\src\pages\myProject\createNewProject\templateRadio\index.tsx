/**
 * @description 
 * <AUTHOR>
 * @date 2023-10-30 11:03:12
*/
import React, { useEffect, useMemo } from 'react';
import { Radio } from 'antd';
import { useBoolean, useRequest } from 'ahooks';
import BdwChooseTemplateStaff from '@/components/bdw-choose-template-staff';
import {templateRadioKey,templateRadioOptions} from './Enum';
import { listReleasedProjectTemplate ,listHistoryProject} from '@/service/projectDos/myProjectApi';
import { listTaskSimpleInfo } from '@/service/projectDos/my-project-detail/projectTasks';
import './index.less';
interface CustomRadioType {
    [propsName: string]: any,
    treeDataChange: (data: any) => void
}

const CustomRadio: React.FC<CustomRadioType> = (props: CustomRadioType) => {
    const { onChange, value ,form,treeDataChange} = props;
    const [show, { setFalse, setTrue }] = useBoolean(false);
    const onchange = (e: any) => {
        onChange(e.target.value)
        treeDataChange([]);
        form.setFieldValue('quoteProjectId',undefined);
        if (e.target.value != templateRadioKey.noAssociation) {
            setTrue();
        } else {
            setFalse();
        }
    }
    const historyProjectChange = (data: any) => {
        form.setFieldValue('quoteProjectId',data.projectId);
        listTaskSimpleInfo(data.projectId).then((res)=>{
            treeDataChange(res);
        }).catch(()=>{
            treeDataChange([]);
        })
    }
    const templateChange = (data: any) => {
        form.setFieldValue('quoteProjectId',data.projectId);
        listTaskSimpleInfo(data.projectId).then((res)=>{
            treeDataChange(res);
        }).catch(()=>{
            treeDataChange([]);
        })
    }
    return (
        <div className='custom-radio-container'>
            <Radio.Group
                options={templateRadioOptions}
                onChange={onchange}
                defaultValue={value}
            />
            {
                show && <div className='project-reference-template'>
                    {
                        value == templateRadioKey.USE_TEMPLATE && <BdwChooseTemplateStaff placeholder='请选择模板'  api={listReleasedProjectTemplate} onChange={templateChange}/>
                    }
                    {
                        value == templateRadioKey.USE_HISTORY_PROJECT && <BdwChooseTemplateStaff placeholder='请选择历史项目'  api={listHistoryProject} onChange={historyProjectChange}/>
                    }
                </div>
            }
        </div>
    )
}
export default CustomRadio