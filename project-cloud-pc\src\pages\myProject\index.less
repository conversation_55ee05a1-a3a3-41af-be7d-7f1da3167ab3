@import '../../styles/base.less';
.my-project-container {
  padding: 16px 0;

  .project-tab-content {
    padding-top: 10px;
    .ant-tabs-top{
      overflow-x: unset!important;
    }
    .ant-tabs-tab{
      margin-left: unset!important;
    }
  }

  .project-statistic-content {
    padding-left: 20px;
  }

  .project-statistic-table {
    padding: 16px 20px;
  }

  .table-show-image-box {
    padding: 6px 0;

    img {
      width: 40px;
      height: 40px;
    }
  }

  .vertical-middle {
    vertical-align: middle;
  }

  .select-width {
    width: 95px;
    min-width: 95px;
  }

  .search-label-container {
    .search-project-label {
      width: 90px;
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #5c5c5c;
    }

    .no-border-search {
      border-bottom: 1px solid @divider !important;

      .ant-input-group-addon {
        background: white;

        .ant-input-search-button {
          height: 27px;
          border: none;
          box-shadow: unset;
        }
      }

    }
  }

}
