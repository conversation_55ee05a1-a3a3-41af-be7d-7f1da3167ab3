/**
 * @description 我的项目
 * <AUTHOR>
 * @date 2023-10-26 16:22:30
*/
import React, { useEffect, useMemo, useState } from "react";
import { Input, Menu, Tabs, Select } from "antd";
import {
    BarsOutlined,
    UserOutlined,
    SolutionOutlined,
    UsergroupAddOutlined,
    FileTextOutlined,
    FileAddOutlined,
    SearchOutlined,
    AppstoreOutlined
} from "@ant-design/icons";
import {
    MenuItemShowNumber,
    BdwCardTable,
    BdwPopover,
    BdwTableSearchWrapper,
    BdwTable,
    BdwTableButton,
    BdwTableHeaderSearchComponent,
    IconTitle,
    BdwRow
} from "@/components";
import ProjectListCardItem from "./components/table-single-card";
import type { ProjectListCardItemProps } from "./components/table-single-card";
import { BdwTableColumn } from '@/components/bdw-table';
import { useRequest } from "ahooks";
import type { TypeDataResult } from "@/type";
import { listProjectType, quickStatistics, listProject } from "@/service/projectDos/myProjectApi";
import { listStatus } from "@/service/projectDos/commonApi";
import { history, useDispatch } from "umi";
import {projectTypeListsCode } from './Enum';
import { setLocalStorage } from "@/utils/utils";
// @ts-ignore
import ProjectDefault from "@/assets/image/project-default.png";
import './index.less';
interface TabDataItemInterface {
    icon: () => React.ReactNode
    name: string
    code: string
}

// 项目搜索状态值
interface StatusItemInterface {
    name: string
    value: string
}

const { Search } = Input;


const MyProject = () => {
    const dispatch = useDispatch();
    // 全局搜索默认字段
    const [globalSearch, setGlobalSearch] = useState("");
    // tabPane的默认状态值
    const [currentTabPaneValue, setCurrentTabPaneValue] = useState<string>(projectTypeListsCode.CUSTOMER);
    // menu的默认状态值
    const [currentMenuValue, setCurrentMenuValue] = useState<string>("ALL");
    // 列表视图 LIST代表列表 CARD代表卡片视图
    const [tableShowWay, setTableShowWay] = useState<string>("LIST");
    // 查询名称
    const [searchName, setSearchName] = useState<string>("");
    //查询状态
    const [status, setStatus] = useState('');
    // 查询用的类型
    const [searchHeader, setSearchHeader] = useState<string>("");
    const { data: projectTypeList, run: runProjectTypeList } = useRequest(listProjectType, { manual: true });
    const { data: topQuickStatistics, run: runTopQuickStatistics } = useRequest<any[]>(()=>quickStatistics({template:false}), { manual: true });
    const { data: statusList, run: runStatusList } = useRequest<any[]>(listStatus, { manual: true });
    useEffect(() => {
        (async () => {
            await runProjectTypeList();
            await runTopQuickStatistics();
            await runStatusList();
        })()
    }, [])
    const projectSearchStatus:any[] = useMemo(() => {
        if (statusList) {
            return statusList;
        }
        return []
    }, [statusList])

    // 项目类型
    const projectTypeLists: any = useMemo(() => {
        if (topQuickStatistics) {
            return topQuickStatistics
        }
        return []
    }, [topQuickStatistics])
    const newTabDataList = projectTypeLists?.map((item: any, index: number) => {
        let icon;
        switch (item.projectTypeCode) {
            case projectTypeListsCode.CUSTOMER:
                icon = () => <UserOutlined />;
                break;
            case projectTypeListsCode.ORDER:
                icon = () => <SolutionOutlined />;
                break;
            case projectTypeListsCode.WORK:
                icon = () => <FileTextOutlined />;
                break;
            case projectTypeListsCode.COOPERATION:
                icon = () => <UsergroupAddOutlined />;
                break;
        }
        return {
            name: item.projectTypeName,
            code: item.projectTypeCode,
            icon,
            projectNum: item.total
        }
    })
    // 顶部统计栏
    const quickStatisticsList: any = useMemo(() => {
        if (topQuickStatistics) {
            const list = topQuickStatistics.filter((item: any) => item.projectTypeCode == currentTabPaneValue)
            return list[0].projectQuickStatistics;
        }
        return []
    }, [currentTabPaneValue, topQuickStatistics])
    const menuShowElement = quickStatisticsList.map((item: TypeDataResult) => {
        const labelContent = <div>
            <span>{item.quickStatusName}</span>
            <MenuItemShowNumber className='ml-8 vertical-middle'>{item.quantity}</MenuItemShowNumber>
        </div>
        return ({
            label: labelContent,
            key: item.quickStatus
        })
    })

    const tabsShowElement = newTabDataList.map((item: any) => {
        const tabShowContent = (<div><span>{item.icon()}</span><span className='c-content'>{item.name}<span className='ml-6 menu-item-number-ball vertical-middle'>{item.projectNum}</span></span></div>);
        return (
            {
                label: tabShowContent,
                key: item.code,
            }
        )
    })

    const changeProjectName = (e: string) => {
        setCurrentTabPaneValue(e)
    }
    //新建项目
    const createNewProject = () => {
        history.push('my-project/create-new-project');
    }
    const tableDropContent = (
        <div>
            <div onClick={() => setTableShowWay("LIST")}>
                <IconTitle className='mb-10 pointer' icon={() => <BarsOutlined />}>列表视图</IconTitle>
            </div>
            <div onClick={() => setTableShowWay("CARD")}>
                <IconTitle className='pointer' icon={() => <AppstoreOutlined />}>卡片视图</IconTitle>
            </div>
        </div>
    )
    const tableDrop = (
        <BdwPopover placement='bottomLeft' content={tableDropContent} trigger='hover'>
            {
                tableShowWay === "LIST"
                    ? <IconTitle className='mr-20' icon={() => <BarsOutlined />}>列表视图</IconTitle>
                    : <IconTitle className='mr-20' icon={() => <AppstoreOutlined />}>卡片视图</IconTitle>
            }
        </BdwPopover>
    )
    const fastButtonsRender = () => {
        return (
            <BdwRow type="flex">
                <BdwTableButton onClick={createNewProject} icon={() => <FileAddOutlined />}>新建项目</BdwTableButton>
                {
                    tableShowWay !== "LIST" &&
                    <BdwRow type="flex" className="search-label-container">
                        <div className="search-project-label">搜索项目:</div>
                        <Search allowClear bordered={false} maxLength={128} onSearch={(e) => setSearchName(e)} className='no-border-search'
                            placeholder='搜索项目名称' />
                    </BdwRow>


                }
            </BdwRow>
        )
    }
    const onRowClick = (item: any) => {
        return {
            onClick: () => {
                history.push(`/my-project-detail/${item.projectId}`)
            }
        }
    }
    const projectImgRender = (text: string) => {
            return (
                <div className='table-show-image-box'>
                    <img src={text?text:ProjectDefault} alt='项目头像' />
                </div>
            )
    }
    const cardTableItem = (dataItem: ProjectListCardItemProps) => {
        return (
            <ProjectListCardItem
                name={dataItem.name}
                coverPictureUrl={dataItem.coverPictureUrl}
                leaderName={dataItem.leaderName}
                leaderId={dataItem.leaderId}
                schedule={dataItem.schedule}
                process={dataItem.process}
                endDate={dataItem.endDate}
                statusName={dataItem.statusName}
                projectId={dataItem.projectId}
                dataItem={dataItem}
            />
        )
    }
    const optionElement = projectSearchStatus.map((it: StatusItemInterface) => {
        return <Select.Option value={it.value} key={it.value}>{it.name}</Select.Option>
    })
    const menuClickEvent = (e: any) => {
        setCurrentMenuValue(e?.key)
    }

    // 根据是卡片还是列表展示数据
    const showTableBtType = tableShowWay === "LIST"
        ?
        <BdwTable
            api={listProject}
            rightButtonRender={() => tableDrop}
            fastButtonRender={fastButtonsRender}
            onRow={onRowClick}
            extraParams={
                {
                    // "globalSearch": globalSearch,
                    "type": currentTabPaneValue,
                    "status": status,
                    "leaderName": searchHeader,
                    "name": searchName,
                    "quickStatus": currentMenuValue === 'ALL' ? "" : currentMenuValue,
                    "template":false
                }
            }
            configPage={true}
            scroll={{
                scrollToFirstRowOnChange:true,
                y:600
            }}
        >
            <BdwTableColumn width={60} dataIndex='coverPictureUrl' title={
                <BdwTableHeaderSearchComponent title='' />
            } render={projectImgRender} />
            <BdwTableColumn width={220} ellipsis dataIndex='name' title={
                <BdwTableHeaderSearchComponent title='项目名称'>
                    <Input value={searchName} maxLength={128} onChange={(e) => setSearchName(e.target.value)} className='no-border-input'
                        placeholder='搜索项目名称' />
                </BdwTableHeaderSearchComponent>
            } />
            <BdwTableColumn width={80} dataIndex='statusName' title={
                <BdwTableHeaderSearchComponent title='状态'>
                    <Select placeholder='请选择...' className='select-width' bordered={false} onSelect={(val: any) => setStatus(val)}>{optionElement}</Select>
                </BdwTableHeaderSearchComponent>
            } />
            <BdwTableColumn width={80} title={
                <BdwTableHeaderSearchComponent title='负责人'>
                    <Input className='no-border-input' maxLength={128} onChange={(e) => setSearchHeader(e.target.value)} />
                </BdwTableHeaderSearchComponent>
            } dataIndex='leaderName' />
            <BdwTableColumn width={80} title={
                <BdwTableHeaderSearchComponent title='进度' />
            } dataIndex='schedule' render={(value: string) => <span>{value}</span>} />
            <BdwTableColumn width={100} title={
                <BdwTableHeaderSearchComponent title='截止日期' />
            } dataIndex='endDate' />
            <BdwTableColumn width={360} ellipsis title={
                <BdwTableHeaderSearchComponent title='当前进程' />
            } dataIndex='process' />
        </BdwTable>
        : <BdwCardTable
            singCardRender={cardTableItem}
            rightButtonRender={() => tableDrop}
            fastButtonRender={fastButtonsRender}
            // globalSearchRender={tableSearch}
            api={listProject}

            extraParams={
                {
                    "type": currentTabPaneValue,
                    // "status": status,
                    // "leaderName": searchHeader,
                    "name": searchName,
                    "quickStatus": currentMenuValue === 'ALL' ? "" : currentMenuValue,
                    "template":false
                }
            }
        />
    return <div className="my-project-container">
        <div className='project-tab-content'>
            <Tabs
                onChange={changeProjectName}
                type="card"
                items={tabsShowElement}
                activeKey={currentTabPaneValue}
            />
        </div>

        <div className='project-statistic-content mt-16'>
            <Menu
                mode="horizontal"
                selectedKeys={[currentMenuValue]}
                onClick={menuClickEvent}
                items={menuShowElement}
            />
        </div>
        <div className='project-statistic-table'>
            {showTableBtType}
        </div>


    </div>
}
export default MyProject;