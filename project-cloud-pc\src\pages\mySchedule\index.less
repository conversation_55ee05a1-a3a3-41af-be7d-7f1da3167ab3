.my_schedule_wrapper {
    display: flex;
    justify-content: space-between;
}
.datePicker_container {
    width: 253px;
    border-right: 1px solid #f2f2f2;
    .ant-radio-group {
        display: none;
    }
    .ant-picker-calendar-header {
        justify-content: flex-start;
    }
    .ant-checkbox {
        margin-right: 5px;
    }
    .datePicker_content {
        padding: 0 15px;
        .tag_wrapper {
            .color_span {
                display: inline-block;
                width: 12px;
                height: 12px;
                margin-right: 6px;
            }
            span {
                color: #696969;
            }
        }
    }
    .add_btn {
        border-radius: 3px;
        background-color: #1890FF;
        border-color: #1890FF;
        margin: 12px 0;
    }
    .date_item {
        border: 1px solid transparent;
    }
    .color_white {
        color: #FFF;
    }
}

.right_wrapper {
    width: 100%;
}


.table_title {
    font-size: 18px;
    font-weight: bold;
    padding: 5px 10px;
    margin-top: 10px;
}

.content_wrapper {
    height: calc(100vh - 48px);
    border-top: 1px solid #f2f2f2;
    .table_wrapper {
        padding: 15px 10px;
        border-right: 1px dotted #f2f2f2;
    }
    .schedule_content {
        padding: 15px 20px;
        .span_title {
            color: #5C5C5C;
            font-size: 14px;
            margin-top: 8px;
            margin-bottom: 3px;
        }
        h3 {
            color: #000;
            font-weight: bold;
        }
        p.schedule_explain {
            margin: 5px 0;
        }
        .schedule_durning {
            font-size: 15px;
            color: #000;
            margin-bottom: 0;
        }
        .schedule_remind {
            font-size: 13px;
            color: #999;
            margin-bottom: 0;
        }
        .divider_line {
            margin: 15px 0;
            border-top: 1px dotted #eee;
        }
        .customer_link, .data_wrapper span {
            color: #4288ca;
        }
        ul {
            padding: 0;
        }
        .data_wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            span {
                margin-left: 5px;
            }
        }
    }
}