import React, { useState, useEffect } from 'react';
import { Input, Row, Col, Calendar, Button, Checkbox } from "antd";
import { PlusOutlined } from '@ant-design/icons'
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import type { ColumnsType } from 'antd/es/table';
import CustomTable from "@/components/customTable";
import AddScheduleModal from '@/components/addScheduleModal';
import './index.less';
import { Moment } from 'moment';

interface IMyScheduleProps {

}
interface DataType {
	key: React.ReactNode
	id: string
	scheduleName: string
	progressTitle: string
	projectName: string
	workDate: string
	startDate: string
	endDate: string
	daily: string
	children?: DataType[] | null
}

// 任务色块及对应描述
const colorTagList = [
	{ id: 1, bgColor: '#EEE', text: '没有任务' }, 
	{ id: 2, bgColor: '#ACD5F2', text: '任务数目为1' }, 
	{ id: 3, bgColor: '#7FA8C9', text: '任务数目为2' }, 
	{ id: 4, bgColor: '#527BA0', text: '任务数目为3' }, 
	{ id: 5, bgColor: '#254E77', text: '任务数目为4以上' }, 
	{ id: 6, bgColor: '#0275D8', text: '代表今天' }
]

// 日历色块显示时间处理
const dateItemBorderColor = {
	'0task': '#EEE',  // 没有任务
	'2023-12-05': '#ACD5F2',  // 任务数为1
	'2023-12-19': '#7FA8C9',  // 任务数为2
	'2023-12-09': '#527BA0',  // 任务数为3
	'2023-12-23': '#254E77',  // 任务数4以上
	[dayjs(new Date()).format('YYYY-MM-DD')]: '#0275D8',  // 今天
	'clickDate': '#d9d9d9'
}

const MySchedule: React.FC<IMyScheduleProps> = () => {
	const [addScheduleModalVisible, setAddScheduleModalVisible] = useState<boolean>(false)  // 新增日程modal visible
	const [showScheduleList, setShowScheduleList] = useState<any[]>(['project', 'other'])  // 查看日程选项
	const [selectedDate, setSelectedDate] = useState<string>(dayjs(new Date()).format('YYYY-MM-DD'))	// 当前选择的日期，默认今日日期
	const [currentTableRowInfo, setCurrentTableRowInfo] = useState<any>(null) // 当前点击选中的日程信息

	const handleOnDateSelect = (date: any) => {
		setSelectedDate(dayjs(date).format('YYYY-MM-DD'))
	}

	// table columns
	const commonColumns: ColumnsType<DataType> = [{
		title: '进程标题',
		dataIndex: 'progressTitle',
		key: 'progressTitle',
		width: '30%',
		render: (item: string, record: DataType) => {
			// console.log(record, item, '<<<<<record');
			if (record.key == 0) {
				return <Input placeholder='搜索...' bordered={false} style={{ height: 23 }} onChange={(val) => console.log(val.target.value, '<<<<val')} />
			}
			return <span>{item}</span>
		}
	}, {
		title: '日程名称',
		dataIndex: 'scheduleName',
		key: 'scheduleName',
		width: '30%',
		render: (item: string, record: DataType) => {
			// console.log(record, item, '<<<<<record');
			if (record.key == 0) {
				return <Input placeholder='搜索...' bordered={false} style={{ height: 23 }} onChange={(val) => console.log(val.target.value, '<<<<val')} />
			}
			return <span>{item}</span>
		}
	}, {
		title: '项目名称',
		dataIndex: 'projectName',
		key: 'projectName'
	}, {
		title: '工期(天)',
		dataIndex: 'workDate',
		key: 'workDate'
	}, {
		title: '开始时间',
		dataIndex: 'startDate',
		key: 'startDate'
	}, {
		title: '截止时间',
		dataIndex: 'endDate',
		key: 'endDate'
	}, {
		title: '日报',
		dataIndex: 'daily',
		key: 'daily'
	}]

	// table data
	const tableData: DataType[] = [{
		key: 0,
		id: 't01',
		scheduleName: '',
		progressTitle: '',
		projectName: '',
		workDate: '',
		startDate: '',
		endDate: '',
		daily: '',
	}, {
		key: 1,
		id: 't02',
		progressTitle: '平台门户首页布局进程',
		scheduleName: '',
		projectName: '大项目名称',
		workDate: '7',
		startDate: '07-01',
		endDate: '08-01',
		daily: '1/1',
	}]

	// 其他日程table data
	const otherScheduleTableData: DataType[] = [{
		key: 0,
		id: 't03',
		scheduleName: '',
		progressTitle: '',
		projectName: '',
		workDate: '',
		startDate: '',
		endDate: '',
		daily: '',
	}, {
		key: 1,
		id: 't04',
		progressTitle: '',
		scheduleName: '其他日程标题',
		projectName: '其他日程项目名称',
		workDate: '3',
		startDate: '07-01',
		endDate: '08-01',
		daily: '',
	}]

	// 设置默认选中激活项
	// useEffect(() => {
	// 	setCurrentTableRowInfo(tableData[1])
	// }, [])

	// 自定义日期单元格样式
	const handleDateCellRender = (date: Moment) => {
		let dateItem = dayjs(date as Dayjs).format('YYYY-MM-DD');
		let dayItem = dayjs(date as Dayjs).format('DD');
		let currentDate = dayjs(new Date()).format('YYYY-MM-DD');
		let currentDay = dayjs(new Date()).format('DD');

		// 被渲染的单元格dom
		return <div
			title={dateItem}
			className={`${(dateItemBorderColor[dateItem] || dateItem == selectedDate) && 'color_white' } date_item`}
			style={{ background: dateItemBorderColor[dateItem] || (dateItem == selectedDate && '#D9D9D9') as string }}
		>
			{dayItem}
		</div>
	}

	console.log(currentTableRowInfo, '<<<currentTableRowInfo');

	return (
		<div className='my_schedule_wrapper'>
			{/* 日历 */}
			<div className="datePicker_container">
				<Calendar 
					fullscreen={false}
					dateFullCellRender={handleDateCellRender}
					onSelect={(date: Moment) => handleOnDateSelect(date)}
				/>

				<div className='datePicker_content'>
					{/* tag span */}
					<div className="tag_wrapper">
						{
							colorTagList.map((item) => (
								<div key={item.id}>
									<span className='color_span' style={{ background: item.bgColor }} />
									<span>{item.text}</span>
								</div>
							))
						}
					</div>

					<Button 
						type="primary" 
						className='add_btn'
						icon={<PlusOutlined />}
						onClick={() => setAddScheduleModalVisible(true)}
					>新增日程</Button>

					<br />
					<Checkbox checked={showScheduleList.length == 2} onChange={(e) => {
						if(e.target.checked) {
							setShowScheduleList(['project', 'other'])
						} else {
							setShowScheduleList([])
						}
					}}>查看全部日程</Checkbox>
					<br />
					<Checkbox.Group
						value={showScheduleList}
						onChange={(checkedList: any[]) => setShowScheduleList(checkedList)}
					>
						<Checkbox value='project'>查看项目日程</Checkbox>
						<br />
						<Checkbox value='other'>查看其他日程</Checkbox>
					</Checkbox.Group>
				</div>
			</div>
			
			{/* 右侧容器 */}
			<div className='right_wrapper'>
				<div className='table_title'>{selectedDate} 项目日程</div>
				<Row className='content_wrapper'>
					{/* 日程列表 */}
					<Col 
						span={currentTableRowInfo ? 16 : 24} 
						className='table_wrapper'
					>
						{/* 多选框选择项目日程 */}
						{
							showScheduleList.includes('project') && 
							<CustomTable 
								rowKey="id"
								data={tableData}
								isRowSelection={false}
								currentTableRowInfo={currentTableRowInfo}
								onRowClick={(clickItem: any) => setCurrentTableRowInfo(clickItem)}
								columns={commonColumns.filter((item: any) => !['scheduleName'].includes(item.dataIndex))}
							/>
						}

						{/* 多选框选择其他日程 */}
						{
							showScheduleList.includes('other') && 
							<>
								<div className='table_title' style={{ paddingLeft: 0 }}>{selectedDate} 其他日程</div>
								<CustomTable 
									rowKey="id"
									data={otherScheduleTableData}
									isRowSelection={false}
									currentTableRowInfo={currentTableRowInfo}
									onRowClick={(clickItem: any) => setCurrentTableRowInfo(clickItem)}
									columns={commonColumns.filter((item: any) => !['progressTitle', 'projectName', 'daily'].includes(item.dataIndex))}
								/>
							</>
						}
					</Col>

					{/* 日程详情 */}
					<Col span={currentTableRowInfo ? 8 : 0} >
						<div className="schedule_content">
							<div className='span_title'>日程名称</div>
							<h3>日程名称，日程名称日程名称日程名称日程名称</h3>
							<div className='span_title'>日程说明</div>
							<p className='schedule_explain'>
								1、把这件事做了。<br />
								2、把那件事做了。<br />
								3、把所有事都做了。
							</p>
							<div className='span_title'>日程持续时间</div>
							<p className='schedule_durning'>2020-09-21 9:00 至 2022-09-21 18:00</p>
							<span className='schedule_remind'>任务开始五小时之前提醒我</span>

							<div className="divider_line" />

							<div className='span_title'>日程关联客户</div>
							<p className='customer_link'>小帅鸽</p>

							<span style={{ marginTop: 10 }}>日程参与人员</span>
							<ul>
								<li>小柳-销售总监 @ 钛合金设计中心</li>
								<li>奥张-设计总监 @ 钛合金设计中心</li>
							</ul>
							<div className='span_title'>日程附件资料</div>
							<div className='data_wrapper'>
								<div>
									<img src="资料" alt="资料icon" />
									<span>施工图纸包V1.zip</span>
								</div>
								<img src="下载" alt="下载icon" />
							</div>
						</div>
					</Col>
				</Row>
			</div>
			<AddScheduleModal 
				visible={addScheduleModalVisible} 
				onCancel={() => setAddScheduleModalVisible(false)} 
				onSubmit={() => {}} 
			/>
		</div>
	)
}

export default MySchedule;
