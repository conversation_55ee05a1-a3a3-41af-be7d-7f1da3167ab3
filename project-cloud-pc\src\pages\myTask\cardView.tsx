import React, { useState, useEffect } from "react";
import { useSelector, useDispatch, connect, Loading } from 'umi';
import {
	FieldTimeOutlined,
	HourglassOutlined,
	FileDoneOutlined,
	StopOutlined
} from "@ant-design/icons";
import { PageLoading } from "@/components";
import TaskCardViewItem from "@/components/TaskCardViewItem";
import { cloneDeep } from "lodash";
import './index.less';

interface TabDataItemInterface {
	icon: () => React.ReactNode
	name: string
	code: string
	dataList: any[]
}
export interface ItemProps {
	projectId: string
	projectName: string
	projectText: string
	projectStatus: string
	projectStatusText: string
	projectManager: string
	projectDesc: string
	taskId:string
}

const MyTaskCardView = ({
	projectId,
	viewMethod,
	loading
}: {
	projectId: string | null
	viewMethod: string
	loading: Loading
}) => {
	const dispatch = useDispatch();
	const [checkedItemList, setCheckedItemList] = useState<any[]>([])  // 已选任务项list
	
	const [waitDeleList, setWaitDeleList] = useState<any[]>([])  // 待处理数据
	const [onGoingList, setOnGoingList] = useState<any[]>([])  // 进行中数据
	const [finishedList, setFinishedList] = useState<any[]>([])  // 已完成数据

	const [cardDataList, setCardDataList] = useState<TabDataItemInterface[]>([{
		icon: () => <FieldTimeOutlined/>,
		name: "待处理",
		code: "WAIT_DEAL",
		dataList: []
	}, {
		icon: () => <HourglassOutlined/>,
		name: "进行中",
		code: "ON_GOING",
		dataList: []
	}, {
		icon: () => <FileDoneOutlined/>,
		name: "已完成",
		code: "FINISHED",
		dataList: []
	}, {
		icon: () => <StopOutlined/>,
		name: "已暂停",
		code: "Suspended",
		dataList: []
	}])  // 页面总的任务项list

	// 获取所有我的任务数据
	useEffect(() => {
		if (viewMethod == 'CARD') {
			handleGetTableData('WAIT_DEAL')  // 待处理
			handleGetTableData('ON_GOING')  // 进行中
			handleGetTableData('FINISHED')  // 已完成
		}
	}, [viewMethod, projectId])

	// 处理获取到的所有任务数据
	useEffect(() => {
		let taskObj = {
			WAIT_DEAL: waitDeleList,
			ON_GOING: onGoingList,
			FINISHED: finishedList
		}
		let newArr = cardDataList.map((item) => ({
			...item,
			dataList: taskObj[item.code]
		}))
		// 更新state
		setCardDataList(newArr)
	}, [waitDeleList, onGoingList, finishedList])

	// 任务项选中状态改变
	const handleOnCheckedChange = (checkedFlag:boolean, checkedItem: any) => {
		const { projectId } = checkedItem;
		let newArr: any[] = cloneDeep(checkedItemList)
		// 已存在list中且checkedFlag状态为false，则将选中项从list中移除
		if(newArr.map((item: ItemProps) => item.projectId).includes(projectId) && !checkedFlag) {
			newArr = newArr.filter((item: ItemProps) => item.projectId !== projectId)
		} else { // 将已选的item push进list中
			newArr.push(checkedItem)
		}
		// 更新list
		setCheckedItemList(newArr)
	}

	// 获取列表数据
	const handleGetTableData = async (currentTabKey: string) => {
		await dispatch({
			// 请求table数据
			type: 'myTask/fetchTaskTableData',
			tableType: currentTabKey,
			payload: {
				projectId
			},
			onSuccess: (res: any) => {
				let newArr = []
				// 处理返回的数据
				if (res.items.length) {
					newArr = res.items.map((item: any) => ({
						...item,
						projectId: item.projectId,
						projectName: item.name,
						projectText: item.message,
						projectStatus: item.statusCode || null,
						projectStatusText: item.statusName || null,
						projectManager: item.leaderName,
						projectDesc: item.projectName
					}))
				}
				// 设置state数据
				switch (currentTabKey) {
					case 'WAIT_DEAL':
						setWaitDeleList(newArr)
						break;
					case 'ON_GOING':
						setOnGoingList(newArr)
						break;
					case 'FINISHED':
						setFinishedList(newArr)
						break;
					default:
						break;
				}
			},
			onFailed: (errInfo: any) => { }
		})
	}

	return <div className="card_view_wrapper">
		{loading ? (
			<PageLoading />
		) : (
		<div className="content_wrapper">
			<div className="card_wrapper">
				{
					cardDataList.map((item: TabDataItemInterface) => (
						<TaskCardViewItem key={item.code} itemInfo={item} handleOnCheckedChange={handleOnCheckedChange} />
					))
				}
			</div>
		</div>
		)}
	</div>
}

export default  connect(({ loading }: any) => ({
  loading: loading.effects['myTask/fetchTaskTableData']
}))(MyTaskCardView);;
