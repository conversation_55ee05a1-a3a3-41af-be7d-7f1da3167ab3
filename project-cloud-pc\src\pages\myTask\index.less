@import "../../styles/base";

.my_task_wrapper {
    background-color: #fafafa;
    padding: 10px 18px;
    height: 100%;
    box-sizing: border-box;
    overflow: hidden;

    .card_view_wrapper {
        height: 100vh;
        overflow: auto;
        padding-bottom: 20px;
        .content_wrapper {
            width: 1260px;
            overflow-y: hidden;
            .card_wrapper {
                overflow: hidden;
                padding: 0 5px;
            }
        }
    }

    .table_view_wrapper {
        height: 100%;
        background-color: #FFF;
    }

    .header_wrapper {
        margin-bottom: 10px;
    }
    .ant-tabs.ant-tabs-card .ant-tabs-nav {
        padding-left: 15px;
    }
    .ant-pagination-item, .ant-pagination-jump-next, .ant-pagination-options, .ant-pagination-jump-prev {
        display: none;
    }
    .tabs_wrapper {
        background-color: #FFF;
        padding-top: 10px;
        .ant-tabs-tab .anticon {
            margin-right: 8px;
        }
        .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab {
            padding: 6px 28px !important;
        }
        .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab.ant-tabs-tab-active {
            font-weight: normal;
            color: #222;
        }
        .operate_wrapper {
            padding: 5px 0;
            .left_wrapper {
                align-items: center;
                justify-content: space-between;
                .operate_bar {
                    min-width: 440px;
                    margin: 0;
                    padding: 0;
                    li {
                        cursor: pointer;
                        margin-right: 15px;
                        color: #014C8C;
                        span {
                            margin-left: 3px;
                        }
                    }
                    .not_allowed {
                        opacity: .45;
                        cursor: not-allowed;
                    }
                }
                .search_span {
                    display: inline-block;
                    line-height: 32px;
                }
                .search_wrapper {
                    position: relative;

                    .search_wrapper_content {
                        border-bottom: 1px solid #212121;
                        &:hover {
                            border-width: 2px;
                        }
                        .ant-input-affix-wrapper:focus, .ant-input-affix-wrapper-focused {
                            // display: none;
                            box-shadow: unset;
                        }
                    }

                    .MuiAutocomplete-endAdornment {
                        right: 20px;
                    }
                    .search_icon {
                        width: 16px;
                    }
                }
            }
        }
    }
}