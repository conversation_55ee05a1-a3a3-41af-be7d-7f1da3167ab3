import { useEffect, useState, createRef } from "react";
import { connect, useSelector, useDispatch, Loading } from 'umi';
import { BarsOutlined, AppstoreOutlined } from '@ant-design/icons';
import { HeaderBar, PageLoading } from "@/components";
import MyTaskTableView from './tableView';
import MyTaskCardView from './cardView';
import './index.less';

interface IMyTaskProps {
	loading: Loading
}

const MyTask = ({
	loading,
}: IMyTaskProps) => {
	const dispatch = useDispatch()
	const { projectList, viewMethod } = useSelector((state: any) => state.myTask);  // 所有的项目数据
	const [currentProjectId, setCurrentProjectId] = useState<string | null>(null);  // 当前选中的项目id，null为所有项目

	// 获取table视图tab栏状态数据
	useEffect(() => {
		dispatch({
			type: 'myTask/fetchTaskStatusList'
		})
	}, [])

	useEffect(() => {
		dispatch({ type: 'myTask/fetchTeamProjectList' })  // 获取项目切换数据
	}, [])

	// 视图改变时
	const handleChangeView = (checkedView: string) => {
		dispatch({ 
			type: 'myTask/setViewMethod',
			payload: checkedView
		})
	}

	// 右侧视图下拉框数据
	const viewList = [{
		title: '列表视图',
		key: 'TABLE',
		icon: () => <BarsOutlined />
	}, {
		title: '看板视图',
		key: 'CARD',
		icon: () => <AppstoreOutlined />
	}]

	return <div className="my_task_wrapper">
		{/* 头部项目、视图切换 */}
		{loading ? (
        <PageLoading />
      ) : (
				<>
					<HeaderBar 
						viewList={viewList}
						projectList={projectList || []}
						onViewChange={(checkedView: string) => handleChangeView(checkedView)}
						onProjectChange={(checkedProject: string) => setCurrentProjectId(checkedProject)}
					/>
					{ viewMethod == "TABLE" ? <MyTaskTableView projectId={currentProjectId} /> : <MyTaskCardView projectId={currentProjectId} viewMethod={viewMethod} /> }
				</>
			)}

	</div>
}

export default connect(({ loading }: any) => ({
  loading: loading.effects['myTask/fetchTaskStatusList']
}))(MyTask);
