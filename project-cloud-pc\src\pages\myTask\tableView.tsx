import React, { useEffect, useState } from "react";
import { useSelector, useDispatch, history, connect, Loading } from 'umi';
import { Input, Select, Pagination, Tabs } from 'antd';
import { 
	CaretDownOutlined, 
	FieldTimeOutlined,
	HourglassOutlined,
	FileDoneOutlined,
	StopOutlined,
	SearchOutlined
} from '@ant-design/icons';
import { BMCTextField } from 'bmc-app-react-component';
import CustomTable from "@/components/customTable";
import type { ColumnsType } from 'antd/es/table';
import type { TabsProps } from 'antd';
import TaskReplyModal from '@/components/taskReplyModal';
import DispatchTaskDrawer from "@/pages/my-project-detail/projectTasks/component/dispatch-task-drawer";
import AcceptTaskDrawer from '@/pages/my-project-detail/projectTasks/component/accept-task-drawer';
import { listTaskFunctionality } from "@/service/projectDos/my-project-detail/projectTasks";
import { TaskFunctionCode, BtnKey, BtnOperateType } from '@/constants/Enum';
import './index.less';

interface DataType {
	key: React.ReactNode
	name: string
	age: string
	address: string
	children?: DataType[] | null
}
interface IMyTaskTableViewProps {
	loading: Loading
	projectId: string | null
}

// 按钮功能是否可用
export const disabledFlag = (arr: any[], type: string, functionCode = BtnKey.taskFunctionCode, usable = 'usable') => {
	if (!arr || (arr && arr.length == 0)) return true;
	const available = arr?.filter((item: any) => item[functionCode] == type);
	return !available[0][usable];
}

const MyTaskTableView = ({
	loading,
	projectId
}: IMyTaskTableViewProps) => {
	const dispatch = useDispatch();
	const { taskStatusList, tableData, paginationInfo } = useSelector((state: any) => state.myTask);	// 从models中获取数据
	const { functionality } = useSelector((state: any) => state.projectTasks);	// 当前操作按钮是否可用
	const [tabDataList, setTabDataList]= useState<TabsProps['items'] | []>([]) // tab data
	const [activeTabKey, setActiveTabKey] = useState<string>(taskStatusList[0]?.name);  // 当前激活的tab key
	const [currentClickSelectedItem, setCurrentClickSelectedItem] = useState<any>(null);  // 当前点击选中任务项的数据
	const [currentSelectedRows, setCurrentSelectedRows] = useState<any[]>([]);  // 当前选中的所有行的key集合
	const [assignType, setAssignType] = useState<any>(null);  // modal type
	const [dispatchTaskModalVisible, setDispatchTaskModalVisible] = useState<boolean>(false);  // 分派modal
	const [receiveReturnTaskModalVisible, setReceiveReturnTaskModalVisible] = useState<boolean>(false);  // 接收退回modal
	const [replyModalVisible, setReplyModalVisible] = useState<boolean>(false);  // 回复modal

	const [searchValue, setSearchValue] = useState<any>(null); /// 搜索框value

	const [currentPage, setCurrentPage] = useState(1)  // 当前页
	const [pageSize, setPageSize] = useState(10)  // 当前一页显示条数
	const [total, setTotal] = useState(0) // 数据总条数

	const tabIcon = {
		'WAIT_DEAL': <FieldTimeOutlined/>,
		'ON_GOING': <HourglassOutlined/>,
		'FINISHED': <FileDoneOutlined/>,
		'PAUSE': <StopOutlined/>,
	}

	// 页面初始化时清空store中的可操作按钮数据
	useEffect(() => {
		handleResetBtnAuthorityStoreFunc()
	}, [])

	// 监听到数据变化时重新渲染视图
	useEffect(() => {
		// 处理tab数据
		if (taskStatusList.length) {
			// 处理tab数据，需同时监听服务端tab数据及当前激活tab key
			let newArr = taskStatusList.map((item: { name: string, value: string }) => ({
				label: (
					<span>
						{tabIcon[item.name]}
						{item.value}
					</span>
				),
				key: item.name,
				disabled: item.name == 'PAUSE',
				children: renderTableContent(item.name)
			}))
			setTabDataList(newArr)
		}
	}, [
		taskStatusList, 
		activeTabKey, 
		tableData, 
		total, 
		currentPage, 
		currentPage, 
		functionality, 
		loading,
		currentClickSelectedItem
	])

	useEffect(() => {
		// 初始化获取table数据
		if (taskStatusList.length) {
			handleGetTableData(taskStatusList[0].name)
		}
	}, [taskStatusList, projectId])

	// table columns
	const commonColumns: ColumnsType<DataType> = [{
		title: '任务名称',
		dataIndex: 'name',
		key: 'name',
		width: '30%',
		render: (item: string, record: any) => {
			if (record.key == 0) { // 首行的过滤功能
				return (
					<Input 
						placeholder='搜索...' 
						bordered={false} 
						style={{ height: 23 }} 
						onChange={(val) => console.log(val.target.value, '<<<<val')} 
					/>
				)
			}
			return (
				<span 
					style={{ cursor: 'pointer' }} 
					title="点击跳转至所属项目" 
					onClick={() => history.push(`/my-project-detail/${record.projectId}?taskId=${record.taskId}`)}
				>{item}</span>
			)
		}
	}, {
		title: '执行人',
		dataIndex: 'leaderName',
		key: 'leaderName',
		render: (item: string, record: any) => {
			if (record.key == 0) { // 首行的过滤功能
				return (
					<Select 
						options={[]} 
						bordered={false}
						style={{ width: '100%', height: 23 }} 
						suffixIcon={<CaretDownOutlined />} 
						onChange={(val) => console.log(val.target.value, '<<<<val')} 
					/>
				)
			}
			return <span>{item}</span>
		}
	}]

	// 待处理
	const todoColumns: ColumnsType<DataType> = [{
		title: '通知日期',
		dataIndex: 'date',
		key: 'date'
	}, {
		title: '状态',
		dataIndex: 'statusName',
		key: 'statusName',
		render: (item: string, record: any) => {
			if (record.key == 0) {
				return (
					<Select
						options={[]}  
						bordered={false} 
						style={{ width: '100%', height: 23 }} 
						suffixIcon={<CaretDownOutlined />} 
						onChange={(val) => console.log(val.target.value, '<<<<val')} 
					/>
				)
			}
			return <span>{item}</span>
		}
	}, {
		title: '已过(天)',
		dataIndex: 'passedDays',
		key: 'passedDays',
		render: (item: string, record: any) => <span>{Number(item) && item || '-'}</span>
	}, {
		title: '消息提醒',
		dataIndex: 'message',
		key: 'message',
		width: '30%'
	}]

	// 进行中
	const doingColumns: ColumnsType<DataType> = [{
		title: '截止日期',
		dataIndex: 'endDate',
		key: 'endDate'
	}, {
		title: '剩余(天)',
		dataIndex: 'remainingDays',
		key: 'remainingDays',
		render: (item: string, record: any) => {
			if (record.key == 0) {
				return (
					<Select 
						options={[]} 
						bordered={false} 
						style={{ width: '100%', height: 23 }} 
						suffixIcon={<CaretDownOutlined />} 
						onChange={(val) => console.log(val.target.value, '<<<<val')} 
					/>
				)
			}
			return <span>{item}</span>
		}
	}, {
		title: '汇报/应报',
		dataIndex: 'process',
		key: 'process',
	}, {
		title: '消息提醒',
		dataIndex: 'message',
		key: 'message',
		width: '30%'
	}]

	// 已完成
	const doneColumns: ColumnsType<DataType> = [{
		title: '标准分',
		dataIndex: 'standardScore',
		key: 'standardScore',
		render: (item: string) => {
			return <span>{item || '-'}</span>
		}
	}, {
		title: '考评分',
		dataIndex: 'standardScore',
		key: 'standardScore',
		render: (item: string, record: any) => {
			if (record.key == 0) {
				return (
					<Select  
						options={[]}  
						bordered={false}  
						style={{ width: '100%', height: 23 }}  
						suffixIcon={<CaretDownOutlined />} 
						onChange={(val) => console.log(val.target.value, '<<<<val')} 
					/>
				)
			}
			return <span>{item || '-'}</span>
		}
	}, {
		title: '评审人',
		dataIndex: 'reviewUserName',
		key: 'reviewUserName'
	}, {
		title: '评审备注',
		dataIndex: 'reviewRemark',
		key: 'reviewRemark',
		width: '30%'
	}]

	// table columns obj
	const changedColumns = {
		'WAIT_DEAL': todoColumns,
		'ON_GOING': doingColumns,
		'FINISHED': doneColumns,
	}

	// operate bar btn list	
	const operateBarBtnList = [{
		icon: () => <SearchOutlined className="search_icon" />,
		title: '分派',
		key: 'assign',
		disable: disabledFlag(functionality, TaskFunctionCode.ASSIGNMENT)
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '接收',
		key: 'receive',
		disable: disabledFlag(functionality, TaskFunctionCode.RECEIVE)
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '退回',
		key: 'return',
		disable: disabledFlag(functionality, TaskFunctionCode.RETURN)
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '提报',
		key: 'submit',
		disable: true
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '回复',
		key: 'apply',
		disable: disabledFlag(functionality, TaskFunctionCode.PROCESS_REPLY)
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '审核',
		key: 'examine',
		disable: disabledFlag(functionality, TaskFunctionCode.AUDIT_APPLY)
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '驳回',
		key: 'reject',
		disable: true
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '导出',
		key: 'export',
		disable: true
	}]

	// pageInfo render
	const showTotalPaging = (total: number): any => {
		return <div>
			<span className="fw-b">{(currentPage - 1) * pageSize + 1}</span>
			&nbsp;至&nbsp;
			<span className="fw-b">{currentPage * pageSize > total! ? total : currentPage * pageSize}</span>
			&nbsp;共计&nbsp;
			<span className="fw-b">{total}</span>
		</div>
	}

	// 获取任务历程
	const handleGetTaskFlowList = () => {
		dispatch({
			type: 'projectOverview/fetchProjectOverview',
			payload: currentClickSelectedItem?.projectId
		})
	}

	// table 操作栏click
	const handleBtnClick = (btnKey: string) => {
		handleGetTaskFlowList()  // 获取任务历程
		switch (btnKey) {
			case 'assign': // 分派
				setDispatchTaskModalVisible(true)
				setAssignType(BtnOperateType.ASSIGNMENT)
				break;
			case 'receive': // 接收
				setReceiveReturnTaskModalVisible(true);
				setAssignType(BtnOperateType.RECEIVE)
				break;
			case 'return': // 退回
				setReceiveReturnTaskModalVisible(true);
				setAssignType(BtnOperateType.RETURN)
				break;
			case 'apply': // 回复
				setReplyModalVisible(true)
				break;
			case 'examine': // 审核
				console.log('<<<审核');
				break;
			case 'submit': // 提报
				break;
			case 'reject': // 驳回
				break;
			case 'export': // 导出
				console.log('<<<导出');
				break;
		
			default:
				break;
		}
	}

	// 查询选中任务数据按钮操作权限
	const handleGetTaskBtnAuthorityData = (currentTaskItem: any) => {
		listTaskFunctionality({
			projectId: currentTaskItem.projectId, 
			taskId: currentTaskItem.taskId 
		}).then((res: any) => {
			dispatch({
				type: 'projectTasks/setFunctionality',
				payload: res
			})
		})
	}

	// 清空store中的按钮权限数据
	const handleResetBtnAuthorityStoreFunc = () => {
		dispatch({
			type: 'projectTasks/setFunctionality',
			payload: []
		})
	}

	// 列选择时
	const handleOnRowSelected = (record: Object, selected: boolean, selectedRows: any[]) => {
		// console.log(record, selected, selectedRows, '<<<<列选择时');

		let currentItem = selectedRows[selectedRows.length - 1]
		setCurrentClickSelectedItem(currentItem) // 存储当点击前选中项的数据

		if (selectedRows.length) {
			setCurrentSelectedRows(selectedRows.map(item => item.taskId))
			if (selectedRows.length > 1) { // 所选任务大于一项则清空权限数据
				handleResetBtnAuthorityStoreFunc()
			} else { 
				// 查询当前任务的按钮的操作权限
				handleGetTaskBtnAuthorityData(currentItem)
			}
		} else {
			// 清空table按钮权限
			handleResetBtnAuthorityStoreFunc()
			setCurrentSelectedRows([])
		}
	}

	// 渲染table及分页器
	const renderTableContent = (contentType: string) => {
		let tabList = activeTabKey == 'WAIT_DEAL' ? operateBarBtnList.filter(item => item.key !== 'export') : operateBarBtnList.filter(item => item.key == 'export')

		return (
			<div style={{ padding: '0 15px' }}>
				<div className="operate_wrapper flex-w">
					<div className="left_wrapper flex-w">
						{/* 操作栏按钮 */}
						<ul className="operate_bar flex-w">
							{
								tabList.map((item) => (
									<li
										key={item.key} 
										className={item.disable && 'not_allowed' || ''}
										onClick={() => !item.disable && handleBtnClick(item.key)}
										title={['submit', 'reject', 'export'].includes(item.key) && '功能暂未开通' || item.title } 
									>
										{item?.icon()}
										<span>{item.title}</span>
									</li>
								))
							}
						</ul>
						{/* 搜索框 */}
						<div className="flex-w search_wrapper">
							<span className="search_span">搜索任务：</span>
							{/* <BMCTextField 
								value={searchValue}
								sx={{ width: 240 }} 
								iconRight={<SearchOutlined className="search_icon" />}
								// TODO 触发待优化
								onChange={(value: string) => {
									setSearchValue(value)
									handleGetTableData(activeTabKey, { name: value })
								}}
							/> */}
							<div className="search_wrapper_content">
								<Input 
									suffix={<SearchOutlined className="search_icon" />}
									value={searchValue}
									style={{ width: 240, border: 'none' }}
									onChange={(value: any) => {
										setSearchValue(value.target.value)
										handleGetTableData(activeTabKey, { name: value.target.value })
									}} 
								/>
							</div>

						</div>
					</div>

					

					<Pagination 
						size='small'
						total={total}
						current={currentPage}
						defaultCurrent={currentPage}
						onChange={onPagingChange}
						showTotal={showTotalPaging} 
					/>
				</div>
				<CustomTable
					rowKey="taskId"
					tableLoading={loading}
					data={tableData}
					isRowSelection={true}
					selectedRowKeys={currentSelectedRows}
					onSelectRow={(record: Object, selected: boolean, selectedRows: any[]) => handleOnRowSelected(record, selected, selectedRows)}
					columns={commonColumns.concat(changedColumns[activeTabKey])}  // 不同状态的项目公用table组件，columns须处理
				/>
			</div>
		)
	}

	// 分页onchange
	const onPagingChange = (currentPageInfo: number) => {
		setCurrentPage(currentPageInfo)

		handleGetTableData(activeTabKey, { page: currentPageInfo })
	}

	// 获取列表数据
	const handleGetTableData = (currentTabKey: string, params?:{ name?: string, page?: number }) => {
		dispatch({
			// 请求table数据
			type: 'myTask/fetchTaskTableData',
			tableType: currentTabKey,
			payload: {
				projectId: projectId,
				name: params?.name || '',
				page: params?.page || 1,
				count: 10,
			},
			onSuccess: (res: any) => {
				// setCurrentPage(res.page)
				setPageSize(res.count)
				setTotal(res.total)
			},
			onFailed: (errInfo: any) => {}
		})
	}

	// tab切换
	const handleTabClick = (currentTabKey: string) => {
		setActiveTabKey(currentTabKey)
		setSearchValue(null) // 清空搜索框数据
		setCurrentClickSelectedItem(null)
		setCurrentSelectedRows([])
		handleGetTableData(currentTabKey)
	}

	return <div className="table_view_wrapper">
		{/* 项目状态tab切换 */}
		<div className="tabs_wrapper">
			<Tabs
				type="card"
				items={tabDataList} 
				onChange={handleTabClick} 
				defaultActiveKey={activeTabKey}
			/>
		</div>
		{/* 任务分派 */}
		{
			currentClickSelectedItem && dispatchTaskModalVisible && assignType &&
			<DispatchTaskDrawer
				assignType={assignType}
				visible={dispatchTaskModalVisible}
				task={currentClickSelectedItem}
				cancelEvent={() => {
					setAssignType(null)
					setDispatchTaskModalVisible(false)
				}}
				successEvent={() => {
					setAssignType(null)  // 清空assignType
					// handleGetTaskBtnAuthorityData(currentClickSelectedItem)  // 查询按钮操作权限接口
					setCurrentClickSelectedItem(null)
					handleGetTableData(activeTabKey) // 刷新列表数据
				}}
			/>
		}
		{/* 任务接收退回 */}
		{
			currentClickSelectedItem && receiveReturnTaskModalVisible && assignType &&
			<AcceptTaskDrawer
				assignType={assignType}
				visible={receiveReturnTaskModalVisible}
				task={currentClickSelectedItem}
				cancelEvent={() => {
					setAssignType(null);
					setReceiveReturnTaskModalVisible(true);
				}}
				successEvent={() => {
					setAssignType(null)  // 清空assignType
					// handleGetTaskBtnAuthorityData(currentClickSelectedItem) // 查询按钮操作权限接口
					setCurrentClickSelectedItem(null)
					handleGetTableData(activeTabKey) // 刷新列表数据
				}}
			/>
		}
		{/* 回复modal */}
		<TaskReplyModal 
			taskInfo={currentClickSelectedItem}
			visible={replyModalVisible}
			onCancel={() => setReplyModalVisible(false)}
			onSubmit={() => {
				setAssignType(null)  // 清空assignType
				setCurrentClickSelectedItem(null)
				setReplyModalVisible(false)
				handleGetTableData(activeTabKey) // 刷新列表数据
			}}
		/>
	</div>
}

export default connect(({ loading }: any) => ({
  loading: loading.effects['myTask/fetchTaskTableData'],
}))(MyTaskTableView);
