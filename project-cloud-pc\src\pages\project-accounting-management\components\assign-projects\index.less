.row_project_container {
  .left_label {
    width: 98px;
    text-align: right;
    display: inline-block;
    color: #666;
    white-space: nowrap;
  }

  .project_col {
    display: flex;
    align-items: center;
  }

}

.order_project_check {
  .ant-checkbox-wrapper {
    span {
      color: #000 !important;
    }
  }
}
.spin_wrapper {
  width: 100%;
  height: 100vh;
  position: absolute;
  bottom: 0;
  right: 15px;
  z-index: 100000;
  background-color: #eaecef;
  opacity: 0.7;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.focus_input_ratio {
  .ant-input-number-focused {
    box-shadow: unset !important;
  }

  .ant-input-number-input {
    padding-left: unset !important;
  }
}

// 线条样式
.line_gap {
  height: 1px;
  background-image: linear-gradient(to right, #ddd 60%, rgba(247, 247, 247, 0.0) 0%);
  background-position: bottom;
  background-size: 10px 1px;
  background-repeat: repeat-x;
  margin: 12px 0;
  margin-right: 16px;
  margin-top: 0px;
}

.select_temp_list {
  .ant-select {
    border-bottom: 1px solid #eaecef;
    color: #000 !important;
  }

  .ant-select-disabled.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    background-color: unset !important;
    color: #000 !important;
  }
}

.create-project-modal {
  background: red;
  box-sizing: border-box;
  padding: unset;
  width: 100vw;
  height: 100vh;
  max-width: unset;
  top: 0;
  left: 0;

  .ant-modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .ant-modal-body {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      padding: unset;
    }
  }

  .ant-spin-nested-loading {
    height: 100%;

    .ant-spin-container {
      height: 100%;

      .create-project-content {
        display: flex;
        align-items: center;
        overflow: hidden;
        height: 100%;

        .left-content {
          width: 350px;
          flex-shrink: 0;
          border-right: 1px solid #eaecef;
          padding: 10px 15px;
          box-sizing: border-box;
          overflow-y: auto;
          height: inherit;

          .header_tip {
            font-size: 15px;
            color: #000;
            font-weight: 700;
            font-family: '微软雅黑';
            margin-bottom: 10px;
          }

          .ant-tabs-nav-list {
            border-right: unset !important;
          }

          .ant-tabs.ant-tabs-card .ant-tabs-nav {
            padding-left: 0px !important;
          }

          .ant-tabs-tab {
            border: unset !important;
            width: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: unset !important;
            height: 28px;

            .ant-tabs-tab-btn {
              color: #000 !important;
            }
          }

          .ant-tabs-tab-active {
            background-color: #0275d8 !important;
            color: #fff !important;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;

            .ant-tabs-tab-btn {
              color: #fff !important;
            }
          }

          .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
            color: #fff !important;
            font-size: 12px;
          }

          .ant-tabs-content-holder {
            border-top: 2px solid #0275d8;
          }

          .ant-collapse-item {
            background-color: #f7f9fc;
            font-weight: 700;

            .ant-collapse-header {
              color: #797d90;
            }

            .ant-collapse-content-box {
              background-color: #fff;
            }
          }

          .ant-collapse-item-active {
            background-color: #dfe8f6;

            .ant-collapse-header {
              color: #000;
            }
          }

          .template_list_box {
            margin: 0 20px 15px;
            background-color: #dfe8f6;
            border-radius: 5px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: space-between;
          }

          .selected_template_type {
            background-color: #3f78fe;

            span {
              color: #fff;
            }
          }

          .sub_collapse_box {
            .ant-collapse-item {
              border-radius: 5px;
              margin-bottom: 5px;

              .ant-collapse-header {
                background-color: #dfe8f6;
                border-bottom: unset !important;
              }
            }

            .ant-collapse-item-active {
              .ant-collapse-header {
                background-color: #3f78fe;
                color: #fff;
              }
            }
          }

          .selected_subject {
            background-color: #2b6bff;

            span {
              color: #fff;
            }
          }

          .template_collapse_btn_area {
            display: flex;
            height: 100%;
            align-items: center;

            .created_subject {
              font-size: 10px;
              background-color: #f1faf1;
              color: #008000;
              transform: scale(0.8);
              padding: 2px;
            }
          }

          .add_template_btn {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 24px !important;
            width: 24px !important;
            background: linear-gradient(90deg, rgba(2, 117, 216, 1), #5aa5e5);
            box-shadow: 2px 0px 12px 0px rgba(2, 90, 165, 0.3);
          }

          .ant-collapse-header {
            border-bottom: 1px solid #eaecef;
          }

          .ant-tabs-ink-bar {
            background-color: #2b6bff !important;
          }

          .ant-table.ant-table-small .ant-table-title,
          .ant-table.ant-table-small .ant-table-footer,
          .ant-table.ant-table-small .ant-table-thead>tr>th,
          .ant-table.ant-table-small .ant-table-tbody>tr>td,
          .ant-table.ant-table-small tfoot>tr>th,
          .ant-table.ant-table-small tfoot>tr>td {
            padding: 4px;
            font-size: 12px;
          }
        }

        .empty_template_left {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .right-content {
          padding: 0px 5px 10px;
          overflow-y: auto;
          flex: 1;
          height: inherit;
          position: relative;
          .header-label {
            height: 48px;
            display: flex;
            align-items: center;
            font-weight: bold;
            letter-spacing: 2px;
          }

          .add_edit_template_modal {
            position: absolute;
            top: 0px;
            left: 0px;
            height: 100%;
            background-color: #fff;
            z-index: 1000;
            width: 100%;
            display: flex;
            flex-direction: column;

            .template_modal_header {
              font-size: 14px;
              font-weight: 700;
              color: #000;
              margin-left: 15px;
              margin-top: 10px;
              flex-shrink: 0;
            }
          }

          .project_item_list_title {
            font-size: 12px;
            color: #5c5c5c;
            margin-bottom: 10px;
          }

          .template_container {
            flex: 1;
          }

          .empty_template_box {
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }

  .left_line {
    display: inline-block;
    width: 2px;
    height: 16px;
    background-color: #2b6bff;
    margin-right: 10px;
  }
}
