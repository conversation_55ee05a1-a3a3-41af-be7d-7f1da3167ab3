import React, { useState, useEffect } from "react";
// antd
import { Row, Col, message, Modal, Checkbox, InputNumber, Select, Spin, Collapse, Tabs, Empty, Button } from 'antd';

import { useParams, useSelector, useDispatch, history, useLocation } from 'umi';
// service
// import { ProjectTypeEnum } from "@/service/project-enum";

// img
import Add_IMG from '@/assets/image/CREATE.svg';
import CREATE_BLUE from '@/assets/image/CREATE_BLUE.svg';
import TEMPLATE_SVG from '@/assets/image/save-template.svg';

// bdw
import {
    BdwFormItem,
    BdwRow,
    BdwInput, BdwSelect,
    BdwChooseCompanyStaff
} from "@/components";

import TemplateTable from '../template-table';  //模板编辑
import ProjectList from '../project-list';
// service
import { addressBookCommon, getTemplateListApi } from '@/service/projectDos/commonApi';
import {
    assignProject,
    batchAssignApi,
    splitProject<PERSON>pi
} from '@/service/projectDos/my-project-detail/projectTasks';
import { templateListTask } from '@/service/projectDos/myProjectApi';
import { getProjectAssignCategory, getLastTemplate, getInitTaskInfo, editOrCreateTemplate } from '@/service/projectDos/my-project-detail/projectTasks';

// utils
import { randomlyGenerateId, findAndAdd, addKey, findAndUpdateById, ngOnInit, findDataById, recalculateValues, deleteTaskItem, extractProps, addFieldToTree, addParentIds, addProjectTaskIdFun } from '@/utils/utils';

// css
import './index.less';
// lodash
import { cloneDeep, concat, find, findIndex, flattenDeep, uniq } from "lodash";

import { addNewProjectRules } from './rules';

const { Panel } = Collapse;

const AssignProjectsModal: React.FC<any> = ({ showModal, closeFun, confirmFun, curEditItem, curTotalRatio, parentRatio, baseInfo, editAssign }) => {
    const { estateId } = useParams<any>();
    const { newProjectList, customBaseInfo } = useSelector((state: any) => state.projectTasks);
    let dispatch = useDispatch();
    // 订单项目
    const [orderList, setOrderList] = useState([]);
    // 设置当前选中的订单项
    const [selectedOrder, setSelectedOrder] = useState([]);
    // 项目负责人默认是选择自己，也可以帮别人创建
    const { userInfo } = useSelector((state: any) => state.commonTask);//当前项目登录人信息
    // 模板列表信息
    const [tempList, setTempList] = useState<any>([]);
    // 员工下拉数据
    const [staffList, setStaffList] = useState([]);
    // 加载状态
    const [loadingStatus, setLoadingStatus] = useState<boolean>(false);
    // 模板表格任务信息
    const [templateList, setTemplateList] = useState<any[]>([]);
    // 当前模板内容
    const [curTemplateInfo, setCurTemplateInfo] = useState([]);
    // 是否展示添加模板
    const [showAddTemplate, setShowAddTemplate] = useState(false);
    // 模板类型数据
    const [templateTypeList, setTemplateTypeList] = useState<any[]>([]);
    // 模板类型展开收起的key
    const [templateTypeKey, setTemplateTypeKey] = useState('');
    // tab项信息
    const [tabOption, setTabOption] = useState([]);
    // 当前是否是可编辑状态
    const [editStatus, setEditStatus] = useState<boolean>(false);
    // 表格展开收起的值
    const [expandTableKeys, setExpandTableKeys] = useState<string[]>([]);
    // 保存当前品项选中的信息块
    const [curSubjectSelected, setCurSubjectSelected] = useState('');
    // 当前选中的模板项
    const [expandKey, setExpandKey] = useState('');
    // 切换tab
    const [tabKey, setTabKey] = useState('');
    // 当前需要保存的模板参数信息
    const [templateParams, setTemplateParams] = useState<any>(null);
    // 当前被选中的品项信息checkbox
    const [selectedSubject, setSelectedSubject] = useState<string[]>([]);
    // 当前展开项的品项信息
    const [curSubjectInfo, setSubjectInfo] = useState([]);
    // 是否是在编辑模板
    const [editTemplateStatus, setEditTemplateStatus] = useState<boolean>(false);
    // 是否能够编辑或者新建模板
    const [canCreateTemplate, setCurCreateTemplate] = useState<boolean>(false);
    // 当前是否是在编辑项目
    const [curEditProject, setCurEditProject] = useState<boolean>(false);
    // 项目列表数据
    const [projectListInfo, setProjectListInfo] = useState(null);
    // 提示是否覆盖更新还是另存
    const [showUpdateOperation, setShowUpdateOperation] = useState<boolean>(false);
    // 原始预览模板内容
    const [originTemplateInfo, setOriginTemplateInfo] = useState<any>(null);
    // 保存编辑模板的参数信息
    const [curEditTemplatePar, setCurEditTemplatePar] = useState<any>(null);
    // 检测是否打开了编辑模板项
    const [openEditTemItem, setOpenEditTemItem] = useState<boolean>(false);
    // 更新时是否处于loading状态
    const [updateBthStatus, setUpdateBtnStatus] = useState<boolean>(false);
    // 加载
    const [showBindLoading, setShowBindLoading] = useState(false);

    // 获取表单内容
    const submitFormData = () => {

    };
    useEffect(() => {
        getTemplateType();
    }, []);
    // 获取模板类型数据
    const getTempListData = () => {
        getTemplateListApi().then((res: any) => {
            const templateList = res?.map((item: any) => ({
                label: item.projectName,
                value: item.projectId
            }))
            if (templateList && templateList?.length) {
                setTempList(templateList);
            }
        })
    }
    // 获取员工信息
    const getStaffMessageInfo = () => {
        addressBookCommon().then((res: any) => {
            const reallyList: any = [];
            let defaultCharge = '';
            const getList = (arr: any[]) => {
                if (arr?.length) {
                    arr.forEach((item: any) => {
                        if (!item.isOrg) {
                            if (!defaultCharge && item.id == userInfo?.userId) {
                                defaultCharge = `${item.id},${item.jobInfo.orgId},${item.jobInfo.positionId}`;
                            }
                            reallyList.push({
                                value: `${item.id},${item.jobInfo.orgId},${item.jobInfo.positionId}`,
                                label: `${item.name}-${item.jobInfo.positionName} @${item.jobInfo.orgName}`
                            })
                        }
                        getList(item.children);
                    })
                }
            }
            getList(res);
            if (!editAssign) {

            }
            setStaffList(reallyList);
        })
    }
    useEffect(() => {
        getTempListData();
        getStaffMessageInfo();
    }, []);
    // 点击新建模板
    const addNewTemplate = (e: any) => {
        e.stopPropagation();
        setEditStatus(true);
        setEditTemplateStatus(false);
        setTemplateList([
            {
                id: '1', name: '一级任务', children: null, projectRatio: '100', ratio: '100', parentId: null
            }
        ]);
    };
    // 确认保存模板信息
    const saveTemplateInfo = () => {
        if (openEditTemItem) {
            message.info('请先保存编辑的任务项');
            return;
        };
        if (editTemplateStatus) {
            const handleList = extractProps(cloneDeep(templateList), ['ratio', 'name', 'taskExplain', 'id']);
            addProjectTaskIdFun(handleList, originTemplateInfo?.projectId);
            const queryParams = {
                classification: templateParams?.classification || null,
                projectBusinessId: templateParams?.subjectId || null,
                rootType: templateParams?.rootType || null,
                level: templateParams?.projectLevel || null,
                tasks: handleList,
                template: true,
                customerId: customBaseInfo?.customerId,
                estateId,
            };
            setCurEditTemplatePar(queryParams);
            // 打开提示更新操作弹框
            setShowUpdateOperation(true);
        } else {
            setLoadingStatus(true);
            const handleList = extractProps(cloneDeep(templateList), ['ratio', 'name', 'taskExplain']);
            const queryParams = {
                classification: templateParams?.classification || null,
                projectBusinessId: templateParams?.subjectId || null,
                rootType: templateParams?.rootType || null,
                level: templateParams?.projectLevel || null,
                tasks: handleList,
                template: true,
                customerId: customBaseInfo?.customerId,
                estateId,
                create: true
            };
            editOrCreateTemplate(queryParams).then(() => {
                setLoadingStatus(false);
                message.success('模板创建成功');
                setCurEditProject(false);
                setEditStatus(false);
            }).catch((err) => {
                setLoadingStatus(false);
                // message.error(err?.message || '出错了');
            });
        }

    };
    // 取消保存模板信息
    const cancelSaveTemplate = () => {
        // 关闭编辑模式
        setEditStatus(false);
        setExpandTableKeys([]);
        setTemplateList([]);
        setOriginTemplateInfo(null);
        setTemplateParams(null);
        setCurEditProject(false);
        setShowAddTemplate(false);
    };
    // 关闭预览
    const closePreview = (e: any) => {
        e.stopPropagation();
        setShowAddTemplate(false);
    };
    // 模板相关按钮
    const templateBtn = [
        <Button onClick={closePreview} key='PREVIEW'>关闭预览</Button>,
        <Button style={{
            borderColor: '#0275d8', color: '#0275d8'
        }} onClick={addNewTemplate} key='add'>新建模板</Button>,
        <Button type="primary" disabled={(templateList?.length === 0) || (originTemplateInfo && userInfo?.id !== originTemplateInfo?.userId)} key="edit" style={{
            backgroundColor: '#0275d8',
            color: '#fff',
            opacity: ((templateList?.length === 0) || (originTemplateInfo && userInfo?.id !== originTemplateInfo?.userId)) ? '0.3' : '1'
        }} onClick={() => {
            setEditStatus(true);
            setEditTemplateStatus(true);
        }}>编辑模板</Button>,
        // <Button type="primary" disabled={templateList?.length === 0} key="select" style={{
        //     backgroundColor:'#2b6bff',
        //     color:'#fff'
        // }}>全新编制创建项目</Button>,
        // <Button type="primary" disabled={templateList?.length === 0} key="select" style={{
        //     backgroundColor:'#42b17b',
        //     color:'#fff'
        // }}>选用当前模板创建项目</Button>
    ];
    // 取消新建项目
    const cancelCreateProject = () => {
        setProjectListInfo(null);
        setCurEditProject(false);
        dispatch({
            type: 'projectTasks/setNewProjectList',
            payload: []
        });
    };
    // 格式化创建新项目参数不包含模板
    const formatParams = (list: any) => {
        if (!list || !list?.length) return null
        const formatList = list.map((item: any) => {
            return {
                children: formatParams(item.children),
                leaderId: item.leaderId,
                document: item.document,
                taskId: null,
                name: item.name,
                taskExplain: item.reallyTaskExplain,
                ratio: item.ratio,
            }
        })
        return formatList;

    }
    // 格式化创建新项目参数模板
    const formatTemplateParams = (list: any) => {
        if (!list || !list?.length) return null
        const formatList = list.map((item: any) => {
            return {
                children: formatTemplateParams(item.children),
                leaderId: userInfo.userId,
                document: null,
                taskId: null,
                name: item.name,
                taskExplain: "",
                ratio: item.ratio,
            }
        })
        return formatList;

    }
    // 确认创建新项目
    const confirmCreate = async () => {
        const list = cloneDeep(newProjectList);
        for (let item of list) {
            for (let i of item.classificationVos) {
                delete i.groupIds
                for (let ii of i.projects) {
                    delete ii.onlyId
                    if (!ii.tasks) {
                        if (!ii.quoteProjectId) {
                            ii.tasks = [
                                {
                                    children: null,
                                    leaderId: userInfo.userId,
                                    document: null,
                                    taskId: null,
                                    name: '任务一',
                                    taskExplain: "",
                                    ratio: null,
                                }
                            ]
                        } else {
                            const res = await templateListTask(ii.quoteProjectId);
                            ii.tasks = formatTemplateParams(res);
                        }
                    } else {
                        // 格式化参数
                        ii.tasks = formatParams(ii.tasks);
                    }
                }
            }
        }
        const params = {
            accountRatio: customBaseInfo.accountRatio,
            amount: customBaseInfo.amount,
            buildingName: customBaseInfo.buildingName,
            customerId: customBaseInfo.customerId,
            customerName: customBaseInfo.customerName,
            estateId,
            orders: customBaseInfo.orders,
            phone: customBaseInfo.phone,
            rootTypeVos: list,
            totalAmount: customBaseInfo.totalAmount,
        }
        setShowBindLoading(true);
        batchAssignApi(params).then(() => {
            setShowBindLoading(false);
            message.success('项目创建成功！');
            confirmFun(list?.[0]?.rootType);
        }).catch(() => {
            setShowBindLoading(false);
        })

    }
    // 保存创建项目按钮
    const saveProjectBtn = [
        <Button key="cancel" onClick={cancelCreateProject}>取消</Button>,
        <Button type="primary" key="save" onClick={confirmCreate}>确认创建</Button>
    ];

    // 保存编辑模板按钮
    const saveTemplateBtn = [
        <Button style={{
            borderColor: '#d9d9d9', color: '#000'
        }} key="cancel_edit" onClick={cancelSaveTemplate}>取消</Button>,
        <Button type="primary" key="save_template" onClick={saveTemplateInfo}>确认保存</Button>
    ];
    // 获取当前code是否要被勾选
    const checkSelected = (code: string, key: string, pCode: string) => {
        // 已有项目
        const curCreatedData = cloneDeep(customBaseInfo?.rootTypeVos);
        // 新建项目
        const curNewProjectData = cloneDeep(newProjectList);
        // 所有项目
        const totalProject = concat(curCreatedData, curNewProjectData);
        // 筛选当前展开项
        const filterRootType = totalProject?.filter((el: any) => el?.rootType === key);
        if (filterRootType?.length) {
            const groupDtoList = flattenDeep(filterRootType?.map((el: any) => el?.classificationVos) || []);
            const filterGroup = groupDtoList?.filter((cl: any) => (cl?.classification === pCode));
            if (filterGroup?.length) {
                const projectDtoList = flattenDeep(filterGroup?.map((el: any) => el?.projects) || []);
                const orderType = projectDtoList?.map((el: any) => {
                    return el?.subjectVo?.map((ele: any) => ele?.id)
                });
                // 将品项信息铺平
                const flatOrderInfo = flattenDeep(orderType);
                // 去重
                const uniqArr = uniq(flatOrderInfo);
                // 当前品项已经被关联创建
                if (uniqArr?.includes(code)) {
                    return true;
                };
                // 当前品项没有被关联创建
                return false;
            };
            return false
        };
        return false
    };
    // 获取模板类型数据
    const getTemplateType = () => {
        try {
            setLoadingStatus(true);
            getProjectAssignCategory().then((data: any) => {
                setLoadingStatus(false);
                // 这里要筛选掉部门和个人页签
                const filterTab = data?.filter((el: any) => el?.projectLevelCode === 'COMPANY');
                setTabOption(filterTab || []);
                setTabKey(filterTab?.[0]?.projectLevelCode || '');
                const handleClassifyData = filterTab?.[0]?.rootTypeList?.map((el: any) => {
                    const groupAccountsInfo = el?.classificationVos?.map((cl: any) => {
                        return {
                            ...cl,
                            subjectVo: cl?.subjectVo?.map((c: any) => {
                                return {
                                    ...c,
                                    checked: checkSelected(c?.id, el?.rootType, cl?.classification)
                                }
                            })
                        }
                    });
                    return {
                        ...el,
                        classificationVos: groupAccountsInfo
                    }
                });
                setCurCreateTemplate(filterTab?.[0]?.createFlag);
                setTemplateTypeList(handleClassifyData || []);
                setTemplateTypeKey(handleClassifyData?.[0]?.rootType);
                setCurTemplateInfo(handleClassifyData?.[0]?.classificationVos || []);
            }).catch((err) => {
                setLoadingStatus(false);
                message.error(err?.message || '出错了');
            });
        } catch (error: any) {
            message.error(error?.message || '出错了');
        }
    };
    // 切换tab项
    const changeTab = (value: any) => {
        const filterTab: any[] = tabOption?.filter((cl: any) => cl?.projectLevelCode === value);
        setCurCreateTemplate(filterTab?.[0]?.createFlag);
        const handleClassifyData = filterTab?.[0]?.rootTypeList?.map((el: any) => {
            const groupAccountsInfo = el?.classificationVos?.map((cl: any) => {
                return {
                    ...cl,
                    subjectVo: cl?.subjectVo?.map((c: any) => {
                        return {
                            ...c,
                            checked: checkSelected(c?.id, el?.rootType, cl?.classification)
                        }
                    })
                }
            });
            return {
                ...el,
                classificationVos: groupAccountsInfo
            }
        });
        setTemplateTypeList(handleClassifyData || []);
        setTemplateTypeKey(handleClassifyData?.[0]?.rootType);
        setCurTemplateInfo(handleClassifyData?.[0]?.classificationVos || []);
        setTabKey(value);
        setExpandKey('');
        setShowAddTemplate(false);
        setCurEditProject(false);
        dispatch({
            type: 'projectTasks/setNewProjectList',
            payload: []
        });
        setSubjectInfo([]);
    };
    // 获取最新模板
    const getTheLatestTemplate = (obj: any) => {
        setLoadingStatus(true);
        getLastTemplate(obj).then((data: any) => {
            setLoadingStatus(false);
            const cloneTaskTemplate = cloneDeep(data?.taskSimpleVos || []);
            const handleParentIdData = addParentIds(cloneTaskTemplate || []);
            setOriginTemplateInfo(data);
            addFieldToTree(handleParentIdData);
            setTemplateList(handleParentIdData || []);
        }).catch((err) => {
            setLoadingStatus(false);
            // message.error(err?.message || '出错了');
        });
    };
    // 打开新建模板
    const addTemplate = (item: any) => {
        setShowAddTemplate(true);
        setTemplateParams({
            projectLevel: tabKey || null,
            classification: item?.classification || null,
            rootType: item?.rootType || null,
        });
        getTheLatestTemplate({
            projectLevel: tabKey || null,
            classification: item?.classification || null,
            rootType: item?.rootType || null,
        });
    };
    // 品项处点击查看模板
    const addTemplateBySubject = (item: any, el: any) => {
        setShowAddTemplate(true);
        setTemplateParams({
            projectLevel: tabKey || null,
            classification: el?.classification || null,
            rootType: el?.rootType || null,
            subjectId: item?.id || null,
        });
        getTheLatestTemplate({
            projectLevel: tabKey || null,
            classification: el?.classification || null,
            rootType: el?.rootType || null,
            subjectId: item?.id || null,
        });
    };
    // 从最外层点击新建项目
    const addProjectByGroup = (item: any) => {
        // 已有项目
        const curCreatedData = cloneDeep(customBaseInfo?.rootTypeVos);
        // 新建项目
        const curNewProjectData = cloneDeep(newProjectList);
        // 所有项目
        const totalProject = concat(curCreatedData, curNewProjectData);
        // 筛选当前展开项
        const filterRootType = totalProject?.filter((el: any) => el?.rootType === templateTypeKey);
        // 当前的group信息
        let curGroupList = cloneDeep(item?.classificationVos);
        if (filterRootType?.length) {
            const groupDtoList = flattenDeep(filterRootType?.map((el: any) => el?.classificationVos) || []);
            // 处理分组内容，看是否已经全部被选择
            curGroupList = item?.classificationVos?.map((el: any) => {
                const filterGroup = groupDtoList?.filter((cl: any) => cl?.rootType === el?.rootType);
                const projectDtoList = flattenDeep(filterGroup?.map((cl: any) => cl?.projects) || []);
                // 这里要收集classification
                let getClassSubject = projectDtoList?.map((cl: any) => {
                    return {
                        classification: cl?.classification,
                        subjectVo: cl?.subjectVo?.map((ele: any) => ele?.id)
                    }
                });
                // 这里要合并相同的classification
                let finalMerge = ngOnInit(getClassSubject, 'classification')?.map((cl: any) => {
                    return {
                        classification: cl?.classification,
                        subjectVo: flattenDeep(cl?.children?.map((i: any) => i?.subjectVo))
                    }
                });
                let findCurClass = find(finalMerge, (o) => o?.classification == el?.classification);
                const orderType = projectDtoList?.map((el: any) => {
                    return el?.subjectVo?.map((ele: any) => ele?.id)
                });
                // 将品项信息铺平并去重
                const flatOrderInfo = uniq(flattenDeep(orderType));
                return {
                    ...el,
                    subjectVo: el?.subjectVo?.filter((cl: any) => {
                        if (!findCurClass?.subjectVo?.includes(cl?.id)) {
                            return cl;
                        }
                    })
                }
            });
        };
        const queryParams = {
            // customerId: customBaseInfo?.customerId,
            estateId,
            level: tabKey,
            rootType: item?.rootType,
            groupParams: curGroupList?.map((el: any) => {
                // 这里要读取当前被选中的品项信息,如果有品项信息未勾选，则直接传递全部
                const curClassifies = el?.subjectVo?.filter((cl: any) => cl?.checked);
                // curClassifies?.length > 0 ? curClassifies?.map((ele: any) => ele?.id) : el?.subjectVo?.map((clc: any) => clc?.id)
                return {
                    classification: el?.classification,
                    subjectIds: el?.subjectVo?.map((clc: any) => clc?.id)
                }
            }),
        };
        setLoadingStatus(true);
        getInitTaskInfo(queryParams).then((data: any) => {
            setLoadingStatus(false);
            setEditStatus(false);
            setEditTemplateStatus(false);
            const cloneProjectList = cloneDeep(newProjectList || []);
            // 这里也要判断是否存在当前的rootType,如果存在，则push,否则直接push
            // 查找是否存在父级的rootType
            const filterRootTypeList = cloneProjectList?.filter((cl: any) => cl?.rootType === item?.rootType);
            if (filterRootTypeList?.length) {
                const mapHandleRoot = cloneProjectList?.map((el: any) => {
                    if (el?.rootType === item?.rootType) {
                        return {
                            ...el,
                            classificationVos: concat(el?.classificationVos, data?.classificationVos)
                        }
                    };
                    return el;
                });
                addKey(mapHandleRoot);
                dispatch({
                    type: 'projectTasks/setNewProjectList',
                    payload: mapHandleRoot
                });
            } else {
                // 这里要按照外层项目那个顺序进行排序push
                const finalProjectList = [...cloneProjectList, data];
                const newProjectListInfo: any[] = [];
                cloneDeep(templateTypeList)?.forEach((cl: any) => {
                    const findIndexInfo = findIndex(finalProjectList, (o) => o?.rootType === cl?.rootType);
                    if (findIndexInfo !== -1) {
                        newProjectListInfo?.push(finalProjectList[findIndexInfo]);
                    }
                });
                addKey(newProjectListInfo);
                dispatch({
                    type: 'projectTasks/setNewProjectList',
                    payload: newProjectListInfo
                });
            };
            // 这里还要判断，如果当前有品项信息未被勾选，就要默认勾选上
            const handleTemplate = templateTypeList?.map((el: any) => {
                if (el?.rootType === item?.rootType) {
                    const groupAccountsInfo = el?.classificationVos?.map((cl: any) => {
                        if (expandKey == cl?.classification) {
                            setSubjectInfo(cl?.subjectVo?.map((c: any) => {
                                return {
                                    ...c,
                                    checked: true
                                }
                            }));
                            setSelectedSubject(cl?.subjectVo?.map((i: any) => i?.id))
                        };
                        return {
                            ...cl,
                            subjectVo: cl?.subjectVo?.map((c: any) => {
                                return {
                                    ...c,
                                    checked: true
                                }
                            })
                        }
                    });
                    setCurTemplateInfo(groupAccountsInfo);
                    return {
                        ...el,
                        classificationVos: groupAccountsInfo
                    }
                };
                return el;
            });
            setTemplateTypeList(handleTemplate);
            setProjectListInfo(data || null);
            setShowAddTemplate(false);
            setCurEditProject(true);
        }).catch((err) => {
            setLoadingStatus(false);
            message.error(err?.message || '出错了');
        });
    };
    // 检测是否能够显示根按钮
    const canShowRootBtn = (node: any) => {
        // 已有项目
        const curCreatedData = cloneDeep(customBaseInfo?.rootTypeVos);
        // 新建项目
        const curNewProjectData = cloneDeep(newProjectList);
        // 所有项目
        const totalProject = concat(curCreatedData, curNewProjectData);
        // 筛选当前展开项
        const filterRootType = totalProject?.filter((el: any) => el?.rootType === templateTypeKey);
        if (filterRootType?.length) {
            const groupDtoList = flattenDeep(filterRootType?.map((el: any) => el?.classificationVos) || []);
            const curNodeGroupList = cloneDeep(node?.classificationVos)?.every((cl: any) => {
                const filterGroupInfo = groupDtoList?.filter((el: any) => el?.rootType === cl?.rootType);
                const projectDtoList = flattenDeep(filterGroupInfo?.map((el: any) => el?.projects) || []);
                const orderType = projectDtoList?.map((el: any) => {
                    return el?.subjectVo?.map((ele: any) => ele?.id)
                });
                // 将品项信息铺平并去重
                const flatOrderInfo = uniq(flattenDeep(orderType));
                // 获取到当前group下的所有品项id
                const curGroupOrder = cl?.subjectVo?.map((ele: any) => ele?.id);
                if (flatOrderInfo?.length < curGroupOrder?.length) {
                    return false
                };
                // 对比两个数组是否被全部包含
                const matchOrder = curGroupOrder?.every((el: any) => flatOrderInfo?.includes(el));
                return matchOrder;
            });
            return !curNodeGroupList;
        };
        return true;
    };
    // 额外按钮
    const getExtraData = (item: any) => {
        return (item?.rootType === templateTypeKey && canShowRootBtn(item)) ? (
            <Button shape="circle" size="small" onClick={(e) => {
                e.stopPropagation();
                addProjectByGroup(item);
            }} className="add_template_btn">
                <img src={Add_IMG} alt="" style={{
                    width: '14px',
                    height: '14px'
                }} />
            </Button>
        ) : null
    };
    // 点击选择模板类型
    const expandCloseCollapse = (value: any) => {
        setExpandKey(value || '');
        setCurSubjectSelected('');
        if (value) {
            const filterTypeOption: any[] = curTemplateInfo?.filter((cl: any) => cl?.classification === value);
            const filterCheckedOption = filterTypeOption?.[0]?.subjectVo?.filter((cl: any) => cl?.checked);
            const mapCode = filterCheckedOption?.map((el: any) => el?.id);
            setSelectedSubject(mapCode);
            setSubjectInfo(filterTypeOption?.[0]?.subjectVo || []);
        };
    };
    // 新增子级模板任务
    const addChildTemplateTask = (id: string, type: string, pId: string) => {
        const sameObj = { id: randomlyGenerateId(), title: '', children: null, projectRatio: null, ratio: null };
        let cloneList = cloneDeep(templateList);
        switch (type) {
            // 新增子级
            case 'SUB':
                let finalArr = findAndAdd(templateList, id, { ...sameObj, parentId: id });
                setExpandTableKeys([...expandTableKeys, id]);
                setTemplateList(finalArr);
                break;
            // 新增同级
            case 'SAME':
                if (pId) {
                    let curArr = findAndAdd(templateList, pId, { ...sameObj, parentId: pId });
                    setTemplateList(curArr);
                } else {
                    setTemplateList([...templateList, { ...sameObj, parentId: null }]);
                }
                break;
            // 删除
            case 'DELETE':
                deleteTaskItem(cloneList, id)
                if (cloneList?.length) {
                    setTemplateList(cloneList);
                } else {
                    message.info('请至少保留一个任务');
                };
                break;
            default:
                break;
        }

    };
    // 展开收起任务表格
    const expandTaskTableFun = (expandedRowKeys: any[]) => {
        setExpandTableKeys(expandedRowKeys);
    };
    // 展开收起折叠框
    const expandTemplateType = (value: any) => {
        setTemplateTypeKey(value || '');
        setCurSubjectSelected('');
        if (value) {
            const filterType: any[] = templateTypeList?.filter((el: any) => el?.rootType === value);
            setCurTemplateInfo(filterType?.[0]?.classificationVos || []);
            setExpandKey('');
        }
    };
    // 二级加项目
    const addSubProject = (item: any) => {
        // 已有项目
        const curCreatedData = cloneDeep(customBaseInfo?.rootTypeVos);
        // 新建项目
        const curNewProjectData = cloneDeep(newProjectList);
        // 所有项目
        const totalProject = concat(curCreatedData, curNewProjectData);
        // 筛选当前展开项
        const filterRootType = totalProject?.filter((el: any) => el?.rootType === templateTypeKey);
        const groupDtoList = flattenDeep(filterRootType?.map((el: any) => el?.classificationVos) || []);
        const filterGroup = groupDtoList?.filter((cl: any) => (cl?.classification === expandKey));
        const projectDtoList = flattenDeep(filterGroup?.map((el: any) => el?.projects) || []);
        const orderType = projectDtoList?.map((el: any) => {
            return el?.subjectVo?.map((ele: any) => ele?.id)
        });
        // 将品项信息铺平并去重
        const flatOrderInfo = uniq(flattenDeep(orderType));
        // 获取到当前group下的所有品项id
        const curGroupOrder = item?.subjectVo?.filter((cl: any) => {
            if (!flatOrderInfo?.includes(cl?.id)) {
                return cl;
            }
        });
        // 确认是否有被选的数据
        const filterSelectedOrder = curGroupOrder?.filter((el: any) => el?.checked);
        if (filterSelectedOrder?.length) {
            const queryParams = {
                // customerId: cId,
                estateId,
                level: tabKey,
                rootType: item?.rootType,
                groupParams: [item]?.map((el: any) => {
                    // 这里要读取当前被选中的品项信息,如果有品项信息未勾选，则直接传递全部
                    const curClassifies = filterSelectedOrder?.filter((cl: any) => cl?.checked);
                    return {
                        classification: el?.classification,
                        subjectIds: curClassifies?.length > 0 ? curClassifies?.map((ele: any) => ele?.id) : filterSelectedOrder?.map((clc: any) => clc?.id)
                    }
                }),
            };
            setLoadingStatus(true);
            getInitTaskInfo(queryParams).then((data: any) => {
                setLoadingStatus(false);
                setShowAddTemplate(false);
                setEditStatus(false);
                setEditTemplateStatus(false);
                const cloneProjectList = cloneDeep(newProjectList || []);
                // 查找是否存在父级的rootType
                const filterRootTypeList = cloneProjectList?.filter((cl: any) => cl?.rootType === item?.rootType);
                if (filterRootTypeList?.length) {
                    const mapHandleRoot = cloneProjectList?.map((el: any) => {
                        if (el?.rootType === item?.rootType) {
                            return {
                                ...el,
                                classificationVos: concat(groupDtoList, data?.classificationVos)
                            }
                        };
                        return el;
                    });
                    addKey(mapHandleRoot);
                    dispatch({
                        type: 'projectTasks/setNewProjectList',
                        payload: mapHandleRoot
                    });
                } else {
                    // 直接push
                    const finalProjectList = [...cloneProjectList, data];
                    const newProjectListInfo: any[] = [];
                    cloneDeep(templateTypeList)?.forEach((cl: any) => {
                        const findIndexInfo = findIndex(finalProjectList, (o) => o?.rootType === cl?.rootType);
                        if (findIndexInfo !== -1) {
                            newProjectListInfo?.push(finalProjectList[findIndexInfo]);
                        }
                    });
                    addKey(newProjectListInfo);
                    dispatch({
                        type: 'projectTasks/setNewProjectList',
                        payload: newProjectListInfo
                    });
                };
                setProjectListInfo(data || null);
                setCurEditProject(true);
            }).catch((err) => {
                setLoadingStatus(false);
                message.error(err?.message || '出错了');
            });

        } else {
            message.info('请至少勾选一个品项');
        }
    };
    // 检测分组项是否可以出现添加按钮
    const readTheGroupAlSelected = (classify: string[]) => {
        // 已有项目
        const curCreatedData = cloneDeep(customBaseInfo?.rootTypeVos);
        // 新建项目
        const curNewProjectData = cloneDeep(newProjectList);
        // 所有项目
        const totalProject = concat(curCreatedData, curNewProjectData);
        // 筛选当前展开项
        const filterRootType = totalProject?.filter((el: any) => el?.rootType === templateTypeKey);
        if (filterRootType?.length) {
            const groupDtoList = flattenDeep(filterRootType?.map((el: any) => el?.classificationVos) || []);
            const filterGroup = groupDtoList?.filter((cl: any) => (cl?.classification === expandKey));
            if (filterGroup?.length) {
                const projectDtoList = flattenDeep(filterGroup?.map((el: any) => el?.projects) || []);
                const orderType = projectDtoList?.map((el: any) => {
                    return el?.subjectVo?.map((ele: any) => ele?.id)
                });
                // 将品项信息铺平
                const flatOrderInfo = flattenDeep(orderType);
                // 去重
                const uniqArr = uniq(flatOrderInfo);
                // 获取到当前group下的所有品项id
                const curGroupOrder = classify?.map((cl: any) => cl?.id);
                if (uniqArr?.length < curGroupOrder?.length) {
                    return true
                };
                // 对比两个数组是否被全部包含
                const matchOrder = curGroupOrder?.every((cl: any) => uniqArr?.includes(cl));
                return !matchOrder;
            };
            return true;
        };
        return true;
    };
    // 模板类型额外按钮
    const extraTemplateBtn = (item: any) => {
        return (item?.classification === expandKey) ? (
            <div className="template_collapse_btn_area">
                <Button shape="circle" size="small" onClick={(e) => {
                    e.stopPropagation();
                    addTemplate(item);
                    // 模板预览
                    setEditStatus(false);
                }} className="add_template_btn" style={{
                    background: 'transparent', boxShadow: 'unset', borderColor: 'transparent', marginRight: '10px'
                }}>
                    <img src={TEMPLATE_SVG} alt="" style={{
                        width: '24px',
                        height: '24px'
                    }} />
                </Button>
                {
                    readTheGroupAlSelected(item?.subjectVo || []) && <Button shape="circle" size="small" onClick={(e) => {
                        e.stopPropagation();
                        addSubProject(item);
                    }} className="add_template_btn" style={{
                        background: '#fff', boxShadow: 'unset', borderColor: '#fff'
                    }}>
                        <img src={CREATE_BLUE} alt="" style={{
                            width: '14px',
                            height: '14px'
                        }} />
                    </Button>
                }
            </div>
        ) : null
    };
    // 当前渲染按钮类型
    const renderBtnContent = () => {
        if (showAddTemplate) {
            if (editStatus) {
                return saveTemplateBtn;
            };
            return canCreateTemplate ? templateBtn : null;
        }
        return curEditProject ? saveProjectBtn : null;
    };
    // 获取到当前表单的内容
    const getCurFormTemplate = (info: any) => {
        if (info?.parentId) {
            const parentId = info?.parentId;
            const curParentData = findDataById(cloneDeep(templateList), parentId);
            const findCurTargetObj = curParentData?.children?.filter((el: any) => el?.id === info?.id);
            const curFormObj = {
                ...findCurTargetObj?.[0],
                name: info?.name,
                taskExplain: info?.taskExplain,
                ratio: info?.ratio,
                projectRatio: parseFloat((((info?.ratio) * (curParentData?.projectRatio)) / 100)?.toFixed(3)),
                parentId
            };
            const finalArr = findAndUpdateById(cloneDeep(templateList), info?.id, curFormObj);
            const curNode = findDataById(finalArr, info?.id);
            recalculateValues(curNode);
            setTemplateList(finalArr);
        } else {
            // 未找到父级，表明是最外层的数据,直接遍历更改
            const mapHandle = templateList?.map((el: any) => {
                if (el?.id === info?.id) {
                    return {
                        ...el,
                        name: info?.name,
                        taskExplain: info?.taskExplain,
                        ratio: info?.ratio,
                        projectRatio: info?.ratio,
                        parentId: null
                    }
                };
                return el;
            });
            const handleParentIdData = addParentIds(cloneDeep(mapHandle || []));
            const curNode = findDataById(handleParentIdData, info?.id);
            recalculateValues(curNode);
            setTemplateList(handleParentIdData);
        }

    };
    // 点击选中品项
    const clickSubjectFun = (item: any) => {
        if (curSubjectSelected === item?.id) {
            setCurSubjectSelected('')
        } else {
            setCurSubjectSelected(item?.id)
        };
    };
    // 点击勾选或者取消品项
    const getCheckSubject = (value: any[], id: string, code: string, rootType: string) => {
        const handleClassifyData: any[] = cloneDeep(templateTypeList)?.map((el: any) => {
            if (el?.rootType === rootType) {
                const groupAccountsInfo = el?.classificationVos?.map((cl: any) => {
                    if (cl?.classification === id) {
                        return {
                            ...cl,
                            subjectVo: cl?.subjectVo?.map((c: any) => {
                                if (value?.includes(c?.id)) {
                                    return {
                                        ...c,
                                        checked: true
                                    }
                                }
                                return {
                                    ...c,
                                    checked: false
                                }
                            })
                        }
                    };
                    return cl;
                });
                return {
                    ...el,
                    classificationVos: groupAccountsInfo
                }
            }
            return el;
        });
        const curNewTemplate = handleClassifyData?.filter((cl: any) => cl?.rootType === rootType);
        setTemplateTypeList(handleClassifyData);
        setCurTemplateInfo(curNewTemplate?.[0]?.classificationVos || []);
        setSelectedSubject(value);
    };
    // 检测是否处于模板编辑状态
    const checkEditStatusFun = (bol: boolean) => {
        setOpenEditTemItem(bol);
    };
    // 更新模板操作
    const updateTemplate = (type: string) => {
        // 覆盖更新
        setUpdateBtnStatus(true);
        if (type === 'COVER') {
            const queryParams = {
                classification: curEditTemplatePar?.classification || null,
                projectBusinessId: curEditTemplatePar?.projectBusinessId || null,
                rootType: curEditTemplatePar?.rootType || null,
                level: curEditTemplatePar?.level || null,
                tasks: curEditTemplatePar?.tasks,
                template: true,
                customerId: customBaseInfo?.customerId,
                estateId,
                create: false
            };
            editOrCreateTemplate(queryParams).then(() => {
                setUpdateBtnStatus(false);
                message.success('更新成功');
                // 关闭提示框
                setShowUpdateOperation(false);
                // 关闭编辑模板状态
                setEditTemplateStatus(false);
                // 关闭表格编辑状态
                setEditStatus(false);
            }).catch((err) => {
                setUpdateBtnStatus(false);
                // message.error(err?.message || '出错了')
            });

        } else {
            const handleList = extractProps(cloneDeep(curEditTemplatePar?.tasks), ['ratio', 'name', 'taskExplain']);
            const queryParams = {
                classification: curEditTemplatePar?.classification || null,
                projectBusinessId: curEditTemplatePar?.projectBusinessId || null,
                rootType: curEditTemplatePar?.rootType || null,
                level: curEditTemplatePar?.level || null,
                tasks: handleList,
                template: true,
                customerId: customBaseInfo?.customerId,
                estateId,
                create: true
            };
            // 保存新模板
            editOrCreateTemplate(queryParams).then(() => {
                setUpdateBtnStatus(false);
                message.success('模板创建成功');
                // 关闭提示框
                setShowUpdateOperation(false);
                // 关闭编辑模板状态
                setEditTemplateStatus(false);
                // 关闭表格编辑状态
                setEditStatus(false);
            }).catch((err) => {
                setUpdateBtnStatus(false);
                // message.error(err?.message || '出错了');
            });

        }

    };
    // 读取当前展开项下，品项是否已经被创建项目
    const readTheProjectAlCreated = (code: string) => {
        // 已有项目
        const curCreatedData = cloneDeep(customBaseInfo?.rootTypeVos);
        // 新建项目
        const curNewProjectData = cloneDeep(newProjectList);
        // 所有项目
        const totalProject = concat(curCreatedData, curNewProjectData);
        // 筛选当前展开项
        const filterRootType = totalProject?.filter((el: any) => el?.rootType === templateTypeKey);
        if (filterRootType?.length) {
            const groupDtoList = flattenDeep(filterRootType?.map((el: any) => el?.classificationVos) || []);
            const filterGroup = groupDtoList?.filter((cl: any) => (cl?.classification === expandKey));
            if (filterGroup?.length) {
                const projectDtoList = flattenDeep(filterGroup?.map((el: any) => el?.projects) || []);
                const orderType = projectDtoList?.map((el: any) => {
                    return el?.subjectVo?.map((ele: any) => ele?.id)
                });
                // 将品项信息铺平
                const flatOrderInfo = flattenDeep(orderType);
                // 去重
                const uniqArr = uniq(flatOrderInfo);
                // 当前品项已经被关联创建
                if (uniqArr?.includes(code)) {
                    return true;
                };
                // 当前品项没有被关联创建
                return false;
            };
            return false
        };
        return false
    };
    // 品项上加项目
    const addProjectByOrder = (code: string, groupId: string | number, rootType: string) => {
        if (selectedSubject?.includes(code)) {
            // 当前确实被选择
            const queryParams = {
                // customerId: cId,
                estateId,
                level: tabKey,
                rootType,
                groupParams: [
                    {
                        classification: groupId,
                        subjectIds: [code]
                    }
                ],
            };
            setLoadingStatus(true);
            getInitTaskInfo(queryParams).then((data: any) => {
                setLoadingStatus(false);
                setShowAddTemplate(false);
                setEditStatus(false);
                setEditTemplateStatus(false);
                const cloneProjectList = cloneDeep(newProjectList || []);
                // 查找是否存在父级的rootType
                const filterRootTypeList = cloneProjectList?.filter((cl: any) => cl?.rootType === rootType);
                if (filterRootTypeList?.length) {
                    const mapHandleRoot = cloneProjectList?.map((el: any) => {
                        if (el?.rootType === rootType) {
                            return {
                                ...el,
                                classificationVos: concat(el?.classificationVos, data?.classificationVos)
                            }
                        };
                        return el;
                    });
                    addKey(mapHandleRoot);
                    dispatch({
                        type: 'projectTasks/setNewProjectList',
                        payload: mapHandleRoot
                    });
                } else {
                    // 直接push
                    const finalProjectList = [...cloneProjectList, data];
                    const newProjectListInfo: any[] = [];
                    cloneDeep(templateTypeList)?.forEach((cl: any) => {
                        const findIndexInfo = findIndex(finalProjectList, (o) => o?.rootType === cl?.rootType);
                        if (findIndexInfo !== -1) {
                            newProjectListInfo?.push(finalProjectList[findIndexInfo]);
                        }
                    });
                    addKey(newProjectListInfo);
                    dispatch({
                        type: 'projectTasks/setNewProjectList',
                        payload: newProjectListInfo
                    });
                };
                setProjectListInfo(data || null);
                setCurEditProject(true);
            }).catch((err) => {
                setLoadingStatus(false);
                message.error(err?.message || '出错了');
            });

        } else {
            message.error('请先勾选品项信息');
        }
    };
    return (
        <Modal
            open={showModal}
            onCancel={closeFun}
            onOk={submitFormData}
            title="创建项目"
            width='100vw'
            footer={renderBtnContent()}
            maskClosable={false}
            className="create-project-modal"
            maskStyle={{ background: 'rgb(255,255,255,0.08)' }}
        >

            <Spin spinning={loadingStatus}>
                <div className="create-project-content">
                    {/* 左侧模板内容 */}
                    <div className={`left-content ${tabOption?.length === 0 ? 'empty_template_left' : ''}`} onClick={() => {
                        setShowAddTemplate(false);
                    }}>
                        {/* 头部提示 */}
                        {
                            tabOption?.length > 0 && <div className="header_tip">项目目录</div>
                        }
                        {
                            tabOption?.length > 0 ? (
                                <Tabs onTabClick={changeTab} activeKey={tabKey} type="card" items={tabOption?.map((cl: any) => {
                                    return {
                                        label: cl?.projectLevelName,
                                        key: cl?.projectLevelCode,
                                        children: <>
                                            {
                                                templateTypeList?.length > 0 ? (
                                                    <Collapse bordered={false} style={{
                                                        marginTop: '10px'
                                                    }} ghost accordion onChange={expandTemplateType} activeKey={templateTypeKey} destroyInactivePanel >
                                                        {
                                                            templateTypeList?.map((item: any) => (
                                                                <Panel header={item?.name} key={item?.rootType} extra={getExtraData(item)}>
                                                                    {
                                                                        curTemplateInfo?.length > 0 ? (
                                                                            <Collapse bordered={false} style={{
                                                                                marginTop: '10px'
                                                                            }} ghost accordion onChange={expandCloseCollapse} activeKey={expandKey} destroyInactivePanel className="sub_collapse_box">
                                                                                {
                                                                                    curTemplateInfo?.map((el: any) => (
                                                                                        <Panel header={el?.name} key={el?.classification} extra={extraTemplateBtn(el)}>
                                                                                            {
                                                                                                curSubjectInfo?.length > 0 ? (
                                                                                                    <Checkbox.Group style={{
                                                                                                        width: '100%'
                                                                                                    }} value={selectedSubject} onChange={(value: any[]) => {
                                                                                                        getCheckSubject(value, el?.classification, cl?.projectLevelCode, item?.rootType);
                                                                                                    }}>
                                                                                                        {
                                                                                                            curSubjectInfo?.map((ele: any) => (
                                                                                                                <div className={`template_list_box ${curSubjectSelected === ele?.id ? 'selected_subject' : ''}`} key={ele?.id}>
                                                                                                                    <Checkbox value={ele?.id} style={{ marginLeft: '10px' }} disabled={readTheProjectAlCreated(ele?.id)}>{ele?.name || '未知'}</Checkbox>
                                                                                                                    <div className="template_collapse_btn_area" style={{
                                                                                                                        marginRight: '10px', flex: 1, justifyContent: readTheProjectAlCreated(ele?.id) ? 'space-between' : 'end'
                                                                                                                    }} onClick={(e) => {
                                                                                                                        e.stopPropagation();
                                                                                                                        clickSubjectFun(ele)
                                                                                                                    }}>
                                                                                                                        {
                                                                                                                            readTheProjectAlCreated(ele?.id) && <div className="created_subject">已创建</div>
                                                                                                                        }
                                                                                                                        {
                                                                                                                            curSubjectSelected === ele?.id ? (
                                                                                                                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                                                                                                                    <Button shape="circle" size="small" onClick={(e) => {
                                                                                                                                        e.stopPropagation();
                                                                                                                                        addTemplateBySubject(ele, el);
                                                                                                                                        // 模板预览
                                                                                                                                        setEditStatus(false);
                                                                                                                                    }} className="add_template_btn" style={{
                                                                                                                                        background: 'transparent', boxShadow: 'unset', borderColor: 'transparent', marginRight: '10px'
                                                                                                                                    }}>
                                                                                                                                        <img src={TEMPLATE_SVG} alt="" style={{
                                                                                                                                            width: '24px',
                                                                                                                                            height: '24px'
                                                                                                                                        }} />
                                                                                                                                    </Button>
                                                                                                                                    {
                                                                                                                                        !readTheProjectAlCreated(ele?.id) && <Button shape="circle" size="small" onClick={(e) => {
                                                                                                                                            e.stopPropagation();
                                                                                                                                            addProjectByOrder(ele?.id, el?.classification, item?.rootType);
                                                                                                                                        }} className="add_template_btn" style={{
                                                                                                                                            background: '#fff', boxShadow: 'unset', borderColor: '#fff'
                                                                                                                                        }}>
                                                                                                                                            <img src={CREATE_BLUE} alt="" style={{
                                                                                                                                                width: '14px',
                                                                                                                                                height: '14px'
                                                                                                                                            }} />
                                                                                                                                        </Button>
                                                                                                                                    }

                                                                                                                                </div>
                                                                                                                            ) : null
                                                                                                                        }

                                                                                                                    </div>
                                                                                                                </div>
                                                                                                            ))
                                                                                                        }
                                                                                                    </Checkbox.Group>
                                                                                                ) : (
                                                                                                    <Empty description='暂无数据' style={{
                                                                                                        marginTop: '20px'
                                                                                                    }} />
                                                                                                )
                                                                                            }
                                                                                        </Panel>
                                                                                    ))
                                                                                }
                                                                            </Collapse>
                                                                        ) : (
                                                                            <Empty description='暂无数据' />
                                                                        )
                                                                    }

                                                                </Panel>
                                                            ))
                                                        }
                                                    </Collapse>
                                                ) : (
                                                    <Empty description='暂无数据' style={{
                                                        marginTop: '20px'
                                                    }} />
                                                )
                                            }
                                        </>
                                    }
                                })}>
                                </Tabs>
                            ) : (
                                <Empty description='暂无内容' />
                            )
                        }
                    </div>
                    {/* 右侧表格内容 */}
                    <div className="right-content">
                        {
                            showAddTemplate && <div className="add_edit_template_modal">
                                <div className="template_modal_header">
                                    {editStatus ? (editTemplateStatus ? '编辑模板' : '新增模板') : '模板预览'}
                                </div>
                                <div className={`template_container ${templateList?.length === 0 ? 'empty_template_box' : ''}`} >
                                    {
                                        templateList?.length > 0 ? (
                                            <TemplateTable templateList={templateList} editStatus={editStatus} addChildTemplateTask={addChildTemplateTask} expandTableKeys={expandTableKeys} expandTaskTableFun={expandTaskTableFun} getCurFormTemplate={getCurFormTemplate} checkEditStatusFun={checkEditStatusFun} />
                                        ) : (
                                            <Empty description={`暂无模板数据${canCreateTemplate ? '，请点击下方按钮新建' : ''}`} />
                                        )
                                    }
                                </div>
                            </div>
                        }

                        {
                            !showAddTemplate && <div>
                                { /* 新建表格项目 */}
                                {
                                    newProjectList?.length > 0 && (
                                        <div>
                                            <div className="header-label new-project-list">
                                                <span className="left_line" />
                                                项目清单
                                            </div>
                                            <ProjectList />
                                        </div>
                                    )
                                }
                                {/* 已有表格项目 */}
                                {
                                    customBaseInfo?.rootTypeVos?.length > 0 && (
                                        <div>
                                            <div className="header-label exist-project-list">
                                                <span className="left_line" />
                                                已有项目
                                            </div>
                                            <ProjectList readonly />
                                        </div>
                                    )
                                }
                            </div>
                        }
                    </div>
                </div>
            </Spin>
            {
                showUpdateOperation && <Modal open={showUpdateOperation} title='更新模板' closable={false} footer={[
                    <Button onClick={() => {
                        setShowUpdateOperation(false);
                    }} key='THINK'>再想想</Button>,
                    <Button style={{
                        borderColor: '#0275d8', color: '#0275d8'
                    }} key='new_create' onClick={() => updateTemplate('NEW')}>另存为新模板</Button>,
                    <Button type="primary" key="update" style={{
                        backgroundColor: '#0275d8',
                        color: '#fff',
                    }} onClick={() => updateTemplate('COVER')}>覆盖更新</Button>
                ]} centered>
                    <p>系统检测到您正在执行更新操作，请选择您要更新的方式 !</p>
                </Modal>
            }
            {
                showBindLoading && <div className="spin_wrapper">
                    <Spin tip="创建中,请稍后~" size="large"  >
                        <div style={{ width: '100px', height: '100px' }}></div>
                    </Spin>
                </div>
            }

        </Modal>
    )
};
export default AssignProjectsModal;
