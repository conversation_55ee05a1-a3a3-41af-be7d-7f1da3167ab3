export const addNewProjectRules = {
    "name": [
      {required: true, message: "项目名称不能为空"}
    ],
    "introduction": [
      {required: true, message: "项目审核备注说明不能为空"}
    ],
    "visibleScope": [
      {required: true, message: "项目可见授权不能为空"}
    ],
    "type": [
      {required: true, message: "项目类型不能为空"}
    ],
    "level": [
      {required: true, message: "项目级别不能为空"}
    ],
    "groupId": [
      {required: true, message: "项目分类不能为空"}
    ],
    "projectName": [
      {required: true, message: "项目名称不能为空"}
    ],
    "powerPersonType": [
      {required: true, message: "岗位标签不能为空"}
    ],
    "accountRatio": [
      {required: true, message: "项目核算计入比不能为空"}
    ],
    "templateId": [
      {required: true, message: "项目事项参考模板不能为空"}
    ],
    "workTime": [
      () => ({
        validator(rule: any, value: {startTime: string, endTime: string, total: number, type: string}) {
          if(value && value.startTime && value.endTime && value.total && value.type) {
            return Promise.resolve()
          }
          return Promise.reject(new Error('项目周期有数据为空，请检查'));
        }
      })
    ],
    "header": [{required: true, message: "项目负责人不能为空"}],
    "participants": [
      () => ({
        validator(rule: any, value: Array<object>) {
          if(value && value.length > 0) {
            return Promise.resolve()
          }
          return Promise.reject(new Error('关联人员至少需要选择一个'));
        }
      })
    ],
    "projectTemplateId": [
      () => ({
        validator(rule: any, value: {id: string | number}) {
          if(value && value.id) {
            return Promise.resolve()
          }
          return Promise.reject(new Error('请选择一个模板'));
        }
      })
    ],
    "projectAboutId": [
      () => ({
        validator(rule: any, value: {id: string | number, orderId: string}) {
          if(value && (value.id || value.orderId)) {
            return Promise.resolve()
          }
          return Promise.reject(new Error('关联关系不能为空'));
        }
      })
    ]
  }