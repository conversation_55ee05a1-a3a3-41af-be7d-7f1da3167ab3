import React, { useState, useEffect, useMemo } from "react";
// antd
import { Row, Col, message, Modal, Checkbox, Form, InputNumber } from 'antd';
import {
    BdwChooseCompanyStaff, BdwRow
} from "@/components";
// umi
import { useParams } from "umi";
// import {getAboutMeProjectTemplateCount,updateAssignProject} from "@/service/project/project-template-action";
import { listTaskLeaderInfo } from '@/service/projectDos/my-project-detail/projectTasks';
import { projectManagementEdit,} from "@/service/projectDos/myProjectApi";
// css
import './index.less';
interface WrapComponentProps {
    labelname?: string
    wrapclassname?: string
}
interface LevelProps {
    key: number,
    level: string
    name: string
}
const levelList: Array<LevelProps> = [
    {
        key: 1,
        name: "公司项目模板",
        level: 'Company'
    },
    {
        key: 2,
        name: "部门项目模板",
        level: 'Department'
    },
    {
        key: 3,
        name: "小组项目模板",
        level: 'Team'
    },
    {
        key: 4,
        name: "个人项目模板",
        level: 'Personal'
    },
]
const { Item } = Form;
const WrapComponent = <T extends {}>(WrappedComponent: React.FC<T>) => {
    const ContentComponent = (props: WrapComponentProps & T) => {
        const { labelname, wrapclassname = '' } = props;
        return (
            <BdwRow type='flex' className={`${wrapclassname} bdw-form-item-container`}>
                {
                    labelname && <div className='form-label'>
                        {labelname}
                    </div>
                }
                <WrappedComponent
                    className='label-reset'  {...props as T}
                />
            </BdwRow>
        )
    }
    return ContentComponent;
}
const BdwFormItem = WrapComponent(Item);


const SaveAsModal: React.FC<any> = ({ showModal, closeFun, confirmFun, curEditItem, baseInfo, currentData }) => {
    const { cId } = useParams();
    const [form] = Form.useForm();




    // 获取表单内容
    const submitFormData = () => {
        form.validateFields().then(async () => {
            try {
                // 代表表单校验通过
                const formValue = form.getFieldsValue();
                const params = {
                    projectId: currentData.id,
                    name: currentData.name,
                    leaderId: formValue.header.id
                }
                await projectManagementEdit(params);
                message.success('更新成功！')
                confirmFun();
            } catch (e: any) {
                message.error(e?.message)
            }
        })

    };

    return (
        <Modal
            visible={showModal}
            okText='确认'
            cancelText='取消'
            onCancel={closeFun}
            onOk={submitFormData}
            title='更改项目负责人'
            width='580px'
            maskClosable={false}
            maskStyle={{ background: 'rgb(255,255,255,0.08)' }}
        >
            <Form form={form} >
                <div className="header-wrapper">
                    <BdwFormItem labelname="负责人员 :" name='header' initialValue={{ id: currentData.leaderId, name: currentData.leaderName }}
                    >
                         <BdwChooseCompanyStaff
                                placeholder="姓名关键字检索"
                                apiSrc={listTaskLeaderInfo}
                                extraParams={{ projectId: currentData.id }}
                                showColumns={['name']}
                            />
                    </BdwFormItem>
                </div>
                
            </Form>

        </Modal>
    )
};

export default SaveAsModal;
