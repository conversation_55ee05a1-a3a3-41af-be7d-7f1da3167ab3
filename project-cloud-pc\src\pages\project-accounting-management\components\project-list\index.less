.project-list-wrapper-add{
   
}
.lowest-collapse{
    .panel-custom-header-wrapper{
        .click-out-side-box{
            // 设置顶部数据随表格一起移动
            overflow: visible!important;
          }
        >.ant-collapse-header{
            padding-right: unset;
            background: #757171;
            .collapse-custom-header{
                font-size: 14px;
                color: white;
                font-weight: bold;
            }
        }
    }
    .panel-custom-header-wrapper.scroll-show{
        .click-out-side-box{
            // 设置顶部数据随表格一起移动
            overflow: auto!important;
          }
    }
    
    .ant-collapse-content-box{
        padding: unset;
    }
    .collapse-custom-header{
        display: flex;
        .project-name{
          font-weight: bold;
          flex: 1;
          color: white;
          font-size: 14px;
          display: flex;
          .project-items-name{
            max-width: 200px;
            overflow: hidden;
            height: 22px;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
          }
          .split-project-btn{
            margin-left: 20px;
            color: rgb(0, 180, 246);
          }
        }
        .head-column {
          color: rgb(0, 180, 246);
          padding:0 6px;
          font-weight: bold;
          padding: 0 6px;
          box-sizing: border-box;
         
        }
      }
}