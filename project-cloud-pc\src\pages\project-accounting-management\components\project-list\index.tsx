/**
 * @description 
*/
import React, { useEffect, useState } from 'react';
import { Collapse } from 'antd';
// import ProjectListTable from '../project-list-table';
import { useDispatch, useParams, useLocation,useSelector } from 'umi';
import { RightOutlined } from "@ant-design/icons";
import CreateAssignProjectTable from '@/pages/project-accounting-management/projectTasks/projectTasksTable/createAssignProjectTable';
// @ts-ignore
import {v4 as uuidv4} from 'uuid';
import './index.less';
import { cloneDeep } from 'lodash';

const { Panel } = Collapse;


const ProjectList: React.FC<{ readonly?: boolean,projectListInfo?:any }> = ({ readonly,projectListInfo }) => {
    const [collapseList,setCollapseList] = useState<any>([]);
    const { customBaseInfo,newProjectList } = useSelector((state: any) => state.projectTasks);
    const { userInfo } = useSelector((state: any) => state.commonTask);//当前项目登录人信息
    // 第一层的collapse key 
    const [firstStageKey,setFirstStageKey] = useState<any>(null);
    // 第二层的collapse key 
    const [secondStageKey,setSecondStageKey] = useState<any>(null);
    // 第三层的collapse key 
    const [threeStageKey,setThreeStageKey] = useState<any>(null);
    const [windowSize,setWindowSize] = useState({
        width:window.innerWidth
      })
        // 监听窗口变化
    useEffect(() => {
        const handleResize = () => {
        setWindowSize({
            width:window.innerWidth
        })
        }
        window.addEventListener('resize',handleResize);
        return () => {
        window.removeEventListener('resize',handleResize);
        }
    },[])
    useEffect(() => {
        // 只读模式下 为已有项目取外面数据
        let list: any = [];
        if(readonly){
            list = cloneDeep(customBaseInfo?.rootTypeVos??[])
            addKey(list);
            setCollapseList(list);
        }else{
            list = cloneDeep(newProjectList);
            setCollapseList(list);
            if(!firstStageKey && !secondStageKey && !threeStageKey){
                setFirstStageKey(list?.[0]?.rootType)
                setSecondStageKey(list?.[0]?.classificationVos?.[0]?.groupIds)
                setThreeStageKey(list?.[0]?.classificationVos?.[0]?.projects?.[0]?.onlyId)
            }
        }
        
    },[readonly,newProjectList])
     // 添加唯一的Key
     const addKey = (list: any) => {
        list.forEach((item: any) =>{
            if(item.classificationVos?.length){
                item.classificationVos.forEach((ii: any) => {
                    if(!ii.groupIds)ii.groupIds = uuidv4();
                    if(ii.projects?.length){
                        ii.projects.forEach((iii: any) =>{
                            if(!iii.onlyId)iii.onlyId= uuidv4();
                        })
                    }
                })
            }
        })
    }
    const renderHeader = (item: any) => {
        const display = (threeStageKey != item.onlyId || threeStageKey == null || windowSize.width<1690)?'block':'none';
        let orderTypeNames: any = '';
        if(readonly){
            orderTypeNames = item?.orders?.map((i: any) => i.orderSubjectName);
        }else{
            orderTypeNames = item?.subjectVo?.map((i: any) => i.name);
        }
        return <div className="collapse-custom-header">
            <div className="project-name">{item.name}
            {
                    orderTypeNames && orderTypeNames?.length > 0 && <>【<div title={orderTypeNames.join('、')} className='project-items-name'>{orderTypeNames.join('、')}</div>】</>
                }
            </div>
         
            <div style={{ minWidth: '80px',display,boxSizing:'content-box' }} className='head-column'>{readonly?(item?.ratio??'--' ):'--'}</div>
            <div style={{ minWidth: '80px' ,display,boxSizing:'content-box' }} className='head-column'>{readonly?(item?.projectRatio?? '--' ):'--'}</div>
            {/* <div style={{ minWidth: '110px',display,boxSizing:'content-box'  }} className='head-column'>{readonly?(item?.accountRatio??'--'):'--'}</div> */}
            <div style={{ minWidth: '80px',display,boxSizing:'content-box'  }} className='head-column'>{readonly?(item?.amount??'--'):'--'}</div>
            <div style={{ width: '80px',display ,boxSizing:'content-box' }} className='head-column'>
                {readonly? item?.leaderName :userInfo.userName}
            </div>
        </div>
    }
    return <div className='project-list-wrapper-add'>
    {
        <Collapse accordion activeKey={firstStageKey} onChange={(e) => setFirstStageKey([e])}>
            {
                collapseList.map((out: any) => (
                    <Panel header={out.name} key={out.rootType}>
                        <Collapse accordion activeKey={secondStageKey} onChange={(e) => {
                            setSecondStageKey([e]);
                        }}>
                            {
                                out.classificationVos.map((middle: any) => (
                                    <Panel header={middle.name} key={middle.groupIds}>
                                        <Collapse accordion activeKey={threeStageKey} onChange={(e) => setThreeStageKey([e])} className='lowest-collapse'  expandIcon={({ isActive }) => <RightOutlined rotate={isActive ? 90 : 0} style={{ color: 'white' }} />}>
                                            {
                                                middle.projects?.map((inner: any) => (
                                                    <Panel className={['panel-custom-header-wrapper',windowSize.width<1690?'scroll-show':''].join(' ')} header={renderHeader(inner)} key={inner.onlyId}>
                                                        <CreateAssignProjectTable readonly={readonly} projectInfo={inner}/>
                                                    </Panel>
                                                ))
                                            }
                                        </Collapse>
                                    </Panel>
                                ))
                            }
                        </Collapse>
                    </Panel>
                ))
            }
     </Collapse>
    }

</div>

}
export default ProjectList;