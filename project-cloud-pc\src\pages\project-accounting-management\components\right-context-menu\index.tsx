import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Menu } from 'antd';
import { withClickOutSide } from '@/components/withClickOutSide';
import styled from 'styled-components';
const FixedMenu = withClickOutSide(styled(Menu)`
position: fixed;
z-index: 1000;
box-shadow: 0 0 5px rgba(0,0,0,0.1) !important;
`);
// 右键菜单
const RightContextMenu: React.FC<{ top: number, left: number, visible: boolean, hide: () => void, handleFun: (type: string) => void }> = (props) => {
    const [menuList, setMenuList] = useState<any[]>([]);

    useEffect(() => {
        const RightMenuAccountingBtn = [
            {
                key: 'SUB',
                name: '新增子级'
            },
            {
                key: 'SAME',
                name: '新增同级'
            },
            // {
            //     key:'DELETE',
            //     name:'删除'
            // }

        ];
        // 再次处理items
        const handleMenuItem = RightMenuAccountingBtn?.map((item: any) => {
            return {
                key: item.key,
                label: <span
                    // className='bdw-menu-item'
                    onClick={(e) => {
                        e.stopPropagation();
                        props?.handleFun(item?.key);
                        props.hide();
                    }}
                >{item.name}</span>
            }
        });
        setMenuList(handleMenuItem);
    }, [])
    return <FixedMenu
        onClickOutSide={() => {
            if (props?.visible) {
                props.hide();
            }
        }} style={{
            top: `${props.top > 400 ? props.top - 130 : props.top}px`,
            left: `${props.left}px`,
            display: props.visible ? 'block' : 'none'
        }}
        selectedKeys={[]}
        items={menuList}
        className='fixed_right_menu'
    >
    </FixedMenu>
};
export default RightContextMenu;