/**
 * @description 项目设置
 * <AUTHOR>
 * @date 2023-11-02 15:51:28
*/
import React, { useEffect, useMemo, useState } from 'react';
import { useBoolean, useRequest } from 'ahooks';
import { Form, Select, Radio, Switch, Modal, Button, Divider, message, Input, Spin } from 'antd';
import {
    BdwRow,
    BdwInput,
    BdwFormItems,
    BdwTextarea,
    BdwChooseCompanyStaff,
    BdwUploadBtn
} from '@/components';
import CustomRadio from '@/pages/myProject/createNewProject/CustomRadio';
import TimePicker from '@/pages/myProject/createNewProject/TimePicker';
import { useSelector, useDispatch, history, useParams } from 'umi';
// api
import {
    listBusinessAssociationType,
    listProjectLevel,
    listProjectType,
    listWorkSystem,
    listProjectVisibleAuthorization,
    loadOptionalList,
    projectManagementEdit,
    saveToTemplate,
    listCustomerServiceCategoryApi
} from "@/service/projectDos/myProjectApi";
import { listEmpByParams } from '@/service/projectDos/commonApi';
import { canUpdateBaseInfo } from '@/service/projectDos/my-project-detail/projectOverview';
import { formatSelectOptions } from '@/utils/utils';
import './index.less';
import { CustomRadioKey } from '@/pages/myProject/createNewProject/CustomRadio/Enum';
import moment from 'moment';
import { projectTypeListsCode, customerServiceCategoryCode } from '@/pages/myProject/Enum';

interface ProjectSettingProps {
    show: boolean
    onClose: () => void,
    template?: boolean,
    creatTemplate?: boolean,
    defaultValue?: any,
    updateBaseInfo?: boolean,
    baseInfo: any,
    currentData: any
}

const ProjectSetting: React.FC<ProjectSettingProps> = (props) => {
    const { show, onClose, template = false, creatTemplate, defaultValue = {}, updateBaseInfo = false,baseInfo,currentData } = props;
    const { projectId } = useParams<{ projectId: string }>();
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(true);
    const { data: classificationId, run: runClassificationId } = useRequest<any>(loadOptionalList, 
        {
            manual: true,
            onSuccess: (res) => {
                form.setFieldValue('classification', res[0]?.key)
            }
        });//项目分类
    useEffect(() => {
        (async () => {
            await runClassificationId();
            setLoading(false);
        })()
    }, [])
    useEffect(() => {
        const name = currentData?.name + '-' + moment().format('YYMMDD');
        form.setFieldValue('name', name);
    },[])
    const { run: submitProjectTemplate } = useRequest(saveToTemplate, {
        manual: true,
        onSuccess: (res: string, params) => {
            onClose();
            message.success('另存模板成功!');
        }
    })

    const isFinish = (data: any) => {
        submitProjectTemplate(currentData.id,data );
    }

    const close = () => {
        onClose();
    }
    return (

        <Modal
            title={'另存为模板'}
            open={show}
            width={'580px'}
            footer={null}
            onCancel={close}
            className='setting-modal'
            maskStyle={{
                background:'rgba(255,255,255,.08)'
            }}
        >
            <Spin spinning={loading}>
                <Form onFinish={isFinish} form={form}
                >
                    <div className='project-setting-container'>
                        <div className='setting-left'>
                            <BdwFormItems label={"模板名称"} name='name' required rules={[{ required: true, message: `请填写模板名称` }]}>
                                <BdwInput 
                                    style={{ fontWeight: 'bold', fontSize: '18px' }}
                                 />
                            </BdwFormItems>
                            <BdwRow type="flex-between">
                                <BdwFormItems
                                    label={`模板分类`}
                                    name='classification'
                                    required
                                    rules={[{ required: true, message: `请选择模板分类` }]}
                                    width="100%"
                                >
                                    <Select
                                        options={classificationId?.map((item: any) =>({label:item.value,value:item.key}))}
                                        bordered={false}
                                    />
                                </BdwFormItems>
                            </BdwRow>
                        </div>
                        <div className='setting-right'>
                            <Button type="primary" htmlType='submit' style={{ marginBottom: '16px' }}>保存</Button>
                            <Button style={{ marginLeft: '16px' }} onClick={close} >取消</Button>
                        </div>
                    </div>
                </Form>
            </Spin>
        </Modal>
    )
}
export default ProjectSetting