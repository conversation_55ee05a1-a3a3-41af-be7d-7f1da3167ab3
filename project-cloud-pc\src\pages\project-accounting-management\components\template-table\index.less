.template_add_new_modal {
  width: 100%;

  .near-full-width {
    width: 100% !important;
  }

  .ant-table-body {
    min-height: 500px;
  }

  .task-edit-content {
    width: 360px;
    height: 100%;
    padding-top: 16px;
    padding-left: 16px;
    overflow: auto;
  }

  .bdw-table .ant-table-content,
  .bdw-table .ant-table-header,
  .bdw-table .ant-table-body {
    padding: unset !important;
  }

  .bdw-table .ant-table-content table .ant-table-tbody tr td,
  .bdw-table .ant-table-header table .ant-table-tbody tr td,
  .bdw-table .ant-table-body table .ant-table-tbody tr td {
    background-color: unset !important;
  }

  .ant-table-thead>tr>th {
    border-top: unset !important;
    background-color: #fafafa !important;
    border-bottom: unset !important;
  }

  .ant-table-tbody>tr>td {
    padding: 8px;
  }

  .template-detail-buttons {
    height: 76px;
    padding: 12px 0;
  }

  .p-l-40 {
    padding-left: 40px;
  }

  .p-l-80 {
    padding-left: 80px;
  }

  .p-l-120 {
    padding-left: 120px;
  }

  .p-l-160 {
    padding-left: 160px;
  }

  .bdColor-b3c6e7 {
    background: #b3c6e7;
  }

  .bdColor-d9e2f3 {
    background: #d9e2f3;
  }

  .bdColor-deebf6 {
    background: #deebf6;
  }

  .task-table-content {
    height: 100%;
    padding: 16px;
    overflow: hidden;

    .click-out-side-box {
      height: calc(100% - 20px);
      overflow-y: auto;
    }
  }

  .task-edit-content {
    width: 360px;
    height: 100%;
    padding-top: 16px;
    padding-left: unset !important;
    overflow: auto;
  }
}
