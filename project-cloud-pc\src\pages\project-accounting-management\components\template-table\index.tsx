import React, { useState, useMemo } from "react";
// 组件
import { BdwTable, BdwRow } from "@/components";
import { withKeydown } from "@/components/withKeydown";
import RightContextMenu from '../right-context-menu';
import CreateTemplateInfo from "@/pages/projectTemplate/createTemplate/createTemplateTable/templateInfo";
// 插件
import styled from 'styled-components';
import { useBoolean } from 'ahooks';
// utils
import { initTaskIndex1, findDataById } from "@/utils/utils";
// umi
import { useSelector, useDispatch } from 'umi';
// antd
import { Empty } from 'antd';
import './index.less';
// 模板表格
interface Position {
    x: number,
    y: number
}
interface TemplateTableProps {
    templateList: any[];
    editStatus: boolean;
    addChildTemplateTask: (id: string, type: string, pId: string) => void;
    expandTableKeys: string[];
    expandTaskTableFun: (expandedRowKeys: any[]) => void;
    getCurFormTemplate: (info: any) => void;
    checkEditStatusFun: (bool: boolean) => void;
};
const IndexSpan = styled.span`
 font-weight: bold;
 font-size: 13px;
 margin-right: 10px;
`;
const WrapperComponent = withKeydown(BdwRow);
const TemplateTable: React.FC<TemplateTableProps> = ({ templateList, editStatus, addChildTemplateTask, expandTableKeys, expandTaskTableFun, getCurFormTemplate, checkEditStatusFun }) => {
    const [contextMenuVisible, { setTrue: showContextMenu, setFalse: hideContextMenu }] = useBoolean(false);
    const { cTaskId } = useSelector((state: any) => state.projectTemplate);
    const [requireInfo, setRequireInfo] = useState();
    const [edit, setEdit] = useState<boolean>(false);
    let dispatch = useDispatch();
    // 保存当前同级占比数据
    const [curTotalRatio, setCurTotalRatio] = useState(0);
    // 保存当前目标id
    const [curHandleId, setCurHandleId] = useState<any>(null);
    const templateListData: any = useMemo(() => {
        if (templateList?.length) {
            initTaskIndex1(templateList);
            return templateList;
        }
        return []
    }, [templateList]);
    const [contextPosition, setContextMenuPosition] = useState<Position>({
        x: -1000,
        y: -1000
    });
    // 计算缩进间距
    const computeSpace = (length?: number) => {
        switch (length) {
            case 2:
                return 'p-l-40';
            case 3:
                return 'p-l-80';
            case 4:
                return 'p-l-120';
            case 5:
                return 'p-l-160';
            default:
                return '';
        }
    }
    // 计算背景色
    const computeBackground = (length?: number) => {
        switch (length) {
            case 1:
                return 'bdColor-b3c6e7';
            case 2:
                return 'bdColor-d9e2f3';
            case 3:
                return 'bdColor-deebf6';
            default:
                return '';
        }
    };
    const TaskTableColumns = [
        {
            title: '业务事项',
            dataIndex: 'name',
            width: 220,
            ellipsis: true,
            render: (value: string, record: any) => {
                const indexSpan = <span
                    className='task-index-num'><IndexSpan>{record.index?.map((it: any) => it + 1).join('.')}</IndexSpan></span>;
                return <div >{indexSpan}{value || '-'}</div>
            }
        },
        {
            title: '同级占比',
            dataIndex: 'ratio',
            width: 200,
            render: function PeriodColumn(value: string, record: any): any {
                const paddingLeft: any = computeSpace(record?.index?.length!);
                return <div className={paddingLeft}>{value ? (value + '%') : '-'}</div>
            }
        }, {
            title: '项目占比',
            dataIndex: 'projectRatio',
            width: 200,
            render: function PeriodColumn(value: string, record: any): any {
                const paddingLeft: any = computeSpace(record?.index?.length!);
                return <div className={paddingLeft}>{value ? (value + '%') : '-'}</div>
            }
        },
        {
            title: '事项说明',
            dataIndex: 'taskExplain',
            width: 220,
            ellipsis: true,
            render: (value: string, record: any) => {
                return <div >{value || ''}</div>
            }
        }
    ];
    const onRowEvent = (item: any) => {
        return {
            onClick: () => {
                if (editStatus) {
                    setRequireInfo(item);
                };
            },
            onDoubleClick: () => {
                if (editStatus) {
                    if (item?.parentId) {
                        const curObjData = findDataById(templateList, item?.parentId);
                        // 这里应该把当前这个去除
                        let filterCurInfo = curObjData?.children?.filter((el: any) => el?.id !== item?.id);
                        const totalRatio = filterCurInfo?.reduce((sum: any, cl: any) => sum + Number(cl?.ratio || 0), 0);
                        setCurTotalRatio(totalRatio);
                    } else {
                        // 表明当前是最外层，直接获取外层的所有同级占比数据
                        // 这里仍然要去掉当前这个
                        let filterTemp = templateList?.filter((cl: any) => cl?.id !== item?.id);
                        const totalRatio = filterTemp?.reduce((sum: any, cl: any) => sum + Number(cl?.ratio || 0), 0);
                        setCurTotalRatio(totalRatio);
                    };
                    checkEditStatusFun(true);
                    setEdit(true);
                }
            },
            onContextMenu(e: any) {
                if (!editStatus) return;
                // 有编辑权限时 鼠标右键单击才弹出相应菜单操作栏
                // 禁用鼠标右键默认行为
                e.preventDefault();
                setCurHandleId(item);
                showContextMenu();
                let y = e.clientY;
                let x = e.clientX;
                if (document.body.offsetHeight - e.clientY < 300) {
                    y = document.body.offsetHeight - 200;
                };
                // @ts-ignore
                if (window.clientWidth - e.clientX < 100) {
                    // @ts-ignore
                    x = window.clientWidth - 100;
                }
                setContextMenuPosition({
                    x,
                    y
                });
                // }
            },
        }
    }
    const changeEditStatus = () => {
        setEdit(false);
        checkEditStatusFun(false);
    };
    // 保存当前项
    const saveCurItem = (info: any) => {
        getCurFormTemplate(info);
    };
    // 操作右键菜单项
    const handleFun = (type: string) => {
        addChildTemplateTask(curHandleId?.id, type, curHandleId?.parentId || null);
    };
    return (
        <BdwRow type='flex' className='flex-column height-100 template_add_new_modal'>
            {/* table */}
            <BdwRow type='flex' className='flex-1 overflow-hidden near-full-width'>
                <div className='flex-1 task-table-content' style={{
                    paddingRight: edit ? 'unset' : '16px'
                }}>
                    <WrapperComponent>
                        {
                            contextMenuVisible && <RightContextMenu top={contextPosition.y} left={contextPosition.x} visible={contextMenuVisible}
                                hide={hideContextMenu} handleFun={handleFun} />
                        }
                        <BdwTable
                            className="template-detail-table"
                            style={{ 'padding': '0 6px' }}
                            // @ts-ignore
                            columns={TaskTableColumns}
                            dataSource={templateListData}
                            useLocalData
                            onRow={onRowEvent}
                            rowClassName={(records: any) => {
                                return computeBackground(records?.index?.length);
                            }}
                            // @ts-ignore
                            rowKey={(record) => record?.id}
                            sticky
                            scroll={{ x: true }}
                            expandable={{
                                defaultExpandAllRows: true,
                                expandedRowKeys: expandTableKeys,
                                onExpandedRowsChange: (expandedRowKeys: any) => {
                                    expandTaskTableFun(expandedRowKeys)
                                },
                            }}
                            rowSelection={{
                                selectedRowKeys: cTaskId && [cTaskId] || [],
                                type: 'radio',
                                columnWidth: 1,
                                renderCell: () => {
                                    return null;
                                },
                                checkStrictly: true,
                            }}
                            locale={{
                                emptyText: <Empty description='暂无数据' />
                            }}
                            showPages={false}
                            pagination={false}
                        />
                    </WrapperComponent>
                </div>
                {edit && <div className='task-edit-content'>
                    <CreateTemplateInfo requireInfo={requireInfo} editableEvent={changeEditStatus} saveCurItem={saveCurItem} showDescription={false} curTotalRatio={curTotalRatio} />
                </div>
                }
            </BdwRow>
        </BdwRow>
    )
};
export default TemplateTable;