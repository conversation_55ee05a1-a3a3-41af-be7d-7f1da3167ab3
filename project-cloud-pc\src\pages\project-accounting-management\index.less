@import '../../styles/base.less';
.my-project-detail-container-accounting{
  .customer-management-grid-no-data-to-display{
    width: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #c5c8cd;

    img {
      width: 180px;
      height: 180px;
    }

    margin: 20px auto;
  }

  width: 100%;
  height: 100%;
  background: #fafafa;

  .my-project-detail-tabs {
    height: 100%;

    .ant-tabs-nav {
      background: white;
      .pl-16();
      height: 40px;
    }

    .ant-tabs-content-holder {
      height: calc(100% - 40px);

      .ant-tabs-content.ant-tabs-content-top {
        height: 100%;

        .ant-tabs-tabpane {
          height: 100%;
        }
      }
    }

    .wrap-container {
      height: 100%;

    }
  }

  .p-wrapper {
    // background: #fff;
    // .ant-spin-nested-loading{
    //     height: 100%;
    //     .ant-spin-container{
    //       height: 100%;
    //     }
    //   }
  }

  .tab_container {
    padding: 0px 0px 20px;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .top_tab_box {
      // border-bottom: 1px solid #eaecef;
      flex-shrink: 0;
      background-color: #2f5497;
      color: #fff;

      .customer_base_info_box {
        display: flex;
        align-items: center;
        padding: 10px 30px;
        // padding-top: 0px;

        .left_base_info {
          flex: 1;

          .top_name_order_box {
            // padding-top: 10px;

            .left_label {
              font-size: 12px;
              font-weight: 700;
              display: inline-block;
              width: 90px;
              text-align: right;
            }

            .ant-select-arrow {
              color: #fff;
            }

            .ant-input-number-handler-wrap {
              display: none;
            }

            .ant-input-number-input {
              font-weight: 700;
            }

            .ant-input-number {
              border-bottom: unset !important;
            }

            .ant-input-number-focused {
              box-shadow: unset !important;
            }
          }
        }

        .save_btn_box {
          flex-shrink: 0;
          width: 10.625rem;
        }
      }
    }

    .middle_project_box {
      flex-shrink: 0;
      padding: 0px 20px;
      padding-right: 80px;

      .middle_left_label {
        display: inline-block;
        width: 86px;
        text-align: right;
      }
      .collapse-custom-header{
        display: flex;
        .split-project-btn{
          margin-left: 20px;
          color: rgb(0, 180, 246);
        }
        .project-name{
          font-weight: bold;
          flex: 1;
          color: white;
          font-size: 14px;
          display: flex;
          .project-items-name{
            max-width: 200px;
            overflow: hidden;
            height: 22px;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
          }
          .split-project-btn{
            margin-left: 20px;
            color: rgb(0, 180, 246);
          }
        }
        .head-column {
          color: rgb(0, 180, 246);
          // padding:0 6px;
          font-weight: bold;
          padding: 0 6px;
          box-sizing: border-box;
         
        }
      }
      // 自定义最里面的一级header样式
      .lowest-collapse{
        .ant-collapse-header{
          padding-right: unset!important;
        }
      }
    }

    .bottom_content {
      display: flex;
      flex-direction: column;
      flex: 1;
      padding: 10px 20px 0px;
      box-sizing: border-box;
      white-space: nowrap;

      .accounting-table .task-table-content {
        padding-top: 10px;
      }

      .near-full-width {
        width: auto;
      }
    }
  }

  .project-list-wrapper {
    margin-top: 20px;

    .wrapper-head {
      width: '100%';
      display: flex;
      align-items: center;
      height: 50px;

    }

    .head {
      cursor: pointer;
      flex: 1;
      background: #757171;
      display: flex;
      align-items: center;
      height: 100%;
      font-weight: bold;
      color: white;
      font-size: 14px;
      box-sizing: border-box;

      .head-column {
        color: rgb(0, 180, 246);
        padding: 0 6px;

      }

      .header-name {
        width: 70px;
      }
    }

    .flex-1.task-table-content {
      padding-top: unset !important;
    }
  }

}
