/**
 * @description 我的项目->详情
 * <AUTHOR>
 * @date 2023-10-31 16:40:41
*/
import React, { useEffect, useState } from 'react';
import { useDispatch, useParams, useLocation, useSelector } from 'umi';
import { useRequest, useBoolean } from 'ahooks';
import { withClickOutSide } from '@/components/withClickOutSide';
import styled from 'styled-components';
import ProjectTasks from './projectTasks';
import { Layout, Button, Col, Row, message, Switch, InputNumber, Menu, Collapse, Modal, Checkbox } from "antd";
import { listProjectFunction, deleteProjectApi } from "@/service/projectDos/my-project-detail/projectOverview";
import { DisplayType } from './Enum';
import { myProjectDetailTabs, myProjectDetailTabKey } from '@/constants/Enum';
import { RightOutlined } from "@ant-design/icons";
import { RightMenuBtn, TaskFunctionCode, RightMenuProjectRowBtn, BtnKey, ProjectOverviewFunctionCode } from '@/constants/Enum';
import { disabledFlag } from './projectTasks';
import { queryProjectInfoApi, listTaskFunctionality, splitProjectApi } from '@/service/projectDos/my-project-detail/projectTasks'
// @ts-ignore
import EMPTY_IMG from '@/assets/image/empty.png';
import { filter } from 'lodash';
// 组件
import AssignProjectsModal from './components/assign-projects';  //分派项目弹框
import SaveAsModal from './components/save-as-modal';  //另存模板弹窗
import ChangeProjectHeader from './components/change-project-header';  //更改项目负责人弹窗
import './index.less';


interface ContentComponentProps {
    padding?: string
}
// @ts-ignore
const FixedMenu = withClickOutSide(styled(Menu)`
  position: fixed;
  z-index: 1000;
  box-shadow: 0 0 5px rgba(0,0,0,0.1) !important;
`);
const { Panel } = Collapse;
const ContextMenu: React.FC<{ top: number, left: number, projectId: string, visible: boolean, hide: () => void, onHandleByType: (type: string) => void }> = (props) => {
    const { projectId } = props;
    const { data: functionalityProject } = useRequest<any>(() => listProjectFunction(projectId), {});
    const { taskLists } = useSelector((state: any) => state.projectTasks);
    const FixedMenuItem = filter(RightMenuProjectRowBtn.map((item: any) => {
        if (disabledFlag(functionalityProject, item.key, BtnKey.projectFunctionCode)) {//忽略校验
            return null
        } else {
            return {
                key: item.key,
                label: <div
                    className='bdw-menu-item'
                    style={{
                        display: 'flex',
                        alignItems: 'center'
                    }}
                    onClick={() => {
                        props.onHandleByType(item.key);
                        props.hide();
                    }}
                >{item.name}</div>
            }
        }
    }), v => v)
    // FixedMenuItem.unshift({
    //     key: ProjectOverviewFunctionCode.SAVE_AS_TEMPLATE,
    //     label: <div
    //         className='bdw-menu-item'
    //         style={{
    //             display: 'flex',
    //             alignItems: 'center'
    //         }}
    //         onClick={() => {
    //             props.onHandleByType(ProjectOverviewFunctionCode.SAVE_AS_TEMPLATE);
    //             props.hide();
    //         }}
    //     >另存为模板</div>
    // })
    if (!taskLists?.length) {
        FixedMenuItem.push({
            key: TaskFunctionCode.ADD_NEW_TASK,
            label: <div
                className='bdw-menu-item'
                style={{
                    display: 'flex',
                    alignItems: 'center'
                }}
                onClick={() => {
                    props.onHandleByType(TaskFunctionCode.ADD_NEW_TASK);
                    props.hide();
                }}
            >添加任务</div>
        })
    }
    return <FixedMenu
        selectable={false}
        onClickOutSide={props.hide}
        style={{
            top: `${props.top}px`,
            left: `${props.left}px`,
            display: props.visible ? 'block' : 'none'
        }}
        items={FixedMenuItem}
    />

}
interface Position {
    x: number,
    y: number,
}

const MyProjectDetail: React.FC = (props) => {
    const [displayType, setDisplayType] = useState<DisplayType>(DisplayType.TABLE);
    // 分派弹框
    const [showAssignModal, setShowAssignModal] = useState({
        show: false,
        editAssign: false,
    });
    // 另存为模板弹窗
    const [showSaveAsModal, setShowSaveAsModal] = useState(false);
    const [aboutMe, setAbountMe] = useState(false);
    const location: any = useLocation();
    const { taskId } = location.query;
    const { estateId } = useParams<any>();
    const dispatch = useDispatch();
    const [contextPosition, setContextMenuPosition] = useState<Position>({
        x: -1000,
        y: -1000,
    });
    const [contextMenuVisible, { setTrue: showContextMenu, setFalse: hideContextMenu }] = useBoolean(false);
    const [deleteProjectModal, { setFalse: deleteProjectModalHide, setTrue: deleteProjectModalShow }] = useBoolean(false);//删除项目
    const { userInfo } = useSelector((state: any) => state.commonTask);//当前项目登录人信息
    const { onlyShowMe } = useSelector((state: any) => state.projectTasks);
    const [baseInfo, setBaseInfo] = useState<any>({});
    const [curProjectId, setCurProjectId] = useState('');
    const [currentData, setCurrentData] = useState<any>(null);
    // 更改项目负责人
    const [showChangeProjectHeader,setShowChangeProjectHeader] = useState(false);
    const [refresh, setRefresh] = useState(0);
    // 第一层的collapse key 
    const [firstStageKey, setFirstStageKey] = useState<any>(null);
    // 第二层的collapse key 
    const [secondStageKey, setSecondStageKey] = useState<any>(null);
    // 第三层的collapse key 
    const [threeStageKey, setThreeStageKey] = useState<any>(null);
    // 分拆项目弹窗
    const [splitProjectModal, setSplitProjectModal] = useState<any>({
        show: false,
        orderList: [],
        pInfo: null
    });
    // 设置分拆的项目订单品项
    const [selectedOrder, setSelectedOrder] = useState([]);
    useEffect(() => {
        getCustomOrderMes();
        dispatch({ type: 'commonTask/fetchUserInfo' });
    }, [])
    useEffect(() => {
        if (curProjectId) {
            dispatch({
                type: 'projectOverview/fetchProjectOverview',
                payload: curProjectId
            })
            dispatch({
                type: 'projectTasks/fetchTaskList',
                payload: curProjectId,
                loading: true,
                taskId
            })
            dispatch({
                type: 'projectTasks/fetchProjectInfo',
                payload: curProjectId
            })
        }
    }, [curProjectId, refresh])
    // 获取客户订单项目信息
    const getCustomOrderMes = (defaultType?: string) => {
        queryProjectInfoApi(estateId).then((res: any) => {
            setBaseInfo(res);
            dispatch({
                type: 'projectTasks/setCustomBaseInfo',
                payload: res
            })
            if (res.rootTypeVos) {
                setRefresh(refresh + 1);
                // 存在defaultType表明是新建完成后默认打开
                if (defaultType) {
                    const index = res.rootTypeVos.findIndex((item: any) => item.rootType == defaultType);
                    // 设置当前的项目id
                    setCurProjectId(res.rootTypeVos?.[index]?.classificationVos[0]?.projects?.[0].id);
                    // 存储当前的项目信息
                    setCurrentData(res.rootTypeVos?.[index]?.classificationVos[0]?.projects?.[0]);
                    // 设置最外层的key
                    setFirstStageKey(res.rootTypeVos?.[index]?.rootType);
                    // 设置第二层的key
                    setSecondStageKey(res.rootTypeVos?.[index]?.classificationVos[0]?.classification);
                    // 设置第三层的key
                    setThreeStageKey(res.rootTypeVos?.[index]?.classificationVos[0]?.projects?.[0].id);
                } else {
                    // 存在项目id 找到并刷新项目信息
                    if (curProjectId && firstStageKey && secondStageKey && threeStageKey) {
                        const firstIndex = res.rootTypeVos?.findIndex((i: any) => i.rootType == firstStageKey);
                        const secondIndex = res.rootTypeVos?.[firstIndex]?.classificationVos?.findIndex((i: any) => i.classification == secondStageKey);
                        const project = res.rootTypeVos?.[firstIndex]?.classificationVos[secondIndex]?.projects?.find((i: any) => i.id == threeStageKey);
                        setCurrentData(project);
                    } else {
                        // 不存在项目id 默认展开第一项
                        // 设置当前的项目id
                        setCurProjectId(res.rootTypeVos?.[0]?.classificationVos[0]?.projects?.[0].id);
                        // 存储当前的项目信息
                        setCurrentData(res.rootTypeVos?.[0]?.classificationVos[0]?.projects?.[0]);
                        // 设置最外层的key
                        setFirstStageKey(res.rootTypeVos?.[0]?.rootType);
                        // 设置第二层的key
                        setSecondStageKey(res.rootTypeVos?.[0]?.classificationVos[0]?.classification);
                        // 设置第三层的key
                        setThreeStageKey(res.rootTypeVos?.[0]?.classificationVos[0]?.projects?.[0].id);
                    }
                }

            }

        })
    }

    // 与我相关按钮
    const PageWrapperTitle = () =>
        <Switch
            className='mr-5 f-13 mb-5 style-cover'
            style={{ width: '35px' }}
            checked={onlyShowMe}
            onChange={checked => {
                dispatch({
                    type: 'projectTasks/setFilterTableData',
                    typeKey: userInfo.userId,
                    typeName: "leaderId",
                    status: checked,
                    onlyShowMe: true,
                })
            }
            }
        />
    // 打开分派项目弹框
    const openAssignProject = () => {
        if (baseInfo?.customerId) {
            setShowAssignModal({
                show: true,
                editAssign: false,
            });
        } else {
            message.error('出错了');
        }

    };
    // 点击表格头部行
    const projectClick = (item: any) => {
        setCurProjectId(item.id);
        setCurrentData(item)
    }
    // 双击表格头部行
    const projectDoubleClick = (item: any) => {
        projectClick(item);
        setShowAssignModal({
            show: true,
            editAssign: true,
        });
    }
    // 右键表格头部行
    const projectContext = (e: React.MouseEvent, item: any) => {
        e.preventDefault();
        e.stopPropagation();
        showContextMenu();
        projectClick(item);
        let y = e.clientY
        let x = e.clientX
        if (document.body.offsetHeight - e.clientY < 300) {
            y = document.body.offsetHeight - 200;
        }
        // @ts-ignore
        if (window.clientWidth - e.clientX < 100) {
            // @ts-ignore
            x = window.clientWidth - 100;
        }
        // console.log({x,y})
        setContextMenuPosition({
            x,
            y,
        });
    }
    // 右键菜单点击
    const onHandleByType = (type: string) => {
        switch (type) {
            case ProjectOverviewFunctionCode.DELETE://删除项目
                deleteProjectModalShow();
                break;
            case ProjectOverviewFunctionCode.SETTING://修改项目负责人
                setShowChangeProjectHeader(true)
            break;
            case TaskFunctionCode.ADD_NEW_TASK://添加任务
                dispatch({
                    type: 'projectTasks/addProjectTask',
                })
                listTaskFunctionality({ projectId: curProjectId }).then((res: any) => {
                    dispatch({
                        type: 'projectTasks/setFunctionality',
                        payload: res
                    })
                })
                break;
        }
    }
    // 关闭分派窗口
    const closeAssignModal = () => {
        dispatch({
            type: 'projectTasks/setNewProjectList',
            payload: []
        });
        setShowAssignModal({
            show: false,
            editAssign: false,
        })
    }
    // 确认分派、编辑分派
    const assignSuccess = (type: any) => {
        dispatch({
            type: 'projectTasks/setNewProjectList',
            payload: []
        });
        getCustomOrderMes(type);
        closeAssignModal();
    }
    const splitProject = (e: any, item: any, list: any) => {
        e.stopPropagation();
        setSplitProjectModal({
            show: true,
            orderList: list,
            pInfo: item
        })
    }
    // collapse Panel 自定义头部
    const renderHeader = (item: any) => {
        const display = (threeStageKey != item.id || threeStageKey == null) ? 'block' : 'none';
        let orderTypeNames: any[] = [], list: any[] = [];
        if (item?.orders) {
            orderTypeNames = item?.orders?.map((i: any) => {
                list.push({
                    name: i.orderSubjectName,
                    id: i.orderId
                })
                return i.orderSubjectName
            });
        } else if (item?.subjectVo) {
            orderTypeNames = item?.subjectVo?.map((i: any) => {
                list.push({
                    name: i.name,
                    id: i.id
                })
                return i.name
            });
        }

        return <div className="collapse-custom-header" onContextMenu={(e) => projectContext(e, item)} onClick={() => projectClick(item)}>
            <div className="project-name">{item.name}
                {
                    orderTypeNames && orderTypeNames?.length > 0 && <>【<div className='project-items-name' title={orderTypeNames.join('、')}>{orderTypeNames.join('、')}</div>】</>
                }
                {
                    orderTypeNames && orderTypeNames?.length > 1 && <div className="split-project-btn" onClick={(e) => splitProject(e, item, list)}>分拆项目</div>
                }
            </div>

            <div style={{ minWidth: '80px', display }} className='head-column'>{item?.ratio ?? '--'}</div>
            <div style={{ minWidth: '80px', display }} className='head-column'>{item?.projectRatio ?? '--'}</div>
            {/* <div style={{ minWidth: '110px', display }} className='head-column'>{item?.accountRatio ?? '--'}</div> */}
            <div style={{ minWidth: '80px', display }} className='head-column'>{item?.amount ?? '--'}</div>
            <div style={{ width: '80px', display }} className='head-column'>
                {item?.leaderName ?? '--'}
            </div>
        </div>
    }
    // 删除项目
    const confirmDelete = () => {
        deleteProjectApi(curProjectId).then(() => {
            message.success('删除成功！')
            getCustomOrderMes();
            deleteProjectModalHide();
        })
    }
    // 选择订单项
    const getSelectedOrderItem = (list: any) => {
        setSelectedOrder(list)
    };
    // 关闭分拆项目弹窗
    const closeSplitModal = () => {
        setSplitProjectModal({
            show: false,
            orderList: [],
            pInfo: null
        })
        setSelectedOrder([])
    }
    // 确认分拆项目
    const confirmSplitProject = () => {
        if (!selectedOrder.length) {
            message.warn('请选择要分拆的品项！');
            return
        }
        if (selectedOrder.length == splitProjectModal.orderList.length) {
            message.warn('当前项目已包含以下所有订单品项！');
            return
        }
        const params = {
            id: splitProjectModal.pInfo.id,
            accountRatio: splitProjectModal.pInfo.accountRatio,
            orgId: splitProjectModal.pInfo.orgId,
            positionId: splitProjectModal.pInfo.positionId,
            leaderId: splitProjectModal.pInfo.leaderId,
            subjectVo: selectedOrder.map((item: any) => ({ id: item }))
        }
        splitProjectApi(params).then(() => {
            message.success('分拆成功！');
            getCustomOrderMes();
            closeSplitModal();
        })

    }
    // 更新项目负责人
    const confirmRenewProject = () => {
        setShowChangeProjectHeader(false);
        queryProjectInfoApi(estateId).then((res: any) => {
            setBaseInfo(res);
        })

    }
    return (
        <div className='my-project-detail-container-accounting'>
            {
                Boolean(curProjectId) && <ContextMenu onHandleByType={onHandleByType} top={contextPosition.y} projectId={curProjectId} left={contextPosition.x} visible={contextMenuVisible}
                    hide={hideContextMenu}></ContextMenu>
            }

            <div className="tab_container">
                {/* 上方客户信息 */}
                <div className="top_tab_box">
                    {/* 客户的基本信息以及保存按钮 */}
                    <div className="customer_base_info_box">
                        {/* 左侧客户基本信息 */}
                        <div className="left_base_info">
                            {/* 客户名以及订单项目切换 */}
                            <div className="top_name_order_box">
                                <Row gutter={10} style={{
                                    display: 'flex',
                                    alignItems: 'end'
                                }}>
                                    <Col span={8}>
                                        <span className="left_label">客户：</span>
                                        <span style={{
                                            fontSize: '14px',
                                            fontWeight: 700,
                                            // paddingLeft: '11px',
                                            lineHeight: '24px'
                                        }}> {baseInfo?.customerName || '--'}</span>
                                    </Col>
                                    <Col span={8}>
                                        <span className="left_label">楼盘：</span>
                                        <span style={{
                                            fontSize: '12px',
                                            fontWeight: 700,
                                        }}> {baseInfo?.buildingName || '--'}</span>
                                    </Col>
                                    <Col span={8}>
                                        <span className="left_label">联系电话：</span>
                                        <span style={{
                                            fontSize: '12px',
                                            fontWeight: 700,
                                        }}> {baseInfo?.phone || '--'}</span>
                                    </Col>
                                </Row>
                            </div>
                        </div>
                        {/* 保存按钮 */}
                        <div className="save_btn_box">
                            <Button type="primary" style={{
                                backgroundColor: '#2b6bff',
                                color: '#fff', width: '150px'
                            }} onClick={openAssignProject}>创建项目</Button>
                        </div>
                        {
                            baseInfo?.rootTypeVos && baseInfo?.rootTypeVos?.length > 0 &&
                            <div style={{ marginLeft: '0.625rem', display: 'flex', }}>

                                {PageWrapperTitle()}<span >只看与我相关</span>
                            </div>
                        }
                    </div>
                </div>

                {/*  */}

                {/* 下方主要内容 */}
                <div className="bottom_content" style={{ width: '100%' }}>
                    {
                        baseInfo?.rootTypeVos?.length > 0 && <Collapse accordion activeKey={firstStageKey} onChange={(e) => setFirstStageKey([e])}>
                            {
                                baseInfo?.rootTypeVos?.map((out: any) => {
                                    // 是否是三级项目 还是二级项目
                                    const isExistThree = out.classificationVos.some((item: any) => item.showFlag);
                                    if(isExistThree){
                                        // 三级
                                        return <Panel header={out.name} key={out.rootType}>
                                                <Collapse activeKey={secondStageKey} accordion onChange={(e) => setSecondStageKey([e])}>
                                                    {
                                                        out.classificationVos.map((middle: any) => (
                                                            <Panel header={middle.name} key={middle.classification} >
                                                                <Collapse accordion activeKey={threeStageKey} onChange={(e: any) => {
                                                                    setThreeStageKey([e]);
                                                                    setCurProjectId(e);
                                                                    if(!e)hideContextMenu();
                                                                    
                                                                }} className='lowest-collapse' expandIcon={({ isActive }) => <RightOutlined rotate={isActive ? 90 : 0} style={{ color: 'white' }} />}>
                                                                    {
                                                                        middle.projects?.map((inner: any) => (
                                                                            <Panel className='panel-custom-header-wrapper' header={renderHeader(inner)} key={inner.id}>
                                                                                <ProjectTasks currentData={inner} projectId={inner.id} showTable={curProjectId == inner.id} />
                                                                            </Panel>
                                                                        ))
                                                                    }
                                                                </Collapse>
                                                            </Panel>
                                                        ))
                                                    }
                                                </Collapse>
                                        </Panel>
                                    }else{
                                        // 二级
                                        return <Panel header={out.name} key={out.rootType}>
    
                                                    {
                                                        out.classificationVos.map((middle: any) => (
                                                            <Collapse accordion activeKey={threeStageKey} onChange={(e: any) => {
                                                                setThreeStageKey([e]);
                                                                setCurProjectId(e);
                                                            }} className='lowest-collapse' expandIcon={({ isActive }) => <RightOutlined rotate={isActive ? 90 : 0} style={{ color: 'white' }} />}>
                                                                {
                                                                    middle.projects?.map((inner: any) => (
                                                                        <Panel className='panel-custom-header-wrapper' header={renderHeader(inner)} key={inner.id}>
                                                                            <ProjectTasks currentData={inner} projectId={inner.id} showTable={curProjectId == inner.id} />
                                                                        </Panel>
                                                                    ))
                                                                }
                                                            </Collapse>
                                                        ))
                                                    }

                                        </Panel>
                                    }

                                    
                                })
                            }
                        </Collapse>
                    }


                </div>
            </div>
            {
                showAssignModal.show && <AssignProjectsModal showModal={showAssignModal.show} baseInfo={baseInfo} closeFun={closeAssignModal} confirmFun={assignSuccess} editAssign={showAssignModal.editAssign} curEditItem={currentData} />
            }
            {/* 更改项目负责人弹窗 */}
            {
                showChangeProjectHeader && <ChangeProjectHeader showModal={showChangeProjectHeader} confirmFun={() => confirmRenewProject()} closeFun={() => setShowChangeProjectHeader(false)} currentData={currentData}  />
            }
            {/* 模板另存弹窗 */}
            {
                showSaveAsModal && <SaveAsModal show={showSaveAsModal} currentData={currentData} baseInfo={baseInfo} onClose={() => setShowSaveAsModal(false)} />
            }
            {
                deleteProjectModal && <Modal
                    open={deleteProjectModal}
                    title="项目删除"
                    onOk={confirmDelete}
                    onCancel={deleteProjectModalHide}
                    maskStyle={{
                        background: 'rgba(255,255,255,.08)'
                    }}
                >
                    确认删除项目吗？
                </Modal>
            }
            {/* 分拆项目 */}
            <Modal
                title='分拆项目'
                open={splitProjectModal.show}
                onCancel={closeSplitModal}
                okText="确认分拆"
                cancelText="取消"
                onOk={confirmSplitProject}
                maskStyle={{
                    background: 'rgba(255,255,255,.08)'
                }}
            >
                <Checkbox.Group style={{
                    width: '100%',
                    marginLeft: '6px',
                    marginTop: '8px'
                }} value={selectedOrder} onChange={getSelectedOrderItem}>
                    <Row gutter={[10, 10]} className="order_project_check">
                        {
                            splitProjectModal.orderList?.map((el: any) => (
                                <Col span={6} key={el.id}>
                                    <Checkbox value={el?.id} >
                                        {el?.name}
                                    </Checkbox></Col>
                            ))
                        }
                    </Row>

                </Checkbox.Group>
            </Modal>

        </div>
    )
}
export default MyProjectDetail;