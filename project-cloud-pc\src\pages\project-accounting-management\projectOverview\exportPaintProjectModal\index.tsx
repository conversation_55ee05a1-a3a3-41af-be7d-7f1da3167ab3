import React, {useEffect, useState} from 'react';
import { Modal} from 'antd'
import {BdwTable,BdwRow,BdwTableButton,BdwIcon} from "@/components";
import './index.less'
import {flatten} from "@/utils/utils";
import { useSelector } from 'umi';

interface ModalProps {
  visible: boolean
  cancelEvent?: () => void
}
const ExportOrPaintProjectModal: React.FC<ModalProps> = (props) =>{
   const {visible=false,cancelEvent}=props;
   const { taskLists} = useSelector((state: any) => state.projectTasks);
  const uidArr=flatten(taskLists).map((it: any)=>it.taskId);

  const TaskTableColumns = [
      {
        title: "任务",
        dataIndex: 'name',
      },
      {
        title:"工期",
        dataIndex: 'executionCycle',
        width: 60,
      }, {
        title: "负责人",
        dataIndex: 'leaderName',
        width: 80,
      }, {
        title: "起始日期",
        dataIndex: 'startTime',
        width: 120,
        render(value: any) {
          return <div>{value}</div>
        },
      }, {
        title:"截止日期",
        dataIndex: 'endTime',
        width: 120,
        render(value: any) {
          return <div>{value}</div>
        },
      }, {
        title:"状态",
        width:120,
        dataIndex: 'statusName',
      }, {
        title: "日报",
        dataIndex: 'process',
        width: 60,
        render:(_: string)=>{
            return <div>{_}</div>
        }
      },
   ];
  const [modalVisible, setModalVisible] = useState<boolean>(false)

  useEffect(() => {
    setModalVisible(visible)
  },[visible])
  // 打印
  const printTaskDetail = () => {
    console.log('打印')
  }

  // 导出
  const exportTaskDetail = () => {
    console.log('导出')
  }

  const fastButtonRenders = () => {
    return (
      <BdwRow type="flex">
        <BdwTableButton
          className='ml-6'
          icon={() => <BdwIcon type='class' icon='iconassign' />}
          onClick={()=>printTaskDetail()}
        >打印</BdwTableButton>
        <BdwTableButton
          className='ml-6'
          icon={() => <BdwIcon type='class' icon='iconreceive' />}
          onClick={()=>exportTaskDetail()}
        >导出</BdwTableButton>
      </BdwRow>
    )
  }

   return (
     <Modal
       title='导出打印'
       open={modalVisible}
       onCancel={cancelEvent}
       width={1000}
       footer={null}
     >
       <div className='project-list-warp'>
         <BdwTable
           className='project-task-table'
          fastButtonRender={fastButtonRenders}
           pagination={false}
           showPages={false}
           expandable={{
             defaultExpandAllRows: true,
             expandedRowKeys: uidArr,
           }}
           size="small"
           rowKey="taskId"
           // @ts-ignore
           columns={TaskTableColumns}
           dataSource={taskLists ?? []}
           useLocalData
         />
       </div>
     </Modal>
   )
}


export default ExportOrPaintProjectModal
