@import '../../../styles/base.less';

.project-overview-container {
  position: relative;

  .btn-overview-wrapper {
    background: white;
    padding: 12px 0;
    height: 74px;
  }

  .project-overview-content-wrapper {
    width: 100%;
    height: auto;

    .project-overview-content {
      margin: 16px auto;
      display: flex;
      width: 996px;

      .content-left {
        width: 628px;
        .mr-16();

        .task-assignment-acceptance {
          background: #fff;
          height: 180px;
          border-radius: 4px;
          .p-16();
          .mb-16();
          box-shadow: 0 1px 2px rgba(0,0,0,0.15);

          .title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
          }

        }

        .project-dynamic {
          background: #fff;
          box-shadow: 0 1px 2px rgba(0,0,0,0.15);
          .p-16();
        }
      }

      .content-right {
        width: 352px;
      }
    }
  }
}

.ant-drawer-title {
  font-weight: 700;
}

.drawer-release-content {
  padding: 0 16px;

  .information {
    font-size: 12px;
    color: #5c5c5c;
  }

  .project-name {
    font-size: 13px;
    font-weight: bold;
  }

  .c-2 {
    color: #222;
  }
}
.copy-project-label{
  position: relative;
  color: #a09e9e;
  display: inline;
  span{
    color: #ff4d4f;
    position: absolute;
    top: -1px;
    right: -8px;
  }
}
.copy-project-name-modal{
  
  .ant-input[disabled]{
    color: #333;
  }
}
.copy-project-name-modal,.disabled-name{
  .bdw-input-container{
    border-bottom: none;
  }
}
.start-abnormal-modal{
  .tips-title{
    margin-bottom: 12px;
    font-weight: bold;
  }
}


