import React, {useState} from "react";

import "./index.less";
import { BdwReadonlySpan, BdwRow, BdwTitle,BdwFileShow} from "@/components";

// import PictureGetById from "@/pages/project-management/list/components/picture-show-by-file-id";
// @ts-ignore
import ProjectDefault from "@/assets/image/project-default.png";
import {useContainer} from "@/utils/Container";
import moment from "moment";
import { Typography,Divider } from "antd";
import {useBoolean} from "ahooks";
import { useParams, useSelector, useDispatch } from 'umi';
import {DOWNLOAD_FILE} from "@/service/attachment/upload";

const projectImgRender = (text: any) => {
    return (
      <div className='table-show-image-box'>
        <img src={!text?ProjectDefault:text.url} alt='项目头像'/>
      </div>
    )
}

const { Paragraph } = Typography

const ProjectDetailReadonlyInfo: React.FC = () => {
  const { projectOverviewDetails:project } = useSelector((state: any) => state.projectOverview);

  const projectAboutUsers = project?.projectMembers?.map((item: any) => item.userName).join(",");

  const [fullProjectDesc, {setFalse:fullProjectDescHide, setTrue: fullProjectDescShow}] = useBoolean(false)
  const [paragraphKey,setParagraphKey] = useState<number>(0)

  const onCollapse = (e:any) => {
    e.preventDefault()
    fullProjectDescHide()
    setParagraphKey(paragraphKey + 1)
  }

  return (
    <div className='project-detail-readonly-info'>
      <BdwRow type='flex'>
        <div className='project-image'>
          {projectImgRender(project?.coverPicture!)}
        </div>
        <div>
          <div className='project-name'>
            {project?.projectName}
          </div>
          <div className='project-other-info'>
            {project?.createUserName}{moment(project?.createTime).format("YYYY年MM月DD日")}创建
          </div>
        </div>
      </BdwRow>
      <div className='mt-16'>
        <BdwTitle>项目介绍</BdwTitle>
        <BdwReadonlySpan className='pre-wrap' key={paragraphKey}>
          <Paragraph ellipsis={{rows:2,expandable:true,symbol:'查看详情',onExpand:fullProjectDescShow}}>
            {project?.instructions}
          </Paragraph>
          {fullProjectDesc && <a href="javascript:void(0);" onClick={onCollapse}>收起详情</a>}
        </BdwReadonlySpan>
      </div>
      <div className='mt-16'>
        <BdwTitle>项目周期</BdwTitle>
        <BdwReadonlySpan>
          <span>{project?.startDate} 至 {project?.endDate}</span>
          <span style={{marginLeft:'16px'}}>总计: {project?.projectCycle} 天</span>
        </BdwReadonlySpan>
      </div>
      <Divider className='mt-16' dashed={true} />
      <div className='important-title mt-16'>项目信息</div>
      <div className='mt-16'>
        <BdwTitle>项目负责人</BdwTitle>
        <BdwReadonlySpan>
          {project?.leaderName}
        </BdwReadonlySpan>
      </div>
      <div className="mt-16">
        <BdwTitle>与此项目关联的人员</BdwTitle>
        <BdwReadonlySpan>
          {
            projectAboutUsers
          }
        </BdwReadonlySpan>
      </div>
      {
        project?.associationInfo &&
        <div className="mt-16">
          <BdwTitle>项目业务关联</BdwTitle>
          <BdwReadonlySpan>
            {project?.associationInfo}
          </BdwReadonlySpan>
        </div>
      }

      <div className="mt-16">
        <BdwTitle>项目分类</BdwTitle>
        <BdwReadonlySpan>
          {project?.classificationName}
        </BdwReadonlySpan>
      </div>
      <div className='mt-16'>
        <BdwTitle>项目附件资料</BdwTitle>
        {
          project?.document && project?.document.length > 0 ?
            <BdwFileShow attachments={project?.document ?? []} /> : <BdwReadonlySpan>无附件资料</BdwReadonlySpan>
        }
      </div>
    </div>
  )
}

export default ProjectDetailReadonlyInfo
