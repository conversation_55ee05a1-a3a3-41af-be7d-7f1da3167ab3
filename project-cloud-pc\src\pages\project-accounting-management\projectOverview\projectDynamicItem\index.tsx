import React from "react";
import "./index.less";
import {MenuItemShowNameIcon,BdwRow,BdwReadonlySpan,BdwFileShow} from "@/components";

interface ProjectDynamicItemProps {
  title: string
  time: string
  detail: string
  className?: string
  personName: string
  attach: string[]
}

const ProjectDynamicItem: React.FC<ProjectDynamicItemProps> = (props) => {
  const {title, time, detail = "", className = '', personName = "", attach = []} = props;
  return (
    <div className={`project-dynamic-item ${className}`}>
      <BdwRow type='flex' className='project-dynamic-item-title'>
        <BdwRow type='flex' className='project-dynamic-item-title-content flex-1'>
          <div className='mr-10'>
            <MenuItemShowNameIcon name={personName}/>
          </div>
          <div className='project-dynamic-item-title-word flex-1'>
            <span className='mr-5 fontWed'>{personName}</span>
            <span>{title}</span>
          </div>
        </BdwRow>
        <div className='project-dynamic-item-title-time'>
          {time}
        </div>
      </BdwRow>
      {
        detail &&
        <BdwRow className='project-dynamic-item-content'>
          <BdwReadonlySpan>
            <div>
              {detail}
            </div>
          </BdwReadonlySpan>
        </BdwRow>
      }
      {
        attach && attach.length > 0 &&
        <div className='project-dynamic-item-attach-content'>
          <BdwFileShow  attachments={attach}/>
        </div>
      }
    </div>
  )
}

export default ProjectDynamicItem
