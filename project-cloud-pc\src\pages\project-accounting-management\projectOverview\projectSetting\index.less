@import '../../../../styles/base.less';

.setting-modal {

  .project-setting-container {
    min-height: 300px;
    // overflow-y: scroll;

    display: flex;
    justify-content: space-between;

    .setting-left {
      width: 70%;
      height: 100%;
      border-right: 1px dashed @divider;
      padding-right: 16px;
    }

    .setting-right {
      width: 30%;
      height: 100%;
      padding-left: 16px;
      display: flex;
      flex-direction: column;
    }

  }

  .project-setting-container::-webkit-scrollbar {
    width: 2px;
    height: 2px;
  }

  .project-setting-container::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px #E1E1E1;
    background-color: #E1E1E1;
    border-radius: 2px;
  }

  .project-setting-container::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 5px #8c8c8c;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 2px;
  }
  // .ant-input[disabled],.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector{
  //   color: #333;
  // }
}
