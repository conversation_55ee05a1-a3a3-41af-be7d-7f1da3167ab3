/**
 * @description 项目设置
 * <AUTHOR>
 * @date 2023-11-02 15:51:28
*/
import React, { useEffect, useMemo, useState } from 'react';
import { useBoolean, useRequest } from 'ahooks';
import { Form, Select, Radio, Switch, Modal, Button, Divider, message, Input, Spin } from 'antd';
import {
    BdwRow,
    BdwInput,
    BdwFormItems,
    BdwTextarea,
    BdwChooseCompanyStaff,
    BdwUploadBtn
} from '@/components';
import CustomRadio from '@/pages/myProject/createNewProject/CustomRadio';
import TimePicker from '@/pages/myProject/createNewProject/TimePicker';
import { useSelector, useDispatch, history, useParams } from 'umi';
// api
import {
    listBusinessAssociationType,
    listProjectLevel,
    listProjectType,
    listWorkSystem,
    listProjectVisibleAuthorization,
    loadOptionalList,
    projectManagementEdit,
    saveToTemplate,
    listCustomerServiceCategoryApi
} from "@/service/projectDos/myProjectApi";
import { listEmpByParams } from '@/service/projectDos/commonApi';
import { canUpdateBaseInfo } from '@/service/projectDos/my-project-detail/projectOverview';
import { formatSelectOptions } from '@/utils/utils';
import './index.less';
import { CustomRadioKey } from '@/pages/myProject/createNewProject/CustomRadio/Enum';
import { VisibleAuthorization } from './Enum';
import moment from 'moment';
import { projectTypeListsCode, customerServiceCategoryCode } from '@/pages/myProject/Enum';

interface ProjectSettingProps {
    show: boolean
    onClose: () => void,
    template?: boolean,
    creatTemplate?: boolean,
    defaultValue?: any,
    updateBaseInfo?: boolean
}

const ProjectSetting: React.FC<ProjectSettingProps> = (props) => {
    const { show, onClose, template = false, creatTemplate, defaultValue = {}, updateBaseInfo = false } = props;
    const { projectId } = useParams<{ projectId: string }>();
    const titleLabel = template ? '模板' : '项目';
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(true);
    const { data: classificationId, run: runClassificationId } = useRequest<any>(loadOptionalList, {manual: true});//项目分类
    const { data: listCustomerServiceCategory, run: runListCustomerServiceCategory } = useRequest<any>(listCustomerServiceCategoryApi, { manual: true });//客户分类
    const { data: projectLevel, run: runProjectLevel } = useRequest(listProjectLevel, { manual: true });//项目级别
    const { data: projectType, run: runProjectType } = useRequest(listProjectType, { manual: true });//项目类型
    const { data: businessAssociationType, run: runBusinessAssociationType } = useRequest<any>(listBusinessAssociationType, { manual: true });//项目业务关联
    const { data: workSystem, run: runWorkSystem } = useRequest(listWorkSystem, {manual: true});//工作制度
    const { data: visibleAuthorization, run: runVisibleAuthorization } = useRequest(listProjectVisibleAuthorization, { manual: true });
    const [templateDisabled, { setFalse: templateDisabledFalse, setTrue: templateDisabledTrue }] = useBoolean(false);
    const [projectSettingDisabled, { setFalse: projectSettingDisabledFalse, setTrue: projectSettingDisabledTrue }] = useBoolean(false);
    const { projectOverviewDetails } = useSelector((state: any) => state.projectOverview);//项目概览数据
    const [copyTemplateId, setCopyTemplateId] = useState('');
    const [bATypeDefaultValue, setBATypeDefaultValue] = useState<any>(undefined);
    useEffect(() => {
        (async () => {
            await runClassificationId();
            await runProjectLevel();
            await runProjectType();
            await runBusinessAssociationType();
            await runWorkSystem();
            await runVisibleAuthorization();
            await runListCustomerServiceCategory();
            //判断项目信息是否能编辑
            if (updateBaseInfo) {
                canUpdateBaseInfo(projectId).then((res) => {
                    if (res) {
                        projectSettingDisabledFalse();
                    } else {
                        projectSettingDisabledTrue();
                    }
                })
            }
            templateDisabledFalse();
            setLoading(false);
        })()
    }, [])
    //保存编辑数据
    const { run: submitCreate, loading: submitLoading } = useRequest(projectManagementEdit, {
        manual: true,
        onSuccess: (res: string, params) => {
            if (creatTemplate) {
                onClose();
                message.success('新建成功!')
                history.push(`/project-template-detail/${res}`);
            } else {
                dispatch({
                    type: 'projectOverview/fetchProjectOverview',
                    payload: res
                })
                dispatch({
                    type: 'projectTasks/fetchProjectInfo',
                    payload: res
                })
                message.success('保存成功');
            }
            // onClose();
        }
    })
    const { run: submitProjectTemplate } = useRequest(saveToTemplate, {
        manual: true,
        onSuccess: (res: string, params) => {
            dispatch({
                type: 'projectOverview/fetchProjectOverview',
                payload: res
            })
            onClose();
            message.success('另存模板成功!');
            setCopyTemplateId(res)
            templateDisabledTrue();
        }
    })

    const isFinish = (data: any) => {
        const projectMembers = template ? undefined : data.projectMembers?.map((item: any) => ({
            userId: item.id
        }));
        const startDate = template ? undefined : moment(data.projectCycle[0]).format('YYYY-MM-DD')
        const endDate = template ? undefined : moment(data.projectCycle[1]).format('YYYY-MM-DD')
        const document = data.document?.map((item: any) => item.id).join();
        const params = {
            ...data,
            projectMembers,
            startDate,
            endDate,
            document,
            coverPictureId: data.coverPictureId?.id,
            leaderId: template ? undefined : data.leaderId.id,
            projectId: template ? undefined : projectOverviewDetails.projectId,
            businessAssociationType: template ? undefined : (data.businessAssociationType == CustomRadioKey.noAssociation ? undefined : data.businessAssociationType),
            customerServiceCategory: template ?  (data.type == projectTypeListsCode.CUSTOMER?data.customerServiceCategory:undefined) : undefined,
            template
        }
        delete params.projectCycle;
        if (template) {//是否是模板项目
            if (creatTemplate) {//是否是新建模板项目
                submitCreate(params);
            } else {
                submitProjectTemplate(projectOverviewDetails.projectId, params);
            }
        } else {
            submitCreate(params);
        }
    }
    const InitFormValue = useMemo(() => {
        if (projectOverviewDetails && !creatTemplate) {
            const projectMembers = projectOverviewDetails.projectMembers?.map((item: any) => ({
                id: item.userId,
                name: item.userName
            }))
            let leaderId:{id?:string,name?:string} = { id: projectOverviewDetails.leaderId, name: projectOverviewDetails.leaderName };
            if(!projectOverviewDetails.leaderId && !projectOverviewDetails.leaderName){
                leaderId = {}
            }
            let projectCycle;
            if (projectOverviewDetails.startDate && projectOverviewDetails.endDate) {
                projectCycle = [moment(projectOverviewDetails.startDate), moment(projectOverviewDetails.endDate)];
            }
            if (projectOverviewDetails.businessAssociationType == CustomRadioKey.CUSTOMER_ASSOCIATION) {
                setBATypeDefaultValue({
                    customerName: projectOverviewDetails.customerName,
                    mobilePhone: projectOverviewDetails.customerPhone,
                    buildingName: projectOverviewDetails.estateName,
                })
            }
            if (projectOverviewDetails.businessAssociationType == CustomRadioKey.ORDER_ASSOCIATION) {
                setBATypeDefaultValue({
                    customerName: projectOverviewDetails.customerName,
                    customerPhone: projectOverviewDetails.customerPhone,
                    orderSubject: projectOverviewDetails.orderSubjectName,
                })
            }
            return {
                name: projectOverviewDetails.projectName,
                instructions: projectOverviewDetails.instructions,
                leaderId: leaderId,
                workSystem: projectOverviewDetails.workSystemCode,
                projectCycle,
                projectMembers: projectMembers,
                businessAssociationType: projectOverviewDetails.businessAssociationType ? projectOverviewDetails.businessAssociationType : CustomRadioKey.noAssociation,
                classification: projectOverviewDetails.classification,
                level: projectOverviewDetails.level,
                visibleAuthorization: projectOverviewDetails.visibleAuthorization,
                type: projectOverviewDetails.type,
                needAudit: projectOverviewDetails.needAudit,
                document: projectOverviewDetails.document,
                canSimultaneouslyParticipateMultipleTasks: projectOverviewDetails.canSimultaneouslyParticipateMultipleTasks,
                coverPictureId: projectOverviewDetails.coverPicture,
                projectBusinessId: projectOverviewDetails.projectBusinessId,
                customerServiceCategory: projectOverviewDetails.customerServiceCategoryCode,
                estateId: projectOverviewDetails.estateId,
                ...defaultValue
            }

        } else {
            return {
                visibleAuthorization: VisibleAuthorization.PARTICIPANT,
                type: projectTypeListsCode.CUSTOMER,
                customerServiceCategory: customerServiceCategoryCode.WHOLESALE_CUSTOMER,
                ...defaultValue,
            }
        }

    }, [projectOverviewDetails])

    const close = () => {
        templateDisabledFalse();
        onClose();
    }
    return (

        <Modal
            title={template ? creatTemplate ? "空白模板设计" : "项目另存为模板" : "项目信息更新"}
            open={show}
            width={'70%'}
            footer={null}
            onCancel={close}
            className='setting-modal'
        >
            <Spin spinning={loading}>
                <Form onFinish={isFinish} form={form}
                    initialValues={InitFormValue}
                    disabled={templateDisabled}
                >
                    <div className='project-setting-container'>
                        <div className='setting-left'>
                            {/* <Form onFinish={isFinish} form={form}> */}
                            <BdwFormItems label={titleLabel + "名称"} name='name' required rules={[{ required: true, message: `请填写${titleLabel}名称` }]}>
                                <BdwInput 
                                    // disabled={projectSettingDisabled} 
                                    style={{ fontWeight: 'bold', fontSize: '18px' }}
                                 />
                            </BdwFormItems>
                            <BdwFormItems label={`${titleLabel}说明`} name='instructions' dependencies={['instructions']} rules={[{ required: true, message: `请填写${titleLabel}说明` }]}>
                                <BdwTextarea />
                            </BdwFormItems>


                            {
                                !template && <BdwFormItems
                                    label="项目负责人"
                                    name='leaderId'
                                    required
                                    rules={[{ required: true, message: "请选择项目负责人" }]}
                                >
                                    <BdwChooseCompanyStaff
                                     placeholder="选择项目负责人" 
                                    //  disabled={projectSettingDisabled} 
                                     apiSrc={listEmpByParams} />
                                </BdwFormItems>
                            }


                            <BdwRow type="flex-between">
                                {
                                    <BdwFormItems
                                        label="工作制度"
                                        name='workSystem'
                                        required
                                        rules={[{ required: true, message: "请选择工作制度" }]}
                                        width="48%"
                                    >
                                        <Radio.Group
                                            options={formatSelectOptions(workSystem)}
                                            disabled={projectSettingDisabled}
                                        />
                                    </BdwFormItems>
                                }

                                {
                                    !template && <Form.Item
                                        noStyle
                                        shouldUpdate={(p: any, c: any) => {
                                            return p.workSystem != c.workSystem
                                        }}
                                    >
                                        {
                                            () => {
                                                return <BdwFormItems
                                                    label="项目周期"
                                                    name='projectCycle'
                                                    required
                                                    rules={[{ required: true, message: "请选择项目周期" }]}
                                                    width="48%"

                                                >
                                                    <TimePicker
                                                        workSystems={form.getFieldValue('workSystem')}
                                                        // disabled={projectSettingDisabled}
                                                    />
                                                </BdwFormItems>
                                            }
                                        }
                                    </Form.Item>
                                }
                            </BdwRow>
                            {

                                !template && <BdwFormItems
                                    label="与此项目关联的人员"
                                    name='projectMembers'
                                    required
                                    rules={[{ required: true, message: "请选择与此项目关联的人员" }]}
                                >
                                    <BdwChooseCompanyStaff 
                                    placeholder="请选择与此项目关联的人员"
                                    //  disabled={projectSettingDisabled} 
                                     apiSrc={listEmpByParams} 
                                     type='multiple' />
                                </BdwFormItems>
                            }
                            {
                                !template && <>
                                    <BdwFormItems label="项目业务关联" name='businessAssociationType'>
                                        <CustomRadio defaultCustomValue={bATypeDefaultValue} disabled={projectSettingDisabled} businessAssociationType={businessAssociationType} form={form} />
                                    </BdwFormItems>
                                    <BdwFormItems hidden name='projectBusinessId'>
                                        <Input disabled />
                                    </BdwFormItems>
                                </>
                            }


                            <BdwRow type="flex-between">
                                <BdwFormItems
                                    label={`${titleLabel}分类`}
                                    name='classification'
                                    required
                                    rules={[{ required: true, message: `请选择${titleLabel}分类` }]}
                                    width="48%"
                                >
                                    <Select
                                        options={classificationId?.map((item: any) =>({label:item.value,value:item.key}))}
                                        bordered={false}
                                        // disabled={projectSettingDisabled}
                                    />
                                </BdwFormItems>
                                <BdwFormItems
                                    label={`${titleLabel}级别`}
                                    name='level'
                                    required
                                    rules={[{ required: true, message: `请选择${titleLabel}级别` }]}
                                    width="48%"
                                >
                                    <Select
                                        options={formatSelectOptions(projectLevel)}
                                        bordered={false}
                                        // disabled={projectSettingDisabled}
                                    />
                                </BdwFormItems>
                            </BdwRow>
                            <BdwRow type="flex-between">
                                <BdwFormItems
                                    label={`${titleLabel}可见授权`}
                                    name='visibleAuthorization'
                                    width="48%"
                                >
                                    <Select
                                        options={formatSelectOptions(visibleAuthorization)}
                                        bordered={false}
                                        // disabled={projectSettingDisabled}
                                    />
                                </BdwFormItems>
                                <BdwFormItems
                                    label={`${titleLabel}类型`}
                                    name='type'
                                    width="48%"
                                    rules={[{ required: true, message: `请选择${titleLabel}类型` }]}
                                >
                                    <Select
                                        options={formatSelectOptions(projectType)}
                                        bordered={false}
                                        // disabled={projectSettingDisabled}
                                    />
                                </BdwFormItems>
                            </BdwRow>
                            {
                                template && <Form.Item 
                                    noStyle
                                    shouldUpdate={(p,c)=>p.type != c.type}
                                >
                                    {
                                        ({getFieldValue}) => {
                                            if(getFieldValue('type') == projectTypeListsCode.CUSTOMER){
                                                return <BdwFormItems
                                                label="客户分类"
                                                name='customerServiceCategory'
                                                required
                                                rules={[{ required: true, message: `请选择客户分类` }]}
                                                width="48%"
                                            >
                                                <Select
                                                    options={formatSelectOptions(listCustomerServiceCategory)}
                                                    bordered={false}
                                                    // disabled={projectSettingDisabled}
                                                />
                                            </BdwFormItems>
                                            }else{
                                                return null
                                            }
                                        }
                                    }

                                </Form.Item>
                            }
                            
                            {
                                !creatTemplate && <BdwRow type="flex-between">
                                    <BdwFormItems
                                        label={`${titleLabel}任务是否需要报审、审核、发布启用`}
                                        name='needAudit'
                                        width="48%"
                                        valuePropName="checked"
                                    >
                                        <Switch disabled={projectSettingDisabled} />
                                    </BdwFormItems>
                                    <BdwFormItems
                                        label="同一人同一时段是否允许参与多个任务"
                                        name='canSimultaneouslyParticipateMultipleTasks'
                                        width="48%"
                                        valuePropName="checked"
                                    >
                                        <Switch 
                                            // disabled={projectSettingDisabled} 
                                        />
                                    </BdwFormItems>
                                </BdwRow>
                            }

                        </div>
                        <div className='setting-right'>
                            {
                                templateDisabled ? <>
                                    <Button
                                        type="primary"
                                        disabled={false}
                                        onClick={() => {
                                            close();
                                            window.open(`/#/my-project-detail/${copyTemplateId}`)
                                        }}
                                        style={{ marginBottom: '16px' }}
                                    >
                                        打开项目模板
                                    </Button>
                                    <Button style={{ marginBottom: '16px' }} disabled={false} onClick={close} >关闭对话框</Button>
                                </> :
                                    <>
                                        <Button type="primary" htmlType='submit' style={{ marginBottom: '16px' }}>保存</Button>
                                        <Button style={{ marginBottom: '16px' }} onClick={close} >取消</Button>
                                    </>
                            }
                            <BdwFormItems
                                label={`${titleLabel}文档`}
                                name='document'
                            >
                                <BdwUploadBtn name={'本地上传'} />
                            </BdwFormItems>
                            <Divider dashed={true} style={{ margin: '0 0 16px 0', borderColor: '#ddd' }} />
                            {
                                !template && <>
                                    <div className='mb-16 f-12'>
                                        <div className='font-form-label'>项目创建人</div>
                                        <div className='f-13' style={{ color: '#000' }}>{projectOverviewDetails.createUserName}</div>
                                    </div>
                                    <div className='mb-16 f-12'>
                                        <div className='font-form-label'>项目创建时间</div>
                                        <div className='f-13' style={{ color: '#000' }}>{projectOverviewDetails.createTime}</div>
                                    </div>
                                </>
                            }
                            <Form.Item
                                noStyle
                                shouldUpdate={(p: any, c: any) => {
                                    return p.coverPictureId != c.coverPictureId
                                }}
                            >
                                {
                                    ({ getFieldValue }) => {
                                        return (<BdwFormItems
                                            label={`${titleLabel}封面`}
                                            name='coverPictureId'
                                        >
                                            <BdwUploadBtn
                                                uploadType='single'
                                                accept='.png,.jpg,.jpeg,.gif,.bmp'
                                                color='#333'
                                                icon={false}
                                                name={getFieldValue('coverPictureId') ? '更换封面图片' : '上传封面图片'}
                                                showUploadList={false}
                                            >
                                                {getFieldValue('coverPictureId') && <img src={getFieldValue('coverPictureId')?.url} width={160} height={120} style={{ margin: '4px 0' }} alt="" />}
                                            </BdwUploadBtn>
                                        </BdwFormItems>)
                                    }
                                }

                            </Form.Item>

                        </div>
                    </div>
                </Form>
            </Spin>
        </Modal>
    )
}
export default ProjectSetting