import React ,{useState,useEffect}from "react";
import {Button, Drawer, Form, message,Divider} from "antd";
import {
  BdwUploadBtn,
  BdwFormItems,
  BdwReadonlySpan,
  BdwTextarea
} from "@/components";
import { dealTaskAssign } from '@/service/projectDos/my-project-detail/projectTasks';;
import {acceptTaskRule} from "./rute";
import "./index.less";
import TaskInfo from "../task-info-comp";
import { useParams, useSelector, useDispatch } from 'umi';
import { BtnOperateType } from '@/constants/Enum';


export interface AcceptTaskDrawerProps {
  visible: boolean
  cancelEvent?: () => void
  successEvent?: () => void
  task: any,
  assignType: string
}

const AcceptTaskDrawer: React.FC<AcceptTaskDrawerProps> = (props) => {
  const {visible, cancelEvent, successEvent, task,assignType} = props;
  const { taskInfo } = useSelector((state: any) => state.projectTasks);
  const { projectId } = useParams<{ projectId: string }>();
  const  [currentType, setCurrentType] = useState<any>(null);
  useEffect(() => {
    if (assignType == BtnOperateType.RETURN) {
      setCurrentType({
        title:'退回任务',
        remarkLabel:'退回原因',
        remarkLabelPlaceholder:'请输入退回原因',
        confirmBtnName:'确认退回',
        acceptTaskRule: acceptTaskRule.refuse,
        successMessage:'任务退回成功！'
      })
    } else if (assignType == BtnOperateType.RECEIVE) {
      setCurrentType({
        title:'接收任务',
        remarkLabel:'备注',
        remarkLabelPlaceholder:'请输入备注',
        confirmBtnName:'确认接收',
        acceptTaskRule: acceptTaskRule.receipt,
        successMessage:'任务接收成功！'
      })
    }
  }, [assignType])

  const [form] = Form.useForm();
  const submitData = async () => {
    try {
      const formValues = form.getFieldsValue();
      const submitInfo = {
        ...formValues,
        taskId:task.taskId,
        dealType: assignType,
        document:formValues.document?.map((item: any) => item.id).join(),
      }
      // 调用成功后保存方法
      await dealTaskAssign(submitInfo)
      message.success(currentType?.successMessage)
      successEvent?.()
    } catch (e) {
    }
  }

  return (
    <Drawer
      zIndex={1001}
      open={visible}
      title= {<BdwReadonlySpan importantLevel="veryImportant">{currentType?.title}</BdwReadonlySpan>}
      width={700}
      onClose={() => cancelEvent?.()}
      destroyOnClose
      maskClosable={false}
    >
      <TaskInfo task={task} >
        <Form form={form} className='accept-task-drawer' onFinish={() => submitData()} preserve={false}>
          <Divider className='mt-16'  />
          <div className='mt-16'>
            <BdwFormItems label={currentType?.remarkLabel} name="dealRemark" rules={currentType?.acceptTaskRule}>
              <BdwTextarea placeholder={currentType?.remarkLabelPlaceholder} autoSize  maxLength={512} onKeyDown={(e)=>{
                if(e.keyCode===13){
                  e.nativeEvent.stopImmediatePropagation()
                }
              }}/>
            </BdwFormItems>
          </div>
          <div className='mt-16'>
            <BdwFormItems label="附件资料" name='document'>
              <BdwUploadBtn/>
            </BdwFormItems>
          </div>
          <div className='mt-16'>
            <Button htmlType='submit' type='primary' className='mr-16'>{currentType?.confirmBtnName}</Button>
            <Button onClick={() => cancelEvent?.()}>返回</Button>
          </div>
        </Form>
      </TaskInfo>
    </Drawer>
  )
}

export default AcceptTaskDrawer
