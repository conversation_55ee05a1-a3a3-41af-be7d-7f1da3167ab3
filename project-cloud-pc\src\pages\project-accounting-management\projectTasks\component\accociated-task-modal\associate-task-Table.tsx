import { DatePicker, Input } from 'antd';
import React from 'react';
import styled from 'styled-components';
import moment from "moment";
import { BdwRow, BdwTable, BdwTableHeaderSearchComponent } from "@/components";
import { useParams, useSelector, useDispatch } from 'umi';
import { findTaskItem } from '@/utils/utils';
import Highlighter from "react-highlight-words"

import "./index.less"

const IndexSpan = styled.span`
 font-weight: bold;
 font-size: 13px;
 margin-right: 10px;
`;

interface TaskTableProps {
  selectedRowKeysArr?: Array<any>
  setSelectedRowKeysArr?: (val: Array<any>,name: string) => void
}



const NotSupport = styled.div`
  cursor: not-allowed;
  width: 100%;
  height: 100%;
`

const AssociateTaskTable: React.FC<TaskTableProps> = (props) => {
  const dispatch = useDispatch();
  const { associatedTasksTableData, ATExpandedRowKeys, associatedTasksFilter, taskInfo, isAddTask } = useSelector((state: any) => state.projectTasks);
  const { selectedRowKeysArr, setSelectedRowKeysArr } = props
   // 计算缩进间距
   const computeSpace = (length?: number) => {
    switch (length) {
        case 2:
            return 'p-l-40';
        case 3:
            return 'p-l-80';
        case 4:
            return 'p-l-120';
        case 5:
            return 'p-l-160';
        default:
            return '';
    }
}
 // 计算背景色
 const computeBackground = (length?: number) => {
  switch (length) {
      case 1:
          return 'bdColor-b3c6e7';
      case 2:
          return 'bdColor-d9e2f3';
      case 3:
          return 'bdColor-deebf6';
      default:
          return 'bdColor-deebf6';
  }
}
  // 任务表格表头项
  const TaskTableColumns = [
    {
      title: <div className='table-header-column'>
      业务事项
  </div>,
      dataIndex: 'name',
      // render: (value: string, record: any) =>  {
      //   const indexSpan = <span
      //     className='task-index-num'><IndexSpan>{record.index?.map(it => it + 1).join('.')}</IndexSpan></span>;
      //   return <BdwRow type='flex'>
      //     <div>{indexSpan}{record?.title}</div>
      //   </BdwRow>

      // },
      ellipsis: true
    },
    // {
    //   title: <BdwTableHeaderSearchComponent title="工期"><NotSupport /></BdwTableHeaderSearchComponent>,
    //   dataIndex: 'executionCycle',
    //   width: 60,
    //   // render: (value: string, record: any): any =>{
    //   //   return <>{record.executionCycle}</>
    //   // }
    // },
    {
      title: <div className='table-header-column'>
          同级占比
      </div>,
      dataIndex: 'leaderName4',
      width: 80,
      ellipsis: true,
      render: function HeaderColumn(value: string, record: any): any {
          const paddingLeft: any = computeSpace(record?.index?.length!);
          return <div  className={paddingLeft}>{'--'}</div>
          return <div  className={paddingLeft}>{value ? (value + '%') : '-'}</div>
      }
  },
  {
      title: <div className='table-header-column'>
          项目占比
      </div>,
      dataIndex: 'leaderName3',
      width: 80,
      ellipsis: true,
      render: function HeaderColumn(value: string, record: any): any {
          const paddingLeft: any = computeSpace(record?.index?.length!);
          return <div  className={paddingLeft}>{'--'}</div>
          return <div className={paddingLeft}>{value ? (value + '%') : '-'}</div>
      }
  },
  // {
  //     title: <div className='table-header-column'>
  //         核算计入比例
  //     </div>,
  //     dataIndex: 'leaderName2',
  //     width: 110,
  //     ellipsis: true,
  //     render: function HeaderColumn(value: string, record: any): any {
  //         const paddingLeft: any = computeSpace(record?.index?.length!);
  //         return <div  className={paddingLeft}>{'--'}</div>
  //         return <div className={paddingLeft}>{value ? (value + '%') : '-'}</div>
  //     }
  // },
  {
      title: <div className='table-header-column'>
          核算金额
      </div>,
      dataIndex: 'leaderName1',
      width: 80,
      ellipsis: true,
      render: function HeaderColumn(value: string, record: any): any {
          const paddingLeft: any = computeSpace(record?.index?.length!);
          return <div  className={paddingLeft}>{'--'}</div>
          return <div className={paddingLeft}>{value ?? '-'}</div>
      }
  },
    {
      title: <div className='table-header-column'>
      负责人
  </div>,
      dataIndex: 'leaderName',
      width: 80,
    },
  ];

  return (
    <BdwTable
      className='accociated-task-table'
      pagination={false}
      loading={{
        spinning: false
      }}
      expandable={{
        defaultExpandAllRows: true,
        expandedRowKeys: ATExpandedRowKeys,
        onExpandedRowsChange: (e) => {
          dispatch({
            type: 'projectTasks/setATExpandedRowKeys',
            payload: e
          })
        },
      }}
      size="small"
      rowKey="taskId"
      // @ts-ignore
      columns={TaskTableColumns}
      dataSource={associatedTasksTableData ?? []}
      useLocalData
      sticky
      // scroll={{ x: true }}
      showPages={false}
      rowClassName={(records: any) => {
        return computeBackground(records?.index?.length);
      }}
      rowSelection={{
        selectedRowKeys:selectedRowKeysArr,
        type: 'checkbox',
        hideSelectAll: true,
        columnWidth: 30,
        checkStrictly: true,
        onChange: ((selectedRowKeys: any) => {
          // @ts-ignore
          const newSelectedRowKeysArr = selectedRowKeys.filter((item:any) => !selectedRowKeysArr.includes(item));
          findTaskItem(associatedTasksTableData,newSelectedRowKeysArr[0],(data)=>{
            setSelectedRowKeysArr?.(newSelectedRowKeysArr,data.name)
          })
        }),
      }}
      // @ts-ignore
      onRow={(task: ITask) => ({
        onClick: async () => {
          if (task.taskId !== taskInfo.taskId) {
            // @ts-ignore
            setSelectedRowKeysArr([task.taskId],task.name);
          }
        },
      })}
    />
  )

};

export default AssociateTaskTable;
