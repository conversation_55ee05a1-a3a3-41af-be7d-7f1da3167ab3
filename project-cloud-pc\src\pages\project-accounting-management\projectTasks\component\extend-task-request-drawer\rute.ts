export const extendTaskRequestRule = {
  "applyExtendDate": [
    {required: true, message: "申请延期截止时间必须填入"}
  ],
  "reasonDelay": [
    {required: true, message: "申请延期原因必须填入"}
  ],
  "recipient": [
    {required: true, message: "任务移交接收人必须填入"}
  ],
  "reasonTransfer": [
    {required: true, message: "任务移交原因必须填入"}
  ],
  "reasonPause": [
    {required: true, message: "申请暂停原因必须填入"}
  ],
  "reasonRecovery": [
    {required: true, message: "申请恢复原因必须填入"}
  ],
}
