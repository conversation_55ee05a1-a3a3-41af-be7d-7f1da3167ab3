/**
 * @description 项目任务
 * <AUTHOR>
 * @date 2023-10-31 17:45:23
*/
import React, { useEffect, useMemo, useState } from 'react';
import { withKeydown } from "@/components/withKeydown";
import { Switch, Divider, Popconfirm, Modal, Tooltip, Spin } from 'antd';
import { useBoolean } from 'ahooks';
import ProjectTasksTable from './projectTasksTable';
import { useParams, useSelector, useDispatch, history,useLocation } from 'umi';
import { moveDownApi, moveUpApi, deleteTasksApi, listTaskFunctionality, getTaskInfo, closeTask } from "@/service/projectDos/my-project-detail/projectTasks";
import { cloneDeep } from 'lodash';
import { TaskFunctionCode, BtnKey, BtnOperateType } from '@/constants/Enum';
import {ProjectTasksTableProps} from './projectTasksTable'
import './index.less';


//按钮功能是否可用
export const disabledFlag = (arr: any[], type: string, functionCode = BtnKey.taskFunctionCode, usable = 'usable') => {
    if (!arr || (arr && arr.length == 0)) return true;
    const available = arr?.filter((item: any) => item[functionCode] == type);
    return !available[0][usable];
}

const { confirm } = Modal;

export const DividerLine = () => <Divider type='vertical' style={{ marginRight: '10px', marginLeft: '0', height: '100%', background: '#eee' }} />
const KeyDownComponent = withKeydown((props) => <div className='project-tasks-container' {...props} />);
const ProjectTasks: React.FC<ProjectTasksTableProps> = (props) => {
    const dispatch = useDispatch();
    const [initFlag,setInitFlag] = useState(true);
    const [assignType, setAssignType] = useState<any>(null);
    const { projectId,showTable } = props;
    const { userInfo } = useSelector((state: any) => state.commonTask);
    const [paramsTaskInfo,setParamsTaskInfo] = useState<any>(null);
    const location: any = useLocation();
    const {taskId} = location.query;
    const { editable, taskInfo, taskLists, onlyShowMe, isAddTask, functionality, loading, taskListsBackUp} = useSelector((state: any) => state.projectTasks);
    const { projectOverviewDetails } = useSelector((state: any) => state.projectOverview);
    const onkeydown = useMemo(() => {
        return (e: KeyboardEvent) => {
            if (e.ctrlKey && e.code === 'KeyS') {
                e.preventDefault();
                dispatch({
                    type: 'projectTasks/setSaveStatus',
                })
            }

            if (!disabledFlag(functionality, TaskFunctionCode.ADD_NEW_TASK)) {
                if (e.key === 'Enter') {
                    e.preventDefault()
                    dispatch({
                        type: 'projectTasks/addProjectTask',
                    })
                    listTaskFunctionality({ projectId }).then((res: any) => {
                        dispatch({
                            type: 'projectTasks/setFunctionality',
                            payload: res
                        })
                    })
                }
            }

            if (!disabledFlag(functionality, TaskFunctionCode.ADD_NEW_CHILD_TASK)) {
                if (e.key === 'Tab') {
                    e.preventDefault()
                    dispatch({
                        type: 'projectTasks/addProjectTask',
                        payload: true
                    })
                    listTaskFunctionality({ projectId }).then((res: any) => {
                        dispatch({
                            type: 'projectTasks/setFunctionality',
                            payload: res
                        })
                    })
                }
            }
            if (editable) {
                if (e.key === 'Escape') {
                    e.preventDefault();
                    dispatch({
                        type: 'projectTasks/setEditable',
                        payload: false
                    })
                }
            }

            if (!disabledFlag(functionality, TaskFunctionCode.DELETE)) {
                if (e.key === 'Delete') {
                    e.preventDefault()
                    confirm({
                        title: '系统提示',
                        content: '确定删除任务吗？',
                        onOk() {
                            deleteTasksApi(taskInfo.taskId).then(() => {
                                dispatch({
                                    type: 'projectTasks/fetchTaskList',
                                    payload: projectId,
                                    changeType: 'delete'
                                })
                            })
                        },
                    })

                }
            }
        }
    }, [taskInfo, editable]);
    useEffect(()=> {
        if(taskListsBackUp?.length  &&  initFlag && userInfo?.userId && projectOverviewDetails?.leaderId){
            setInitFlag(false);
            // 如果存在跳转的taskId,默认选中该条任务
            if(taskId){
                dispatch({
                    type: 'projectTasks/setFilterTableData',
                    typeKey: taskId,
                    typeName: "taskId",
                    status: true,
                    onlyShowMe: false,
                    onSuccess:(task: any) => {
                        if(task.taskId == taskId){
                            setParamsTaskInfo(task);
                        }
                    }
                })
                
            }else{
                // 当前是项目负责人默认查看全部 ，不是默认查看与我相关的任务
                if(projectOverviewDetails.leaderId != userInfo.userId){
                    dispatch({
                        type: 'projectTasks/setFilterTableData',
                        typeKey: userInfo.userId,
                        typeName: "leaderId",
                        status: true,
                        onlyShowMe: true,
                    })
                }
            }
           
        }
    },[taskListsBackUp,userInfo,projectOverviewDetails])
    useEffect(() => {
        if(paramsTaskInfo){
            dispatch({
                type: 'projectTasks/setCTaskId',
                payload: taskId,
            })
            dispatch({
                type: 'projectTasks/fetchTaskInfo',
                payload: paramsTaskInfo,
                projectId
            })
        }
       
    },[paramsTaskInfo])
    return (
        <Spin spinning={loading} wrapperClassName='task-spin-wrapper'>
            <div className='project-task-warper'>
                <KeyDownComponent  onKeydown={onkeydown}>
                    <ProjectTasksTable {...props} />
                </KeyDownComponent>
            </div>
        </Spin>


    )
}
export default ProjectTasks;