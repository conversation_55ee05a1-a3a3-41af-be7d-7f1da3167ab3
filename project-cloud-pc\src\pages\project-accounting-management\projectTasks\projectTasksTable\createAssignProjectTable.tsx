/**
 * @description 项目任务表格
 * <AUTHOR>
 * @date 2023-11-07 11:00:43
*/
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Badge, DatePicker, Input, Menu, Tooltip, Modal, message } from 'antd';
import TableRowInformation from '../tableRowInformation/createAssignProjectInfo';
import { withKeydown } from '@/components/withKeydown';
import { withClickOutSide } from '@/components/withClickOutSide';
import styled from 'styled-components';
import Highlighter from "react-highlight-words";
import { useParams, useSelector, useDispatch } from 'umi';
import { moveDownApi, moveUpApi, deleteTasksApi, listTaskFunctionality, downgradeTask, upgradeTask, getTaskInfo } from "@/service/projectDos/my-project-detail/projectTasks";
import { templateListTask } from '@/service/projectDos/myProjectApi';
import { initTaskIndex, addTaskItem, deleteTaskItem, filterTaskItem, findTaskItem, flatten,initIndex,renewTaskItem,findDataById ,recalculateValues} from "@/utils/utils";
import {listProjectFunction,} from "@/service/projectDos/my-project-detail/projectOverview";
import { BdwTable, BdwTableHeaderSearchComponent, BdwRow, BdwIcon, BdwTableButton, EditableContent } from "@/components";
import { getListTask } from '@/service/projectDos/my-project-detail/projectTasks';
import { disabledFlag } from '../../projectTasks';
import moment from 'moment';
import { useBoolean, useUpdateEffect,useRequest } from 'ahooks';
import { RightMenuBtn, TaskFunctionCode ,RightMenuProjectRowBtn,BtnKey} from '@/constants/Enum';
import './index.less';
import { isObject, isString, filter, cloneDeep } from 'lodash';
import { useResizeDetector } from 'react-resize-detector';

const IndexSpan = styled.span`
 font-weight: bold;
 font-size: 13px;
 margin-right: 10px;
`;
const NotSupport = styled.div`
  cursor: not-allowed;
  width: 100%;
  height: 100%;
`
// @ts-ignore
const FixedMenu = withClickOutSide(styled(Menu)`
  position: fixed;
  z-index: 1000;
  box-shadow: 0 0 5px rgba(0,0,0,0.1) !important;
`);
const WrapperComponent = withKeydown(BdwRow);
type MenuTypes = 'TABLE' | 'HEAD';
interface Position {
    x: number,
    y: number,
}



const { confirm } = Modal;
const ContextMenu: React.FC<{ top: number, left: number,  visible: boolean, hide: () => void,fun:any,taskInfo: any,taskList: any }> = (props) => {
    const dispatch = useDispatch();
    const onHandleMenuItem = (type: string) => {
        switch (type) {
            case TaskFunctionCode.ADD_NEW_CHILD_TASK://添加子任务
                props.fun.addChildTask();
                break;
            case TaskFunctionCode.ADD_NEW_TASK://添加同级任务
                props.fun.addTask();
                break;
            case TaskFunctionCode.DELETE://删除
                props.fun.deleteTask();
                break;
        }
        props.hide();
    }
    
    const RightMenuAccountingBtn  = [
        {
            key:'ADD_NEW_CHILD_TASK',
            name:'添加子级任务'
        },
        {
            key:'ADD_NEW_TASK',
            name:'添加同级任务'
        },
        {
            key:'DELETE',
            name:'删除任务'
        }
       
    ]
    if(props.taskInfo?.index?.length == 1 && props.taskList?.length == 1){
        RightMenuAccountingBtn.pop();
    }
    const FixedMenuItem = filter(RightMenuAccountingBtn.map((item: any) => {
        return {
            key: item.key,
            label: <div
                className='bdw-menu-item'
                style={{
                    display: 'flex',
                    alignItems: 'center'
                }}
                onClick={() => {
                    onHandleMenuItem(item.key)
                }}
            >{item.name}</div>
        }
    }), v => v)
    return <FixedMenu
        onClickOutSide={props.hide}
        style={{
            top: `${props.top}px`,
            left: `${props.left}px`,
            display: props.visible ? 'block' : 'none'
        }}
        items={FixedMenuItem}
    />

}
export interface ProjectTasksTableProps{
    showTable?: boolean,
    projectId?: string,
    onContext?: (e: React.MouseEvent) => void,
    onClick?: () => void,
    onDoubleClick?: () => void,
    currentData?: any,
    readonly?: boolean//是否只读
    projectInfo?: any//当前项目信息
    
}
const ProjectTasksTable: React.FC<ProjectTasksTableProps> = (props) => {
    const dispatch = useDispatch();
    const { readonly,projectInfo } = props;
    const [showToolTip, setShowToolTip] = useState<boolean>(false);
    const [contextMenuVisible, { setTrue: showContextMenu, setFalse: hideContextMenu }] = useBoolean(false);
    const { projectId,showTable,onContext,onClick ,onDoubleClick} = props;
    const { filter: filterObj, functionality,newProjectList } = useSelector((state: any) => state.projectTasks);
    const { userInfo } = useSelector((state: any) => state.commonTask);//当前项目登录人信息
    const [contextPosition, setContextMenuPosition] = useState<Position>({
        x: -1000,
        y: -1000,
    });
    // 当前的表格数据
    const [taskLists,setTaskLists] = useState<any>([]);
    // 当前的任务信息
    const [taskInfo,setTaskInfo] = useState<any>(null);
    // 是否是编辑状态
    const [editable,setEditable] = useState(false);
    // 是否是添加任务状态
    const [isAddTask,setIsAddTask] = useState(false);
    // 当前展开的key
    const [expandedRowKeys,setExpandedRowKeys] = useState<any>([]);
    const [windowSize,setWindowSize] = useState({
        width:window.innerWidth
      })
        // 监听窗口变化
    useEffect(() => {
        const handleResize = () => {
        setWindowSize({
            width:window.innerWidth
        })
        }
        window.addEventListener('resize',handleResize);
        return () => {
        window.removeEventListener('resize',handleResize);
        }
    },[])
    const taskRefs = useRef<any>({});
    useEffect(() => {
        taskRefs.current = {
            taskLists,
            taskInfo,
            editable,
            isAddTask
        }
    },[taskLists,taskInfo,editable,isAddTask])
    const [windowWidth,setWidowWidth] = useState(window.innerWidth);
    const defaultTableList = [
        {
            children: null,
            leaderId: userInfo.userId,
            leaderName: userInfo.userName,
            parentId: null,
            taskId: new Date().getTime(),
            name: '任务一',
            parentTitle: null,
            beforeDocument: [],
            index: [0],
            ratio:null,
            projectRatio:null
        }
    ]
    useEffect(() => {
        if(readonly){//只读
            getListTask(projectInfo.id).then((res: any)=>{
                setTaskLists(res);
            })
        }else{
            if(projectInfo.tasks){
                setTaskLists(cloneDeep(projectInfo.tasks));
                setTaskInfo(cloneDeep(projectInfo.tasks)[0]);
            }else{
                if(projectInfo.quoteProjectId){
                    templateListTask(projectInfo.quoteProjectId).then((res: any) => {
                        setDefaultUserId(res);
                        setTaskLists(res);
                        setTaskInfo(res[0]);
                    })
                }else{
                    setTaskLists(defaultTableList);
                    setTaskInfo(defaultTableList[0]);
                }
            }
            // setEditable(true);
            
        }
        const handleResize = () => {
            setWidowWidth(window.innerWidth);
        }
        window.addEventListener('resize',handleResize);
        return () => {
            window.removeEventListener('resize',handleResize);
        }
    },[readonly,projectInfo])
    // 设置默认负责人id
    const setDefaultUserId = (arr:any) => {
        arr.forEach((item: any) => {
            item.leaderName = userInfo.userName;
            item.leaderId = userInfo.userId;
            if(item.children && item.children?.length){
                setDefaultUserId(item.children);
            }
        })
    }
    //是否有编辑权限
    const getEditAuthNew = () => {
        return disabledFlag(functionality, TaskFunctionCode.EDIT)
    }
    // 表格对象容器
    const { width, ref } = useResizeDetector();

    useEffect(() => {
        dispatch({
            type: 'projectTasks/renewTaskInfo',
            payload: null
        })
    }, [projectId])
    // 计算缩进间距
    const computeSpace = (length?: number) => {
        switch (length) {
            case 2:
                return 'p-l-40';
            case 3:
                return 'p-l-80';
            case 4:
                return 'p-l-120';
            case 5:
                return 'p-l-160';
            default:
                return '';
        }
    }
    // 计算背景色
    const computeBackground = (length?: number) => {
        switch (length) {
            case 1:
                return 'bdColor-b3c6e7';
            case 2:
                return 'bdColor-d9e2f3';
            case 3:
                return 'bdColor-deebf6';
            default:
                return 'bdColor-deebf6';
        }
    }
    // 任务表格表头项
    const TaskTableColumns = [
        {
            title: <div className='table-header-column'>
                业务事项
            </div>,
            ellipsis: true,
            dataIndex: 'name',
            render: function TitleColumn(value: string, record: any) {
                const indexSpan = <span
                    className='task-index-num'><IndexSpan>{record.index?.map((it: any) => it + 1).join('.')}</IndexSpan></span>;
                return <BdwRow type='flex'>
                    <div>{indexSpan}</div>
                    <div title={record.name} className='task-name-show'>
                        {record.name}
                </div>
                </BdwRow>;
            },
        },
        // leaderName
        {
            title: <div className='table-header-column'>
                同级占比
                <div>{projectInfo?.ratio??'--'}</div>
            </div>,
            dataIndex: 'ratio',
            width: 80,
            ellipsis: true,
            render: function HeaderColumn(value: string, record: any): any {
                const paddingLeft: any = computeSpace(record?.index?.length!);
                // return <div  className={paddingLeft}>{'--'}</div>
                return <div  className={paddingLeft}  style={{width:'80px'}}>{value ?? '--'}</div>
            }
        },
        {
            title: <div className='table-header-column'>
                项目占比
                <div>{projectInfo?.projectRatio??'--'}</div>
            </div>,
            dataIndex: 'projectRatio',
            width: 80,
            ellipsis: true,
            render: function HeaderColumn(value: string, record: any): any {
                const paddingLeft: any = computeSpace(record?.index?.length!);
                // return <div  className={paddingLeft}>{'--'}</div>
                return <div className={paddingLeft}  style={{width:'80px'}}>{value ?? '--'}</div>
            }
        },
        // {
        //     title: <div className='table-header-column'>
        //         核算计入比例
        //         <div>{projectInfo?.accountRatio??'--'}</div>
        //     </div>,
        //     dataIndex: 'accountRatio',
        //     width: 110,
        //     ellipsis: true,
        //     render: function HeaderColumn(value: string, record: any): any {
        //         const paddingLeft: any = computeSpace(record?.index?.length!);
        //         // return <div  className={paddingLeft}>{'--'}</div>
        //         return <div className={paddingLeft}  style={{width:'110px'}}>{value ??'--'}</div>
        //     }
        // },
        {
            title: <div className='table-header-column'>
                核算金额
                <div>{projectInfo?.amount??'--'}</div>
            </div>,
            dataIndex: 'amount',
            width: 80,
            ellipsis: true,
            render: function HeaderColumn(value: string, record: any): any {
                const paddingLeft: any = computeSpace(record?.index?.length!);
                return <div className={paddingLeft} style={{width:'80px'}}>{value ?? '-'}</div>
            }
        },
        {
            title: <div className='table-header-column'>
                负责人
                <div>{readonly?projectInfo?.leaderName:userInfo?.userName}</div>
            </div>,
            dataIndex: 'leaderName',
            width: 80,
            ellipsis: true,
            render: function HeaderColumn(value: string, record: any): any {
                return <div  style={{width:'80px'}}>{record?.leaderName}</div>
            }
        },
    ];
    const taskTableColumns = useMemo(() => {
        if (width) {
            if (width > 880) {
                return TaskTableColumns
            } else {
                return TaskTableColumns.filter((item: any) => !['editorName', 'endTime', 'process'].includes(item.dataIndex))
            }
        }
    }, [width,TaskTableColumns])
    // const showRightContent = useMemo(() => {
    //     if (windowWidth) {
    //         if (windowWidth > 1380) {
    //             return true
    //         } else {
    //             return false
    //         }
    //     }
    //     return true
    // }, [windowWidth])
    // 添加子级任务
    const addChildTask = () => {
        const {taskLists,taskInfo,editable,isAddTask} =  taskRefs.current;
        if(editable){
            message.warn('请先保存当前任务信息，再进行添加任务！')
            return
        }
        const cTaskList = cloneDeep(taskLists);
        const cTask = cloneDeep(taskInfo);
        const item: any = {
            ...defaultTableList?.[0],
            parentId: cTask.taskId,
            name:'',
            taskId: new Date().getTime(),
            children:null,
            leaderId: userInfo.userId,
            leaderName: userInfo.userName,
        }
        const keys = cloneDeep(expandedRowKeys);
        keys.push(cTask.taskId);
        setExpandedRowKeys([...new Set(keys)]);
        addTaskItem(cTask.taskId, cTaskList, item);
        initTaskIndex(cTaskList);
        setTaskLists(cTaskList);
        setTaskInfo(item);
        setEditable(true);
        setIsAddTask(true);
    }
    // 添加同级任务
    const addTask = () => {
        const {taskLists,taskInfo,editable} =  taskRefs.current;
        if(editable){
            message.warn('请先保存当前任务信息，再进行添加任务！')
            return
        }
        const list = cloneDeep(taskLists);
        const curTask = cloneDeep(taskInfo);
        const item: any = {
            ...defaultTableList?.[0],
            parentId: curTask.parentId,
            taskId: new Date().getTime(),
            name:'',
            children:null
        }
        if (curTask.parentId) {
            addTaskItem(curTask.parentId, list, item)
        } else {
            list?.splice(curTask.index[0] + 1, 0, item);
        }
        initTaskIndex(list);
        setTaskLists(list);
        setTaskInfo(item);
        setEditable(true);
        setIsAddTask(true);
    }
    // 删除任务
    const deleteTask = () => {
        const {taskLists,taskInfo} =  taskRefs.current;
        const curTask = cloneDeep(taskInfo);
        const list = cloneDeep(taskLists);
        deleteTaskItem(list, curTask.taskId);
        initTaskIndex(list);
        setTaskLists(list);
        const savedList: any = cloneDeep(list);
        const projectListInfo: any = cloneDeep(newProjectList);
        projectListInfo.forEach((item: any) => {
            if(item.rootType == projectInfo.rootType){
                item.classificationVos.forEach((i: any) => {
                    if(i.classification == projectInfo.classification){
                        i.projects.forEach((ii: any) => {
                            if(ii.onlyId == projectInfo.onlyId){
                                ii.tasks = savedList;
                            }
                        })
                    }
                })
            }
        })
        setTaskInfo(null);
    }
    // 保存任务 
    const saveTask = (data: any) => {
        const {taskLists,taskInfo} =  taskRefs.current;
        if(!taskInfo.name){
            message.warn('任务名不能为空！')
            return
        }
        const ratio = parseFloat(taskInfo?.ratio || 0)?.toFixed(3);
        if(taskInfo?.index?.length == 1){
            renewTaskItem(taskLists,taskInfo.taskId,{...taskInfo,...data,projectRatio:Number(ratio),ratio:Number(ratio)});
        }else{
            // 这里要更新子级的ratio信息
            const curParentData = findDataById(cloneDeep(taskLists), taskInfo?.parentId,'taskId');
            let curProjectRatio=parseFloat((((Number(ratio)) * (Number(curParentData?.projectRatio))) / 100)?.toFixed(3));
            renewTaskItem(taskLists,taskInfo.taskId,{...taskInfo,...data,ratio:Number(ratio),projectRatio: Number(curProjectRatio)});
        };
        const curNode = findDataById(taskLists, taskInfo?.taskId);
        recalculateValues(curNode);
        initTaskIndex(taskLists);
        const savedList: any = cloneDeep(taskLists);
        const projectListInfo: any = cloneDeep(newProjectList);
        projectListInfo.forEach((item: any) => {
            if(item.rootType == projectInfo.rootType){
                item.classificationVos.forEach((i: any) => {
                    if(i.classification == projectInfo.classification){
                        i.projects.forEach((ii: any) => {
                            if(ii.onlyId == projectInfo.onlyId){
                                ii.tasks = savedList;
                            }
                        })
                    }
                })
            }
        })
        dispatch({
            type: 'projectTasks/setNewProjectList',
            payload: projectListInfo
        })
        setTaskLists(taskLists);
        setEditable(false);
        setTaskInfo(null);
        setIsAddTask(false);
    }
    // 更改编辑状态
    const editChange = (edit: boolean) => {
        setEditable(edit);
    }
    // 更新任务信息
    const renewTaskInfo = (renew: any) => {
        const {taskInfo} =  taskRefs.current;
        setTaskInfo({
            ...taskInfo,
            ...renew,
        });
    }
    // 新建任务 点击取消删除任务
    const closeTaskInfo = () => {
        const {taskLists,taskInfo,editable,isAddTask} =  taskRefs.current;
        if(isAddTask){
            const curTask = cloneDeep(taskInfo);
            const list = cloneDeep(taskLists);
            deleteTaskItem(list, curTask.taskId);
            initTaskIndex(list);
            setTaskLists(list);
            setIsAddTask(false);
        }
        setTaskInfo(null);
    }
    return (<BdwRow type='flex' className={['project-task-accounting-table-container',(windowSize.width<1690)?'project-task-accounting-table-scroll':''].join(' ')}>
        <div ref={ref} className={`table-details ${(taskInfo?.taskId) ? 'table-details-w' : ''}`}>
            {/* <div className={`table-details table-details-w`}> */}
            <div style={{width:'100%'}}>
            <WrapperComponent>
                {/* <div className='wrapper-head' onClick={onClick} onContextMenu={onContext} onDoubleClick={onDoubleClick}>
                <div style={{ flex: 1, paddingLeft: '24px', fontSize: '15px',display:'flex',alignItems:'center',color:'#fff',fontWeight:'bold' }}>
                      {currentData?.name}
                      {
                        rowData?.orderTypeName && rowData?.orderTypeName?.length > 0 && <div>
                          【{rowData?.orderTypeName.join('、')}】
                        </div>
                      }
                </div>
                {
                      !showTable && <>
                        <div style={{ minWidth: '80px', }} className="head-column">{currentData?.ratio?(currentData?.ratio+ '%'):'--'}</div>
                        <div style={{ minWidth: '80px' }} className="head-column">{currentData?.projectRatio?(currentData?.projectRatio+ '%'):'--'}</div>
                        <div style={{ minWidth: '110px' }} className="head-column">{currentData?.accountRatio?(currentData?.accountRatio+ '%'):'--'}</div>
                        <div style={{ minWidth: '80px' }} className="head-column">{currentData?.amount?(currentData?.amount+ '%'):'--'}</div>
                        <div style={{ width: '70px' }} className='head-column'>
                          {currentData?.name ?? '--'}
                        </div>
                      </>
                    }
                </div> */}
                <ContextMenu top={contextPosition.y} taskList={taskLists}  left={contextPosition.x} visible={contextMenuVisible}
                    hide={hideContextMenu} taskInfo={taskInfo}  fun={{addChildTask,addTask,deleteTask}} />
                <BdwTable
                    className='project-task-table project-task-table-accounting-create'
                    pagination={false}
                    loading={{
                        spinning: false
                    }}
                    tableLayout='auto'
                    expandable={{
                        defaultExpandAllRows: true,
                        expandedRowKeys,
                        onExpandedRowsChange: (e) => {
                            setExpandedRowKeys(e);
                        },
                    }}
                    size="small"
                    rowKey="taskId"
                    // @ts-ignore
                    columns={taskTableColumns}
                    dataSource={taskLists ?? []}
                    useLocalData
                    sticky
                    scroll={{ x: true }}
                    showPages={false}
                    rowSelection={{
                        selectedRowKeys: taskInfo?.taskId && [taskInfo?.taskId] || [],
                        type: 'radio',
                        columnWidth: 1,
                        renderCell: () => {
                            return null;
                        },
                        checkStrictly: true,
                    }}
                    rowClassName={(records: any) => {
                        return computeBackground(records?.index?.length);
                    }}
                    // @ts-ignore
                    onRow={(task: any) => ({
                        onClick: async () => {
                            if (task.taskId !== taskInfo?.taskId) {
                                setTaskInfo(task);
                                
                                // getTaskInfo(task.taskId).then((res: any) => {
                                //     let taskExplain = null;
                                //     if (res.taskExplain) {
                                //         try {
                                //           taskExplain = JSON.parse(res.taskExplain);
                                //         } catch {
                                //           taskExplain = [{
                                //             value: res.taskExplain,
                                //             fileList: []
                                //           }]
                                //         }
                                //       }
                              
                                //       const taskInfo = { ...task, ...res, beforeDocument: res?.document, taskExplain };
                                //     setTaskInfo(task);
                                // })
                                
                            }
                        },
                        onDoubleClick() {
                            // 有编辑权限时 双击才进入编辑状态
                            if (task.taskId !== taskInfo?.taskId) return
                        },
                        onContextMenu: async (e) => {
                            // 有编辑权限时 鼠标右键单击才弹出相应菜单操作栏
                            // 禁用鼠标右键默认行为
                            dispatch({
                                type: 'projectTasks/setCTaskId',
                                payload: task.taskId,
                            })
                            if (task.taskId !== taskInfo?.taskId) {
                                setTaskInfo(task);
                            }
                            e.preventDefault();
                            showContextMenu();
                            let y = e.clientY
                            let x = e.clientX
                            if (document.body.offsetHeight - e.clientY < 300) {
                                y = document.body.offsetHeight - 200;
                            }
                            // @ts-ignore
                            if (window.clientWidth - e.clientX < 100) {
                                // @ts-ignore
                                x = window.clientWidth - 100;
                            }
                            setContextMenuPosition({
                                x,
                                y,
                            });
                        },
                    })}
                />
                
            </WrapperComponent>
            </div>

        </div>
        {
            taskInfo?.taskId  && <TableRowInformation isAddTask={isAddTask} onClose={closeTaskInfo} readonly={readonly} taskInfo={taskInfo} taskLists={taskLists} renewTaskInfo={renewTaskInfo} editable={editable} saveTask={saveTask} editChange={editChange} />
        }
        {/* <TableRowInformation /> */}

    </BdwRow>




    )
}

export default ProjectTasksTable;
