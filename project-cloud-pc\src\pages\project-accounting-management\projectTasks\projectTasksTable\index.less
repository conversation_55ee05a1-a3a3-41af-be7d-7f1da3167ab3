@import '../../../../styles/base.less';
.project-task-accounting-table-container {
  
 min-height: 200px;
  .ant-input-cover-style{
    background: white!important;
  }
  .ant-table-expanded-row-fixed{
    margin: -16px -16px!important;
  }
  .ant-table-sticky-scroll-bar,.ant-table-sticky-scroll{
    display: none!important;
  }
  width: 100%;
  // height: calc(100% - 70px);
  // border-top: 1px solid @divider;
  .table-details{
      height: 100%;
      // .p-16()
      margin-bottom: 16px;
      width: 100%;
  }
  .table-details-w{
    width: calc(100% - 400px);
  }
  .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before{
    background-color: unset;
  }
  .project-task-table-accounting-create{
    border: unset!important;
  }
  .ant-table-cell{
    text-indent: unset!important;
  }
  .ant-table-thead{
    .ant-table-cell-ellipsis{
      overflow: visible;
      .table-header-column{
        // padding: 0 5px;
        color: rgb(118,117,128);
        font-size: 14px;
        position: relative;
        >div{
          position: absolute;
          top: -42px;
          left: 0px;
          color: #00b4f6;
          font-weight: bold;
        }
      }
    }
  }
  .datePicker{
    padding-left: unset;
    padding-right: unset;
  }
  .status-span{
    background-color: #efefef;
    padding: 3px 6px;
    border-radius: 2px;
  }
  .task-name-show{
      width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
  }
  .p-l-40{
    padding-left: 40px;
  }
  .p-l-80{
    padding-left: 80px;
  }
  .p-l-120{
    padding-left: 120px;
  }
  .p-l-160{
    padding-left: 160px;
  }
  .bdColor-b3c6e7{
    background: #b3c6e7!important;
  }
  .bdColor-d9e2f3{
    background: #d9e2f3!important;
  }
  .bdColor-deebf6{
    background: #deebf6!important;
  }
  .min-width-80{
    min-width: 80px;
  }
  .min-width-80{
    
  }
  .min-width-80{
    
  }
  .min-width-80{
    
  }
  .ant-table-container{
    .ant-table-header.ant-table-sticky-holder{
      overflow: visible!important;
    }
    .ant-table-header,.ant-table-body{
      padding: unset!important;
    }
    .ant-table-thead{
      height: 35px!important;
    }
    td{
      background-color: unset!important;
      border-top: unset!important;
    }
  }
  .wrapper-head{
    height: 50px;
    background: #757171;
    display: flex;
    align-items: center;
    .head-column{
      font-size: 14px;
      color: #00b4f6;
      font-weight: bold;
    }
  }
}
.project-task-accounting-table-scroll.project-task-accounting-table-container{
  .ant-table-container{
    .ant-table-header.ant-table-sticky-holder{
      overflow: hidden!important;
    }
  }
  .ant-table-thead{
    .ant-table-cell-ellipsis{
      overflow: hidden;
    }
  }
}
.ant-modal-wrap{
  overflow-x: hidden;
}
