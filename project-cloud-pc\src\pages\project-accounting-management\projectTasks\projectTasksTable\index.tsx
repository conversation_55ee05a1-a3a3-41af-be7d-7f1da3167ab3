/**
 * @description 项目任务表格
 * <AUTHOR>
 * @date 2023-11-07 11:00:43
*/
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Badge, DatePicker, Input, Menu, Tooltip, Modal } from 'antd';
import TableRowInformation from '../tableRowInformation';
import { withKeydown } from '@/components/withKeydown';
import { withClickOutSide } from '@/components/withClickOutSide';
import styled from 'styled-components';
import Highlighter from "react-highlight-words";
import { useParams, useSelector, useDispatch } from 'umi';
import { moveDownApi, moveUpApi, deleteTasksApi, listTaskFunctionality, downgradeTask, upgradeTask, getTaskInfo } from "@/service/projectDos/my-project-detail/projectTasks";
import {listProjectFunction,} from "@/service/projectDos/my-project-detail/projectOverview";
import { BdwTable, BdwTableHeaderSearchComponent, BdwRow, BdwIcon, BdwTableButton, EditableContent } from "@/components";
import { getListTask } from '@/service/projectDos/my-project-detail/projectTasks';
import { initTaskIndex } from '@/utils/utils';
import { disabledFlag } from '../../projectTasks';
import moment from 'moment';
import { useBoolean, useUpdateEffect,useRequest } from 'ahooks';
import { RightMenuBtn, TaskFunctionCode, RightMenuAccountingBtn ,RightMenuProjectRowBtn,BtnKey} from '@/constants/Enum';
import './index.less';
import { isObject, isString, filter } from 'lodash';
import { useResizeDetector } from 'react-resize-detector';

const IndexSpan = styled.span`
 font-weight: bold;
 font-size: 13px;
 margin-right: 10px;
`;
const NotSupport = styled.div`
  cursor: not-allowed;
  width: 100%;
  height: 100%;
`
// @ts-ignore
const FixedMenu = withClickOutSide(styled(Menu)`
  position: fixed;
  z-index: 1000;
  box-shadow: 0 0 5px rgba(0,0,0,0.1) !important;
`);
const WrapperComponent = withKeydown(BdwRow);
type MenuTypes = 'TABLE' | 'HEAD';
interface Position {
    x: number,
    y: number,
}

const { confirm } = Modal;
const ContextMenu: React.FC<{ top: number, left: number, projectId: string, visible: boolean, hide: () => void }> = (props) => {
    const dispatch = useDispatch();
    const { projectId } = props;
    const { functionality, taskInfo, editable } = useSelector((state: any) => state.projectTasks);
    const onHandleMenuItem = (type: string) => {
        switch (type) {
            case TaskFunctionCode.ADD_NEW_CHILD_TASK://添加子任务
                dispatch({
                    type: 'projectTasks/addProjectTask',
                    payload: true
                })
                listTaskFunctionality({ projectId }).then((res: any) => {
                    dispatch({
                        type: 'projectTasks/setFunctionality',
                        payload: res
                    })
                })
                break;
            case TaskFunctionCode.ADD_NEW_TASK://添加同级任务
                dispatch({
                    type: 'projectTasks/addProjectTask',
                })
                listTaskFunctionality({ projectId }).then((res: any) => {
                    dispatch({
                        type: 'projectTasks/setFunctionality',
                        payload: res
                    })
                })
                break;
            case TaskFunctionCode.EDIT://编辑
                dispatch({
                    type: 'projectTasks/setEditable',
                    payload: true
                })
                break;
            case TaskFunctionCode.MOVE_UP://上移
                moveUpApi(taskInfo.taskId).then(() => {
                    getTaskInfo(taskInfo.taskId).then((res) => {
                        dispatch({
                            type: 'projectTasks/fetchTaskList',
                            payload: projectId,
                            changeType: 'move',
                            taskId: taskInfo.taskId,
                            successRes: res
                        })
                    })

                })
                break;
            case TaskFunctionCode.MOVE_DOWN://下移
                moveDownApi(taskInfo.taskId).then(() => {
                    getTaskInfo(taskInfo.taskId).then((res) => {
                        dispatch({
                            type: 'projectTasks/fetchTaskList',
                            payload: projectId,
                            changeType: 'move',
                            taskId: taskInfo.taskId,
                            successRes: res
                        })
                    })
                })
                break;
            case TaskFunctionCode.UPGRADE://升级
                upgradeTask(taskInfo.taskId).then(() => {
                    getTaskInfo(taskInfo.taskId).then((res) => {
                        dispatch({
                            type: 'projectTasks/fetchTaskList',
                            payload: projectId,
                            changeType: 'upgrade',
                            taskId: taskInfo.taskId,
                            successRes: res
                        })
                    })
                })
                break;
            case TaskFunctionCode.DOWNGRADE://降级
                downgradeTask(taskInfo.taskId).then(() => {
                    getTaskInfo(taskInfo.taskId).then((res) => {
                        dispatch({
                            type: 'projectTasks/fetchTaskList',
                            payload: projectId,
                            changeType: 'downgrade',
                            taskId: taskInfo.taskId,
                            successRes: res
                        })
                    })

                }).catch((error) => {
                    //降级时错误处理
                    if (error.code == 10190) {
                        confirm({
                            title: '系统提示',
                            content: error.message,
                            onOk() {
                                let data = {};
                                //根据后端返回的字段判断是调整的是开始时间还是结束时间，再次调用接口传递不同的参数
                                // START_TIME：调整开始时间
                                // END_TIME：调整截至时间
                                if (error.data == 'START_TIME') {
                                    data = { autoAdjustStartTime: true }
                                } else {
                                    data = { useParentTaskEndTime: true }
                                }
                                downgradeTask(taskInfo.taskId, data).then(() => {
                                    dispatch({
                                        type: 'projectTasks/fetchTaskList',
                                        payload: projectId,
                                        changeType: 'move',
                                        taskId: taskInfo.taskId
                                    })
                                })
                            },
                            onCancel() {
                                //不调整截至时间需要再次调用一下接口，开始时间不用调接口
                                if (error.data == 'END_TIME') {
                                    downgradeTask(taskInfo.taskId, { useParentTaskEndTime: false }).then(() => {
                                        dispatch({
                                            type: 'projectTasks/fetchTaskList',
                                            payload: projectId,
                                            changeType: 'move',
                                            taskId: taskInfo.taskId
                                        })
                                    })
                                }
                            }
                        })
                    }
                })
                break;
            case TaskFunctionCode.DELETE://删除任务
                deleteTasksApi(taskInfo.taskId).then(() => {
                    dispatch({
                        type: 'projectTasks/fetchTaskList',
                        payload: projectId,
                        changeType: 'delete'
                    })
                })
                break;
                
        }
        props.hide();
    }
    const FixedMenuItem = filter(RightMenuAccountingBtn.map((item: any) => {
        if (disabledFlag(functionality, item.key)) {//查看是否有权限
            return null
        } else {
            return {
                key: item.key,
                label: <div
                    className='bdw-menu-item'
                    style={{
                        display: 'flex',
                        alignItems: 'center'
                    }}
                    onClick={() => {
                        onHandleMenuItem(item.key)
                    }}
                >{item.name}</div>
            }
        }
    }), v => v)
    return <FixedMenu
        onClickOutSide={props.hide}
        style={{
            top: `${props.top > 400 ? props.top - 130 : props.top}px`,
            left: `${props.left}px`,
            display: props.visible ? 'block' : 'none'
        }}
        items={FixedMenuItem}
    />

}
export interface ProjectTasksTableProps{
    showTable: boolean,
    projectId: string,
    onContext?: (e: React.MouseEvent) => void,
    onClick?: () => void,
    onDoubleClick?: () => void,
    currentData: any
    
}
const ProjectTasksTable: React.FC<ProjectTasksTableProps> = (props) => {
    const dispatch = useDispatch();
    const [showToolTip, setShowToolTip] = useState<boolean>(false);
    const [contextMenuVisible, { setTrue: showContextMenu, setFalse: hideContextMenu }] = useBoolean(false);
    const { projectId,showTable,onContext,onClick ,onDoubleClick,currentData} = props;
    const { taskLists, taskInfo, isAddTask, editable, expandedRowKeys, filter: filterObj, functionality, cTaskId } = useSelector((state: any) => state.projectTasks);
    const [contextPosition, setContextMenuPosition] = useState<Position>({
        x: -1000,
        y: -1000,
    });
    const [windowWidth,setWidowWidth] = useState(window.innerWidth);
    useEffect(() => {
        const handleResize = () => {
            setWidowWidth(window.innerWidth);
        }
        window.addEventListener('resize',handleResize);
        return () => {
            window.removeEventListener('resize',handleResize);
        }
    },[])
    //是否有编辑权限
    const getEditAuthNew = () => {
        return disabledFlag(functionality, TaskFunctionCode.EDIT)
    }
    // 表格对象容器
    const { width, ref } = useResizeDetector();

    useEffect(() => {
        dispatch({
            type: 'projectTasks/renewTaskInfo',
            payload: null
        })
    }, [projectId])
    // 计算缩进间距
    const computeSpace = (length?: number) => {
        switch (length) {
            case 2:
                return 'p-l-40';
            case 3:
                return 'p-l-80';
            case 4:
                return 'p-l-120';
            case 5:
                return 'p-l-160';
            default:
                return '';
        }
    }
    // 计算背景色
    const computeBackground = (length?: number) => {
        switch (length) {
            case 1:
                return 'bdColor-b3c6e7';
            case 2:
                return 'bdColor-d9e2f3';
            case 3:
                return 'bdColor-deebf6';
            default:
                return 'bdColor-deebf6';
        }
    }

    // 任务表格表头项
    const TaskTableColumns = [
        {
            title: <div className='table-header-column'>
                业务事项
            </div>,
            ellipsis: true,
            dataIndex: 'name',
            render: function TitleColumn(value: string, record: any) {
                const indexSpan = <span
                    className='task-index-num'><IndexSpan>{record.index?.map((it: any) => it + 1).join('.')}</IndexSpan></span>;
                const renderShow = <div title={record.name} className='task-name-show'>
                    <Highlighter
                        highlightClassName="title-highlight"
                        searchWords={[filterObj?.name]}
                        autoEscape
                        textToHighlight={record.name ?? ""}
                    />
                </div>
                const renderEditor = (
                    <BdwRow type='flex' className='flex-1'>
                        <div className='flex-1'>
                            {
                                isAddTask ?
                                    <Tooltip title='提示：仅限输入64个字符！' open={showToolTip}>
                                        <Input
                                            tabIndex={-1}
                                            size="small"
                                            className='task-name-input paddingStyle'
                                            maxLength={32}
                                            autoFocus
                                            // onBlur={(e) => {

                                            // }}
                                            value={taskInfo?.name}
                                            onChange={(e) => {
                                                if (e.target.value.length > 32) {
                                                    setShowToolTip?.(true)
                                                } else {
                                                    setShowToolTip?.(false)
                                                }
                                                dispatch({
                                                    type: 'projectTasks/renewTaskInfo',
                                                    payload: {
                                                        name: e.target.value
                                                    }
                                                })
                                            }}
                                        /></Tooltip> :
                                    <span>{taskInfo?.name}</span>
                            }
                        </div>
                    </BdwRow>)

                return <BdwRow type='flex'>
                    <div>{indexSpan}</div>
                    <EditableContent
                        editable={editable && record.taskId === taskInfo?.taskId}
                        renderShow={renderShow}
                        renderEditor={renderEditor}
                    />
                </BdwRow>;
            },
        },
        // leaderName
        {
            title: <div className='table-header-column'>
                同级占比
                <div>{currentData?.ratio??'--'}</div>
            </div>,
            dataIndex: 'ratio',
            width: 80,
            ellipsis: true,
            render: function HeaderColumn(value: string, record: any): any {
                const paddingLeft: any = computeSpace(record?.index?.length!);
                return <div  className={paddingLeft}>{value ?? '--'}</div>
            }
        },
        {
            title: <div className='table-header-column'>
                项目占比
                <div>{currentData?.projectRatio??'--'}</div>
            </div>,
            dataIndex: 'projectRatio',
            width: 80,
            ellipsis: true,
            render: function HeaderColumn(value: string, record: any): any {
                const paddingLeft: any = computeSpace(record?.index?.length!);
                return <div className={paddingLeft}>{value ??  '--'}</div>
            }
        },
        // {
        //     title: <div className='table-header-column'>
        //         核算计入比例
        //         <div>{currentData?.accountRatio??'--'}</div>
        //     </div>,
        //     dataIndex: 'accountRatio',
        //     width: 110,
        //     ellipsis: true,
        //     render: function HeaderColumn(value: string, record: any): any {
        //         const paddingLeft: any = computeSpace(record?.index?.length!);
        //         return <div className={paddingLeft}>{value ??  '--'}</div>
        //     }
        // },
        {
            title: <div className='table-header-column'>
                核算金额
                <div>{currentData?.amount??'--'}</div>
            </div>,
            dataIndex: 'amount',
            width: 80,
            ellipsis: true,
            render: function HeaderColumn(value: string, record: any): any {
                const paddingLeft: any = computeSpace(record?.index?.length!);
                return <div className={paddingLeft}>{value ?? '--'}</div>
            }
        },
        {
            title: <div className='table-header-column'>
                负责人
                <div>{currentData?.leaderName }</div>
            </div>,
            dataIndex: 'leaderName',
            width: 80,
            ellipsis: true,
            render: function HeaderColumn(value: string, record: any): any {
                return <div>{record?.leaderName}</div>
            }
        },
    ];
    const taskTableColumns = useMemo(() => {
        if (width) {
            if (width > 880) {
                return TaskTableColumns
            } else {
                return TaskTableColumns.filter((item: any) => !['editorName', 'endTime', 'process'].includes(item.dataIndex))
            }
        }
    }, [width,TaskTableColumns,currentData])
    const showRightContent = useMemo(() => {
        if (windowWidth) {
            if (windowWidth > 1380) {
                return true
            } else {
                return false
            }
        }
        return true
    }, [windowWidth])
    return (<BdwRow type='flex' className='project-task-accounting-table-container'>
        <div ref={ref} className={`table-details ${(taskInfo?.taskId) ? 'table-details-w' : ''}`}>
            {/* <div className={`table-details table-details-w`}> */}
            <div style={{width:'100%'}}>
            <WrapperComponent>
                {/* <div className='wrapper-head' onClick={onClick} onContextMenu={onContext} onDoubleClick={onDoubleClick}>
                <div style={{ flex: 1, paddingLeft: '24px', fontSize: '15px',display:'flex',alignItems:'center',color:'#fff',fontWeight:'bold' }}>
                      {currentData?.name}
                      {
                        rowData?.orderTypeName && rowData?.orderTypeName?.length > 0 && <div>
                          【{rowData?.orderTypeName.join('、')}】
                        </div>
                      }
                </div>
                {
                      !showTable && <>
                        <div style={{ minWidth: '80px', }} className="head-column">{currentData?.ratio?(currentData?.ratio+ '%'):'--'}</div>
                        <div style={{ minWidth: '80px' }} className="head-column">{currentData?.projectRatio?(currentData?.projectRatio+ '%'):'--'}</div>
                        <div style={{ minWidth: '110px' }} className="head-column">{currentData?.accountRatio?(currentData?.accountRatio+ '%'):'--'}</div>
                        <div style={{ minWidth: '80px' }} className="head-column">{currentData?.amount?(currentData?.amount+ '%'):'--'}</div>
                        <div style={{ width: '70px' }} className='head-column'>
                          {currentData?.name ?? '--'}
                        </div>
                      </>
                    }
                </div> */}
                <ContextMenu top={contextPosition.y} projectId={projectId} left={contextPosition.x} visible={contextMenuVisible}
                    hide={hideContextMenu} />
                { showTable && <BdwTable
                    className='project-task-table project-task-table-accounting-create'
                    pagination={false}
                    loading={{
                        spinning: false
                    }}
                    tableLayout='auto'
                    expandable={{
                        defaultExpandAllRows: true,
                        expandedRowKeys,
                        onExpandedRowsChange: (e) => {
                            dispatch({
                                type: 'projectTasks/setExpandedRowKeys',
                                payload: e
                            })
                        },
                    }}
                    size="small"
                    rowKey="taskId"
                    // @ts-ignore
                    columns={taskTableColumns}
                    dataSource={taskLists ?? []}
                    useLocalData
                    sticky
                    scroll={{ x: true }}
                    showPages={false}
                    rowSelection={{
                        selectedRowKeys: cTaskId && [cTaskId] || [],
                        type: 'radio',
                        columnWidth: 1,
                        renderCell: () => {
                            return null;
                        },
                        checkStrictly: true,
                    }}
                    rowClassName={(records: any) => {
                        return computeBackground(records?.index?.length);
                      }}
                    // @ts-ignore
                    onRow={(task: any) => ({
                        onClick: async () => {
                            dispatch({
                                type: 'projectTasks/setCTaskId',
                                payload: task.taskId,
                            })
                            if (task.taskId !== taskInfo?.taskId) {
                                if ((isAddTask || editable) && taskInfo.name) {
                                    await dispatch({
                                        type: 'projectTasks/setSubmitStatus',
                                        payload: task
                                    })
                                } else {
                                    dispatch({
                                        type: 'projectTasks/fetchTaskInfo',
                                        payload: task,
                                        projectId
                                    })
                                }
                            }
                        },
                        onDoubleClick() {
                            // 有编辑权限时 双击才进入编辑状态
                            if (task.taskId !== taskInfo?.taskId) return
                            if (getEditAuthNew()) return
                            dispatch({
                                type: 'projectTasks/setEditable',
                                payload: true
                            })
                        },
                        onContextMenu: async (e) => {
                            // 有编辑权限时 鼠标右键单击才弹出相应菜单操作栏
                            // 禁用鼠标右键默认行为
                            dispatch({
                                type: 'projectTasks/setCTaskId',
                                payload: task.taskId,
                            })
                            if (task.taskId !== taskInfo?.taskId) {
                                if ((isAddTask || editable) && taskInfo.name) {
                                    await dispatch({
                                        type: 'projectTasks/setSubmitStatus',
                                        payload: task
                                    })
                                } else {
                                    dispatch({
                                        type: 'projectTasks/fetchTaskInfo',
                                        payload: task,
                                        projectId
                                    })
                                }
                            }
                            e.preventDefault();
                            // safe(taskStore.selectTask)(task.uid);
                            showContextMenu();
                            let y = e.clientY
                            let x = e.clientX
                            if (document.body.offsetHeight - e.clientY < 300) {
                                y = document.body.offsetHeight - 200;
                            }
                            // @ts-ignore
                            if (window.clientWidth - e.clientX < 100) {
                                // @ts-ignore
                                x = window.clientWidth - 100;
                            }
                            setContextMenuPosition({
                                x,
                                y,
                            });
                        },
                    })}
                />}
                
            </WrapperComponent>
            </div>

        </div>
        {
            taskInfo?.taskId && <TableRowInformation  projectId={projectId}/>
        }
        {/* <TableRowInformation /> */}

    </BdwRow>




    )
}

export default ProjectTasksTable;
