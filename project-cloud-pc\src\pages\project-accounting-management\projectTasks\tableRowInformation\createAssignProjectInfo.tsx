/**
 * @description 项目任务表格行-详情
 * <AUTHOR>
 * @date 2023-11-07 11:27:11
*/
import React, { useState, } from 'react';
import { message, Tabs, } from 'antd';
import { projectAccountingTasksRowTab, projectTasksRowTabKey } from '@/constants/Enum';
import ProgressReport from './progressReport';
import TaskHistory from './taskHistory';
import ResultEvaluation from './resultEvaluation';
import TaskInformation from './taskInformation/createAssignProject';
import StandardReference from './standardReference';
import { CloseOutlined } from '@ant-design/icons';
import { useSelector, useDispatch } from 'umi';
import { useUpdateEffect } from 'ahooks';
import {TaskInformationProps} from './taskInformation/createAssignProject';
import './index.less';

const TableRowInformation: React.FC<TaskInformationProps> = (props) => {
    const dispatch = useDispatch();
    const [activeTabKey, setActiveTabKey] = useState<any>(projectTasksRowTabKey.TASK_INFORMATION);
    const { editable, taskInfo, isAddTask } = useSelector((state: any) => state.projectTasks);
    const TabsItem: any[] =   [
        {
            label: '任务信息',
            key: 'TASK_INFORMATION',
            children:<TaskInformation {...props} />
        }
    ]
  
    useUpdateEffect(()=>{
        if(isAddTask){
            setActiveTabKey(projectTasksRowTabKey.TASK_INFORMATION);
        }
    },[isAddTask])
    function change<T>(e: T) {
        if ((isAddTask || editable)) {
            if (taskInfo.name) {
                dispatch({type: 'projectTasks/setSubmitStatus'})
            } else {
                // message.error('未填写标题');
                return
            }
        }
        setActiveTabKey(e)
    }
    const closeRightContent = () => {
        props?.onClose?.();
    }
    return (
        <div className='table-row-information'>
            <CloseOutlined onClick={closeRightContent} className='close-icon'></CloseOutlined>
            <Tabs
                className='table-row-information-tabs'
                items={TabsItem}
                onChange={change}
                activeKey={activeTabKey}
                destroyInactiveTabPane
            />
        </div>
    )
}
export default TableRowInformation;