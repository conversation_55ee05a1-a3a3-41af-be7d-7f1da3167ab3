/**
 * @description 进程汇报
 * <AUTHOR>
 * @date 2023-11-07 11:52:51
*/
import React, {  useRef, useMemo } from 'react';
import { BdwUploadBtn, BdwLeaveMessage, BdwFormItems } from '@/components';
import { Form, Popconfirm, Divider, Button, message ,Spin} from 'antd';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { taskReport,listTaskFunctionality } from '@/service/projectDos/my-project-detail/projectTasks';
import TaskDayReportComponent from './components/task-day-report-component';
import { useSelector, useDispatch,useParams } from 'umi';
import { CloseOutlined } from '@ant-design/icons';
import { useBoolean } from 'ahooks';
import {disabledFlag} from '../../../projectTasks';
import {TaskFunctionCode} from '@/constants/Enum';
import './index.less';



const ProgressReport: React.FC<{show?: boolean,projectId?: string,onHide:() => void}> = ({show,projectId,onHide}) => {
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const { taskInfo,functionality } = useSelector((state: any) => state.projectTasks);
    const { taskId } = taskInfo;
    const taskDayReportComponentRef = useRef<any>();
    const [saveLoading, { setFalse: loadingHide, setTrue: loadingShow }] = useBoolean(false)

    const refreshComponentFun = () => {
        if (taskDayReportComponentRef && taskDayReportComponentRef.current) {
            taskDayReportComponentRef.current.refreshComponent();
        }
    }
    const btnStatus = useMemo(()=>{
        if(functionality){
            return {
                canAdd:!disabledFlag(functionality, TaskFunctionCode.PROCESS_REPORT),
                canReply:!disabledFlag(functionality, TaskFunctionCode.PROCESS_REPLY),
            }
        }else{
            return {}
        }
    },[functionality]);
    //确认提报
    const getFormValue = (data: any) => {
        loadingShow();
        const taskReportDetails = data.taskReportDetails.map((item: any) => {
            const document = item.document?item.document?.map((i: any) => i.id):[];
            const fileList = item?.reportContent?.fileList?item?.reportContent?.fileList?.map((i: any) => i.id):[];
            return {
                document: [...new Set([...document,...fileList])].join(),
                reportContent: item.reportContent.value
            }
        })
        const params = {
            taskReportDetails,
            taskId: taskInfo.taskId
        }
        taskReport(params).then((res) => {
            loadingHide()
            form.resetFields();
            refreshComponentFun();
            message.success('汇报成功!')
            onHide();
            listTaskFunctionality({ projectId,taskId:taskInfo.taskId }).then((res: any) => {
                dispatch({
                    type: 'projectTasks/setFunctionality',
                    payload: res
                })
            })
        }).catch((err)=>{
            loadingHide();
        })
    }
    return (
        <>
            <Spin spinning={saveLoading}>
                <div className='progress-report'>
                    {btnStatus?.canAdd && show && <Form form={form} className='pr-16 pb-16' onFinish={getFormValue} initialValues={{ taskReportDetails: [{}] }}>
                        <div className='help-title mb-10'>
                            可以使用ctrl + v 上传截图
                            <CloseOutlined onClick={() => onHide()} className='close-icon'></CloseOutlined>
                        </div>
                        <Form.List name='taskReportDetails'>
                            {(fields, { add, remove }) => {
                                return (
                                    <>
                                        {fields.map(({ key, name, ...restField }, index) => (
                                            <div key={key} className="position-parent-relative">
                                                <BdwFormItems {...restField} name={[name, 'reportContent']} rules={[{ message: '该项不能为空', required: true }]}>
                                                    <BdwLeaveMessage maxLength={2048} onKeyDown={(e) => {
                                                        if (e.keyCode === 13) {
                                                            e.nativeEvent.stopImmediatePropagation()
                                                        }
                                                    }}
                                                    />
                                                </BdwFormItems>
                                                {fields.length > 1 ? (
                                                    <Popconfirm
                                                        title="确认删除此输入域？"
                                                        onConfirm={() => remove(name)}
                                                        okText="确认"
                                                        cancelText="取消"
                                                    >
                                                        <DeleteOutlined className="delete-has-upload-textarea-btn" key={key} title="删除" />
                                                    </Popconfirm>
                                                ) : null}
                                                {/* 增加上传资料 */}

                                                <BdwFormItems {...restField}  name={[name, 'document']} >
                                                    <BdwUploadBtn  color='rgba(0, 0, 0, 0.85)' />
                                                </BdwFormItems>
                                            </div>
                                        ))}
                                        <Button onClick={() => add()} style={{marginTop:'6px'}}><PlusOutlined className='f-16' />继续新增事项</Button>
                                    </>
                                )
                            }}
                        </Form.List>

                        <div className='mt-16'>
                            <Button htmlType='submit' className='mr-16' type='primary'>确认提报</Button>
                        </div>
                        <Divider dashed={true} className='mt-16' />
                    </Form>}
                    <div className='pr-16'>
                        {taskId && <TaskDayReportComponent canReply={btnStatus?.canReply} canAdd={btnStatus?.canAdd} ref={taskDayReportComponentRef} taskId={taskId} />}
                    </div>
                </div>
            </Spin>
        </>

    )
}
export default ProgressReport;