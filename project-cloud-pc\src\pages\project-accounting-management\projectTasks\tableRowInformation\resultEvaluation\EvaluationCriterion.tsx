import React, { useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, Divider, Form, message, Spin, Select } from "antd";
import {
  BdwEnumSelect,
  BdwFormItems,
  BdwInput,
  BdwRow,
  BdwTextarea,
  BdwTitle,
} from "@/components";
import { PlusOutlined } from "@ant-design/icons";
import { SelectSubmitEvaluationMaterials, editResultEvaluationCriteria } from '@/service/projectDos/my-project-detail/projectTasks';
import { useBoolean, useRequest } from "ahooks";

import "./index.less"
import { operationFormRules } from "./form-rules";
import type { StandardListItem } from "@/type";
import { formatSelectOptions } from '@/utils/utils';
import { useSelector } from 'umi';

interface EvaluationCriterionItem extends StandardListItem {
  isEdit?: boolean
}

interface EvaluationCriterionProps {
  // 任务Id
  taskId: string | number
  // 保存成功之后的回调
  saveSuccessCallback?: () => void
  // 取消编辑的回调
  cancelCallback?: () => void
}

const EvaluationCriterion: React.FC<EvaluationCriterionProps> = ({ taskId, saveSuccessCallback, cancelCallback }) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<EvaluationCriterionItem[]>([]);
  const [saveLoading, { setFalse: loadingHide, setTrue: loadingShow }] = useBoolean(false);
  const { taskInfo, attachmentFormatOptions,ProjectUploadFileType } = useSelector((state: any) => state.projectTasks);


  const getTheCanHandleFlag = () => {
    if (fileList.length === 0) {
      return true
    }
    const canHandleFlag = fileList.filter((item) => item.isEdit).length;
    return canHandleFlag === 0
  }

  // 需要获取历史已经保存的数据
  const { data: historyData,run:runHistoryData } = useRequest<any>(() => SelectSubmitEvaluationMaterials(taskId), {
    manual:true
  })
  useEffect(()=>{
    if(taskId && taskId != 'newAdd'){
        runHistoryData();
    }
},[taskId])

  // 将获取到的已保存的数据处理
  const handleHistoryData: any = useMemo(() => {
    if (historyData) {
      return historyData;
    }
    return null
  }, [historyData])

  useEffect(() => {
    if (handleHistoryData && handleHistoryData.evaluationUploadStandards) {
      setFileList(handleHistoryData.evaluationUploadStandards.map((item) => {
        return { 
          isEdit: false,
          des: item.attachmentDescription,
          type:item.attachmentFormat,
          id: Date.now()
        }
      }));
    }
    form.setFieldsValue({
      evaluationStandard: handleHistoryData && handleHistoryData.evaluationStandard
    })
  }, [handleHistoryData])

  const cancelAddFile = (index: number) => {
    const attachmentDescription = form.getFieldValue("attachmentDescription");
    const oldEvaluationName = fileList[index].des;
    if (!attachmentDescription && !oldEvaluationName) {
      setFileList(fileList.filter((item) => !item.isEdit))
      return
    }

    setFileList(fileList.map((item) => {
      return { ...item, isEdit: false }
    }));
  }

  // 代表是第几条数据
  const sureAddFile = (index: number) => {
    const attachmentDescription = form.getFieldValue("attachmentDescription");
    const attachmentFormat = form.getFieldValue("attachmentFormat");
    const handleFileList = [...fileList];
    if (!attachmentDescription) {
      message.error("附件要求名称必须填入");
      return
    }
    handleFileList.splice(index, 1, { des: attachmentDescription, type: attachmentFormat, isEdit: false, id: Date.now() })
    setFileList(handleFileList)
  }

  const editFileInfo = (index: number) => {
    const canHandleFlag = getTheCanHandleFlag();
    if (!canHandleFlag) {
      message.error("汇报附件要求还未进行保存，请确认！")
      return
    }
    const handleFileList = [...fileList];
    handleFileList[index].isEdit = true;
    setFileList(handleFileList)
    form.setFieldsValue({
      attachmentDescription: handleFileList[index].des,
      attachmentFormat: handleFileList[index].type,
    })
  }

  const deleteFile = (index: number) => {
    const handleFileList = [...fileList];
    handleFileList.splice(index, 1)
    setFileList(handleFileList)
  }

  const showFileList = fileList?.map((item, index: number) => {
    return (
      item.isEdit ?
        <BdwRow className='evaluation-file-item' type='flex' key={item.id}>
          <div className='flex-1 evaluation-file-name'>
            <BdwFormItems name='attachmentDescription' initialValue={item.des}>
              <BdwInput placeholder='附件要求名称' maxLength={128} onKeyDown={(e) => {
                if (e.keyCode === 13) {
                  e.nativeEvent.stopImmediatePropagation()
                }
              }} />
            </BdwFormItems>
          </div>
          <div className='evaluation-file-type'>
            <BdwFormItems name='attachmentFormat'>
              <Select placeholder="文件类型" options={formatSelectOptions(attachmentFormatOptions)} />
            </BdwFormItems>
          </div>
          <div className='evaluation-file-control'>
            <div>
              <Button type='link' className='mr-16 p-0' onClick={() => sureAddFile(index)}>保存</Button>
              <Button type='link' className='p-0' onClick={() => cancelAddFile(index)}>取消</Button>
            </div>
          </div>
        </BdwRow> :
        <BdwRow className='evaluation-file-item mb-10' type='flex' key={item.id}>
          <div className='evaluation-file-readonly-item flex-1'>
            {item.des}【{ProjectUploadFileType[item.type]}】
          </div>
          <div className='evaluation-file-control'>
            <Button type='link' className='mr-16 p-0' onClick={() => editFileInfo(index)}>编辑</Button>
            <Button type='link' className='p-0' onClick={() => deleteFile(index)}>删除</Button>
          </div>
        </BdwRow>
    )
  })

  const addOneFile = () => {
    const canHandleFlag = getTheCanHandleFlag();
    if (!canHandleFlag) {
      message.error("汇报附件要求还未进行保存，请确认！")
      return
    }
    form.resetFields(["attachmentDescription", "attachmentFormat"])
    setFileList([...fileList, { des: "", type: "", isEdit: true, id: "" }]);
  }

  const submitData = async (values: any) => {
    const canHandleFlag = getTheCanHandleFlag();
    if (!canHandleFlag) {
      message.error("汇报附件要求还未进行保存，请确认！")
      return
    }
    try {
      loadingShow();
      const { evaluationStandard } = values;
      const standardList = fileList.map((item) => {
        return {
          attachmentDescription: item.des,
          attachmentFormat: item.type,
        }
      })
      await editResultEvaluationCriteria({
        taskId,
        evaluationStandard,
        evaluationUploadStandards: standardList
      });
      message.success("评价标准保存成功!");
      saveSuccessCallback?.();
    } catch (e) {
    } finally {
      loadingHide();
    }
  }

  const cancelEdit = () => {
    cancelCallback?.();
  }

  return (
    <Spin spinning={saveLoading}>
      <Form form={form} className='pt-16 pr-16' onFinish={submitData}>
        <BdwFormItems label='任务考评要求：' name='evaluationStandard' rules={operationFormRules.evaluationStandard}>
          <BdwTextarea autoSize maxLength={512} onKeyDown={(e) => {
            if (e.keyCode === 13) {
              e.nativeEvent.stopImmediatePropagation()
            }
          }} />
        </BdwFormItems>
        <Divider className='mb-16' dashed={true} />

        <BdwRow type='flex' className='mb-10'>
          <div className='flex-1'>
            <BdwTitle>汇报附件要求：</BdwTitle>
          </div>
          <div>
            <Button className='p-0 add-file-button' type='link' onClick={addOneFile}><PlusOutlined className="f-16" />添加</Button>
          </div>
        </BdwRow>
        <div className='mb-16'>
          {showFileList}
        </div>
        <div className="mt-16">
          <Button className="mr-16" htmlType='submit' type="primary">保存</Button>
          <Button onClick={() => cancelEdit()}>取消</Button>
        </div>
      </Form>
    </Spin>
  )
}

export default EvaluationCriterion
