import React, { Key, useMemo } from "react";
import { SelectSubmitEvaluationMaterials } from '@/service/projectDos/my-project-detail/projectTasks';
import "./index.less"
import { useRequest } from "ahooks";
import { BdwFileShow, BdwReadonlySpan, BdwRow } from "@/components";
import { Spin } from "antd";
import { useSelector } from 'umi';
import { AuditResult } from "@/constants/Enum";

interface EvaluationCriterionReadonlyProps {
  taskId: Key
  task?: any
  selectedShow?: boolean,
  handleHistoryData: any
}

const EvaluationCriterionReadonly: React.FC<EvaluationCriterionReadonlyProps> = ({ selectedShow = true, taskId, task,handleHistoryData }) => {
  const { ProjectUploadFileType } = useSelector((state: any) => state.projectTasks);


  const {
    evaluationUploadStandards = [],
    evaluationStandard = "",
    taskReviewInfo,
    taskStandardScore,
    scoreProportion
  } = handleHistoryData ?? {};

  const {
    auditDocument,//审核资料文件
    auditOpinion,//审核文件
    reportContent,//汇报内容
    reportDocument,//汇报资料文件
    reviewId,//任务评审id
    statusName,//状态名
    statusCode,//状态编码
  } = taskReviewInfo ?? {};

  let auditResultDesc: string
  switch (AuditResult[statusCode!]) {
    case 'APPROVED': auditResultDesc = '合格'; break;
    case 'REJECTED': auditResultDesc = '不合格'; break;
    default: auditResultDesc = '暂未评审';
  }


  const showFileList = evaluationUploadStandards?.map((item: any, index: number) => {
    return (
      <BdwRow className='evaluation-must-file-item' type='flex' key={`${index}`}>
        <span className='evaluation-must-file-readonly-item flex-1 content-exist-color line-height-32'>
          {item.attachmentDescription}【{item.attachmentFormat ? ProjectUploadFileType[item.attachmentFormat] : '任意格式'}】
        </span>
      </BdwRow>
    )
  })

  return (
    <>
      
        {/* 此处!!表示standardScore为一个布尔值，作为显示的判断条件，否则会认为此处需要显示standardScore的值 */}
        <div className='evaluation-criterion-readonly'>
          <div className='mb-20 flex'>
            <div className="bdw-title-row-wrapper">
              <div className="bdw-title-row">任务考评要求<i /></div>
            </div>
            {
              evaluationStandard ?
                <div className="ml-16 line-height-same content-exist-color">{evaluationStandard}</div> :
                <span className="back-show-info ml-16">无考评要求</span>
            }
          </div>
          {
            evaluationUploadStandards?.length > 0 &&
            <div className='mb-20'>
              <div className="bdw-title-row-wrapper">
                <div className="bdw-title-row">汇报附件要求<i /></div>
              </div>
              {showFileList}
            </div>
          }
          {
            evaluationUploadStandards?.length === 0 &&
            <div className='mb-20 flex'>
              <div className="bdw-title-row-wrapper">
                <div className="bdw-title-row">汇报附件要求<i /></div>
              </div>
              <span className="back-show-info ml-16">无附件要求</span>
            </div>
          }
          {
            selectedShow &&
            <>
              <BdwRow type='flex-center' className='bdw-standard-score-readonly mb-20'>
                <div className="bdw-title-row-wrapper">
                  <div className="bdw-title-row">价值评分<i /></div>
                </div>
                {
                  taskStandardScore ? (
                    <div className='flex-1'>
                      <BdwReadonlySpan className='mr-5 ml-16 line-height-same flex'>{taskStandardScore}分</BdwReadonlySpan>
                    </div>
                  ) : (
                    <span className='mr-5 back-show-info ml-16'>暂未设置</span>
                  )
                }
              </BdwRow>
              {
                taskStandardScore && <BdwRow type='flex-center' className='bdw-standard-score-readonly mb-20'>
                  <div className="bdw-title-row-wrapper">
                    <div className="bdw-title-row">评分占比<i /></div>
                  </div>
                  {
                    scoreProportion ? (
                      <div className='flex-1'>
                        <BdwReadonlySpan className='mr-5 ml-16 line-height-same flex'>{scoreProportion}</BdwReadonlySpan>
                      </div>
                    ) : (
                      <span className='mr-5 back-show-info ml-16 color-c5c8cd f-12'>暂未设置</span>
                    )
                  }
                </BdwRow>
              }
            </>
          }
          {
            reportContent &&
            <div className='mb-20 flex'>
              <div className="bdw-title-row-wrapper">
                <div className="bdw-title-row">汇报内容<i /></div>
              </div>
              <span className="ml-16 line-height-same content-exist-color">{reportContent}</span>
            </div>
          }
          {
            reportDocument &&
            <div className='mb-20 '>
              <div className="bdw-title-row-wrapper">
                <div className="bdw-title-row">汇报附件<i /></div>
              </div>
              <BdwRow className='file-show-content mt-5'>
                <BdwFileShow attachments={reportDocument} />
              </BdwRow>
            </div>
          }
          {
            AuditResult[statusCode!] !== "Blank" &&
            <div className='mb-20 flex'>
              <div className="bdw-title-row-wrapper">
                <div className="bdw-title-row">评审结果<i /></div>
              </div>
              <span className="ml-16 line-height-same content-exist-color">{statusName??<span className='mr-5 back-show-info ml-16 color-c5c8cd f-12'>暂未设置</span>}</span>
            </div>
          }
          {
            // AuditResult[statusCode!] === "Qualified" && actualScore !== 0 &&
            // <div className='mb-20 flex'>
            //   <div className="bdw-title-row-wrapper">
            //     <div className="bdw-title-row">评审分数<i /></div>
            //   </div>
            //   <span className="ml-16 line-height-same content-exist-color">{actualScore}分</span>
            // </div>
          }

          {
            // (executeStatus === "Rejected" || executeStatus === "Normal") &&
            (AuditResult[statusCode!] === "APPROVED" || AuditResult[statusCode!] === "REJECTED") &&
            auditOpinion &&
            <div className='mb-20 flex'>
              <div className="bdw-title-row-wrapper">
                <div className="bdw-title-row">评审意见<i /></div>
              </div>
              <span className="assign-remark ml-16 line-height-same content-exist-color">{auditOpinion}</span>
            </div>
          }
          {
            AuditResult[statusCode!] === "REJECTED" && auditDocument &&
            <div className='mb-20'>
              <div className="bdw-title-row-wrapper">
                <div className="bdw-title-row">评审附件<i /></div>
              </div>
              <BdwRow className='file-show-content mt-5'>
                <BdwFileShow attachments={auditDocument} />
              </BdwRow>
            </div>
          }
        </div>
        {/* {(task?.assigned !== "Blank" && task?.assigned !== "Refuse") && !assignRequirement && !standardScore &&  selectedShow && <Empty className='mt-16' description="暂无评价标准" />} */}
    
    </>
  )
}

export default EvaluationCriterionReadonly
