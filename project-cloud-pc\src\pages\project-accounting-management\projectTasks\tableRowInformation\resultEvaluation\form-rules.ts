const rules = {
    "taskAudit": {
        "actualScore": [
            { required: true, message: "考核成绩不能为空" }
        ],
        "assignRemark": { message: "评审意见不能为空" },
        "enclosures": []
    }
}
export const operationFormRules = {
    "evaluationStandard": [
        { required: true, message: "任务考评要求不能为空" }
    ],
    "standardScore": [
        () => ({
            validator(rule: any, value: string | number) {
                const handleValue = parseFloat(value.toString());
                if (!isNaN(handleValue) && handleValue >= 0) {
                    return Promise.resolve()
                }
                return Promise.reject(new Error('必须为数字，且大于0'));
            }
        })
    ],
}


export default rules
