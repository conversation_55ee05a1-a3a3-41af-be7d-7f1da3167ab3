import React, { useEffect, useRef, useState } from 'react';
import './index.less';
import { Modal } from 'antd';
import ReactZmage from "react-zmage";
import { useUpdateEffect } from 'ahooks';

//引入图标
// import proImg from '@/pages/proImg'

//API
import { saveBatch } from '@/service/projectDos/commonApi';


//antd组件
import { message } from 'antd'
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { rest } from 'lodash';

interface UploadImgType {
    getImgId?: (value: any) => void;
    labelName?: string
    viewFileUrls?: any[];
    disabled?: boolean;
    canUploadMore?: boolean,
    getProofId?: (value: any) => void;
    required?: boolean,
    helperText?: string
    accept?: string
    uploadType?: 'video' | 'picture',
    onChange?: (e: any) => void,
    emptyMessage?: string
}

const UploadImg: React.FC<UploadImgType> = (props): any => {
    const uploadInput = useRef();
    const { getImgId, onChange, labelName, viewFileUrls, disabled, canUploadMore, getProofId, required, helperText, accept = '.png,.jpg,.jpeg,.gif,.bmp', uploadType, emptyMessage = '未上传数据' } = props;
    const [imgListView, setImgListView] = useState<any[]>([])
    const [viewVideo, setViewVideo] = useState(false);
    const [videoInfo, setVideoInfo] = useState<{videoName?: string,videoUrl?: string}>({});

    useEffect(() => {
        if (viewFileUrls && viewFileUrls.length) {
            setImgListView(viewFileUrls);
        } else {
            setImgListView([]);
        }
    }, [viewFileUrls])

    useUpdateEffect(() => {
        if(!disabled){
            getImgId?.(imgListView);
            const newImgList = imgListView.map(item => item.id);
            getProofId?.(newImgList.join());
            onChange?.(newImgList.join())
        }
    }, [imgListView])
    //上传图片
    const upLoad = () => {
        uploadInput.current.click();
    }
    //获取视频第一帧封面
    const generateCoverImage = (videoElement: any) => {
        let canvas = document.createElement('canvas');
        let context = canvas.getContext('2d');
        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;
        context?.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        let coverImageDataUrl = canvas.toDataURL('image/jpeg');
        return coverImageDataUrl;
    }
    const inputOnChange = (e) => {
        const files = e.target.files;
        let vUrl = '';
        if (files.length) {
            if (uploadType == 'picture') {
                let pattern = /^image/;
                if (!pattern.test(files[0].type)) {
                    message.error('上传图片格式不正确!');
                    return
                }
            }
            if (uploadType == 'video') {
                let videoUrl = window.URL.createObjectURL(files[0]);
                let videoElement = document.createElement('video');
                videoElement.src = videoUrl;
                videoElement.onloadedmetadata = function () {
                    vUrl = generateCoverImage(videoElement);
                }

            }
            let formData = new FormData();
            formData.set('files', files[0]);
            saveBatch(formData).then(
                (res) => {
                    if (res) {
                        uploadInput.current.value = "";//置空input value 防止删除图片后再次上传没有反应
                        if (uploadType == 'video') {
                            res[0].videoUrl = vUrl;
                        }
                        if (canUploadMore) {
                            const newImgListView = JSON.parse(JSON.stringify(imgListView));
                            newImgListView.push(res[0])
                            setImgListView(newImgListView);
                        } else {

                            setImgListView(res);
                        }

                    }
                }
            ).catch((error) => {
                console.log(error)
            })
        }
    }
    //删除图片
    const deleteViewImg = (index: number) => {
        const newImgListView = JSON.parse(JSON.stringify(imgListView));
        newImgListView.splice(index, 1);
        setImgListView(newImgListView);
    }
    const viewPlayer = (data: any) => {
        setVideoInfo({
            videoUrl:data.url,
            videoName: data.fileName
        });
        setViewVideo(true);
    }
    return (
        <>
            <div className='upload'>
                <div className='upload-box'>
                    <div className='upload-list'>
                        {imgListView.length ?
                            (imgListView.map((item, index) => {
                                return (
                                    <div className='show-img' key={item.id}>

                                        {
                                            uploadType == 'picture' ? <ReactZmage key={index} backdrop='rgba(0,0,0,0.7)' src={item.url} /> :
                                                <img className='video-player' onClick={() => viewPlayer(item)} src={item.videoUrl} alt="" />
                                        }
                                        {
                                            disabled ? null :
                                                <div className='delete-image' onClick={() => { deleteViewImg(index) }}><CloseOutlined style={{ color: 'white' }} /></div>
                                        }
                                    </div>
                                )
                            })
                            )

                            : disabled ? <div className='f-12 color-c5c8cd'>{emptyMessage}</div> : null
                        }
                    </div>
                    {
                        disabled ?
                            null : (
                                <div className='upload-img' onClick={upLoad}>
                                    <div className='upload-logo'>
                                        <PlusOutlined style={{ fontSize: "30px", color: "#aaa" }}></PlusOutlined>
                                    </div>
                                    <input type="file" accept={accept} ref={uploadInput} onChange={inputOnChange} />
                                </div>
                            )
                    }

                </div >
                <div className='upload-helperText'>
                    {helperText ?? ''}
                </div>
            </div >
            <Modal
                width={1032}
                open={viewVideo}
                onCancel={() => {
                    setViewVideo(false);
                }}
                footer={null}
                title={videoInfo.videoName}
                maskClosable={false}
            >
                <div>
                    <video src={videoInfo.videoUrl} controls width={1000} height={800}></video>
                </div>

            </Modal>
        </>
    )
}
export default UploadImg;