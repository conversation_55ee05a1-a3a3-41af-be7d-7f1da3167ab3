/**
 * @description 标准参考
 * <AUTHOR>
 * @date 2024-01-18 10:19:40
*/
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector, useParams } from 'umi';
import { Form, Input, Upload, Divider, Button, Checkbox, DatePicker, InputNumber, Popconfirm, Select, message, Modal, SelectProps, TreeSelect, Empty } from 'antd';
import './index.less';
import { BdwFormItem, BdwLeaveMessage, BdwRow, BdwButton, BdwRichText } from '@/components';
import {
    editStandardReferenceApi,
    infoDirectoryCatalogApi,
    selectAddressBook,
    getTaskSubmitReviewInfo,
    listUserTypeApi,
    listProductProviderApi,
    loadByCustomerApi
} from '@/service/projectDos/my-project-detail/projectTasks';
import CustomUpload from './customUpload/index';
import TreeSelectComponent from './TreeSelectComponent';
import TreeSelectC from './TreeSelectC';
import { DoubleLeftOutlined } from '@ant-design/icons';
// @ts-ignore
import Add from '@/assets/image/Add.svg';
import LONG_DASH from '@/assets/image/LONG_DASH.svg';
// @ts-ignore
import EMPTY_IMG from '@/assets/image/empty.png';
import { cloneDeep } from 'lodash';
import ReactZmage from "react-zmage";
import { TaskFunctionCode, ConfirmPersonType } from '@/constants/Enum';
import { disabledFlag } from '../../../projectTasks';
import { getRichImgSrc, getText } from '@/utils/utils';
const { TextArea } = Input;

const initFormValue = {
    standardReferenceDetails: [
        {
            imageAttachmentIds: undefined,//参考图
            interpretation: undefined,//说明
            videoAttachmentIds: undefined,//参考视频
            details: undefined,//细节说明
            uploadTypes: [{
                typeId: '',
            }],//上传结果类型
            foldFlag: true
        }
    ],
    projectTaskUploadResultConfirms: [
        {
            confirmPersonnel: [],
            uploadTypes: [],
            userType: '',
            userSubType: ''
        }
    ],
}
//过滤部门下人员为空的数据
const filterData = (list: any) => {
    return list.filter((i: any) => {
        if (i.isOrg) {
            if (i.children?.length) {
                if (filterData(i.children).length) {
                    return true
                } else {
                    return false
                }
            } else {
                return false
            }
        } else {
            return true
        }
    });
}
const TreeDAtaList = (arr: any, value?: any) => {
    if (!arr || !arr.length) {
        return []
    }
    let newList: any[] = [];
    arr.forEach((item: any) => {
        if (item.isOrg) {
            if (item.children && item.children.length) {

                const fc = filterData(item.children);
                const Children = TreeDAtaList(fc);
                const obj = {
                    ...item,
                    children: Children
                }
                newList.push(obj)
            }
        } else {
            newList.push(item)
        }
    })
    return newList;
}
const findTaskItem = (tree: any, id: string, callback?: (data: any) => void) => {
    if (!(tree && tree.length)) return []
    let arr: any[] = [];
    for (let i = 0; i < tree.length; i++) {
        if (tree[i].key.includes(id)) {
            arr.push(tree[i].key)
        }
        if (tree[i].children) {
            const carr = findTaskItem(tree[i].children, id, callback);
            if (carr && carr.length) {
                arr = [...arr, ...carr]
            }
        }
    }
    return arr;
}

interface optionType {
    value?: string,
    name?: string,
    key?: string
}
interface FoldPanelProps {
    onChange?: (e: boolean) => void,
    value?: boolean
}
interface SelectConfirmResultTypeProps extends SelectProps {
    onChange?: (e: any) => void,
}
const FoldPanel: React.FC<FoldPanelProps> = (props) => {
    const { value, onChange } = props;
    const change = () => {
        const flag = value == undefined ? true : value;
        onChange?.(!flag);
    }
    return <div className='fold-change-wrapper' onClick={change}>
        <div className='label' >{value ? '收起' : '展开'}</div>
        <div className='icon'>
            <DoubleLeftOutlined className={`double-up-down ${value ? 'up-icon' : 'down-icon'}`}></DoubleLeftOutlined>
        </div>
    </div>
}
const EchoText: React.FC<{ value?: any }> = (props) => {
    const { value } = props
    return value ? <div className='echo-text'>{value}</div> : <div className='f-12 color-c5c8cd'>未填写标准说明</div>
}
const DetailsShow: React.FC<{ value?: any }> = (props) => {
    const { value } = props
    if (!value) return <div className='f-12 color-c5c8cd'>未填写细节说明</div>
    const detailsArr = getRichImgSrc(value.value);
    return <div>
        <div className='detail-info-word'>{getText(value.value)}</div>
        {
            detailsArr.length > 0 && detailsArr.map((i: any, index: number) => (
                // @ts-ignore
                <ReactZmage key={index} backdrop='rgba(0,0,0,0.7)' src={i} />
            ))
        }
    </div>
    return <div dangerouslySetInnerHTML={{ __html: value.value }}></div>
    return <BdwRichText
        hideTool
        sx={{ width: '100%', height: '120px', marginBottom: '10px' }}
        placeholder='填写任务说明(可直接粘贴图片)'
        disabled={true}
        value={value}
    >

    </BdwRichText>
}
const SelectConfirmResultType: React.FC<SelectConfirmResultTypeProps> = (props) => {
    const { onChange, ...others } = props;
    return <Select
        {...others}

        onChange={(e, r) => {
            console.log(e, r)
            const data = r.find((i: any) => i.label == '全部');
            if (data) {
                if (r.length > 1) {
                    onChange?.([data.value]);
                    if (r.length == 2) {
                        if (r.findIndex((i: any) => i.label == '全部') == 0) {
                            onChange?.([r[1].value]);
                        } else {
                            onChange?.([data.value]);
                        }
                    }
                } else {
                    onChange?.(e)
                }
            } else {
                onChange?.(e)
            }

        }}

    ></Select>
}
const StandardReference: React.FC = () => {
    const { taskInfo, functionality } = useSelector((state: any) => state.projectTasks);
    const { userInfo } = useSelector((state: any) => state.commonTask);//当前项目登录人信息
    const { currentCustomerId } = useSelector((state: any) => state.projectOverview);
    const [form] = Form.useForm();
    const [addressBookValue, setAddressBookValue] = useState<any>([]);
    const [typeOptions, setTypeOptions] = useState<any>([]);
    const [edit, setEdit] = useState(false);
    const [editDetails, setEditDetails] = useState<any>(null);
    const [userTypeOption, setUserTypeOption] = useState([]);
    const [supplierOption, setSupplierOption] = useState([]);
    const [customerOption, setCustomerOption] = useState([]);
    const [configFlag, setConfigFlag] = useState(false);
    const { projectId } = useParams<{ projectId: string }>();
    const [formData, setFormData] = useState<any>();
    useEffect(() => {
        // 查询结果类型
        infoDirectoryCatalogApi().then((res: any) => {
            const resOption = res.map((item: optionType) => {
                return {
                    label: item.value,
                    value: item.name,
                }
            })
            setTypeOptions(resOption);
        })
        // 查询人员类型
        listUserTypeApi().then((res: any) => {
            const resOption = res.map((item: optionType) => {
                return {
                    label: item.value,
                    value: item.name,
                }
            })
            setUserTypeOption(resOption);
        })
        // 选择供应商
        listProductProviderApi().then((res: any) => {
            const resOption = res.map((item: optionType) => {
                return {
                    label: item.value,
                    value: item.key,
                }
            })
            setSupplierOption(resOption)
        })
        // 选择员工
        selectAddressBook().then((res: any) => {
            const arr = TreeDAtaList(res.map(formatAddress));
            setAddressBookValue(arr);
        })
        // 选择客户及其联系人
        if (currentCustomerId) {
            loadByCustomerApi(currentCustomerId).then((res: any) => {
                const resOption = res.map((item: any) => {
                    return {
                        label: item.name,
                        value: item.id + '-' + item.userSubType,
                    }
                })
                setCustomerOption(resOption)
            })
        } else {
            setCustomerOption([])
        }


    }, [])
    useEffect(() => {
        if (addressBookValue.length) {
            getInformation();
        }
    }, [taskInfo.taskId, addressBookValue, projectId])
    const getInformation = () => {
        getTaskSubmitReviewInfo({ taskId: taskInfo.taskId }).then((res: any) => {
            setEditDetails(res);
            let standardReferenceDetails = [
                {
                    imageAttachmentIds: undefined,//参考图
                    interpretation: undefined,//说明
                    videoAttachmentIds: undefined,//参考视频
                    details: undefined,//细节说明
                    uploadTypes: [{
                        typeId: '',
                    }],//上传结果类型
                    foldFlag: true,
                }
            ]
            let projectTaskUploadResultConfirms = [
                {
                    confirmPersonnel: [],
                    uploadTypes: [],
                    userType: '',
                    userSubType: ''
                }
            ]
            if (res.submitReviewItems && res.submitReviewItems.length) {
                standardReferenceDetails = res.submitReviewItems.map((i: any) => {
                    const uploadTypes = i.submitReviewItems.map((item: any) => ({
                        typeId: item.uploadTypeCode
                    }))
                    if (!uploadTypes.length) {
                        uploadTypes.push({
                            typeId: '',
                        })
                    }
                    let details = undefined;
                    if (i.taskStandardReferenceInfo.details) {
                        details = JSON.parse(i.taskStandardReferenceInfo.details);
                    }
                    let imageAttachmentIds = undefined;
                    if (i.taskStandardReferenceInfo.images) {
                        imageAttachmentIds = i.taskStandardReferenceInfo.images.map((i: any) => i.id).join();
                    }
                    let videoAttachmentIds = undefined;
                    if (i.taskStandardReferenceInfo.videos) {
                        videoAttachmentIds = i.taskStandardReferenceInfo.videos.map((i: any) => i.id).join();
                    }
                    return {
                        foldFlag: true,
                        interpretation: i.taskStandardReferenceInfo.interpretation,
                        details,
                        id: i.taskStandardReferenceInfo.id,
                        uploadTypes,
                        imageAttachmentIds,
                        videoAttachmentIds
                    }
                })
            }
            if (res.projectTaskUploadResultConfirmVos && res.projectTaskUploadResultConfirmVos.length) {
                setConfigFlag(true);
                projectTaskUploadResultConfirms = res.projectTaskUploadResultConfirmVos.map((i: any) => {
                    let uploadTypes: any[] = [], confirmPersonnel: any[] = [];
                    if (i.uploadTypes && i.uploadTypes.length) {
                        uploadTypes = i.uploadTypes.map((ii: any) => ii.key);
                    }
                    if (i.confirmPersonals && i.confirmPersonals.length) {
                        i.confirmPersonals.forEach((ii: any) => {
                            if (findTaskItem(addressBookValue, ii.key).length) {
                                confirmPersonnel.push(findTaskItem(addressBookValue, ii.key)[0]);
                            }
                        });
                    }
                    return {
                        confirmPersonnel,
                        uploadTypes,
                        userType: i.userTypeCode,
                        userSubType: i.userSubType
                    }
                })
            } else {
                setConfigFlag(false);
            }
            form.setFieldsValue({
                projectTaskUploadResultConfirms,
                standardReferenceDetails
            })
            setFormData({
                projectTaskUploadResultConfirms,
                standardReferenceDetails
            })
        })
    }
    const formatAddress = (item: any, index: number) => {
        return {
            key: item.jobInfo?.orgId ? item.id + '-' + item.jobInfo?.orgId : item.id,
            title: item.name,
            // value: item.name,
            children: item.children?.map(formatAddress),
            isOrg: item.isOrg,
            posName: item.jobInfo?.positionName,
            value: item.jobInfo?.orgId ? item.id + '-' + item.jobInfo?.orgId : item.id,
        }
    }
    const submit = () => {
        form.validateFields().then((data: any) => {
            const standardReferenceDetails = data.standardReferenceDetails.map(((i: any) => {
                let uploadTypes: any = '';
                if (i.uploadTypes.length) {
                    uploadTypes = i.uploadTypes.map((ii: any) => (ii.typeId)).join();
                }
                return {
                    details: JSON.stringify(i.details),
                    id: i.id,
                    imageAttachmentIds: i.imageAttachmentIds,
                    interpretation: i.interpretation,
                    uploadTypes,
                    videoAttachmentIds: i.videoAttachmentIds
                }
            }))
            let projectTaskUploadResultConfirms: any[] = [];

           
                data.projectTaskUploadResultConfirms.forEach((item: any) => {
                    if (item.userType == ConfirmPersonType.CUSTOMER) {
                        const cType = item.confirmPersonnel.map((citem: any) => citem.split("-")[1]);
                        const temArr = [...new Set(cType)];
                        if (temArr.length > 1) {
                            const customer: any[] = [];
                            const emyplee: any[] = [];
                            item.confirmPersonnel.forEach((it: any) => {
                                console.log(it.split("-")[1], it.split("-")[1])
                                if (it.split("-")[1] == temArr[0]) {
                                    customer.push(it.split("-")[0]);
                                } else {
                                    emyplee.push(it.split("-")[0]);
                                }
                            });
                            projectTaskUploadResultConfirms.push({
                                confirmPersonals: customer.join(),
                                userType: item.userType,
                                userSubType: temArr[0],
                                uploadTypes: item.uploadTypes.join(),
                            });
                            projectTaskUploadResultConfirms.push({
                                confirmPersonals: emyplee.join(),
                                userType: item.userType,
                                userSubType: temArr[1],
                                uploadTypes: item.uploadTypes.join(),
                            });
                        } else {
                            const cType = item.confirmPersonnel.map((citem: any) => citem.split("-")[0]);
                            projectTaskUploadResultConfirms.push({
                                confirmPersonals: cType.join(),
                                userType: item.userType,
                                userSubType: item.confirmPersonnel[0].split("-")[1],
                                uploadTypes: item.uploadTypes.join(),
                            });
                        }
                    } else if (item.userType == ConfirmPersonType.EMPLOYEE) {
                        projectTaskUploadResultConfirms.push({
                            confirmPersonals: [...new Set(item.confirmPersonnel.map((i: any) => i.split('-')[0]))].join(),
                            userType: item.userType,
                            uploadTypes: item.uploadTypes.join(),
                        });
                    } else {
                        if(item.userType && item.confirmPersonnel.length){
                            projectTaskUploadResultConfirms.push({
                                confirmPersonals: item.confirmPersonnel.join(),
                                userType: item.userType,
                                uploadTypes: item.uploadTypes.join(),
                            });
                        }
                        
                    }
                });
            
            


            const params = {
                standardReferenceDetails,
                projectTaskUploadResultConfirms
            }
            editStandardReferenceApi(taskInfo.taskId, params).then((res) => {
                setEdit(false);
                message.success('保存成功!')
                getInformation();

            })
        })
    }


    return (
        <div className="standard-reference-wrapper">
            <div className={`title-label  ${edit ? '' : 'no-edit'}`}>
                <div className='label'>
                    执行项名称
                </div>
                <div className='title-name'>
                    {taskInfo.name}
                </div>
            </div>

            <Form form={form} initialValues={initFormValue} >
                <Form.List name='standardReferenceDetails'>
                    {(fields, { add, remove }) => {
                        return (
                            <div className={`add-edit-standard-wrapper  ${edit ? '' : 'no-edit'}`}>
                                {fields.map((field, index) => {
                                    return <div className='form-block-item' key={field.key}>
                                        {/* @ts-ignore */}
                                        <BdwFormItem className='interpretation' label="标准说明" required rules={[{ required: true, message:"请填写标准说明" }]} name={[field.name, 'interpretation']}>
                                            {
                                                edit ? <TextArea placeholder='填写标准说明'></TextArea> : <EchoText />
                                            }

                                        </BdwFormItem>
                                        <Form.Item noStyle shouldUpdate={(p, c) => {
                                            return p.standardReferenceDetails[index]?.foldFlag != c.standardReferenceDetails[index]?.foldFlag
                                        }}>
                                            {
                                                ({ getFieldValue }) => {
                                                    return getFieldValue('standardReferenceDetails')[index] == undefined || getFieldValue('standardReferenceDetails')[index]?.foldFlag ?
                                                        <>
                                                            {/* @ts-ignore */}
                                                            <BdwFormItem label="标准参考图" required rules={[{ required: true, message:"请上传标准参考图" }]} name={[field.name, 'imageAttachmentIds']}>
                                                                <CustomUpload canUploadMore={true} uploadType='picture' viewFileUrls={editDetails?.submitReviewItems[index]?.taskStandardReferenceInfo?.images} disabled={!edit} />
                                                            </BdwFormItem>
                                                            {/* @ts-ignore */}
                                                            <BdwFormItem label="标准参考视频" name={[field.name, 'videoAttachmentIds']}>

                                                                <CustomUpload canUploadMore={true} emptyMessage="未上传标准参考视频" uploadType='video' accept='video/*' viewFileUrls={editDetails?.submitReviewItems[index]?.taskStandardReferenceInfo?.videos} disabled={!edit} />
                                                            </BdwFormItem>
                                                            {/* @ts-ignore */}
                                                            <BdwFormItem className='mt-10 rich-details-wrapper' label="细节说明" {...field} name={[field.name, 'details']}>
                                                                {edit ? <BdwRichText
                                                                    hideTool
                                                                    sx={{ width: '100%', height: '120px' }}
                                                                    placeholder='填写任务说明(可直接粘贴图片)'

                                                                >

                                                                </BdwRichText> : <DetailsShow></DetailsShow>}

                                                            </BdwFormItem>
                                                            {/* @ts-ignore */}
                                                            <Form.List name={[field.name, 'uploadTypes']} >
                                                                {(typeFields, { add: typeAdd, remove: typeRemove }) => {
                                                                    return (<>
                                                                        <div className='interpretation-label'>
                                                                            <div className='label-left'>
                                                                                上传执行结果类型
                                                                                {
                                                                                    edit && <div className='add-interpretation' onClick={() => {
                                                                                        if (typeOptions.length == typeFields.length) {
                                                                                            message.warn('已选择全部类型选项，不能再添加')
                                                                                            return
                                                                                        }

                                                                                        typeAdd();
                                                                                    }}>
                                                                                        <img style={{ width: '25px', height: '25px' }} src={Add} alt="" /> 添加
                                                                                    </div>
                                                                                }
                                                                            </div>



                                                                        </div>
                                                                        {
                                                                            edit ? <div className='select-container'>
                                                                                {
                                                                                    typeFields.map((insideField, insideIndex) => (
                                                                                        <div className='select-confirm-wrapper' key={insideField.key}>
                                                                                            <Form.Item
                                                                                                noStyle
                                                                                                shouldUpdate={(p, c) => {
                                                                                                    return p.standardReferenceDetails[index].uploadTypes.length != c.standardReferenceDetails[index].uploadTypes.length
                                                                                                }}
                                                                                            >

                                                                                                {
                                                                                                    ({ getFieldValue }) => {
                                                                                                        const hasSelected = getFieldValue('standardReferenceDetails')[index].uploadTypes.map((i: any) => i?.typeId);
                                                                                                        const resSelected: any = [];
                                                                                                        const nowOption = cloneDeep(typeOptions).map((i: any) => {

                                                                                                            if (hasSelected.includes(i.value)) {
                                                                                                                resSelected.push(i);
                                                                                                                return {
                                                                                                                    ...i,
                                                                                                                    disabled: true
                                                                                                                }
                                                                                                            } else {
                                                                                                                return i
                                                                                                            }
                                                                                                        })


                                                                                                        return <Form.Item noStyle name={[insideField.name, 'typeId']} >
                                                                                                            <Select
                                                                                                                options={nowOption}
                                                                                                                bordered={false}
                                                                                                                style={{ width: 220 }}
                                                                                                                onSelect={(e, r) => {

                                                                                                                }}
                                                                                                            ></Select>
                                                                                                        </Form.Item>



                                                                                                    }}

                                                                                            </Form.Item>


                                                                                        </div>

                                                                                    ))
                                                                                }
                                                                            </div> : <>
                                                                                {
                                                                                    (editDetails?.submitReviewItems[index]?.submitReviewItems && editDetails.submitReviewItems[index]?.submitReviewItems?.length > 0) ?
                                                                                        <>
                                                                                            {
                                                                                                editDetails?.submitReviewItems[index].submitReviewItems?.map((item: any, index: number) => (
                                                                                                    <div className='upload-type-readonly' key={item.uploadTypeCode}>
                                                                                                        <div className='u-t-r-label'>{item.uploadTypeName}（{item.attachments?.length ?? 0}）</div>
                                                                                                        {
                                                                                                            item.attachments && item.attachments.length > 0 ?
                                                                                                                <div className='u-t-r-content'>
                                                                                                                    {
                                                                                                                        item.attachments.map((i: any) => (
                                                                                                                            // @ts-ignore
                                                                                                                            <ReactZmage key={i.id} backdrop='rgba(0,0,0,0.7)' src={i.url} />
                                                                                                                        ))
                                                                                                                    }
                                                                                                                </div> : <div className='u-t-r-content'>
                                                                                                                    <div className='empty-content'>
                                                                                                                        <img src={EMPTY_IMG} alt="" />
                                                                                                                        还未上传执行结果~
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                        }

                                                                                                    </div>
                                                                                                ))
                                                                                            }
                                                                                        </>
                                                                                        : <div className='f-12 color-c5c8cd'>未选择上传执行结果类型</div>
                                                                                }
                                                                            </>


                                                                        }


                                                                    </>)
                                                                }}
                                                            </Form.List>


                                                        </>
                                                        : null
                                                }
                                            }

                                        </Form.Item>

                                        <BdwFormItem className='fold-unfold' noStyle name={[field.name, 'foldFlag']}>
                                            <FoldPanel></FoldPanel>
                                        </BdwFormItem>
                                    </div>

                                })}
                                {
                                    edit && <div className='continue-add-standard' onClick={() => add()}>
                                        <img style={{ width: '25px', height: '25px' }} src={Add} alt="" /> 继续添加标准是事项
                                    </div>
                                }


                            </div>
                        )
                    }}
                </Form.List>
                <div className='split-dash-wrapper' />
                <div className='mb-10'>
                    <Checkbox checked={configFlag} onChange={(e) => { setConfigFlag(e.target.checked) }} style={{ color: '#171717' }}>设定执行结果确认方及人员</Checkbox>
                </div>
                <div className={`${configFlag ? 'show' : 'hidden'}`}>
                    {
                        edit ? <Form.List name="projectTaskUploadResultConfirms">
                            {(fields, { add, remove }) => {
                                return <div className='confirm-person-wrapper'>
                                    {fields.map((field, index) => (
                                        // @ts-ignore
                                        <div className='confirm-person-wrapper-container' key={field.key}>
                                            <BdwRow type='flex' className='choose-type-p-c'>
                                                <BdwFormItem name={[field.name, 'userType']} label="人员类型" className='mr-20'>
                                                    <Select
                                                        options={userTypeOption}
                                                        bordered={false}
                                                        style={{ width: 160 }}
                                                        onSelect={(e, r) => {
                                                            const data = cloneDeep(form.getFieldValue('projectTaskUploadResultConfirms'));
                                                            console.log(data, '选择人员类型变化')
                                                            data[index].confirmPersonnel = [];
                                                            form.setFieldValue('projectTaskUploadResultConfirms', data);
                                                        }}
                                                        placeholder="选择人员类型"
                                                    ></Select>
                                                </BdwFormItem>
                                                <Form.Item shouldUpdate noStyle>
                                                    {
                                                        ({ getFieldValue }) => {
                                                            const newData = cloneDeep(getFieldValue('standardReferenceDetails'));
                                                            const s: any[] = [];
                                                            newData.forEach((item: any) => {
                                                                item.uploadTypes?.forEach((i: any) => { if (i?.typeId) { s.push(i?.typeId) } });
                                                            })
                                                            const cROptions = typeOptions.filter((item: any) => {
                                                                return [...new Set(s)].includes(item.value);
                                                            })
                                                            if (cROptions.length > 1) {
                                                                const allId = cROptions.map((i: any) => i.value);
                                                                cROptions.unshift({
                                                                    label: '全部',
                                                                    value: allId.join(),
                                                                })

                                                            }


                                                            return <BdwFormItem name={[field.name, 'uploadTypes']} label="确认结果类型">
                                                                <SelectConfirmResultType
                                                                    options={cROptions}
                                                                    bordered={false}
                                                                    style={{ minWidth: 160 }}
                                                                    mode="multiple"
                                                                    placeholder="选择确认结果类型"
                                                                >

                                                                </SelectConfirmResultType>
                                                            </BdwFormItem>
                                                        }
                                                    }
                                                </Form.Item>

                                            </BdwRow>
                                            <Form.Item noStyle
                                                shouldUpdate={(p, c) => {
                                                    if (c.projectTaskUploadResultConfirms[index]) {
                                                        return (p.projectTaskUploadResultConfirms[index]?.userType != c.projectTaskUploadResultConfirms[index].userType) || (p.projectTaskUploadResultConfirms[index].confirmPersonnel != c.projectTaskUploadResultConfirms[index].confirmPersonnel)
                                                    }
                                                    return false
                                                }}
                                            >
                                                {
                                                    ({ getFieldValue }) => {
                                                        // if (!getFieldValue('projectTaskUploadResultConfirms')[index].userType) return null;
                                                        // @ts-ignore
                                                        if (getFieldValue('projectTaskUploadResultConfirms')[index]?.userType == ConfirmPersonType.EMPLOYEE) {
                                                            return <div className='choose-confirm-person-wrapper'>
                                                                <BdwFormItem className='con-no-mb select-confirm-person' name={[field.name, 'confirmPersonnel']} label="确认人员">
                                                                    <TreeSelectC
                                                                        treeNodeData={addressBookValue}
                                                                        handleOnTreeCheck={(data) => { console.log(data) }}>
                                                                    </TreeSelectC>
                                                                </BdwFormItem>

                                                            </div>
                                                        } else {
                                                            let sOptions: { label?: string, value: string }[] = [];
                                                            if (getFieldValue('projectTaskUploadResultConfirms')[index]?.userType == ConfirmPersonType.SUPPLIER) {
                                                                sOptions = supplierOption;
                                                            }
                                                            if (getFieldValue('projectTaskUploadResultConfirms')[index]?.userType == ConfirmPersonType.CUSTOMER) {
                                                                sOptions = customerOption
                                                            }
                                                            return <BdwFormItem className='con-no-mb' name={[field.name, 'confirmPersonnel']} label="确认人员">
                                                                <Select
                                                                    options={sOptions}
                                                                    bordered={false}
                                                                    // style={{ width: 160 }}
                                                                    onSelect={(e, r) => {
                                                                        if (getFieldValue('projectTaskUploadResultConfirms')[index]?.userType == ConfirmPersonType.CUSTOMER) {
                                                                            const confirmData = cloneDeep(getFieldValue('projectTaskUploadResultConfirms'));
                                                                            // @ts-ignore
                                                                            confirmData[index].userSubType = r.userSubType
                                                                            form.setFieldValue('projectTaskUploadResultConfirms', confirmData)
                                                                        }

                                                                    }}
                                                                    mode='multiple'
                                                                    placeholder="选择确认人员"
                                                                ></Select>
                                                            </BdwFormItem>
                                                        }
                                                    }
                                                }


                                            </Form.Item>



                                        </div>
                                    ))}
                                    {
                                        edit && <div className='continue-add-standard' onClick={() => add()}>
                                            <img style={{ width: '25px', height: '25px' }} src={Add} alt="" /> 添加确认方
                                        </div>
                                    }
                                </div>
                            }}
                        </Form.List> :


                            (editDetails?.projectTaskUploadResultConfirmVos && editDetails?.projectTaskUploadResultConfirmVos?.length > 0) ? editDetails?.projectTaskUploadResultConfirmVos.map((i: any, index:numner) => {
                                let confirmPersonalsName = '', uploadTypes = '';
                                if (i.confirmPersonals && i.confirmPersonals.length) {
                                    confirmPersonalsName = i.confirmPersonals.map((item: any) => item.value).join('、');
                                }
                                if (i.uploadTypes && i.uploadTypes.length) {
                                    uploadTypes = i.uploadTypes.map((item: any) => item.value).join('、');
                                }
                                return <div className='readonly-confirm' key={index}>
                                    <BdwRow type='flex'>
                                        <BdwRow type='flex' className='readonly-confirm-item'>
                                            <div className='c-label'>人员类型：</div>
                                            <div className='c-value'>{i.userTypeName}</div>
                                        </BdwRow>
                                        <BdwRow type='flex' className='readonly-confirm-item'>
                                            <div className='c-label'>确认结果类型：</div>
                                            <div className='c-value'>{uploadTypes}</div>
                                        </BdwRow>
                                    </BdwRow>
                                    <BdwRow type='flex' className='readonly-confirm-item'>
                                        <div className='c-label'>确认人员：</div>
                                        <div className='c-value'>{confirmPersonalsName}</div>
                                    </BdwRow>
                                </div>

                            }) : <div style={{ height: '160px', textAlign: 'center', fontSize: '12px', color: '#c5c8cd' }}>未设定执行结果确认方及人员</div>
                    }
                </div>
                {!configFlag && <div style={{ height: '160px' }}></div>}



            </Form>
            {
                // 是否可编辑
                // !disabledFlag(functionality, TaskFunctionCode.EDIT_RESULT_EVALUATION_CRITERIA) ?
                <BdwRow className='basic-information-btn'>
                    {
                        edit ? <><BdwButton style={{ background: '#2b6bff' }} mr='10px' type='primary' onClick={submit} >
                            保存
                        </BdwButton>
                            <BdwButton mr='10px' onClick={() => {
                                form.setFieldsValue(formData);
                                setEdit(false);
                                form.setFields([
                                    {
                                        name:"imageAttachmentIds",
                                        errors: []
                                    },
                                    {
                                        name:"interpretation",
                                        errors: []
                                    }
                                ])
                            }}>
                                取消
                            </BdwButton></> : 
                            (!disabledFlag(functionality, TaskFunctionCode.EDIT_RESULT_EVALUATION_CRITERIA) ?<BdwButton width='136px' style={{ background: '#2b6bff' }} mr='10px' type='primary' onClick={() => setEdit(true)} >
                            编辑标准参考模板
                        </BdwButton>:null)
                       
                            
                    }
                </BdwRow>
                // : null
            }

        </div>
    )
}
export default StandardReference