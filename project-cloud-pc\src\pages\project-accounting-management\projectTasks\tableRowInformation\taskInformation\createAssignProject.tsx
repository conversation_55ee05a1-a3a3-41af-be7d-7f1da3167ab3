/**
 * @description 任务信息
 * <AUTHOR>
 * @date 2023-11-07 11:51:00
*/
import React, { useEffect, useMemo, useRef, useState } from 'react';
import type { MutableRefObject } from 'react';
import { Form, Input, Divider, Button, Checkbox, DatePicker, InputNumber, Popconfirm, Select, message, Modal, Radio, Space } from 'antd';
import styled from 'styled-components';
import { BdwRow, BdwChooseCompanyStaff, BdwUploadBtn, BdwButton, EditableContent, BdwReadonlySpan, BdwLeaveMessage, BdwLeaveMessageReadonly, BdwFileShow, BdwRichText } from '@/components';
import { CloseOutlined, NotificationOutlined, PlusOutlined } from "@ant-design/icons/lib";
import { listTaskLeaderInfo } from '@/service/projectDos/my-project-detail/projectTasks';
import { useParams, useSelector, useDispatch } from 'umi';
import {
    editProjectTasksApi,
    calcWorkDays,
    getTaskInfo,
    listPositionSelectOptionsApi,
    listDirectoryProcessApi,
    listMaterialSupplyApi,
    listUserTypeVisibilityApi
} from '@/service/projectDos/my-project-detail/projectTasks';
import { listEmpByParams } from '@/service/projectDos/commonApi';
import AssociatedTaskModal from "../../component/accociated-task-modal";
import ProgressReport from '../progressReport';
import moment from 'moment';
import { useBoolean, useRequest, useUpdateEffect } from 'ahooks';
import { hourAndMinuteList, workTimeAdjustOption } from './hourAndMinuteList';
import { TaskFunctionCode } from '@/constants/Enum';
import { disabledFlag } from '@/pages/my-project-detail/projectTasks';
import './index.less';
import { cloneDeep, isString, isArray, isObject, rest } from 'lodash';
import ReactZmage from "react-zmage";
import type { defaultTimeAdjustType } from '@/type';
// @ts-ignore
import COPY_PRODUCT_PRICE_INFO from '@/assets/image/COPY_PRODUCT_PRICE_INFO.svg';
import { getRichImgSrc, getText, getLocalStorage,findDataById } from '@/utils/utils';
// @ts-ignore
import EMPTY_IMG from '@/assets/image/empty.png';

const { Item } = Form;
const { confirm } = Modal;
const ImgIcon = styled.img`
 width:18px;
 height:18px;
 margin-right:10px;
`;

interface WrapComponentProps {
    labelname?: string
    wrapclassname?: string
}



export interface TaskRemarkFormItem {
    value: "",
    fileList: string[]
}

export const taskRemarkRules = {
    "item": [
        () => ({
            validator: async (rule: any, value: TaskRemarkFormItem) => {
                if ((value.value && value.value !== '')) {
                    return Promise.resolve()
                }
                return Promise.reject(new Error('该项不能为空'));
            }
        })
    ],

}

const WrapComponent = <T extends {}>(WrappedComponent: React.FC<T>) => {
    const ContentComponent = (props: WrapComponentProps & T) => {
        const { labelname, wrapclassname = '' } = props;
        return (
            <BdwRow type='flex' className={`${wrapclassname} bdw-form-item-container`}>
                {
                    labelname && <div className='form-label'>
                        {labelname}
                    </div>
                }
                <WrappedComponent
                    className='label-reset'  {...props as T}
                />
            </BdwRow>
        )
    }
    return ContentComponent;
}
const BdwFormItem = WrapComponent(Item);
export interface TaskInformationProps{
    editable: boolean,
    saveTask: (data: any) => void
    editChange: (edit: boolean) => void
    renewTaskInfo: (data: any) => void,
    taskInfo: any
    readonly?: boolean
    onClose?: () => void,
    taskLists:any,
    isAddTask?: boolean,
}




const PeriodEditableContent = <T extends { editable: boolean, onChange: (data: any) => void, value: any }>(props: T) => {
    const { editable, onChange, value } = props;
    return <EditableContent
        editable={editable}
        renderShow={value && <BdwReadonlySpan>{value}天</BdwReadonlySpan> ||
            <span className="back-show-info">未填写</span>}
        renderEditor={
            <span className="flex execution-cycle-setting border-bottom-c0">
                <InputNumber
                    step={0.1}
                    placeholder='输入执行周期天数'
                    maxLength={3}
                    min={0}
                    value={value}
                    style={{
                        width: "100%",
                        fontSize: '14px',
                        border: "none !important",
                        outline: "none !important",
                    }}
                    bordered={false}
                    onChange={onChange}
                />
            </span>}
    />
}
const TaskInformation: React.FC<TaskInformationProps> = (props) => {
    const dispatch = useDispatch();
    const scrollRef = useRef();
    const [form] = Form.useForm();
    const {  submitStatus, saveStatus, showEmList } = useSelector((state: any) => state.projectTasks);
    const { editable,saveTask,editChange,renewTaskInfo,taskInfo,readonly,taskLists,isAddTask,onClose} = props;
    const [beforeEditData,setBeforeEditData] = useState<any>(null);
    let remark = taskInfo?.taskExplain;
    const showHasSaveTaskRemark = remark && isArray(remark) && remark?.map((item: any, index: number) => {
        const detailsArr = getRichImgSrc(item.value);
        return <div key={index}>
            <div className='detail-info-word'>{getText(item.value)}</div>
            {
                detailsArr.length > 0 && detailsArr.map((i: any, index: number) => (
                    // @ts-ignore
                    <ReactZmage key={index} backdrop='rgba(0,0,0,0.7)' src={i} />
                ))
            }
        </div>
    })
    useEffect(() => {
        if (taskInfo) {
            const formValue = {
                taskExplain: taskInfo?.taskExplain || [],
                document: [],
            }
            form.setFieldsValue(formValue);
        }
    }, [taskInfo?.taskId])


    const submit = (data: any, type?: boolean, callback?: () => void) => {
        if(!taskInfo?.ratio){
            message.warn('请填写同级占比！');
            return
        }
        const beforeDocument = (taskInfo?.beforeDocument && taskInfo?.beforeDocument.length) ? taskInfo?.beforeDocument?.map((item: any) => item.id) : [];
        const document = [...data.document?.map((item: any) => item.id), ...beforeDocument].join();
        let taskExplain = data.taskExplain.length >= 1 ? JSON.stringify(data.taskExplain) : "";
        let reallyBeforeDocument = [...data.document];
        if(taskInfo?.beforeDocument && taskInfo?.beforeDocument.length){
            reallyBeforeDocument = [...reallyBeforeDocument,...taskInfo?.beforeDocument]
        }
        const params = {
            name: taskInfo?.name,
            leaderId: taskInfo?.leaderId,
            parentId: taskInfo?.parentId,
            taskId: taskInfo?.taskId == "newAdd" ? null : taskInfo?.taskId,
            ...data,
            taskExplain: data.taskExplain,
            document: document ? document : null,//上传的文件id
            beforeDocument:reallyBeforeDocument,//存储上传的文件信息
            reallyTaskExplain:taskExplain,//保存项目需要传的真实的任务说明
        }
        setBeforeEditData(null);
        saveTask(params);
    }
    useUpdateEffect(() => {
        form.validateFields().then((values) => {
            submit(values, true)
        })
    }, [submitStatus])
    useUpdateEffect(() => {
        form.validateFields().then((values) => {
            submit(values)
        })
    }, [saveStatus])
    //获取通知栏title内容
    // const getTaskInformationTitle = useMemo(() => {
    //     let title: string = '';
    //     if (taskInfo?.taskId && !isAddTask) {
    //         if (taskInfo?.parentTitle) {
    //             title = `你正在编辑：任务【${taskInfo?.parentTitle}】的子级任务`
    //         } else {
    //             title = `你正在编辑一级任务【${taskInfo?.name}】`
    //         }
    //     } else {
    //         if (taskInfo?.parentTitle && taskInfo?.parentId) {
    //             title = `你正在新建：任务【${taskInfo?.parentTitle}】的子级任务`
    //         } else {
    //             title = `你正在新增一级任务`
    //         }
    //     }

    //     return <span className='f-12 color-5c'>{title}</span>;
    // }, [taskInfo])

    //保存表单数据
    const saveForm = () => {
        form.validateFields().then((data: any) => {
            submit(data);
        })
    }
    return (
        <div className='basic-information-container-accounting'>
            {/* @ts-ignore */}
            <div ref={scrollRef} style={{ height: '100%', overflow: 'auto', transition: 'all .3s' }}>
                {/* {
                    editable && <BdwRow type='flex-center' className='mb-10 px-16'>
                        <NotificationOutlined style={{ color: '#5c5c5c', marginRight: '16px' }} />
                        {getTaskInformationTitle}
                    </BdwRow>
                } */}

                <Form form={form} initialValues={{ document: [], taskExplain: [],  }}>
                    {/* 任务名称-任务说明 */}
                    <div className='padding-custom pt-0'>
                        <BdwRow type='flex' className='mb-10'>
                            <div className='form-label'>业务名称</div>
                            <EditableContent
                                editable={editable}
                                renderShow={<div className='form-content'>{taskInfo?.name}</div>}
                                renderEditor={
                                    <Input
                                        bordered={false}
                                        className='input-style'
                                        style={{ flex: 1 }}
                                        placeholder='标题限制输入64个字符(必填项)'
                                        value={taskInfo?.name}
                                        onChange={(e) => {
                                            renewTaskInfo({
                                                name: e.target.value
                                            })
                                        }}
                                    />


                                }
                            />

                        </BdwRow>
                        <BdwRow type='flex' className='mb-10'>
                            <div className='form-label'>同级占比</div>
                            <EditableContent
                                editable={editable}
                                renderShow={taskInfo?.ratio?<div className='form-content'>{taskInfo?.ratio}</div>:<div className='back-show-info'>未填写同级占比</div>}
                                renderEditor={
                                    <InputNumber
                                        bordered={false}
                                        className='ratio-style'
                                        style={{ flex: 1 }}
                                        placeholder='输入同级占比'
                                        value={taskInfo?.ratio}
                                        formatter={value => `${value ? (value + '%') : ''}`}
                                        parser={value => value!.replace('%', '')}
                                        onChange={(e) => {
                                            let num = 0;
                                            if(taskInfo.parent){
                                                taskInfo.parent.children.forEach((item: any) => {
                                                    if(item.ratio && item.taskId != taskInfo.taskId){
                                                        num += Number(item.ratio);
                                                    }
                                                    
                                                })
                                            }else{
                                                taskLists.forEach((item: any) => {
                                                    if(item.ratio  && item.taskId != taskInfo.taskId){
                                                        num += Number(item.ratio);
                                                    }
                                                    
                                                })
                                              
                                            }
                                            if((num + e)>100){
                                                message.warn('同级占比总和不能大于100%！')
                                                renewTaskInfo({
                                                    ratio: 0
                                                })
                                                return
                                            }
                                            renewTaskInfo({
                                                ratio: e
                                            })
                                        }}
                                    />
                                }
                            />

                        </BdwRow>


                        <BdwRow type='flex-between' className='mb-10 zx-fz-wrapper'>
                            <BdwRow type="flex" className={['width-100-person', showEmList ? 'h-500-person' : ''].join(' ')}>
                                <div className='form-label'>负责人员</div>
                                <EditableContent
                                    editable={editable}
                                    renderShow={taskInfo?.leaderName ? <div className='form-content'>{taskInfo?.leaderName}</div> : <div className='back-show-info'>未选择任务负责人</div>}
                                    renderEditor={
                                        <div className='flex-1 choose-responsible-person'>
                                            <BdwChooseCompanyStaff
                                                placeholder="姓名关键字检索"
                                                apiSrc={listEmpByParams}
                                                value={{ id: taskInfo?.leaderId, name: taskInfo?.leaderName }}
                                                showColumns={['name']}
                                                onChange={(e: any) => {
                                                    renewTaskInfo({
                                                        leaderId: e.id,
                                                        leaderName: e.name
                                                    })
                                                    
                                                }}
                                            />
                                        </div>
                                    }
                                />
                            </BdwRow>

                        </BdwRow>
                        <BdwRow type='flex' className='mb-10'>
                            <div className='form-label'>业务事项说明</div>
                            <div className='project-information-add'>
                                {
                                    editable ? <Form.List name='taskExplain'>
                                        {(fields, { add, remove }) => {
                                            return (
                                                <div>
                                                    {fields.map((field, index) => (
                                                        <div key={field.key} className="position-parent-relative">
                                                            <BdwFormItem {...field} validateTrigger="onChange" initialValue={{ value: '', fileList: [] }} rules={taskRemarkRules.item}>
                                                                <BdwRichText
                                                                    hideTool
                                                                    sx={{ width: '100%', minHeight: '120px' }}
                                                                    placeholder='填写任务说明(可直接粘贴图片)'

                                                                >

                                                                </BdwRichText>
                                                            </BdwFormItem>
                                                            {fields?.length >= 1 ? (
                                                                <Popconfirm
                                                                    title="确认删除此输入域？"
                                                                    onConfirm={() => remove(field.name)}
                                                                    okText="确认"
                                                                    cancelText="取消"
                                                                >
                                                                    <CloseOutlined key={index} className="delete-has-upload-textarea-btn" title="删除" />
                                                                </Popconfirm>
                                                            ) : null}
                                                        </div>
                                                    ))}
                                                    <div style={{ height: '28px', display: 'flex', alignItems: 'center', color: '#0275d8', cursor: 'pointer', fontSize: '12px' }}  ><div onClick={add}><PlusOutlined className='f-12' style={{ marginRight: '8px' }} />添加</div></div>


                                                </div>
                                            )
                                        }}
                                    </Form.List> : <div>{isArray(remark) && remark?.length !== 0 ? showHasSaveTaskRemark : <span className='back-show-info'>暂无任务说明</span>}</div>
                                }
                            </div>

                        </BdwRow>
                        {/* 相关资料 */}
                        <div className="">
                            <EditableContent
                                editable={editable}
                                renderShow={<BdwRow type='flex-center'>
                                    <div className='form-label'>相关资料</div>

                                </BdwRow>}
                                renderEditor={
                                    <BdwFormItem labelname="相关资料" name='document' wrapclassname='upload-btn-w'
                                    >
                                        <BdwUploadBtn name="本地上传" mt='0px' color='#0275d8' fontSize='12px' />
                                    </BdwFormItem>
                                }
                            />

                            {
                                taskInfo?.beforeDocument?.length >= 1 &&
                                <div style={{ paddingLeft: '68px' }}>
                                    <BdwFileShow  attachments={taskInfo?.beforeDocument} onChange={(data: any) => {
                                             renewTaskInfo({
                                                beforeDocument: data,
                                            })
                                    }} enableEdit={editable} />
                                </div>

                            }
                            <Form.Item noStyle
                                shouldUpdate={(p, c) => p.document != c.document}
                            >
                                {({ getFieldValue }) => {
                                    return (getFieldValue('document')?.length >= 1 || taskInfo?.beforeDocument?.length >= 1) ? null :
                                        <div className='no-files-empty'>
                                            <img src={EMPTY_IMG} alt="" />
                                            还没有上传资料
                                        </div>
                                }}
                            </Form.Item>
                        </div>
                    </div>

                </Form>
            </div>


            {
                !readonly && <BdwRow className='basic-information-btn-1'>
                {
                     editable ? <>
                        <BdwButton mr='10px' type='primary' onClick={saveForm} >
                            保存
                        </BdwButton>
                        <BdwButton mr='10px' onClick={() => {
                            if(isAddTask){
                                onClose?.()
                            }else{
                                editChange(false);
                                if(beforeEditData){
                                    renewTaskInfo(beforeEditData);
                                }
                                setBeforeEditData(null);
                            }
 
                        }}>
                            取消
                        </BdwButton>
                    </> :
                        <BdwButton type='primary' mr='10px'  onClick={() => {
                            setBeforeEditData(cloneDeep(taskInfo));
                            editChange(true)
                        }}>
                            编辑
                        </BdwButton>
                }

            </BdwRow>
            }
            
        </div>
    )
}
export default TaskInformation