/**
 * @description 团队任务
 * <AUTHOR>
 * @date 2023-10-31 17:46:54
*/
import React from 'react';
// @ts-ignore
import { history,useParams } from 'umi';
import TeamTask from '@/pages/teamTask';
import './index.less';

const OperationButtons: React.FC = () => {
    const { projectId } = useParams<{ projectId: string }>();
    return (
        <TeamTask type="project" projectId={projectId}/>

    )
};

export default OperationButtons;