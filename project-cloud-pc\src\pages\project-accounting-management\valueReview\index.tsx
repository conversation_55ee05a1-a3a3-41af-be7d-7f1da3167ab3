/**
 * @description 价值评审
 * <AUTHOR>
 * @date 2023-10-31 17:47:35
*/
import React, { useEffect } from 'react';
import { BdwRow, BdwCommonIcon } from '@/components';
import { Divider, message } from 'antd';
import TaskProcessDrawer from './task-process-drawer';
import ProjectValueAssessmentSettingDrawer from './projectValueAssessmentSettingDrawer';
import ProjectMemberValueAssessmentSettingDrawer from './projectMemberValueAssessmentSettingDrawer';
import VersionRecordDrawer from './versionRecordDrawer';
import { DividerLine } from '../projectTasks';
import ScoreTable from './scoreTable';
import { useBoolean } from 'ahooks';
import { useDispatch, useParams, useSelector } from 'umi';
import {getLocalStorage} from "@/utils/utils";
import type {successFunType} from './versionRecordDrawer'
import './index.less';




const ValueReview: React.FC = () => {
    const { projectId } = useParams<{ projectId: string }>();
    const [taskProcessVisible, { setFalse: taskProcessHide, setTrue: taskProcessShow }] = useBoolean(false);//任务历程
    const [projectScoreSettingVisible, { setFalse: projectScoreHide, setTrue: projectScoreShow }] = useBoolean(false);//评分设定
    const [projectScoreVersionVisible, { setFalse: projectScoreVersionHide, setTrue: projectScoreVersionShow }] = useBoolean(false);//版本记录
    const { taskInfo, projectScoreVersionList } = useSelector((state: any) => state.valueReview);
    const { basicProjectInfo } = useSelector((state: any) => state.projectTasks);
    const { leaderInfo } = basicProjectInfo
    const { userInfo } = useSelector((state: any) => state.commonTask);//当前项目登录人信息
    const dispatch = useDispatch();
    const onRefresh = (callback?: () => void) => {
        dispatch({
            type: 'valueReview/fetchCurrentProjectScoreList',
            payload: projectId,
            callback
        })
    }
    useEffect(() => {
        onRefresh();
    }, [])
    //版本记录选用 查看详情
    const successFun = (type: successFunType) => {
        if(type == 'chooseNew'){
            onRefresh();
        }
        projectScoreVersionHide();
    }
    //关闭弹窗刷新页面
    const refreshTable = () => {
        onRefresh();
        projectScoreHide();
    }
    return (
        <div className='value-review-container'>
            <BdwRow type='flex-center' className='btn-value-review-wrapper' >
                <BdwCommonIcon
                    name='返回'
                    icon='iconback'
                    className="mr-10 ml-10"
                />
                <BdwCommonIcon
                    name='刷新'
                    icon='iconrefresh'
                    className="mr-10"
                    onClick={()=>{onRefresh(()=>{message.success('刷新成功')})}}
                />
                <DividerLine />
                <BdwCommonIcon
                    name='评分设定'
                    icon='icontask-paren'
                    className="mr-10"
                    onClick={() => projectScoreShow()}
                />
                <DividerLine />
                <BdwCommonIcon
                    name='评分版本'
                    icon='icontask-paren'
                    className="mr-10"
                    onClick={() => projectScoreVersionShow()}
                    disabled={projectScoreVersionList?.length == 0}
                />
                <DividerLine />
                <BdwCommonIcon
                    name='任务历程'
                    icon='icontask-paren'
                    className="mr-10"
                    onClick={() => taskProcessShow()}
                    disabled={!taskInfo}
                />
            </BdwRow>
            <ScoreTable />
            {/* 任务历程 */}
            {
                taskProcessVisible && taskInfo &&
                <TaskProcessDrawer visible={taskProcessVisible} task={taskInfo} cancelEvent={() => taskProcessHide()} />
            }
            {/* 评分设定 */}
            {
                // (Number(leaderInfo?.leaderId) === Number(userInfo.userId)) ?
                <ProjectValueAssessmentSettingDrawer
                    successEvent={() => refreshTable()}
                    visible={projectScoreSettingVisible}
                    setVisibleFun={projectScoreHide}
                    cancelEvent={() => refreshTable()} />
                // :<ProjectMemberValueAssessmentSettingDrawer
                //     visible={projectScoreSettingVisible}
                //     setVisibleFun={projectScoreHide}
                //     successEvent={() => projectScoreHide()}
                //     cancelEvent={() => projectScoreHide()} />
            }
            {/* 评分版本记录 */}
            {
                projectScoreVersionVisible &&
                <VersionRecordDrawer
                    visible={projectScoreVersionVisible}
                    cancelEvent={() => projectScoreVersionHide()}
                    successFun={successFun}
                />
            }
        </div>
    )
}
export default ValueReview;