import React, { useMemo, useState } from "react";
import { Button } from "antd";
import OtherLevelValueAssessmentSetting
  from "../otherLevelValueAssessmentSetting";
import { BdwTreeSelect } from "@/components";
import { useSelector } from 'umi';


interface ProjectValueAssessmentSettingSecondProps {
  setSuccessEvent?: () => void
  needSkipButton?: boolean
  skipSettingEvent?: () => void
}
const ProjectValueAssessmentSettingSecond: React.FC<ProjectValueAssessmentSettingSecondProps> = (props) => {
  const [showTipTitleFlag, setShowTipTitleFlag] = useState(true);
  const { projectScoreList } = useSelector((state: any) => state.valueReview);
  const [currentTaskTitleValue, setCurrentTaskTitleValue] = useState<string>("");
  const { setSuccessEvent, needSkipButton = true, skipSettingEvent } = props;

  const skipSetting = () => {
    skipSettingEvent?.();
  }
  const setSuccess = () => {
    setSuccessEvent?.();
  }

  const startSetting = () => {
    setShowTipTitleFlag(false)
  }


  return (
    <div className='project-value-assessment-setting-second'>
      <div className='project-value-assessment-setting-second-introduce'>
        <div className='light-tips-title mb-16'>
          <span className='font-bolder'>分值设定方法：</span>
          <span>选择一项上级任务，通过设置子级任务的评分百分占比，快捷设置子项评分。</span>
        </div>
        {
          showTipTitleFlag && needSkipButton &&
          <div className='project-value-assessment-setting-second-introduce'>
            <div className='introduce-buttons mb-16'>
              <Button type='primary' onClick={startSetting}>开始设置</Button>
            </div>
          </div>
        }
        {
          (!showTipTitleFlag || !needSkipButton) &&
          <div className='project-value-assessment-setting-content mb-16'>
            <OtherLevelValueAssessmentSetting needSkipButton={needSkipButton} skipSetting={() => skipSetting?.()} setSuccess={() => setSuccess?.()} />
          </div>
        }
        <div className='dark-tips-title'>
          <span className='font-bolder'>引导说明：</span>
          子项任务分值可由任务执行人评估设定，如当前不设置可
          <span className="skip-setting-text" onClick={skipSetting}>【跳过】</span>
          , 系统会通知执行人知晓
        </div>
      </div>
    </div>
  )
}

export default ProjectValueAssessmentSettingSecond

