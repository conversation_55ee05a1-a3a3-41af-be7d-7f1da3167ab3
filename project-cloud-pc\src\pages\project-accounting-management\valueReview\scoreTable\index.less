@import '../../../../styles/base.less';
.score-table-container{
    border-top: 1px solid @divider;
    width: 100%;
    height: calc(100% - 70px);
    .table-details{
        height: 100%;
        .p-16()
    }
    .table-details-w{
      width: calc(100% - 480px);
    }
    .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before{
      background-color: unset;
    }
    .project-value-assessment-table {
        .project-task-table {
          // height: 600px;
          // overflow-y: auto;
          
          .task-index-num {
            margin-right: 0.8em;
            font-weight: bold;
          }
        }
        .ant-table-row-level-0{
          font-weight: 800;
        }
      }
      .no-border-input-1{
        background: white!important;
      }
      
}