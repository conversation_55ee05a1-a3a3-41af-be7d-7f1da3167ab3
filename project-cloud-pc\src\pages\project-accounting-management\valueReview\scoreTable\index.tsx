/**
 * @description 价值评分table
 * <AUTHOR>
 * @date 2023-11-16 17:12:11
*/
import React from 'react';
import type { Key } from 'react';
import TableRowInformation from '../tableRowInformation';
import { BdwTable, BdwTableHeaderSearchComponent, BdwRow} from "@/components";
import Highlighter from "react-highlight-words";
import {getTaskInfo} from '@/service/projectDos/my-project-detail/projectTasks';
import { useParams, useDispatch, useSelector } from 'umi';
import { Input } from 'antd';
import styled from "styled-components";
import './index.less';
const IndexSpan = styled.span`
 font-weight: bold;
 font-size: 13px;
 margin-right: 10px;
`;
const NotSupport = styled.div`
  cursor: not-allowed;
  width: 100%;
  height: 100%;
`



const ScoreTable: React.FC = () => {
    const dispatch = useDispatch();
    const { projectScoreList, expandedRowKeys,taskInfo,filter,reviewTaskId } = useSelector((state: any) => state.valueReview);

    const TaskTableColumns = [
        {
            title: <BdwTableHeaderSearchComponent title="任务">
                <Input placeholder="请输入任务名称进行检索"
                    className='no-border-input ant-input-cover-style no-border-input-1'
                    value={filter?.name}
                    maxLength={128}
                    onChange={(e) => {
                        dispatch({
                            type: 'valueReview/setFilterTableData',
                            typeKey: e.target.value,
                            typeName: "name",
                            status: e.target.value
                        })
                    }} 
                />
            </BdwTableHeaderSearchComponent>,
            dataIndex: 'name',
            render: function TitleColumn(value: string, record: any) {
                const indexSpan = <span className='task-index-num'><IndexSpan>{record.index?.map((it: any) => it + 1).join('.')}</IndexSpan></span>;
                const renderShow = <span className='task-name-show'>
                    <Highlighter
                        highlightClassName="title-highlight"
                        searchWords={[filter?.name]}
                        autoEscape
                        textToHighlight={record.name ?? ""}
                    />
                </span>
                return (
                    <BdwRow type='flex'>
                        <div>{indexSpan}</div>
                        <div>{renderShow}</div>
                    </BdwRow>
                )
            },
        },
        {
            title: <BdwTableHeaderSearchComponent title="任务分值"><NotSupport /></BdwTableHeaderSearchComponent>,
            dataIndex: 'taskScore',
            width: 150,
            render: function standardScoreColumn(value: Key, record: any): any {
                return <div className='f-weight'>{record.taskScore ? record.taskScore : "-"}</div>
            }
        }, {
            title: <BdwTableHeaderSearchComponent title="分组占比"><NotSupport /></BdwTableHeaderSearchComponent>,
            dataIndex: 'groupProportion',
            width: 150,
            render: function scoreRatioColumn(value: Key, record: any): any {
                const finallyShow = record.groupProportion ? record.groupProportion + "%" : "-";
                return (
                    <div className='f-weight'>{finallyShow}</div>
                )

            }
        },
        {
            title: <BdwTableHeaderSearchComponent title="总计占比"><NotSupport /></BdwTableHeaderSearchComponent>,
            dataIndex: 'totalProportion',
            width: 150,
            render: (value: Key, record: any) => {
                return value?<div className='f-weight'>{value}</div>:<div>-</div>
            }
        },
    ];
    return (
        <BdwRow type="flex" className='score-table-container'>
            <div className={`table-details ${taskInfo?.taskId ? 'table-details-w' : ''}`}>
                <BdwTable
                    className='project-task-table'
                    pagination={false}
                    expandable={{
                        defaultExpandAllRows: true,
                        expandedRowKeys,
                        onExpandedRowsChange: (e) => {
                            dispatch({
                                type: 'valueReview/setExpandedRowKeys',
                                payload: e
                            })
                        },
                    }}
                    sticky
                    // scroll={{ x: true }}
                    size="small"
                    rowKey="taskId"
                    // @ts-ignore
                    columns={TaskTableColumns}
                    dataSource={projectScoreList ?? []}
                    showPages={false}
                    useLocalData
                    rowSelection={{
                        selectedRowKeys: reviewTaskId && [reviewTaskId] || [],
                        type: 'radio',
                        columnWidth: 1,
                        renderCell: () => {
                            return null;
                        },
                        checkStrictly: true,
                    }}

                // @ts-ignore
                onRow={(task: any) => ({
                    onClick() {
                        dispatch({
                            type: 'valueReview/setReviewTaskId',
                            payload: task.taskId
                        })
                        getTaskInfo(task.taskId).then((res: any)=>{
                            dispatch({
                                type: 'valueReview/setTaskInfo',
                                payload: {...task,...res,taskExplain: res.taskExplain?JSON.parse(res.taskExplain):null}
                            })
                        })
                    },
                })}
                />
            </div>
            {
                taskInfo?.taskId &&  <TableRowInformation />
            }
        </BdwRow>
    )
}
export default ScoreTable