/**
 * @description 相关资料
 * <AUTHOR>
 * @date 2023-11-17 11:37:29
*/
import React from "react";
import { BdwFileShow } from "@/components";
import { Empty } from "antd";

interface RelateDataInfoProps {
    task: any
}

const Document: React.FC<RelateDataInfoProps> = ({ task }) => {
    const fileIds = task?.document;
    return (
        <div className="task-relate-data-info mt-16">
            {fileIds.length > 0 && <BdwFileShow attachments={fileIds} />}
            {fileIds.length === 0 && <Empty description="无任务相关资料" />}
        </div>
    )
}

export default Document
