/**
 * @description 评价标准
 * <AUTHOR>
 * @date 2023-11-17 11:37:52
*/
import React,{useEffect, useMemo} from "react";
import EvaluationCriterionReadonly from "@/pages/my-project-detail/projectTasks/tableRowInformation/resultEvaluation/component/evaluation-criterion-readonly";
import { SelectSubmitEvaluationMaterials} from '@/service/projectDos/my-project-detail/projectTasks';
import { useRequest } from 'ahooks';


export interface EvaluationCriterionInfoProps {
    task: any
}

const EvaluationCriterionInfo: React.FC<EvaluationCriterionInfoProps> = ({ task }) => {
    const taskId = task?.taskId;
    // 需要获取历史已经保存的数据
    const { data: historyData, loading,run:runHistoryData } = useRequest(() => SelectSubmitEvaluationMaterials(taskId), {
        manual:true
    })
    useEffect(()=>{
        if(taskId && taskId != 'newAdd'){
            runHistoryData();
        }
    },[taskId])
    // 将获取到的已保存的数据处理
    const handleHistoryData: any = useMemo(() => {
        if (historyData) {
            return historyData;
        }
        return null
    }, [historyData])
    return (
        <div className='evaluation-criterion-readonly-info'>
            {taskId && <EvaluationCriterionReadonly  taskId={taskId} handleHistoryData={handleHistoryData} />}
        </div>
    )
}

export default EvaluationCriterionInfo
