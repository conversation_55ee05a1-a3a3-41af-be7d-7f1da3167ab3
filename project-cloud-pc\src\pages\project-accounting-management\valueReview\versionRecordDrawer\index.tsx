/**
 * @description 版本记录
 * <AUTHOR>
 * @date 2023-11-21 10:50:03
*/
import React, { useState } from 'react';
import type { Key } from 'react';
import { BdwTable, BdwTableHeaderSearchComponent, BdwRow, BdwIcon, BdwTableButton, EditableContent } from "@/components";
import { Drawer, Button, Input, Divider, Modal, message } from 'antd';
import { useSelector, useDispatch,useParams } from 'umi';
import { chooseByVersionId, deleteVersion } from '@/service/projectDos/my-project-detail/valueReview';
import Highlighter from "react-highlight-words";
import { scoreVersionRecord } from '@/type'
import { useBoolean } from 'ahooks';
import styled from "styled-components";

import './index.less';
import { cloneDeep } from 'lodash';
const NotSupport = styled.div`
  cursor: not-allowed;
  width: 100%;
  height: 100%;
`
export type successFunType = 'view' | 'chooseNew';
interface VersionRecordDrawerProps {
    visible: boolean
    cancelEvent: () => void
    successFun: (type: successFunType) => void
}


const VersionRecordDrawer: React.FC<VersionRecordDrawerProps> = (props) => {
    const dispatch = useDispatch();
    const { visible, cancelEvent, successFun } = props;
    const { projectId } = useParams<{ projectId: string }>();
    const { taskInfo, projectScoreVersionList, versionFilter } = useSelector((state: any) => state.valueReview);
    const [currentRecord, setCurrentRecord] = useState<scoreVersionRecord | null>(null);

    //查看版本详情
    const viewDetail = async (record: scoreVersionRecord) => {
        await dispatch({
            type: 'valueReview/fetchProjectScore',
            payload: record.versionId
        })
        await dispatch({
            type: 'valueReview/setCurrentVersionInfo',
            payload: {
                versionId: record.versionId
            }
        })
        successFun('view');
    }
    //删除版本
    const deleteVersionRecord = (record: scoreVersionRecord, index: number) => {

        Modal.confirm({
            title: '系统提示',
            icon: <></>,
            content: '确认删除该版本记录吗?',
            okText: "确认",
            cancelText: "取消",
            onOk: async () => {
                await deleteVersion(record.versionId).then((res) => {
                    dispatch({
                        type: 'valueReview/fetchCurrentProjectScoreList',
                        payload: projectId,
                    })
                })
                message.success('删除成功!')
            },
        })
    }
    const VersionTableColumns = [
        {
            title: <BdwTableHeaderSearchComponent title="版本名称">
                <Input placeholder="搜索版本名称..."
                    className='no-border-input ant-input-cover-style'
                    value={versionFilter?.versionName}
                    maxLength={128}
                    onChange={(e) => {
                        dispatch({
                            type: 'valueReview/setFilterVersionTableData',
                            typeKey: e.target.value,
                            typeName: "versionName",
                            status: e.target.value
                        })
                    }}
                />
            </BdwTableHeaderSearchComponent>,
            dataIndex: 'versionName',
            render: function TitleColumn(value: string, record: any) {
                const renderShow = <span className='task-name-show'>
                    <Highlighter
                        highlightClassName="title-highlight"
                        searchWords={[versionFilter?.versionName]}
                        autoEscape
                        textToHighlight={record.versionName ?? ""}
                    />
                </span>
                return (
                    <BdwRow type='flex'>
                        <div>{renderShow}</div>
                    </BdwRow>
                )
            },
        },
        {
            title: <BdwTableHeaderSearchComponent title="项目分值" />,
            dataIndex: 'projectScore',
            width: 150,
            render: function standardScoreColumn(value: Key, record: any): any {
                return <>{record.projectScore ? record.projectScore : "-"}</>
            }
        }, {
            title: <BdwTableHeaderSearchComponent title="创建时间" />,
            dataIndex: 'createTime',
            width: 150,
            render: function scoreRatioColumn(value: Key, record: any): any {
                return (
                    <>{record.createTime ? record.createTime : "-"}</>
                )

            }
        },
        {
            title: <BdwTableHeaderSearchComponent title="操作" />,
            dataIndex: 'totalProportion',
            width: 150,
            align: 'right',
            render: (value: Key, record: any) => {
                return <BdwRow type='flex-center' className='operate-btn-container'>
                    <div className='operate-btn' onClick={() => viewDetail(record)}>详情</div>
                    {
                        !record.choose && <>
                            <Divider type='vertical' style={{ borderColor: '#ddd' }} />
                            <div className='operate-btn' onClick={() => setCurrentRecord(record)}>选用</div>
                        </>
                    }
                    <Divider type='vertical' style={{ borderColor: '#ddd' }} />
                    <div className='operate-btn' onClick={() => deleteVersionRecord(record)}>删除</div>

                </BdwRow>
            },
        },
    ];
    //确认发布
    const confirmReLease = async () => {
        await chooseByVersionId(currentRecord!.versionId);
        await dispatch({
            type: 'valueReview/setCurrentVersionInfo',
            payload: { versionId: null, isNew: false }
        })
        message.success('启用成功!');
        setCurrentRecord(null);
        successFun('chooseNew');
    }
    return (
        <>
            <Drawer
                open={visible}
                width={750}
                title='价值评分版本记录'
                onClose={() => cancelEvent?.()}
                footer={
                    <div style={{ textAlign: 'right' }}>
                        <Button onClick={() => cancelEvent?.()}>取消</Button>
                    </div>
                }
                maskStyle={{ backgroundColor: "transparent" }}
                maskClosable={false}
                className='versionRecordContainer'
            >
                <BdwTable
                    className='version-record-table'
                    pagination={false}
                    sticky
                    scroll={{ x: true }}
                    size="small"
                    rowKey="versionId"
                    // @ts-ignore
                    columns={VersionTableColumns}
                    dataSource={projectScoreVersionList ?? []}
                    showPages={false}
                    useLocalData
                    rowSelection={{
                        selectedRowKeys: taskInfo?.taskId && [taskInfo.taskId] || [],
                        type: 'radio',
                        columnWidth: 1,
                        renderCell: () => {
                            return null;
                        },
                        checkStrictly: true,
                    }}

                    // @ts-ignore
                    onRow={(task: any) => ({
                        onClick() {

                        },
                    })}
                />
            </Drawer>
            <Modal
                title="评分版本发布确认"
                onOk={confirmReLease}
                open={Boolean(currentRecord)}
                onCancel={() => { setCurrentRecord(null) }}
                okText={'确认发送'}
                maskClosable={false}
                bodyStyle={{ height: '180px' }}
            >
                <div className='f-12 color-5c'>版本名称</div>
                <div className='f-14 mb-10'>{currentRecord?.versionName}</div>
                <div className='f-12 color-5c'>创建时间</div>
                <div className='f-14 mb-10' >{currentRecord?.createTime}</div>
                <div className='f-14 skip-setting-text'>确认设定当前版本为执行版本？</div>
            </Modal>
        </>
    )
}
export default VersionRecordDrawer;