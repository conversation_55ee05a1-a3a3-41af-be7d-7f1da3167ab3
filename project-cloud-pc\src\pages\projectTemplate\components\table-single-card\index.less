@import "../../../../styles/base.less";

.project-list-card-item {
  box-sizing: border-box;
  width: 260px;
  margin-top: 16px;
  margin-right: 16px;
  padding: 16px;
  box-shadow: #0000001a 0px 0px 4px;
  cursor: pointer;

  .project-img-content {
    margin-right: 8px;

    img {
      width: 40px;
      height: 40px;
    }
  }

  .project-other-info-show {
    overflow: hidden;

    .project-title-content {
      height: 40px;
      color: @content;
      .f-13();

      .project-title {
        margin-right: 6px;
        overflow: hidden;
        color: #222;
      }
    }

    .project-process {
      height: 40px;
      margin-top: 4px;
      overflow: hidden;
      color: @help;
      .f-12();
    }

    .project-finish-time {
      padding: 4px 0;
      color: @help;
      .f-12();
    }
  }

  &:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

