import React from "react";
import "./index.less"
import { CalendarOutlined } from "@ant-design/icons";
import { BdwCardStatus, MenuItemShowNameIcon, BdwRow } from "@/components";
// @ts-ignore
import ProjectDefault from "@/assets/image/project-default.png";
import { history } from "@@/core/history";

export interface ProjectListCardItemProps {
  coverPictureUrl: string
  endDate: string
  leaderName: string
  leaderId: string
  name: string
  process: string
  projectId: string
  schedule: string
  statusName: string
  dataItem: any
}

const ProjectListCardItem: React.FC<ProjectListCardItemProps> = (props) => {
  const { coverPictureUrl} = props;
  const projectImgRender = () => {
    return (
      <div className='table-show-image-box'>
        <img src={coverPictureUrl || ProjectDefault} alt='项目头像' />
      </div>
    )
  }

  const toDetail = () => {
    history.push(`/my-project-detail/${props.projectId}`)
  }

  return (
    <BdwRow type='flex' className='project-list-card-item' onClick={toDetail}>
      <div className='project-img-content'>
        {projectImgRender()}
      </div>
      <div className='project-other-info-show flex-1'>
        <BdwRow className='project-title-content' type='flex'>
          <div className='project-title flex-1'>
            {props.name}
          </div>
          <div className='project-duty-person'>
            <MenuItemShowNameIcon name={props.leaderName} />
          </div>
        </BdwRow>
        <div className='project-process'>
          <span>当前进程:</span>
          {props.process}
        </div>
        <div className='project-finish-time'>
          <span className='f-12 mr-5'><CalendarOutlined style={{ marginRight: '10px' }} />截止日期:</span>
          {props.endDate}
        </div>
        <div className='project-status-content'>
          <BdwCardStatus theme='blue'>
            {props.statusName}
          </BdwCardStatus>
        </div>
      </div>
    </BdwRow>
  )
}

export default ProjectListCardItem
