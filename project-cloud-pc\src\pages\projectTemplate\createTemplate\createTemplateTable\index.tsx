/**
 * @description 项目模板table
 * <AUTHOR>
 * @date 2023-11-29 16:31:07
*/
import React, { useState ,useEffect} from 'react';
import type { Key } from 'react';
import { Button, Input } from 'antd';
import { BdwTable, BdwTableHeaderSearchComponent, BdwRow } from "@/components";
import Highlighter from "react-highlight-words";
import { useSelector, useDispatch,useParams } from 'umi';
import CreateTemplateInfo from './templateInfo';
import { BtnKey, ProjectOverviewFunctionCode } from '@/constants/Enum';
import { disabledFlag } from '@/pages/my-project-detail/projectTasks';
import { useBoolean } from 'ahooks';
import './index.less';



const CreateTemplateTable: React.FC = () => {
    const { templateListTasks,filter,expandedRowKeys,taskInfo,templateBtnUse,cTaskId } = useSelector((state: any) => state.projectTemplate);
    const dispatch = useDispatch();
    const { projectId } = useParams<{ projectId: string }>();
    const TaskTableColumns = [
        {
            title: <BdwTableHeaderSearchComponent title="任务节点名称">
                <Input placeholder="请输入模板任务名称进行检索"
                    className='no-border-input ant-input-cover-style'
                    value={filter?.name}
                    maxLength={128}
                    onChange={(e) => {
                        dispatch({
                            type: 'projectTemplate/setFilterTableData',
                            typeKey: e.target.value,
                            typeName: "name",
                            status: e.target.value
                        })
                     }}
                />
            </BdwTableHeaderSearchComponent>,
            dataIndex: 'name',
            render: function TitleColumn(value: string, record: any) {
                const renderShow = <span className='task-name-show'>
                    <Highlighter
                        highlightClassName="title-highlight"
                        searchWords={[filter?.name]}
                        autoEscape
                        textToHighlight={record.name ?? ""}
                    />
                </span>
                return (
                    <BdwRow type='flex'>
                        <div>{renderShow}</div>
                    </BdwRow>
                )
            },
        },
        {
            title: <BdwTableHeaderSearchComponent title="工期" />,
            dataIndex: 'executionCycle',
            width: 150,
        },
        {
            title: <BdwTableHeaderSearchComponent title="标准分" />,
            dataIndex: 'standardScore',
            width: 150,
        },
        {
            title: <BdwTableHeaderSearchComponent title="创建人" />,
            dataIndex: 'creatorName',
            width: 150,
        },
        {
            title: <BdwTableHeaderSearchComponent title="创建日期" />,
            dataIndex: 'createDate',
            width: 150,
        },
        {
            title: <BdwTableHeaderSearchComponent title="更新日期" />,
            dataIndex: 'updateDate',
            width: 150,
        },
        {
            title: <BdwTableHeaderSearchComponent title="任务说明" />,
            dataIndex: 'taskExplain',
            width: 150,
            render: (value: string,record: any) => {
                let taskExplain;
                try{
                    const v = JSON.parse(value);
                    taskExplain = v.map((item: any)=>item.value).join();
                }catch{
                    taskExplain = value
                }
                return <div>{taskExplain}</div>;
            }
        },
        {
            title: <BdwTableHeaderSearchComponent title="考评要求" />,
            dataIndex: 'evaluationStandard',
            width: 150,
        },

    ];
    useEffect(()=>{
        dispatch({
            type: 'projectTemplate/setTaskInfo',
            payload: null
        })
    },[projectId])
    return (
        <div className='create-template-table-container'>
            <div className={`create-template-table-content ${taskInfo?'create-template-table-content-w':''}`}>
                <BdwTable
                    className='create-template-table'
                    pagination={false}
                    expandable={{
                        defaultExpandAllRows: true,
                        expandedRowKeys,
                        onExpandedRowsChange: (e) => {
                            dispatch({
                                type: 'projectTemplate/setExpandedRowKeys',
                                payload: e
                            })
                        },
                    }}
                    sticky
                    // scroll={{ x: true }}
                    size="small"
                    rowKey="taskId"
                    // @ts-ignore
                    columns={TaskTableColumns}
                    dataSource={templateListTasks ?? []}
                    showPages={false}
                    useLocalData
                    rowSelection={{
                        selectedRowKeys: cTaskId && [cTaskId] || [],
                        type: 'radio',
                        columnWidth: 1,
                        renderCell: () => {
                            return null;
                        },
                        checkStrictly: true,
                    }}

                    // @ts-ignore
                    onRow={(task: any) => ({
                        onClick() {
                            if(!disabledFlag(templateBtnUse, ProjectOverviewFunctionCode.IMPORT_EXCEL_TEMPLATE, BtnKey.projectFunctionCode)){
                                dispatch({
                                    type: 'projectTemplate/fetTaskInfo',
                                    payload: task
                                })
                            }
                            dispatch({
                                type: 'projectTemplate/setCTaskId',
                                payload: task.taskId
                            })
                        },
                    })}
                />
            </div>
            {
                taskInfo && <CreateTemplateInfo editableEvent={()=>{
                    dispatch({
                        type: 'projectTemplate/setTaskInfo',
                        payload: null
                    })
                }} />
            }
        </div>
    )
}
export default CreateTemplateTable;