@import '../../../../../styles/base.less';

.create-template-info {
  width: 400px;
  height: 100%;
  border-left: 1px dashed @divider;
  padding: 20px 16px;

  .create-template-info-tabs {
    height: 100%;
  }

  .ant-tabs-content.ant-tabs-content-top {
    height: unset !important;
  }

  .evaluation-file-item {
    line-height: 24px;
    align-items: flex-end;

    .evaluation-file-type {
      width: 90px;
      word-break: break-all;
      margin: 0 5px;
    }

    .evaluation-file-readonly-item {
      color: @help;
      font-size: 13px;
    }

    .evaluation-file-control {
      .ant-btn {
        height: 24px;
      }
    }

    .ant-form-item {
      margin-bottom: unset;
    }
  }

  .ant-form-item {
    margin-bottom: 16px;

    .bdw-input-container {
      input {
        padding: unset;
      }
    }
  }

  .h-27 {
    height: 27px;
    display: flex;
    align-items: flex-end;
    padding-bottom: 5px;
  }

  .ant-tabs-content-holder {
    overflow: hidden;
  }

  .same_level_ratio {
    .ant-input-number-handler-wrap {
      display: none;

      input {
        padding-left: unset !important;
      }
    }
    .ant-input-number-input{
      padding-left: unset !important;
    }
    .ant-input-number-focused {
      box-shadow: unset !important;
    }

    .ant-input-number {
      border-top: unset;
      border-left: unset;
      border-right: unset;
    }
  }
}
