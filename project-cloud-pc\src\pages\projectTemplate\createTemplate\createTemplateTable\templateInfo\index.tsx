/**
 * @description 新建项目模板表格右侧内容
 * <AUTHOR>
 * @date 2023-11-29 16:39:55
*/
import React, { useEffect, useMemo, useState } from 'react';
import { Tabs, Form, Button, message, Select, InputNumber } from 'antd';
import { BdwFormItem, BdwInput, BdwTitle, BdwTextarea, BdwRow } from '@/components';
import { useSelector, useParams, useDispatch } from 'umi';
import { FileTextOutlined, CalendarOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { editProjectTasksApi } from '@/service/projectDos/my-project-detail/projectTasks';
import { formatSelectOptions } from '@/utils/utils';
import { rule } from './rule';
import './index.less';

interface CreateTemplateInfoProps {
    editableEvent: () => void;
    saveCurItem?: (info: any) => void;
    showDescription?: boolean;
    curTotalRatio?: number;
    requireInfo?: any;
}


const CreateTemplateInfo: React.FC<CreateTemplateInfoProps> = (props) => {
    const { editableEvent, saveCurItem, showDescription = true, curTotalRatio = 0, requireInfo } = props;
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const { projectId } = useParams<{ projectId: string }>();
    const [fileList, setFileList] = useState<Array<any>>([]);
    const { attachmentFormatOptions, ProjectUploadFileType } = useSelector((state: any) => state.projectTasks);
    const { taskInfo } = useSelector((state: any) => state.projectTemplate);
    const sureAddFile = (index: number) => {
        const evaluationName = form.getFieldValue("evaluationName");
        const evaluationType = form.getFieldValue("evaluationType");
        const handleFileList = [...fileList];
        if (!evaluationName) {
            message.error("附件要求名称必须填入");
            return
        }
        if (!evaluationType) {
            message.error("附件类型必须选择");
            return
        }
        handleFileList.splice(index, 1, { des: evaluationName, type: evaluationType, isEdit: false })
        setFileList(handleFileList)
    }
    const getTheCanHandleFlag = () => {
        if (fileList.length === 0) {
            return true
        }
        const canHandleFlag = fileList.filter((item) => item.isEdit).length;
        return canHandleFlag === 0
    }
    const cancelAddFile = (index: number) => {
        const evaluationName = form.getFieldValue("evaluationName");
        const oldEvaluationName = fileList[index].des;
        if (!evaluationName && !oldEvaluationName) {
            setFileList(fileList.filter((item) => !item.isEdit))
            return
        }

        setFileList(fileList.map((item) => {
            return { ...item, isEdit: false }
        }));
    }

    const editFileInfo = (index: number) => {
        const canHandleFlag = getTheCanHandleFlag();
        if (!canHandleFlag) {
            message.error("汇报附件要求还未进行保存，请确认！")
            return
        }
        const handleFileList = [...fileList];
        handleFileList[index].isEdit = true;
        setFileList(handleFileList)
        form.setFieldsValue({
            ...form.getFieldsValue(),
            evaluationName: handleFileList[index].des,
            evaluationType: handleFileList[index].type,
        })
    }
    const deleteFile = (index: number) => {
        const handleFileList = [...fileList];
        handleFileList.splice(index, 1)
        setFileList(handleFileList)
    }
    const showFileList = fileList.map((item: any, index: number) => {
        return (
            item.isEdit ?
                <BdwRow key={item.id} className='evaluation-file-item' type='flex'>
                    <div className='flex-1 evaluation-file-name'>
                        <BdwFormItem name='evaluationName' >
                            <BdwInput placeholder='附件要求名称' maxLength={128} />
                        </BdwFormItem>
                    </div>

                    <div className='evaluation-file-type'>
                        <BdwFormItem name='evaluationType'>
                            <Select placeholder="文件类型" options={formatSelectOptions(attachmentFormatOptions)} />
                        </BdwFormItem>
                    </div>

                    <div className='evaluation-file-control'>
                        <div>
                            <Button type='link' className='mr-10 p-0' onClick={() => sureAddFile(index)}>保存</Button>
                            <Button type='link' className='p-0' onClick={() => cancelAddFile(index)}>取消</Button>
                        </div>
                    </div>
                </BdwRow> :
                <BdwRow className='evaluation-file-item mb-10' type='flex' key={index}>
                    <div className='evaluation-file-readonly-item flex-1'>
                        {item.des}【{ProjectUploadFileType[item.type]}】
                    </div>
                    <div className='evaluation-file-control'>
                        <Button type='link' className='mr-10 p-0 ml-6' onClick={() => editFileInfo(index)}>编辑</Button>
                        <Button type='link' className='p-0' onClick={() => deleteFile(index)}>删除</Button>
                    </div>
                </BdwRow>
        )
    })
    const addOneFile = () => {
        const canHandleFlag = getTheCanHandleFlag();
        if (!canHandleFlag) {
            message.error("汇报附件要求还未进行保存，请确认！")
            return
        }
        form.setFieldsValue({
            ...form.getFieldsValue(),
            evaluationName: '',
            evaluationType: '',
        })
        setFileList([...fileList, { des: "", type: "", isEdit: true, id: Date.now() }]);

    }
    const submitData = async (data: any) => {
        if(!data?.ratio){
            message.warn('请填写同级占比！');
            return
        }
        if (saveCurItem) {
            saveCurItem({
                id: requireInfo?.id,
                name: data?.name || '',
                taskExplain: data?.taskExplain || '',
                ratio: data?.ratio,
                parentId: requireInfo?.parentId || requireInfo?.parent?.id || null
            });
            editableEvent();
        } else {
            const canHandleFlag = getTheCanHandleFlag();
            if (!canHandleFlag) {
                message.error("汇报附件要求还未进行保存，请确认！")
                return
            }
            const standardList = fileList.map((item) => {
                return {
                    attachmentDescription: item.des,
                    attachmentFormat: item.type,
                }
            })
            const params = {
                ...data,
                projectId,
                taskId: taskInfo.taskId,
                parentId: taskInfo.parentId,
                evaluationUploadStandards: standardList
            };
            await editProjectTasksApi(params);
            message.success('修改成功');
            dispatch({
                type: 'projectTemplate/fetchTemplateListTasks',
                payload: projectId,
            })
            dispatch({
                type: 'projectTemplate/setTaskInfo',
                payload: null,
            })
        }


    }
    useEffect(() => {
        if (!showDescription) return;
        if (taskInfo?.evaluationUploadStandards && taskInfo?.evaluationUploadStandards?.length) {
            const dataList = taskInfo.evaluationUploadStandards.map((item: any) => ({
                des: item.attachmentDescription,
                type: item.attachmentFormat,
                isEdit: false,
                id: Date.now()
            }))
            setFileList(dataList);
        } else {
            setFileList([]);
        }
        const initialValues = {
            name: taskInfo?.name ?? '',
            executionCycle: taskInfo?.executionCycle ?? '',
            evaluationStandard: taskInfo?.evaluationStandard ?? '',
        }
        let taskExplain;
        try {
            const v = JSON.parse(taskInfo?.taskExplain);
            taskExplain = v.map((item: any) => item.value).join();
        } catch {
            taskExplain = taskInfo?.taskExplain
        }
        form.setFieldsValue({
            ...form.getFieldsValue(),
            ...initialValues,
            taskExplain
        })
    }, [taskInfo]);
    useEffect(() => {
        if (showDescription) return;
        const initialValues = {
            name: requireInfo?.name ?? '',
            ratio: requireInfo?.ratio ?? '',
            taskExplain: requireInfo?.taskExplain ?? '',
        };
        form.setFieldsValue({
            ...form.getFieldsValue(),
            ...initialValues,
        });

    }, [requireInfo])

    return (
        <div className='create-template-info' style={{
            width: !showDescription ? 'unset' : '400px'
        }}>
            <Tabs
                className='create-template-info-tabs'
                items={[{
                    label: '模板任务项信息',
                    key: 'TEMPLATE_TASK_INFO',
                    children: <Form form={form} onFinish={submitData} preserve={false}>
                        {
                            showDescription && (
                                <>
                                    <BdwFormItem className='mt-16' labelAlign='left' labelRender={
                                        () => <div className='h-27'><FileTextOutlined className='mr-5' /><BdwTitle>标&emsp;题：</BdwTitle></div>
                                    } name="name">
                                        <BdwInput maxLength={128} placeholder='请输入标题' />
                                    </BdwFormItem>
                                    <BdwFormItem labelAlign='left' rules={rule.executionCycle} labelRender={() =>
                                        <div className='h-27'><CalendarOutlined className='mr-5' /><BdwTitle>工&emsp;期：</BdwTitle></div>
                                    } name="executionCycle" >
                                        <BdwInput type="number" min={1} />
                                    </BdwFormItem>

                                    <div className='mt-16'>
                                        <BdwFormItem label="任务说明" name='taskExplain'>
                                            <BdwTextarea autoSize maxLength={1024} />
                                        </BdwFormItem>
                                    </div>
                                    <div className='mt-16'>
                                        <BdwFormItem label="任务考评要求" name='evaluationStandard'>
                                            <BdwTextarea autoSize maxLength={512} />
                                        </BdwFormItem>
                                    </div>
                                    <BdwRow className='mb-10' type='flex'>
                                        <div className='flex-1'>
                                            <BdwTitle>汇报附件要求：</BdwTitle>
                                        </div>
                                        <div>
                                            <Button className='p-0 add-file-button' type='link' onClick={addOneFile}><PlusCircleOutlined className="f-16" />添加</Button>
                                        </div>
                                    </BdwRow>

                                    <div className='mb-16'>
                                        {
                                            showFileList
                                        }
                                    </div>
                                </>
                            )
                        }
                        {
                            !showDescription && <>
                                <BdwFormItem className='mt-16' labelAlign='left' labelRender={
                                    () => <div className='h-27'><FileTextOutlined className='mr-5' /><BdwTitle>标&emsp;&emsp;题：</BdwTitle></div>
                                } name="name">
                                    <BdwInput maxLength={128} placeholder='请输入标题' />
                                </BdwFormItem>
                                <BdwFormItem labelAlign='left' labelRender={() =>
                                    <div style={{
                                        height:'30px',display:'flex',alignItems:'center'
                                    }}><CalendarOutlined className='mr-5' /><BdwTitle>同级占比：</BdwTitle></div>
                                } name="ratio" className='same_level_ratio' rules={[
                                    () => ({
                                        validator(rule: any, value: number) {
                                            if (value >= 0) {
                                                if (value + curTotalRatio > 100) {
                                                    return Promise.reject(new Error(`同级占比总计不能超过100%`));
                                                }
                                                return Promise.resolve()
                                            }
                                            return Promise.reject(new Error('同级占比不能为空'));
                                        }
                                    })
                                ]}
                                >
                                    {/* <BdwInput type="number" min={1} /> */}
                                    <InputNumber min={0} max={100} formatter={value => `${value ? (value + '%') : ''}`}
                                        parser={value => value!.replace('%', '')} precision={3} style={{
                                            width: '100%',
                                            // fontSize: '12px',
                                            fontWeight: 700,
                                            color: '#000',
                                            borderBottom: '1px solid #c0c0c0',
                                            paddingLeft: '0px',
                                            height: '25px'
                                        }} placeholder="请输入同级占比" bordered={false}
                                    />
                                </BdwFormItem>
                                <div className='mt-10' style={{ marginTop: 'unset' }}>
                                    <BdwFormItem label="业务事项说明" name='taskExplain'>
                                        <BdwTextarea autoSize maxLength={1024} placeholder='请输入业务事项说明' />
                                    </BdwFormItem>
                                </div>
                            </>
                        }

                        <div className='mt-16'>
                            <Button htmlType='submit' type='primary' className='mr-16'>保存</Button>
                            <Button onClick={() => editableEvent()}>取消</Button>
                        </div>
                    </Form>
                }]}
                defaultActiveKey={'TEMPLATE_TASK_INFO'}
                destroyInactiveTabPane
            />
        </div>
    )
}
export default CreateTemplateInfo