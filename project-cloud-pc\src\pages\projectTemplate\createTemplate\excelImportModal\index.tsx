import React, { useState } from "react";
import { Button, Form, message, Modal, Upload, Spin } from 'antd'
import { BdwFormItem, BdwTableButton } from "@/components";
import { useParams, useDispatch } from "umi";
import { importExcelTemplate } from "@/service/projectDos/myProjectApi";

import { DownloadOutlined } from '@ant-design/icons'

import './index.less'
import {DownloadTemplateUrl} from './Enum';

interface ModalProps {
  visible: boolean
  closeEvent: () => void
}
const ExcelImportModal: React.FC<ModalProps> = (props) => {
  const { visible, closeEvent } = props;
  const dispatch = useDispatch();
  const { projectId } = useParams<{ projectId: string }>();
  const [fileObject, setFileObject] = useState<any>();
  const [loading, setLoading] = useState<boolean>(false);
  const [fileArr, setFileArr] = useState();
  const [errMessage, setErrMessage] = useState('');

  const [form] = Form.useForm();

  const testChange = (file: any) => {
    if (file.fileList.length > 1) {
      message.warning('一次仅限上传一个文件！');
      const newList = file.fileList.slice(-1)
      // const newList=file.fileList.splice(0,1);
      setFileArr(newList)
      setFileObject(file.file)
    } else {
      setFileArr(file.fileList)
      setFileObject(file.file)
    }
    setErrMessage('');
  }
  // modal 关闭，清楚数据
  const modalClose = () => {
    setLoading(false)
    closeEvent()
    // @ts-ignore
    setFileArr([]);
    setErrMessage('');
  }

  const submitData = async () => {

    form.validateFields().then(async () => {
      try {
        setLoading(true)
        const formData = new FormData();
        formData.set('file', fileObject);
        importExcelTemplate(projectId, formData).then((res) => {
          message.success('上传成功!');
          dispatch({
            type: 'projectTemplate/fetchTemplateListTasks',
            payload: projectId,
          })
          setLoading(true);
          modalClose();
        }).catch((e) => {
          setErrMessage(e.message);
          setLoading(false);
        })
      } catch (e) {
        modalClose();
      }
    })
  }

  return (
    <>
      <Modal
        title='通过excel模板导入项目模板信息'
        width={500}
        open={visible}
        onCancel={modalClose}
        destroyOnClose
        footer={[
          <Button key='1' type='primary' disabled={Boolean(errMessage)} loading={loading} onClick={() => submitData()} className='mr-16'>{loading ? '正在导入' :'确认导入'}</Button>,
          <Button key='2' onClick={() => modalClose?.()}>取消</Button>
        ]}
      >
        <Spin spinning={loading} tip='正在导入中...'>
          <Form form={form} onFinish={() => submitData()} className='excelImportContent'>
            <div className='mt-16'>
              <BdwFormItem label="您还没有没有导入数据的excel模板？请点击" className='downLoadDiv'>
                <BdwTableButton href={DownloadTemplateUrl} >下载模板</BdwTableButton>
              </BdwFormItem>
            </div>
            <div className='mt-16'>
              <BdwFormItem className='downLoadDiv' label="上传导入模板" name='file' required rules={[{ required: true, message: '请选择上传文件' }]}>
                <Upload onChange={testChange} fileList={fileArr} beforeUpload={() => false} action="" accept='.xlsx,.xls'>
                  <Button icon={<DownloadOutlined />}>选择模板</Button>
                </Upload>
              </BdwFormItem>
              {errMessage && <div className="upload-error-message">上传模板内容有误，请检查上传文件，错误提示：{errMessage}</div>}
            </div>
          </Form>
        </Spin>
      </Modal>
    </>
  )
}


export default ExcelImportModal
