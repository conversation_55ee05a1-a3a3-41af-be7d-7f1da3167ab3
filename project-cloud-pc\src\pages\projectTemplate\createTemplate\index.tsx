/**
 * @description 创建模板
 * <AUTHOR>
 * @date 2023-11-29 14:32:12
*/
import React, { useEffect, useState } from 'react';
import {
    BdwRow, BdwCommonIcon, BdwFormItems,
    BdwTextarea,
    BdwUpload, BdwChooseCompanyStaff,
} from '@/components';
import { DividerLine } from '@/pages/my-project-detail/projectTasks';
import CreateTemplateTable from './createTemplateTable';
import { history, useParams, useDispatch, useSelector } from 'umi';
import { Modal, message, Form, Button } from 'antd';
import { useBoolean } from 'ahooks';
import ExcelImportModal from './excelImportModal';
import { disabledFlag } from '@/pages/my-project-detail/projectTasks';
import { BtnKey, ProjectOverviewFunctionCode, AuditRelease } from '@/constants/Enum';
import { listEmpByParams } from '@/service/projectDos/commonApi';
import {
    deleteProject<PERSON>pi,
    applyRelease,
    applyReleaseByProjectId,
    auditApplyInfoApi
} from "@/service/projectDos/my-project-detail/projectOverview";
import { fileExport } from '@/utils/utils';
import './index.less';



const CreateTemplate: React.FC = () => {
    const { projectId } = useParams<{ projectId: string }>();
    const dispatch = useDispatch();
    const [excelImportModal, { setFalse: excelImportModalHide, setTrue: excelImportModalShow }] = useBoolean(false);//导入Excel模板
    const [deleteTemplateModal, { setFalse: deleteTemplateModalHide, setTrue: deleteTemplateModalShow }] = useBoolean(false);//删除模板
    const [auditTemplateModal, { setFalse: auditTemplateModalHide, setTrue: auditTemplateModalShow }] = useBoolean(false);//模板审核
    const [releaseTemplateModal, { setFalse: releaseTemplateModalHide, setTrue: releaseTemplateModalShow }] = useBoolean(false);//申请发布
    const [projectAuditMes, setProjectAuditMes] = useState<any>(null);
    const [releaseForm] = Form.useForm();
    const [auditForm] = Form.useForm();
    const { templateBtnUse, projectTemplateData,templateListTasks } = useSelector((state: any) => state.projectTemplate);
    const { userInfo } = useSelector((state: any) => state.commonTask);
    useEffect(() => {
        dispatch({
            type: 'projectTemplate/fetchTemplateInit',
            payload: projectId,
        })
        dispatch({
            type: 'projectTemplate/fetchProjectTemplateData',
            payload: projectId,
        })
        dispatch({
            type: 'projectTemplate/fetchTemplateListTasks',
            payload: projectId,
        })
    }, [])
    // 删除模板
    const confirmDelete = () => {
        deleteProjectApi(projectId).then(() => {
            message.success('删除成功！')
            deleteTemplateModalHide();
            history.push('/project-template')
        })
    }
    //模板项目申请发布
    const confirmSubmit = async (data: any) => {
        const params = {
            ...data,
            auditorId: data.auditorId.id,
            projectId
        }
        await applyRelease(params);
        dispatch({
            type: 'projectTemplate/fetchTemplateInit',
            payload: projectId,
        })
        message.success('申请成功!');
        releaseTemplateModalHide();
    }
    //打开模板项目审核弹窗
    const openAuditModal = () => {
        applyReleaseByProjectId(projectId).then((res) => {
            setProjectAuditMes(res)
        })
        auditTemplateModalShow();
    }
    //模板项目审核
    const auditProjectTemplate = (type: any) => {
        auditForm.validateFields().then(async (res) => {
            const params = {
                ...res,
                projectApplyId: projectAuditMes?.projectApplyId,
                projectAuditStatus: type
            }
            await auditApplyInfoApi(params);
            setProjectAuditMes(null)
            dispatch({
                type: 'projectTemplate/fetchTemplateInit',
                payload: projectId,
            })
            message.success('审核完成!');
            auditTemplateModalHide();
        })
    }
    //导出EXCEL
    const exportExcel = () => {
        fileExport(`/project-dos/project-template-management/export-excel-template/${projectId}`);
    }
    return (
        <div className='create-template-container'>
            <div className='create-template-wrapper'>
                <BdwRow type='flex-center' className='btn-create-template-wrapper' >
                    <BdwCommonIcon
                        name='返回'
                        icon='iconback'
                        className="mr-10 ml-10"
                        onClick={() => { history.push('/project-template') }}
                    />
                    <BdwCommonIcon
                        name='刷新'
                        icon='iconrefresh'
                        className="mr-10"
                        onClick={() => { history.go(0) }}
                    />
                    <DividerLine />
                    <BdwCommonIcon
                        name='删除模板'
                        icon='icondelete'
                        className="mr-10"
                        onClick={() => deleteTemplateModalShow()}
                        disabled={disabledFlag(templateBtnUse, ProjectOverviewFunctionCode.DELETE, BtnKey.projectFunctionCode)}
                    />
                    <DividerLine />
                    <BdwCommonIcon
                        name='模板审核'
                        icon='iconcheck'
                        className="mr-10"
                        onClick={openAuditModal}
                        disabled={disabledFlag(templateBtnUse, ProjectOverviewFunctionCode.AUDIT_APPLY, BtnKey.projectFunctionCode)}
                    />
                    <DividerLine />
                    <BdwCommonIcon
                        name='申请发布'
                        icon='iconsend'
                        className="mr-10"
                        onClick={() => releaseTemplateModalShow()}
                        disabled={disabledFlag(templateBtnUse, ProjectOverviewFunctionCode.APPLY_RELEASE, BtnKey.projectFunctionCode)}
                    />
                    <BdwCommonIcon
                        name='导入EXCEL模板'
                        icon='iconimport'
                        className="mr-10"
                        onClick={() => excelImportModalShow()}
                        disabled={disabledFlag(templateBtnUse, ProjectOverviewFunctionCode.IMPORT_EXCEL_TEMPLATE, BtnKey.projectFunctionCode)}
                    />
                    <BdwCommonIcon
                        name='导出EXCEL'
                        icon='iconexport'
                        className="mr-10"
                        onClick={() => exportExcel()}
                        disabled={!templateListTasks || templateListTasks.length == 0}
                    />
                    {/* 删除模板 */}
                    {
                        deleteTemplateModal && <Modal
                            open={deleteTemplateModal}
                            title="模板删除"
                            onOk={confirmDelete}
                            onCancel={deleteTemplateModalHide}
                        >
                            确认删除模板吗？
                        </Modal>
                    }
                    {/* 导入Excel对话框 */}
                    <ExcelImportModal
                        visible={excelImportModal}
                        closeEvent={excelImportModalHide}
                    />

                    {/* 申请发布 */}
                    {
                        releaseTemplateModal && <Modal
                            open={releaseTemplateModal}
                            title="项目模板申请发布"
                            destroyOnClose
                            footer={null}
                            className='project-template-release-modal'
                            width={800}
                            onCancel={releaseTemplateModalHide}
                        >
                            <Form onFinish={confirmSubmit} form={releaseForm}>
                                <BdwRow type='flex'>
                                    <div className='flex-1'>
                                        <div className='information mb-5'>
                                            模板名称
                                        </div>
                                        <div className='project-name mb-14'>
                                            {/* @ts-ignore */}
                                            {projectTemplateData?.projectName}
                                        </div>
                                        <div className='information mb-5'>
                                            模板简介
                                        </div>
                                        <div className='f-13 mb-14'>
                                            {projectTemplateData?.instructions}
                                        </div>
                                        <BdwFormItems label="审核人" name='auditorId' rules={[{ required: true, message: "请选择审核人" }]}>
                                            <BdwChooseCompanyStaff apiSrc={listEmpByParams} />
                                        </BdwFormItems>
                                        <BdwFormItems label="备注" name='applyNote' rules={[{ required: true, message: "请填写备注" }]}>
                                            <BdwTextarea />
                                        </BdwFormItems>
                                    </div>

                                    <BdwRow className='project-template-release-modal-btn'>
                                        <Button htmlType='submit' type="primary" style={{ marginBottom: '10px' }}>申请发布</Button>
                                        <Button onClick={() => { releaseTemplateModalHide() }}>取消</Button>
                                    </BdwRow>

                                </BdwRow>


                            </Form>
                        </Modal>
                    }
                    {/* 模板审核 */}
                    {
                        auditTemplateModal && projectAuditMes && <Modal
                            destroyOnClose
                            open={auditTemplateModal}
                            title="项目模板申请发布"
                            width={500}
                            footer={null}
                            className='project-template-audit-modal'
                            onCancel={auditTemplateModalHide}
                        >
                            <Form form={auditForm}>
                                <BdwRow type='flex'>
                                    <div className='color-5c f-12 mb-10'>模板名称&nbsp;:&nbsp;</div>
                                    <div className='f-14 f-weight pl-4'>{projectAuditMes?.projectName}</div>
                                </BdwRow>
                                <BdwRow type='flex'>
                                    <div className='color-5c f-12 mb-10'>审核人员&nbsp;:&nbsp;</div>
                                    <div className='f-14 f-weight pl-4'>{userInfo?.userName}</div>
                                </BdwRow>
                                <BdwFormItems label="审核意见" name='auditNote' rules={[{ required: true, message: "请填写审核意见" }]}>
                                    <BdwTextarea />
                                </BdwFormItems>
                                <BdwRow type='flex' className='project-template-audit-modal-btn'>
                                    <Button style={{ marginRight: '10px', background: '#0275d8', padding: '2px 16px', width: '86px', border: 'none', fontSize: '13px', color: '#fff' }} onClick={() => { auditProjectTemplate(AuditRelease.PASSED) }} >审核通过</Button>
                                    <Button style={{ marginRight: '10px', background: '#ff4d4f', padding: '2px 16px', width: '86px', border: 'none', fontSize: '13px', color: '#fff' }} onClick={() => { auditProjectTemplate(AuditRelease.REJECTED) }} >审核驳回</Button>
                                    <Button onClick={() => { auditTemplateModalHide() }} >取消</Button>
                                </BdwRow>
                            </Form>

                        </Modal>
                    }

                </BdwRow>
                <CreateTemplateTable />
            </div>
        </div>
    )
}
export default CreateTemplate