/**
 * @description 我的项目
 * <AUTHOR>
 * @date 2023-10-26 16:22:30
*/
import React, { useEffect, useMemo, useState } from "react";
import { Input, Menu, Tabs, Select, Modal, Form, message } from "antd";
import {
    UserOutlined,
    UsergroupAddOutlined,
    FileAddOutlined,
    ProjectOutlined,
    BankOutlined
} from "@ant-design/icons";
import {
    MenuItemShowNumber,
    BdwTable,
    BdwTableButton,
    BdwTableHeaderSearchComponent,
    BdwRow,
} from "@/components";
import { BdwTableColumn } from '@/components/bdw-table';
import { useBoolean, useRequest } from "ahooks";
import type { TypeDataResult } from "@/type";
import { listProjectTemplate,listProjectLevelTemplateName,templateQuickStatistics } from "@/service/projectDos/myProjectApi";
import { history, useDispatch } from "umi";
import ProjectSetting from "../my-project-detail/projectOverview/projectSetting";
// @ts-ignore
import ProjectDefault from "@/assets/image/project-default.png";
import './index.less';
import {TemplateClassification} from './Enum'

interface TemplateTypeDataResult extends Omit<TypeDataResult,'quickStatus'>{
    quickStatusCode?: string
}

const { Search } = Input;


const MyProject = () => {
    const dispatch = useDispatch();
    // 全局搜索默认字段
    const [globalSearch, setGlobalSearch] = useState("");
    // tabPane的默认状态值
    const [currentTabPaneValue, setCurrentTabPaneValue] = useState<string>(TemplateClassification.COMPANY);
    // menu的默认状态值
    const [currentMenuValue, setCurrentMenuValue] = useState<string>("ALL");
    // 查询名称
    const [searchName, setSearchName] = useState<string>("");
    // 查询用的类型
    const { data: TemplateList} = useRequest(listProjectLevelTemplateName);
    const { data: TemplateStatusList} = useRequest<any[]>(templateQuickStatistics);
    const [createVisible, { setTrue, setFalse }] = useBoolean(false);
    const [form] = Form.useForm();
    const projectSearchStatus = useMemo(() => {
        if (TemplateList) {
            return TemplateList;
        }
        return []
    }, [TemplateList])

    // 项目类型
    const projectTypeLists: any = useMemo(() => {
        if (TemplateStatusList) {
            return TemplateStatusList
        }
        return []
    }, [TemplateStatusList])
    const newTabDataList = projectTypeLists?.map((item: any, index: number) => {
        let icon;
        switch (item.projectLevelCode) {
            case TemplateClassification.COMPANY:
                icon = () => <BankOutlined />;
                break;
            case TemplateClassification.DEPARTMENT:
                icon = () => <ProjectOutlined />;
                break;
            case TemplateClassification.TEAM:
                icon = () => <UsergroupAddOutlined />;
                break;
            case TemplateClassification.PERSONAL:
                icon = () => <UserOutlined />;
                break;
        }
        return {
            name: item.projectLevelName,
            code: item.projectLevelCode,
            icon,
            projectNum: item.total
        }
    })
    // 顶部统计栏
    const quickStatisticsList: any = useMemo(() => {
        if (TemplateStatusList) {
            const list = TemplateStatusList.filter((item: any) => item.projectLevelCode == currentTabPaneValue)
            return list[0].projectQuickStatistics;
        }
        return []
    }, [currentTabPaneValue, TemplateStatusList])
    const menuShowElement = quickStatisticsList.map((item: TemplateTypeDataResult) => {
        const labelContent = <div>
            <span>{item.quickStatusName}</span>
            <MenuItemShowNumber className='ml-8 vertical-middle'>{item.quantity}</MenuItemShowNumber>
        </div>
        return ({
            label: labelContent,
            key: item.quickStatusCode
        })
    })

    const tabsShowElement = newTabDataList.map((item: any) => {
        const tabShowContent = (<div><span>{item.icon()}</span><span className='c-content'>{item.name}<span className='ml-6 menu-item-number-ball vertical-middle'>{item.projectNum}</span></span></div>);
        return (
            {
                label: tabShowContent,
                key: item.code,
            }
        )
    })

    const changeProjectName = (e: string) => {
        setCurrentTabPaneValue(e)
    }
    //新建项目
    const createNewProjectTemplate = () => {
        setTrue();
    }
    const fastButtonsRender = () => {
        return (
            <BdwRow type="flex">
                <BdwTableButton onClick={createNewProjectTemplate} icon={() => <FileAddOutlined />}>新建模板</BdwTableButton>
            </BdwRow>
        )
    }
    const onRowClick = (item: any) => {
        return {
            onClick: () => {
                history.push(`/project-template-detail/${item.projectId}`)
            }
        }
    }
    const projectImgRender = (text: string) => {
        return (
            <div className='table-show-image-box'>
                <img src={text ? text : ProjectDefault} alt='项目头像' />
            </div>
        )
    }
    //状态
    const showCurrentStatusName = (v: string,r: any) => {
        return (
            <div className="template-status-name">{v}</div>
        )
    }
    //菜单切换
    const menuClickEvent = (e: any) => {
        setCurrentMenuValue(e?.key)
    }

    const showTableBtType =
        <BdwTable
            api={listProjectTemplate}
            fastButtonRender={fastButtonsRender}
            onRow={onRowClick}
            extraParams={
                {
                    "level":currentTabPaneValue,
                    "name":searchName,
                    "statusCode":currentMenuValue === 'ALL' ? "" : currentMenuValue,
                }
            }
        >
            <BdwTableColumn width={60} dataIndex='coverPic' title={
                <BdwTableHeaderSearchComponent title='' />
            } render={projectImgRender} />
            <BdwTableColumn width={220} ellipsis dataIndex='name' title={
                <BdwTableHeaderSearchComponent title='模板名称'>
                    <Input value={searchName} maxLength={128} onChange={(e) => setSearchName(e.target.value)} className='no-border-input'
                        placeholder='请输入模板名称进行检索' />
                </BdwTableHeaderSearchComponent>
            } />
            <BdwTableColumn width={120} dataIndex='levelName' title={
                <BdwTableHeaderSearchComponent title='模板等级'/>
            } />
            <BdwTableColumn width={120} title={
                <BdwTableHeaderSearchComponent title='模板分类'/>
            } dataIndex='classificationName' />
            <BdwTableColumn width={120} title={
                <BdwTableHeaderSearchComponent title='处理人' />
            } dataIndex='dealUserName' render={(value: string) => <span>{value}</span>} />
            {/* <BdwTableColumn width={120} title={
                <BdwTableHeaderSearchComponent title='处理部门' />
            } dataIndex='dealUserOrgName' /> */}
            <BdwTableColumn width={120} ellipsis title={
                <BdwTableHeaderSearchComponent title='处理时间' />
            }  dataIndex='dealTime' />
            <BdwTableColumn width={120} ellipsis title={
                <BdwTableHeaderSearchComponent title='状态' />
            } render={showCurrentStatusName} dataIndex='statusName' />
            <BdwTableColumn width={360} ellipsis title={
                <BdwTableHeaderSearchComponent title='审核意见' />
            } dataIndex='auditOpinion' />
        </BdwTable>
    return <div className="my-project-container">
        <div className='project-tab-content'>
            <Tabs
                onChange={changeProjectName}
                type="card"
                items={tabsShowElement}
                activeKey={currentTabPaneValue}
            />
        </div>

        <div className='project-statistic-content mt-16'>
            <Menu
                mode="horizontal"
                selectedKeys={[currentMenuValue]}
                onClick={menuClickEvent}
                items={menuShowElement}
            />
        </div>
        <div className='project-statistic-table'>
            {showTableBtType}
        </div>
        {
            createVisible && <ProjectSetting creatTemplate={true} show={createVisible} template={true} onClose={setFalse} defaultValue={{level:currentTabPaneValue}} />
        }
    </div>
}
export default MyProject;