import React, { useState } from "react";
import { useSelector, useDispatch } from 'umi';
import {
	FieldTimeOutlined,
	HourglassOutlined,
	FileDoneOutlined,
	StopOutlined
} from "@ant-design/icons";
import TaskCardViewItem from "@/components/TaskCardViewItem";
import { cloneDeep } from "lodash";
import './index.less';

interface TabDataItemInterface {
	icon: () => React.ReactNode
	name: string
	code: string
	dataList: any[]
}
export interface ItemProps {
	projectId: string
	projectName: string
	projectText: string
	projectStatus: string
	projectStatusText: string
	projectManager: string
	projectDesc: string
}
  
// 任务类型切换Tab选项列表
const tabDataList: Array<TabDataItemInterface> = [
	{
		icon: () => <FieldTimeOutlined/>,
		name: "待处理",
		code: "Pending",
		dataList: [{
			projectId: '1',
			projectName: '平台首页及体验注册上线，引用集团产品价格表',
			projectText: '已经根据要求规格，引用了集团产品价格表要做数据测试校验确认',
			projectStatus: 'waitExamine',
			projectStatusText: '待评审',
			projectManager: '杨黎',
			projectDesc: '平台首页及注册项目体验'
		}, {
			projectId: '2',
			projectName: '平台首页及体验注册上线，引用集团产品价格表',
			projectText: '已经根据要求规格，引用了集团产品价格表要做数据测试校验确认',
			projectStatus: 'waitReply',
			projectStatusText: '待回复',
			projectManager: '张三',
			projectDesc: '平台首页及注册项目体验'
		}, {
			projectId: '3',
			projectName: '平台首页及体验注册上线，引用集团产品价格表',
			projectText: '已经根据要求规格，引用了集团产品价格表要做数据测试校验确认',
			projectStatus: 'waitSubmit',
			projectStatusText: '待提报',
			projectManager: '张思',
			projectDesc: '平台首页及注册项目体验'
		}, {
			projectId: '4',
			projectName: '平台首页及体验注册上线，引用集团产品价格表',
			projectText: '已经根据要求规格，引用了集团产品价格表要做数据测试校验确认',
			projectStatus: 'reject',
			projectStatusText: '已驳回',
			projectManager: '张务',
			projectDesc: '平台首页及注册项目体验'
		}]
	}, {
		icon: () => <HourglassOutlined/>,
		name: "进行中",
		code: "OnGoing",
		dataList: [{
			projectId: '1',
			projectName: '平台首页及体验注册上线，引用集团产品价格表',
			projectText: '已经根据要求规格，引用了集团产品价格表要做数据测试校验确认',
			projectStatus: null,
			projectStatusText: null,
			projectManager: '刘留',
			projectDesc: '平台首页及注册项目体验'
		}, {
			projectId: '2',
			projectName: '平台首页及体验注册上线，引用集团产品价格表',
			projectText: '已经根据要求规格，引用了集团产品价格表要做数据测试校验确认',
			projectStatus: null,
			projectStatusText: null,
			projectManager: '刘琦',
			projectDesc: '平台首页及注册项目体验'
		}]
	}, {
		icon: () => <FileDoneOutlined/>,
		name: "已完成",
		code: "Finished",
		dataList: [{
			projectId: '1',
			projectName: '平台首页及体验注册上线，引用集团产品价格表',
			projectText: '已经根据要求规格，引用了集团产品价格表要做数据测试校验确认',
			projectStatus: null,
			projectStatusText: null,
			projectManager: '孙霸',
			projectDesc: '平台首页及注册项目体验'
		}, {
			projectId: '2',
			projectName: '平台首页及体验注册上线，引用集团产品价格表',
			projectText: '已经根据要求规格，引用了集团产品价格表要做数据测试校验确认',
			projectStatus: null,
			projectStatusText: null,
			projectManager: '孙久',
			projectDesc: '平台首页及注册项目体验'
		}, {
			projectId: '3',
			projectName: '平台首页及体验注册上线，引用集团产品价格表',
			projectText: '已经根据要求规格，引用了集团产品价格表要做数据测试校验确认',
			projectStatus: null,
			projectStatusText: null,
			projectManager: '孙室',
			projectDesc: '平台首页及注册项目体验'
		}]
	}, {
		icon: () => <StopOutlined/>,
		name: "已暂停",
		code: "Suspended",
		dataList: []
	}
]

const CardView = () => {
	const dispatch = useDispatch()
	// 从models中获取数据
	// const taskState = useSelector((state: any) => state.myTask);
	const [checkedItemList, setCheckedItemList] = useState<any[]>([])  // 已选任务项list

	// 任务项选中状态改变
	const handleOnCheckedChange = (checkedFlag:boolean, checkedItem: any) => {
		const { projectId } = checkedItem;
		let newArr: any[] = cloneDeep(checkedItemList)

		// 已存在list中且checkedFlag状态为false，则将选中项从list中移除
		if(newArr.map((item: ItemProps) => item.projectId).includes(projectId) && !checkedFlag) {
			newArr = newArr.filter((item: ItemProps) => item.projectId !== projectId)
		} else { // 将已选的item push进list中
			newArr.push(checkedItem)
		}
		// 更新list
		setCheckedItemList(newArr)
	}

	return <div className="card_view_wrapper">
		<div className="content_wrapper">
			<div className="card_wrapper">
				{
					tabDataList.map((item: TabDataItemInterface) => (
						<TaskCardViewItem key={item.code} itemInfo={item} handleOnCheckedChange={handleOnCheckedChange} />
					))
				}
			</div>
		</div>
	</div>
}

export default CardView;
