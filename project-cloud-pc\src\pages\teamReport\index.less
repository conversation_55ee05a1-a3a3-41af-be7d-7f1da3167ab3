@import "../../styles/base";

.team_report_wrapper {
    background-color: #fafafa;
    padding: 10px 18px;
    height: 100%;
    box-sizing: border-box;
    overflow: hidden;

    .card_view_wrapper {
        .content_wrapper {
            // margin: 0 auto;
            width: 1260px;
            overflow-y: hidden;
            .card_wrapper {
                overflow: hidden;
                padding: 0 5px;
                .card_col {
                    width: 300px;
                    float: left;
                    margin-right: 16px;
                    &:nth-last-child(1) {
                        margin-right: 0;
                    }
                    .card_item {
                        cursor: pointer;
                        overflow: hidden;
                        width: 100%;
                        padding: 12px 16px;
                        box-shadow: 0px 0px 5px rgba(0, 0, 0, .1);
                        border-radius: 2px;
                        background-color: #FFF;
                        margin: 16px 0;
                        transition: all .3s;
                        &:hover {
                            transition: all .3s;
                            box-shadow: 0px 0px 10px rgba(0, 0, 0, .1);
                        }

                        .title_wrapper {
                            display: flex;
                            align-items: flex-start;
                            justify-content: space-between;
                            font-size: 13px;
                            color: #000;
                            .title_info {
                                width: 208px;
                                padding-top: 2px;
                                -webkit-line-clamp: 2;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                display: -webkit-box;
                                -webkit-box-orient: vertical;
                            }
                            .user {
                                width: 28px;
                                height: 28px;
                                text-align: center;
                                line-height: 28px;
                                font-size: 10px;
                                border-radius: 50%;
                                color: #fff;
                                background-color: #428bca;
                            }

                        }

                        .project_content_wrapper {
                            float: right;
                            width: 240px;
                            display: flex;
                            align-items: flex-start;
                            margin: 6px 0;
                            justify-content: space-between;
                            color: #657599;
                            font-size: 12px;

                            .project_text {
                                // width: 245px;
                                -webkit-line-clamp: 2;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                display: -webkit-box;
                                -webkit-box-orient: vertical;
                            }
                        }

                        .desc_wrapper {
                            float: right;
                            width: 240px;
                            display: flex;
                            align-items: center;
                            font-size: 12px;
                            .ant-tag {
                                border-color: transparent;
                                border-radius: 3px;
                            }
                            .desc_content {
                                display: flex;
                                align-items: center;
                                .desc_text, .icon {
                                    color: #9F9F9F;
                                }
                                .icon {
                                    margin-right: 10px;
                                }
                                .long_desc {
                                    width: 223px!important;
                                }
                                .desc_text {
                                    width: 160px;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .table_view_wrapper {
        height: 100%;
        background-color: #FFF;
    }

    .header_wrapper {
        margin-bottom: 10px;
    }
    .ant-tabs.ant-tabs-card .ant-tabs-nav {
        padding-left: 15px;
    }
    .ant-pagination-item, .ant-pagination-jump-next, .ant-pagination-options, .ant-pagination-jump-prev {
        display: none;
    }
    .operate_wrapper {
        padding: 5px 0;
        .left_wrapper {
            align-items: center;
            justify-content: space-between;
            .operate_bar {
                margin: 0;
                padding: 0;
                li {
                    cursor: pointer;
                    margin-right: 15px;
                    color: #014C8C;
                    span {
                        margin-left: 3px;
                    }
                }
                .not_allowed {
                    opacity: .45;
                    cursor: not-allowed;
                }
            }
            .search_span {
                display: inline-block;
                line-height: 32px;
            }
            .search_wrapper {
                position: relative;
                .MuiAutocomplete-endAdornment {
                    right: 20px;
                }
                .search_icon {
                    position: absolute;
                    right: 5px;
                    top: 10px;
                }
            }
        }
    }
}