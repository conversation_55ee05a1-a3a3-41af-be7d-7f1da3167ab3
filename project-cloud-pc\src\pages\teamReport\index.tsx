import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from 'umi';
import { BarsOutlined, AppstoreOutlined } from '@ant-design/icons';
import { HeaderBar } from "@/components";
import TableView from './tableView';
import CardView from './cardView';
import './index.less';

const TeamReport = () => {
	const dispatch = useDispatch()

	// 从models中获取数据
	const taskState = useSelector((state: any) => state.myTask);
	
	const [viewMethod, setViewMethod] = useState<string>("TABLE");  // 视图 TABLE：列表 CARD：卡片

	// 接口调用示例
	useEffect(() => {
		// dispatch({
		// 	type: 'myTask/fetchTaskInfo',
		// 	onSuccess: (res: any) => {
		// 		console.log(res, '<<<<request info');
		// 	}
		// })
	}, [])

	// 左侧项目下拉框数据
	const projectList = [{
		title: '所有项目',
		key: 'all'
	}, {
		title: '项目一',
		key: 'project1'
	}, {
		title: '项目二',
		key: 'project2'
	}]

	// 右侧视图下拉框数据
	const viewList = [{
		title: '列表视图',
		key: 'TABLE',
		icon: () => <BarsOutlined />
	}, {
		title: '看板视图',
		key: 'CARD',
		icon: () => <AppstoreOutlined />
	}]

	return <div className="team_report_wrapper">
		{/* 头部项目、视图切换 */}
		<HeaderBar 
			viewList={viewList}
			projectList={projectList}
			onViewChange={(checkedView: string) => setViewMethod(checkedView)}
			onProjectChange={(checkedProject: string) => console.log(checkedProject, '<<<checkedproject')}
		/>
		{ viewMethod == "TABLE" ? <TableView /> :	<CardView /> }
	</div>
}

export default TeamReport;
