import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from 'umi';
import { Input, Select, Pagination, Tabs } from 'antd';
import { CaretDownOutlined, SearchOutlined } from '@ant-design/icons';
import { BMCAutocomplete } from 'bmc-app-react-component';
import CustomTable from "@/components/customTable";
import type { ColumnsType } from 'antd/es/table';
import type { TabsProps } from 'antd';
import './index.less';

interface DataType {
	key: React.ReactNode
	name: string
	age: string
	address: string
	children?: DataType[] | null
}

const TableView = () => {
	const dispatch = useDispatch()

	// 从models中获取数据
	const taskState = useSelector((state: any) => state.myTask);
	
	const [currentPage, setCurrentPage] = useState(1)  // 当前页
	const [pageSize, setPageSize] = useState(10)  // 当前页显示条数
	const [total, setTotal] = useState(490) // 数据总条数

	// 接口调用示例
	useEffect(() => {
		dispatch({
			type: 'myTask/fetchTaskInfo',
			onSuccess: (res: any) => {
				console.log(res, '<<<<request info');
			}
		})
	}, [])

	// table columns
	const tableColumns: ColumnsType<DataType> = [{
		title: '任务名称',
		dataIndex: 'name',
		key: 'name',
		width: '30%',
		render: (item: string, record: DataType) => {
			// console.log(record, item, '<<<<<record');
			if (record.key == 0) {
				return <Input placeholder='搜索...' bordered={false} style={{ height: 23 }} onChange={(val) => console.log(val.target.value, '<<<<val')} />
			}
			return <span>{item}</span>
		}
	}, {
		title: '执行人',
		dataIndex: 'age',
		key: 'age',
		render: (item: string, record: DataType) => {
			// console.log(record, item, '<<<<<record');
			if (record.key == 0) {
				return <Select  options={[]} bordered={false}  style={{ width: '100%', height: 23 }} suffixIcon={<CaretDownOutlined />} onChange={(val) => console.log(val.target.value, '<<<<val')} />
			}
			return <span>{item}</span>
		}
	}, {
		title: '截止日期',
		dataIndex: 'address',
		key: 'address'
	}, {
		title: '剩余(天)',
		dataIndex: 'age',
		key: 'age',
	}, {
		title: '汇报/应报',
		dataIndex: 'address',
		key: 'address',
	}, {
		title: '消息提醒',
		dataIndex: 'address',
		key: 'address',
		width: '30%'
	}]

	// table data
	const tableData: DataType[] = [{
		key: 0,
		name: '',
		age: '',
		address: '',
		children: null
	}, {
		key: 1,
		name: '周杰',
		age: '34',
		address: '文化路',
		// children: [{
		//     key: '1-1',
		//     name: '小杰',
		//     age: '28',
		//     address: '文化路',
		// }, {
		//     key: '1-2',
		//     name: '小小杰',
		//     age: '4',
		//     address: '文化路',
		//     children: [{
		//         key: '1-2-1',
		//         name: 'balabala',
		//         age: '1',
		//         address: '文化路',
		//     }]
		// }]
	}, {
		key: 2,
		name: '周羽',
		age: '42',
		address: '建设路'
	}]

	// operate bar btn list
	// TODO icon待替换
	const operateBarBtnList = [{
		icon: () => <SearchOutlined className="search_icon" />,
		title: '分派',
		key: 'assign'
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '接收',
		key: 'receive'
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '退回',
		key: 'return'
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '提报',
		key: 'submit'
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '回复',
		key: 'apply'
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '审核',
		key: 'examine'
	}, {
		icon: () => <SearchOutlined className="search_icon" />,
		title: '驳回',
		key: 'reject'
	}]

	// pageInfo render
	const showTotalPaging = (total: number): any => {
		return <div>
			<span className="fw-b">{(currentPage - 1) * pageSize + 1}</span>
			&nbsp;至&nbsp;
			<span className="fw-b">{currentPage * pageSize > total! ? total : currentPage * pageSize}</span>
			&nbsp;共计&nbsp;
			<span className="fw-b">{total}</span>
		</div>
	}

	const onPagingChange = (currentPageInfo: number) => {
		setCurrentPage(currentPageInfo)
		console.log(currentPageInfo, '<<<<分页触发后的页码');
	}

	return <div className="table_view_wrapper">
		<div style={{ padding: '0 15px' }}>
			<div className="operate_wrapper flex-w">
				<div className="left_wrapper flex-w">
					{/* 操作栏按钮 */}
					<ul className="operate_bar flex-w">
						{
							operateBarBtnList.map((item) => (
								<li 
									key={item.key} 
									title={item.title} 
									onClick={() => console.log(item.key)}
									className={item.key == 'reject' && 'not_allowed' || undefined}
								>
									{/* <SearchOutlined className="search_icon" /> */}
									{item?.icon()}
									<span>{item.title}</span>
								</li>
							))
						}
					</ul>
					{/* 搜索框 */}
					<div className="flex-w search_wrapper">
						<span className="search_span">搜索任务：</span>
						<BMCAutocomplete 
							sx={{ width: 240 }} 
							options={[{ title: '选项1', id: 1 }, { title: '选项2', id: 2 }]} 
							getOptionLabel={(item: any) => item.title}
							isOptionEqualToValue={(o, v) => o.id === v.id}
							onChange={(e: any, value: any) => console.log(value, '<<<<选择的')}
						/>
						<SearchOutlined className="search_icon" />
					</div>
				</div>

				<Pagination 
					size='small'
					total={total}
					current={currentPage}
					defaultCurrent={currentPage}
					onChange={onPagingChange}
					showTotal={showTotalPaging} 
				/>
			</div>
			<CustomTable
				rowKey="key"
				data={tableData}
				isRowSelection={true}
				columns={tableColumns}  // 不同状态的项目公用table组件，columns须处理
			/>
		</div>
	</div>
}

export default TableView;
