import React, { useEffect, useMemo, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Drawer, Form, message, Space } from "antd";
import {
  BdwUploadBtn,
  BdwFormItems,
  BdwReadonlySpan,
  BdwTextarea,
  BdwChooseCompanyStaff
} from "@/components";
import { InfoCircleOutlined } from "@ant-design/icons/lib";
import { dispatchTaskRule } from "./rute";
import "./index.less";
import TaskInfo from "../task-info-comp";
import { taskAssign ,listTaskLeaderInfo} from '@/service/projectDos/my-project-detail/projectTasks';
import { getTaskTipsArr } from "@/utils/getTaskTipsArr";
import { useParams, useSelector, useDispatch } from 'umi';
import { BtnOperateType } from '@/constants/Enum';

interface TipsObjType {
  id: number,
  content: string
}

export interface DispatchTaskDrawerProps {
  visible: boolean
  cancelEvent?: () => void
  successEvent?: () => void
  task: any,
  assignType: string
}

const DispatchTaskDrawer: React.FC<DispatchTaskDrawerProps> = (props) => {
  const dispatch = useDispatch();
  const { visible, cancelEvent, task, successEvent, assignType } = props;
  const { projectId } = useParams<{ projectId: string }>();
  const taskTipsArr = getTaskTipsArr(task)
  const [form] = Form.useForm();
  const  [currentType, setCurrentType] = useState<any>({});
  useEffect(() => {
    if (assignType == BtnOperateType.ASSIGNMENT) {
      setCurrentType({
        title:'任务分派',
        headerLabel:'任务负责人',
        remarkLabel:'分派备注',
        remarkLabelPlaceholder:'请输入分派备注',
        documentLabel:'分派附件资料',
        getInitValue:{ id: task.leaderId, name: task.leaderName },
        confirmBtnName:'确认分派',
        successMessage:'任务分派成功！'
      })
    } else if (assignType == BtnOperateType.REASSIGN) {
      setCurrentType({
        title:'任务改派',
        headerLabel:'任务改派负责人',
        remarkLabel:'改派备注',
        remarkLabelPlaceholder:'请输入改派备注',
        documentLabel:'改派附件资料',
        getInitValue:null,
        confirmBtnName:'确认改派',
        successMessage:'任务改派成功！'
      })
    }
  }, [assignType])

  const submitData = async () => {
    try {
      const formValues = form.getFieldsValue();
      const submitInfo = {
        ...formValues,
        taskId: task.taskId,
        assignType: assignType,
        leaderId: formValues.leaderId.id,
        document: formValues.document?.map((item: any) => item.id).join(),
      }
      await taskAssign(submitInfo);
      message.success(currentType?.successMessage);
      successEvent?.();
    } catch (e) {
      console.error(e)
    }
  }

  return (
    <Drawer
      zIndex={1001}
      open={visible}
      title={currentType?.title}
      width={700}
      onClose={() => cancelEvent?.()}
      destroyOnClose
      className="dispatch-task-drawer-container"
      maskClosable={false}
    >
      <TaskInfo task={task} >
        <Form form={form} className='dispatch-task-drawer' onFinish={() => submitData()} preserve={false}>
          <Divider className='mt-16' />
          <div className='mt-16'>
            <BdwFormItems label={currentType?.headerLabel} name='leaderId' required rules={dispatchTaskRule.header} initialValue={currentType?.getInitValue}>
              <BdwChooseCompanyStaff
                apiSrc={listTaskLeaderInfo}
                extraParams={{ projectId: projectId || task.projectId }}
              />
            </BdwFormItems>
          </div>
          <div className='mt-16'>
            <BdwFormItems label={currentType?.remarkLabel} name="assignRemark" rules={dispatchTaskRule.note}>
              <BdwTextarea placeholder={currentType?.remarkLabelPlaceholder} autoSize maxLength={512} onKeyDown={(e) => {
                if (e.keyCode === 13) {
                  e.nativeEvent.stopImmediatePropagation()
                }
              }} />
            </BdwFormItems>
          </div>
          <div className='mt-16'>
            <BdwFormItems label={currentType?.documentLabel} name='document'>
              <BdwUploadBtn />
            </BdwFormItems>
          </div>
          {
            assignType == 'ASSIGNMENT' && <div className='mt-16'>
              {
                taskTipsArr.length !== 0 &&
                <BdwFormItems label="任务分解存在未设置信息项，是否继续分派?">
                  {taskTipsArr?.map((item: TipsObjType) => {
                    return (
                      <BdwReadonlySpan key={item.id} className="dispatch-task-form-tips">
                        <Space>
                          <InfoCircleOutlined className='f-16' />
                          <span>{item.content}</span>
                        </Space>
                      </BdwReadonlySpan>
                    )
                  })}
                </BdwFormItems>
              }
            </div>
          }

          <div className='mt-16'>
            <Button type='primary' htmlType='submit' className='mr-16'>{currentType.confirmBtnName}</Button>
            <Button onClick={() => cancelEvent?.()}>返回</Button>
          </div>
        </Form>
      </TaskInfo>
    </Drawer>
  )
}

export default DispatchTaskDrawer
