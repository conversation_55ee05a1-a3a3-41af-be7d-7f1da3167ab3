import React, { useMemo, useState, useEffect } from "react";
import { <PERSON><PERSON>, Divider, Drawer, Form, message } from "antd";
import {
  BdwDatePicker,
  BdwUploadBtn,
  BdwFormItems,
  BdwReadonlySpan,
  BdwTextarea, BdwTitle,
  BdwChooseCompanyStaff
} from "@/components";
import moment from "moment";
import { extendTaskRequestRule } from "./rute";
import { taskApplyType, listTaskLeaderInfo ,queryTaskApply} from '@/service/projectDos/my-project-detail/projectTasks';
import TaskInfo from "../task-info-comp";
import { useSelector, useParams } from 'umi';
import { BtnOperateType } from '@/constants/Enum';
import {getLocalStorage} from '@/utils/utils';
import "./index.less";

export interface ExtendTaskRequestDrawerProps {
  visible: boolean
  cancelEvent?: () => void
  successEvent?: () => void
  task: any,
  assignType: string
}

const ExtendTaskRequestDrawer: React.FC<ExtendTaskRequestDrawerProps> = (props) => {
  const { visible, cancelEvent, successEvent, task, assignType } = props;
  const {
    taskId,
    leaderName
  } = task ?? {}
  const { projectId } = useParams<{ projectId: string }>();
  const { basicProjectInfo } = useSelector((state: any) => state.projectTasks);
  const [form] = Form.useForm();
  const { leaderInfo } = basicProjectInfo;
  const [currentType, setCurrentType] = useState<any>(null);
  useEffect(() => {
    queryTaskApply(taskId).then((res)=>{console.log(res)})
    if (assignType == BtnOperateType.TASK_DELAY) {//延迟
      setCurrentType({
        title: '申请延期任务',
        successMessage:'任务延期申请成功',
        rule:extendTaskRequestRule.reasonDelay
      })
    } else if (assignType == BtnOperateType.TASK_HANDOVER) {//移交
      setCurrentType({
        title: '任务移交申请',
        successMessage:'任务移交申请成功',
        rule:extendTaskRequestRule.reasonTransfer
      })
    } else if (assignType == BtnOperateType.TASK_PAUSE) {//暂停
      setCurrentType({
        title: '申请暂停任务',
        successMessage:'任务暂停申请成功',
        rule:extendTaskRequestRule.reasonPause
      })
    } else if (assignType == BtnOperateType.TASK_RECOVERY) {//恢复
      setCurrentType({
        title: '申请恢复任务',
        successMessage:'任务恢复申请成功',
        rule:extendTaskRequestRule.reasonRecovery
      })
    }
  }, [assignType])
  const submitData = async () => {
    try {
      const formValues = form.getFieldsValue();
      const {applyDocument} = formValues;
      let applyContent: any = null,auditorId =  '';
      if(assignType == 'TASK_DELAY'){
        applyContent = moment(formValues.applyContent).format("YYYY-MM-DD");
      }
      if(assignType == 'TASK_HANDOVER'){
        applyContent = formValues.applyContent.id;
      }
      if(task.parent){
        auditorId = task.parent?.leaderId
      }else{
        auditorId = leaderInfo?.leaderId
      }
      const submitInfo = {
        ...formValues,
        taskId,
        type: assignType,
        applyContent,
        auditorId,
        applyDocument: applyDocument?.map((item: any) => item.id).join(),
      }
      // 调用成功后保存方法
      await taskApplyType(submitInfo)
      message.success(currentType.successMessage)
      successEvent?.()

    } catch (e) {
      console.error(e)
    }
  }
  const reviewPerson = useMemo(() => {
    if (task.parent) {
      return `${task.parent?.leaderName}`
    }
    return `${leaderInfo?.leaderName}`
  }, [taskId])

  return (
    <Drawer
      zIndex={1001}
      open={visible}
      title={<BdwReadonlySpan importantLevel="veryImportant">{currentType?.title}</BdwReadonlySpan>}
      width={700}
      onClose={() => cancelEvent?.()}
      destroyOnClose
      bodyStyle={{
        padding: '10px 20px'
      }}
      maskClosable={false}
    >
      <TaskInfo task={task} >
        <Form form={form} className='extend-task-request-drawer' onFinish={() => submitData()} preserve={false}>
          <Divider className='mt-16' />
          <div className='mt-16'>
            <BdwTitle>申请人</BdwTitle>
            <BdwReadonlySpan>{leaderName}</BdwReadonlySpan>
          </div>
          <div className='mt-16'>
            <BdwTitle>审核人</BdwTitle>
            <BdwReadonlySpan>{reviewPerson}</BdwReadonlySpan>
          </div>
          {
            assignType == 'TASK_DELAY' && <div className='mt-16'>
              <BdwFormItems label="申请延期的截止时间" required name="applyContent" rules={extendTaskRequestRule.applyExtendDate}>
                <BdwDatePicker />
              </BdwFormItems>
            </div>
          }

          {
            assignType == 'TASK_HANDOVER' && <div className='mt-16'>
              <BdwFormItems label='任务移交接收人' name='applyContent' required rules={extendTaskRequestRule.recipient}>
                <BdwChooseCompanyStaff
                  apiSrc={listTaskLeaderInfo}
                  extraParams={{ projectId }}
                />
              </BdwFormItems>
            </div>
          }

          <div className='mt-16'>
            <BdwFormItems label="理由说明" required name="applyRemark" rules={currentType?.rule}>
              <BdwTextarea placeholder='请输入' autoSize maxLength={512} onKeyDown={(e) => {
                if (e.keyCode === 13) {
                  e.nativeEvent.stopImmediatePropagation()
                }
              }} />
            </BdwFormItems>
          </div>
          <div className='mt-16'>
            <BdwFormItems label="附件资料" name='applyDocument'>
              <BdwUploadBtn />
            </BdwFormItems>
          </div>
          <div className='mt-16'>
            <Button type='primary' htmlType='submit' className='mr-16'>确认申请</Button>
            <Button onClick={() => cancelEvent?.()}>返回</Button>
          </div>
        </Form>
      </TaskInfo>

    </Drawer>
  )
}

export default ExtendTaskRequestDrawer;
