import React, { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON>, Divider, Drawer, Form, message, Spin } from "antd";
import {
  BdwUploadBtn,
  BdwFormItems,
  BdwReadonlySpan,
  BdwTextarea, BdwTitle,
  BdwFileShow
} from "@/components";
import { reviewTaskPauseRequestRule } from "./rute";
import { queryTaskApply, auditTaskApply } from '@/service/projectDos/my-project-detail/projectTasks';
import { useRequest } from 'ahooks';
import "./index.less";
import TaskInfo from "../task-info-comp";
import moment from "moment";
import {AuditType} from '@/constants/Enum';

export interface ReviewTaskPauseRequestDrawerProps {
  visible: boolean
  cancelEvent?: () => void
  successEvent?: () => void
  task: any,
}
const ReviewTaskRequestDrawer: React.FC<ReviewTaskPauseRequestDrawerProps> = (props) => {
  const { visible, cancelEvent, successEvent, task } = props;
  const { taskId } = task ?? {}
  const { data: auditInfo, loading } = useRequest<any>(() => queryTaskApply(taskId))

  const [form] = Form.useForm();
  const submitData = async (val: boolean) => {
    form.validateFields().then(async () => {
      try {
        const formValues = form.getFieldsValue();
        const {
          auditOpinion,
          auditDocument
        } = formValues;
        const submitInfo = {
          applyId: auditInfo.applyId,
          auditDocument: auditDocument?.map((item: any) => item.id).join(),
          auditOpinion,
          status:val?AuditType.APPROVED:AuditType.REJECTED
        }
        // 调用成功后保存方法
        await auditTaskApply(submitInfo)
        message.success(auditInfo?.applyTypeName + '审核完成')
        successEvent?.()
      } catch (e) {
        console.error(e)
      }
    })
  }

  return (

    <Drawer
      zIndex={1001}
      open={visible}
      title={<BdwReadonlySpan importantLevel="veryImportant">{auditInfo?.applyTypeName}申请审核</BdwReadonlySpan>}
      width={700}
      onClose={() => cancelEvent?.()}
      destroyOnClose
      bodyStyle={{
        padding: '10px 20px'
      }}
      maskClosable={false}
    >
      <Spin spinning={loading}>
        <TaskInfo task={task} >
          <Form form={form} className='review-task-pause-request-drawer' preserve={false}>
            <Divider className='mt-16' />
            {
              auditInfo?.applyTypeName == '任务延期' &&
              <div className='mt-16'>
                <BdwTitle>申请延期的截止时间</BdwTitle>
                <BdwReadonlySpan>{moment(auditInfo?.applyContent).format("YYYY-MM-DD")}</BdwReadonlySpan>
              </div>
            }
            <div className='mt-16'>
              <BdwTitle>申请人</BdwTitle>
              <BdwReadonlySpan>{auditInfo?.applicantName || '-'}</BdwReadonlySpan>
            </div>
            {
              auditInfo?.applyTypeName == '任务移交' &&
              <div className='mt-16'>
                <BdwTitle>任务移交接收人</BdwTitle>
                <BdwReadonlySpan>{auditInfo?.applyContent}</BdwReadonlySpan>
              </div>
            }
            {
              auditInfo?.applyRemark &&
              <div className='mt-16'>
                <BdwTitle>申请理由说明</BdwTitle>
                <BdwReadonlySpan>{auditInfo?.applyRemark}</BdwReadonlySpan>
              </div>
            }
            {
              auditInfo?.applyDocuments && auditInfo?.applyDocuments.length != 0 && 
              <div className='mt-16'>
                <BdwTitle>申请附件资料</BdwTitle>
                <BdwFileShow attachments={auditInfo?.applyDocuments}/>
              </div>
              
            }
            <div className='mt-16'>
              <BdwFormItems label="审核意见" required name="auditOpinion" rules={reviewTaskPauseRequestRule.auditOpinion}>
                <BdwTextarea placeholder='请输入' autoSize maxLength={512} onKeyDown={(e) => {
                  if (e.keyCode === 13) {
                    e.nativeEvent.stopImmediatePropagation()
                  }
                }} />
              </BdwFormItems>
            </div>
            <div className='mt-16'>
              <BdwFormItems label="附件资料" name='auditDocument'>
                <BdwUploadBtn />
              </BdwFormItems>
            </div>
            <div className='mt-16'>
              <Button type='primary' onClick={() => submitData(true)} className='mr-16'>同意{auditInfo?.applyTypeName?.split('任务').filter(i => i)[0]}</Button>
              <Button type='primary' danger onClick={() => submitData(false)} className='mr-16'>驳回申请</Button>
              <Button onClick={() => cancelEvent?.()}>返回</Button>
            </div>
          </Form>
        </TaskInfo>
      </Spin>

    </Drawer>

  )
}

export default ReviewTaskRequestDrawer;
