@import "../../../../styles/base.less";
@import "../../../../styles/scrollbar.less";
.ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot{
  background-color: transparent;
  border: 2px solid #5cb85c;
}
.ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot{
  background-color: transparent;
  border: 2px solid #428bca;
}
.ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon .ant-steps-icon-dot{
  background-color: transparent;
  border: 2px solid rgba(0, 0, 0, 0.25);
}
.ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-tail::after{
  background-color: rgba(0, 0, 0, 0.25);
}
.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-tail::after{
  background-color: rgba(0, 0, 0, 0.25);
}
.ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-tail::after{
  background-color: rgba(0, 0, 0, 0.25);
}
.ant-steps-dot .ant-steps-item-icon{
  width: 12px;
  height: 12px;
  line-height: 12px;
}
.ant-steps-dot .ant-steps-item-process .ant-steps-item-icon{
  width: 15px;
  height: 15px;
  line-height: 15px;
}
.ant-steps-dot .ant-steps-item-tail::after{
  margin-left: 14px;
}
.bdw-readonly-span{
  color: #333;
}
.task-process-info{
  height: 100%;
  overflow: hidden;
  .task-process-content{
    flex: 1;
    overflow-y: auto;
    color: #333;
    font-size: 13px;
    line-height: 16px;
  }
  .task-process-row-first .ant-steps-icon-dot,.task-process-row-first .ant-steps-item-tail{
    visibility: hidden;
  }
}
// 修改
.ant-timeline-item-label{
  width: 100px !important;
}
.ant-timeline-item-tail,.ant-timeline-item-head{left:120px !important;}
.ant-timeline-item-content {
  width:700px;margin:0 0 0 22%;
}
.bold{font-weight: 700;}
.timeLineBox{
  width:600px;
}
