import React, { useMemo } from "react";
import {
  BdwRow,
  BdwTitle
} from "@/components";
import "./index.less";
import "@/styles/base.less"
import { bdwShowTimeFunction } from '@/utils/utils'
import { useRequest } from "ahooks";
import ProjectDynamicItem from "../../../my-project-detail/projectOverview/projectDynamicItem";
import { HourglassOutlined } from "@ant-design/icons/lib";
import { useSelector } from 'umi';

interface TaskBasicInfo {
  task: any
}

const TaskProcessInfo: React.FC<TaskBasicInfo> = ({ task }) => {
  const { projectOverviewDetails } = useSelector((state: any) => state.projectOverview);
  const { id, projectId } = task ?? {};
  const params = {
    id: projectId,
    type: 'Task',
    taskId: id,
  }

  // const {data:taskProcessData}=useRequest(()=>getCourseTaskData(params),{
  //   manual:false,
  //   refreshDeps:[projectId,id],
  // })

  // const handleTaskData=useMemo(()=>{
  //   if(taskProcessData !=='undefined'){
  //     return taskProcessData
  //   }
  //   return []
  // },[taskProcessData])

  const processElement = projectOverviewDetails?.projectTaskNews?.map((item: any, index: number) => {
    return <div key={index}>
      <ProjectDynamicItem
        key={`${index}`}
        className='mt-16'
        personName={item.handlerName}
        title={item.eventMainContent}
        time={item.handleTime}
        attach={item.attachments}
        detail={item.eventSubContent} />
    </div>
  })
  return (
    <BdwRow type='flex' className='mt-16 pb-40 flex-column task-process-info'>
      <div>
        <HourglassOutlined className='mr-5' />
        <BdwTitle>
          <span>任务历程</span>
        </BdwTitle>
      </div>
      <div className='task-process-content flex-1 mt-16'>
        <div className='timeLineBox'>
          {processElement}
        </div>
      </div>
    </BdwRow>
  )
}

export default TaskProcessInfo;
