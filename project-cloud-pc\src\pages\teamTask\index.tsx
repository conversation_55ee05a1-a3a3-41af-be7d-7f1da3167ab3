import React, { useEffect, useState } from 'react';
import { Spin} from 'antd';
import ProjectTasksTable from './projectTasksTable';
import { useDispatch, connect, useSelector } from 'umi';
import { HeaderBar } from "@/components";
import './index.less';

interface ITeamTaskProps {
	loading: any
	projectId?: string,
	type?: string,
}

// 团队任务
const TeamTasks: React.FC<ITeamTaskProps> = ({ 
	loading,
	projectId,
	type
}) => {
	const dispatch = useDispatch();
	const [relatedMeFlag, setRelatedMeFlag] = useState(false)  // 与我相关是否开启
	const { projectList } = useSelector((state: any) => state.myTask);  // 所有的项目数据
	const [currentProjectId, setCurrentProjectId] = useState<string | null>(null);  // 当前选中的项目id，null为所有项目

	useEffect(() => {
		if(!type){
			dispatch({ type: 'myTask/fetchTeamTaskList'	})  // 获取团队任务数据
			dispatch({ type: 'myTask/fetchTeamProjectList' })  // 获取项目切换数据
		}
		
	}, [])

	// 项目切换功能
	useEffect(() => {
		setRelatedMeFlag(false)
		if(projectId){
			dispatch({ 
				type: 'myTask/fetchTeamTaskList',
				payload: {
					projectId: projectId || currentProjectId
				}
			}) 
		}
		 // 获取团队任务数据
	}, [projectId, currentProjectId])

	return (
		<Spin spinning={loading}>
			<div className='project-task-warper'>
				<HeaderBar 
					viewList={[]}
					relatedToMeFlag
					relatedMeIsChecked={relatedMeFlag}
					handleRelatedToMeChange={(flag: boolean) => setRelatedMeFlag(flag)}
					projectList={!projectId && projectList || []} // 外部引入此组件时，无切换项目功能
					onViewChange={(checkedView: string) => {}}
					onProjectChange={(checkedProject: string) => setCurrentProjectId(checkedProject)}
				/>
				<ProjectTasksTable relatedMeFlag={relatedMeFlag} projectId={projectId || currentProjectId} />
			</div>
		</Spin>
	)
}

export default connect(({ loading }: any) => ({
  loading: loading.effects['myTask/fetchTeamTaskList']
}))(TeamTasks);
