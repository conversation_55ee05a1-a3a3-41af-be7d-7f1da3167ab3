import React, { useEffect } from 'react';
import { Input, Menu } from 'antd';
import { withKeydown } from '@/components/withKeydown';
import { withClickOutSide } from '@/components/withClickOutSide';
import { useSelector, useDispatch } from 'umi';
import { BdwTable, BdwTableHeaderSearchComponent, BdwRow } from "@/components";
import TableRowInformation from '../tableRowInformation';
import Highlighter from "react-highlight-words";
import styled from 'styled-components';
import './index.less';

interface ITeamTaskTableProps {
	relatedMeFlag: boolean
	projectId: string | null
}

// @ts-ignore
const FixedMenu = withClickOutSide(styled(Menu)`
  position: fixed;
  z-index: 1000;
  box-shadow: 0 0 5px rgba(0,0,0,0.1) !important;
`);

const WrapperComponent = withKeydown(BdwRow);

// 团队任务table
const TeamTasksTable: React.FC<ITeamTaskTableProps> = ({
	projectId,
	relatedMeFlag
}) => {
	const dispatch = useDispatch();
	const { userInfo } = useSelector((state: any) => state.commonTask);
	const { taskLists, taskInfo, isAddTask, editable, functionality, cTaskId } = useSelector((state: any) => state.projectTasks);
	const { filterInfo: filterObj } = useSelector((state: any) => state.myTask);
	const { expandedRowKeys, teamTaskList } = useSelector((state: any) => state.myTask);

	// 初始化展开所有的节点
	useEffect(() => {
		if (teamTaskList?.length) {
			let orgIdsList = handleGetOrgId(teamTaskList);
			dispatch({
				type: 'myTask/setExpandedRowKeys',
				payload: orgIdsList
			})
		}
	}, [teamTaskList])

	// 处理只看与我相关
	useEffect(() => {
		if (Object.keys(userInfo).length) {
			dispatch({
				type: 'myTask/handleSetTeamTaskList',
				payload: {
					projectId,
					userInfo,
					relatedMeFlag
				}
			})
		}
	}, [relatedMeFlag, userInfo])

	// 获取到orgId集合，用于设置默认展开项
	const handleGetOrgId = (teamTaskList: any[]) => {
		let orgIds: any[] = []; // 存放所有orgId
		
		for (let i = 0; i < teamTaskList.length; i++) {
			teamTaskList[i].orgId && orgIds.push(teamTaskList[i].orgId);
			if (teamTaskList[i].children) {  // 有children则继续合并children下的ids
				orgIds = orgIds.concat(handleGetOrgId(teamTaskList[i].children))
			}
		}
		return orgIds;
	}

	// 任务表格表头项
	const TaskTableColumns = [
		{
			title: <BdwTableHeaderSearchComponent title="进程标题">
				<Input placeholder="请输入进程标题进行检索"
					className='no-border-input ant-input-cover-style wordPt'
					value={filterObj?.name}
					onChange={(e) => {
						// 搜索功能触发
						dispatch({
							type: 'myTask/setFilterTableData',
							payload: {
								name: e.target.value,
							}
						})
					}}
				/>
			</BdwTableHeaderSearchComponent>,
			dataIndex: "name",
			ellipsis: true,
			render: function TitleColumn(value: string, record: any) {
				return <span className='task-name-show'>
				<Highlighter
					highlightClassName="title-highlight"
					searchWords={[filterObj?.name]}
					autoEscape
					textToHighlight={record.name || ""}
				/>
			</span>
			},
		},
		{
			title: <BdwTableHeaderSearchComponent title="项目名称">
				<Input className='no-border-input ant-input-cover-style wordPt'
					placeholder="请输入项目名称"
					value={filterObj?.projectName}
					onChange={(e) => {
						// 搜索功能触发
						dispatch({
							type: 'myTask/setFilterTableData',
							payload: {
								projectName: e.target.value,
							}
						})
					}}
				/>
			</BdwTableHeaderSearchComponent>,
			dataIndex: 'projectName',
			ellipsis: true,
			width: 200,
			render: (value: any, record: any) => {
				return <div style={{minWidth:'200px'}}>
					<Highlighter
					highlightClassName="title-highlight"
					searchWords={[filterObj?.projectName]}
					autoEscape
					textToHighlight={record.projectName ?? ""}
				/>
				</div>
			}
		},
		{
			title: <BdwTableHeaderSearchComponent title="工期(天)">
			</BdwTableHeaderSearchComponent>,
			dataIndex: 'executionCycle',
			width: 70,
			ellipsis: true,
			render: (value: any, record: any) => {
				return <div style={{minWidth:'70px'}}>{value}</div>
			}
		},
		{
			title: <BdwTableHeaderSearchComponent title="开始时间">
			</BdwTableHeaderSearchComponent>,
			dataIndex: 'startTime',
			width: 120,
			ellipsis: true,
			render: (value: any, record: any) => {
				return <span>{value}</span>
			}
		},
		{
			title: <BdwTableHeaderSearchComponent title="截止时间">
			</BdwTableHeaderSearchComponent>,
			dataIndex: 'endTime',
			width: 120,
			ellipsis: true,
			render: (value: any, record: any) => {
				return <span>{value}</span>
			}
		},
		{
			title: <BdwTableHeaderSearchComponent title="状态">
			</BdwTableHeaderSearchComponent>,
			width: 120,
			ellipsis: true,
			dataIndex: 'statusName',
			render: (value: any, record: any) => {
				return value ? <span className='color-5c5c5c f-12 status-span' >{value}</span> : null
			}
		},
		{
			title: <BdwTableHeaderSearchComponent title="日报">
			</BdwTableHeaderSearchComponent>,
			dataIndex: 'process',
			width: 100,
			ellipsis: true,
		},
	];

	// console.log(projectId, userInfo, '<<<relatedMeFlag');
	
	return (
		<BdwRow type='flex' className='project-task-table-container'>
			<div className={`table-details ${taskInfo?.taskId ? 'table-details-w' : ''}`}>
				<WrapperComponent>
					<div
						style={{
							height: '90vh',
							overflowY: 'scroll'
						}}
					>
						<BdwTable
							className='project-task-table'
							pagination={false}
							expandable={{
								defaultExpandAllRows: true,
								expandedRowKeys,  // 所有展开的keys
								onExpandedRowsChange: (keysList) => {
									// 设置点击展开的key
									dispatch({
										type: 'myTask/setExpandedRowKeys',
										payload: keysList
									})
								},
							}}
							size="small"
							rowKey="orgId"
							columns={TaskTableColumns}
							dataSource={teamTaskList ?? []}
							useLocalData
							sticky
							// scroll={{ x: true }}
							showPages={false}
							rowSelection={{
								selectedRowKeys: cTaskId && [cTaskId] || [],
								type: 'radio',
								columnWidth: 1,
								renderCell: () => {
									return null;
								},
								checkStrictly: true,
							}}
							onRow={(currentRowInfo: any) => ({
								onClick: async () => {
									// 设置点击行的背景色
									dispatch({
										type: 'projectTasks/setCTaskId',
										payload: currentRowInfo?.orgId
									})
									// 点击任务行，设置taskInfo
									if (currentRowInfo.taskId) {
										dispatch({
											type: 'projectTasks/renewTaskInfo',
											payload: currentRowInfo
										})
									} else {  // 点击到公司部门行，清空taskInfo
										dispatch({
											type: 'projectTasks/renewTaskInfo',
											payload: null
										})
									}
								},
								onDoubleClick() { },
								onContextMenu(e) { },
							})}
						/>
					</div>
				</WrapperComponent>
			</div>

			{/* 右侧任务信息 */}
			{taskInfo?.taskId && <TableRowInformation taskInfo={taskInfo} />}
		</BdwRow>
	)
}

export default TeamTasksTable;
