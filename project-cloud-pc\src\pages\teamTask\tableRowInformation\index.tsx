import React, { useState, useEffect } from 'react';
import { Tabs } from 'antd';
import { projectTasksRowTab, projectTasksRowTabKey } from '@/constants/Enum';
import ProgressReport from './progressReport';
import TaskHistory from './taskHistory';
import ResultEvaluation from './resultEvaluation';
import TaskInformation from './taskInformation';
import StandardReference from './standardReference';
import { getTaskInfo } from "@/service/projectDos/my-project-detail/projectTasks";
import './index.less';

interface ITeamTaskProps {
	taskInfo: any
}

const TableRowInformation: React.FC<ITeamTaskProps> = ({
	taskInfo
}) => {
	const [activeTabKey, setActiveTabKey] = useState<any>(projectTasksRowTabKey.TASK_INFORMATION);
	const [taskBaseInfo, setTaskBaseInfo] = useState<any>(null)

	// 查询任务基本信息
	useEffect(() => {
		if (taskInfo) {
			getTaskInfo(taskInfo.taskId).then((res) => {
				setTaskBaseInfo(res)
			})
		}
	}, [taskInfo])

	const TabsItem: any[] = projectTasksRowTab.map((item: any, index: number) => {
		let children: any;
		console.log(item, '<<<<item');
		switch (item.key) {
			case projectTasksRowTabKey.TASK_INFORMATION://任务信息
				children = <TaskInformation taskBaseInfo={taskBaseInfo} />;
				break;
			case projectTasksRowTabKey.TASK_HISTORY://任务历程
				children = <TaskHistory />;
				break;
			case projectTasksRowTabKey.PROGRESS_REPORT://进程汇报
				children = <ProgressReport />;
				break;
			// todo 标准参考
			case projectTasksRowTabKey.STANDARD_REFERENCE://标准参考
				children = <StandardReference />;
				break;
			case projectTasksRowTabKey.RESULT_EVALUATION://结果评价
				children = <ResultEvaluation />;
				break;
		}
		return {
			...item,
			children
		}
	})

	return (
		<div className='table-row-information-1'>
			<Tabs
				className='table-row-information-tabs'
				items={TabsItem}
				onChange={(e) => setActiveTabKey(e)}
				activeKey={activeTabKey}
				destroyInactiveTabPane
			/>
		</div>
	)
}

export default TableRowInformation;
