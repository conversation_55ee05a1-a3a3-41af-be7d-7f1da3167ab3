import React, { useState } from "react";
import { Button, Form, message, Modal, Spin } from "antd";
import {
  BdwUploadBtn,
  BdwFormItems,
  BdwInput,
  BdwReadonlySpan,
  BdwRow,
  BdwTextarea,
  BdwTitle,
  EnumRadio
} from "@/components";
import { projectReview } from '@/service/projectDos/my-project-detail/projectTasks';
import { useBoolean } from "ahooks";
import FormRules from "./form-rules"
import { AuditResult, ProjectFileTypeAccepts,ProjectAuditTaskEnum } from "@/constants/Enum";

interface TaskAuditProps {
  visible: boolean,
  sureFun: () => void
  cancelFun: () => void
  standScore?: number
  taskId: string | number
  reviewId?: string
}

const TaskAudit: React.FC<TaskAuditProps> = (props) => {
  const { visible = false, sureFun, cancelFun, standScore, taskId,reviewId } = props;
  const [auditResult, setAuditResult] = useState<"APPROVED" | "REJECTED">("APPROVED");
  const [isRequesting, { setFalse: requestFinish, setTrue: requestStart }] = useBoolean(false);
  const [form] = Form.useForm();
  const audit = () => {
    // 代表检验通过
    form.validateFields().then(async () => {
      try {
        requestStart();
        const formValue = form.getFieldsValue();
     
        const { auditOpinion, assessmentScore, auditDocument } = formValue;
        const auditDocumentId = auditDocument?.map((item: any)=> item.id).join();
        const submitInfo = {
          reviewId,
          auditOpinion,
          auditDocument:auditDocumentId,
          assessmentScore,
          status:auditResult
        }
        await projectReview(submitInfo);
        message.success("信息提交成功")
        sureFun();
      } catch (e) {
      } finally {
        requestFinish();
      }
    });
  }

  const actualScoreCheck = [
    () => ({
      validator(rule: any, value: string) {
        const handleValue = parseFloat(value);
        if (!isNaN(handleValue) && handleValue >= 0 && handleValue <= standScore!) {
          return Promise.resolve()
        }
        return Promise.reject(new Error('必须为数字，要求大于0，且不能大于标准分'));
      }
    })
  ]

  return (
    <Modal getContainer={false} open={visible} title='任务评审' className="project-task-review-modal" width={550} onOk={audit} onCancel={() => cancelFun()} footer={
      [
        <Button key='cancel' onClick={() => cancelFun()}>取消</Button>,
        <Button key='sure' type='primary' onClick={audit}>确认</Button>,
      ]
    }>
      <Spin spinning={isRequesting}>
        <Form className='bdw-form' form={form}>
          <BdwFormItems className='mb-16' label="汇报结果"  >
            <EnumRadio enumObj={ProjectAuditTaskEnum} defaultValue="APPROVED" value={auditResult} onChange={(e) => setAuditResult(e.target.value)} />
          </BdwFormItems>
          {
            auditResult === "APPROVED" && !!standScore && standScore !== 0 &&
            <BdwRow className='mb-16' type='flex'>
              <div className='flex-1'>
                <BdwTitle>标准分</BdwTitle>
                {
                  standScore ? (
                    <BdwReadonlySpan className='mr-5 h-32'>{standScore}分</BdwReadonlySpan>
                  ) : (
                    <BdwReadonlySpan className='mr-5 h-32'>暂未设置</BdwReadonlySpan>
                  )
                }

              </div>
              <BdwFormItems style={{width:'260px'}} initialValue={0} className='flex-1 ml-16' label="考核分" required name="assessmentScore" rules={actualScoreCheck}>
                <BdwInput type="number" bordered={false}  min={0} suffix={<span>分</span>} />
              </BdwFormItems>
            </BdwRow>
          }
          <BdwFormItems
            className='mb-16'
            label="评审意见"
            required={auditResult === "REJECTED"}
            name="auditOpinion"
            rules={[{ required: auditResult === "REJECTED", ...FormRules.taskAudit.assignRemark }]}>
            <BdwTextarea autoSize placeholder="请输入评审意见" maxLength={512} onKeyDown={(e) => {
              if (e.keyCode === 13) {
                e.nativeEvent.stopImmediatePropagation()
              }
            }} />
          </BdwFormItems>
          {
            auditResult !== "APPROVED" &&
            <BdwFormItems label="附件资料" name="auditDocument">
              <BdwUploadBtn />
            </BdwFormItems>
          }
        </Form>
      </Spin>
    </Modal>
  )
}

export default TaskAudit
