@import "../../../../../../styles/base.less";

.evaluation-criterion-readonly {
  .back-show-info{
    color: #c5c8cd;
    font-size: 12px;
  }
  .flex{
    align-items: center;
  }
  padding-top: 16px;
  padding-right: 16px;
  .evaluation-must-file-item {
    padding-left: 4px;
    margin-top: 8px;
    color: @help;
  }
  //.bdw-standard-score-readonly {
  //  line-height: 26px;
  //}
  .assign-remark{
    word-break: break-all;
  }
}
.content-exist-color{
  color: @title;
}
.bdw-title-row-wrapper{
  height: 16px;
  display: flex;
  align-items: center;
  line-height: 1.5715;
  .bdw-title-row{
    width: 75px;
    height: 16px;
    font-size: 12px;
    color: #5c5c5c;
    line-height: 1.5715;
    padding-left: 3px;
    text-align: justify;
    text-align-last: justify;
    // 设置两端对齐兼容火狐，IE
    word-break: break-all;
    text-justify: distribute;
    white-space: pre-line;
    i{
      display: inline-block;
      width: 100%;
    }
  }

}
.file-show-content{
  width: 95% !important;
}
.line-height-same{
  line-height: 1.5715;
}
.line-height-32{
  line-height: 32px;
  font-size: 13px;
}
//.bdw-upload-file-item-content{
//  margin-left: 4px;
//}
.evaluation-criterion-readonly .evaluation-must-file-item{
  margin-top: 0;
}
.ec-wrapper{
  .flex{
    align-items: center;
  }
}

