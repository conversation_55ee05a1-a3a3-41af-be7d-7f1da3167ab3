.standard-reference-wrapper {
  .show{
    display: block;
  }
  .hidden{
    display: none;
  }
  // padding: 20px 16px;
  height: 100%;
  padding: 20px 16px 48px 16px;
  position: relative;
  display: flex;
  flex-direction: column;
    //   样式覆盖
  .ant-select {
    .ant-select-selection-placeholder{
        font-size: 13px;
    }
  }

  .ant-form {
    overflow: auto;
  }

  .split-dash-wrapper {
    position: relative;
    margin-bottom: 20px;
    width: 100%;
    height: 2px;
    background: linear-gradient(#fff, #fff) padding-box, repeating-linear-gradient(-45deg, #eaecef 0, #eaecef 5px, #fff 0, #fff 8px);
    border-bottom: 1px dashed transparent;
  }


  .basic-information-btn {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 48px;
    display: flex;
    align-items: center;
    box-shadow: 0px -1px 3px #00000014;

    .ant-btn.ant-btn-primary:hover {
      border-color: #2b6bff !important;
    }
  }

  .title-label {
    height: 48px;
    min-height: 48px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: 16px;

    .label {
      font-size: 12px;
      color: #666;

      &::after {
        content: '*';
        padding-left: 4px;
        color: #0275d8;
        font-size: 12px;
      }
    }
  }

  .no-edit {
    .label {
      &::after {
        content: '';
      }
    }
  }

  .title-name {
    min-height: 27px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgb(211, 211, 211);
    font-size: 13px;
    font-weight: bold;
  }

  .no-edit {
    .bdw-title {
      &::after {
        content: '';
      }
    }
  }

  .add-edit-standard-wrapper {
    .form-block-item {
      border-radius: 4px;
      border: 1px solid #f3f3f4;
      padding: 16px;
      margin-bottom: 16px;
      position: relative;

      .fold-unfold {
        position: absolute;
        right: 16px;
        top: 16px;
        display: flex;
        color: #8c8c8c;
        font-size: 12px;
        cursor: pointer;
      }

      .bdw-title {
        &::after {
          color: #0275d8;
        }
      }

      .ant-form-item {
        margin-top: 5px;
        margin-bottom: 16px;
      }

      .interpretation-label {
        font-size: 12px;
        margin-bottom: 5px;
        color: #666;
        display: flex;
        justify-content: space-between;

        .label-left {
          display: flex;

          .add-interpretation {
            cursor: pointer;
            margin-left: 20px;
            color: #2b6bff;
            position: relative;
            padding-left: 25px;

            img {
              position: absolute;
              left: 0;
              top: 50%;
              transform: translate(0, -50%);
            }
          }
        }

        .label-right {
          display: flex;
          width: 220px;
        }

      }

      .upload-type-readonly {
        padding-top: 10px;
        margin-bottom: 5px;

        .u-t-r-label {
          font-size: 13px;
          margin-bottom: 5px;
          font-weight: bold;
        }

        .u-t-r-content {
          min-height: 70px;
          img{
              width: 90px;
              height: 90px;
              margin: 0 10px 10px 0;
          }

          .empty-content {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #c5c8cd;
            img{
                width: 32px;
                height: 32px;
                margin-right: 5px;
                margin-bottom: unset;
            }
          }
        }
      }

      .upload-type-readonly:last-child {
        margin-bottom: unset;
      }

      .select-container {
        display: flex;
        flex-direction: column;

        .select-confirm-wrapper {
          display: flex;
          justify-content: space-between;
          width: 100%;


        }
      }

      .ant-input,
      .ant-select {
        border-color: rgb(211, 211, 211);
      }

      .ant-select {
        border-bottom: 1px solid rgb(211, 211, 211);
        margin-bottom: 5px;

        .ant-select-selector {
          padding: 0;
        }
        
      }

      .bdw-paste-picture-content {
        min-height: 80px !important;
      }

      .interpretation {
        textarea {
          min-height: 80px !important;
        }

      }

      .bdw-paste-picture-content {
        border-style: solid;
        border-color: rgb(211, 211, 211);
      }
      .rich-details-wrapper{
        .detail-info-word{
          font-size: 13px;
          margin-bottom: 8px;
        }

          img{
            width: 100px;
            height: 100px;
            margin: 0 10px 10px 0;
          }
        
        
      }
    }

    .continue-add-standard {
      width: 160px;
      height: 28px;
      color: #2b6bff;
      border-radius: 4px;
      border: 1px solid #2b6bff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      margin-top: 25px;
      margin-bottom: 20px;
      cursor: pointer;
    }

  }

  
  .readonly-confirm {
    border-radius: 4px;
    border: 1px solid #f3f3f4;
    padding: 16px;
    margin-bottom: 16px;

    .readonly-confirm-item {
      font-size: 12px;
      height: 24px;
      align-items: center;
      min-width: 180px;

      .c-label {
        color: #666;
      }
    }
  }
  .confirm-person-wrapper {
    
    .confirm-person-wrapper-container {
      border-radius: 4px;
      border: 1px solid #f3f3f4;
      padding: 16px;
      margin-bottom: 16px;
      position: relative;

      .ant-select-selection-item {
        background: none !important;
        border: none !important;
      }

      .choose-confirm-person-wrapper {
        position: relative;

        // width: 220px;
        // height: 50px;
        .choose-type-p-c {}

        .ant-select {
          width: 100%;
          border-bottom: 1px solid #c0c0c0;

        }

        .name-pos-wrapper {
          position: absolute;
          width: 100%;
          height: 30px;
          left: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #333;

          .select-name {
            font-size: 12px;
            margin-right: 10px;
          }
        }

      }
    }

    .con-no-mb {
      .ant-form-item {
        margin-bottom: unset;
      }
    }

    .select-confirm-person {
      .ant-select-selection-item {
        // color: #fff;
        margin-right: 10px;
        margin-bottom: 3px;
        background: #f5f5f5 !important;
      }

      .ant-select-selector {
        height: unset !important;
        min-height: 30px;
      }
    }

    .continue-add-standard {
      width: 100px;
      height: 28px;
      color: #2b6bff;
      border-radius: 4px;
      border: 1px solid #2b6bff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      margin-top: 25px;
      margin-bottom: 20px;
      cursor: pointer;
    }
  }




}

.fold-change-wrapper {
  display: flex;
  align-items: center;
  user-select: none;

  .label {
    font-size: 12px;
    color: #8c8c8c;
  }

  .icon {
    height: 16px;
    width: 16px;
    border: 1px solid #e4e4e4;
    margin-left: 5px;
    display: flex;
    align-items: center;
    justify-content: center;

    .down-icon {
      transform: rotate(-180deg);
    }

    .up-icon {
      transform: rotate(90deg);
    }

  }
}

.echo-text {
  font-size: 13px;
  color: #000;
}

.details-show {
  font-size: 13px;
  color: #000;

  .details-words {
    margin-bottom: 10px;
  }

  img {
    width: 90px;
    height: 90px;
    margin-right: 10px;
  }
  
}
