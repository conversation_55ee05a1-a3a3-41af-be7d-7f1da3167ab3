/**
 * @description 任务历程
 * <AUTHOR>
 * @date 2023-11-07 11:52:04
*/
import React, { useEffect, useMemo } from "react";
import { useRequest } from "ahooks";
import { message, Spin, Timeline, Avatar } from "antd";
// import Task from "@/pages/project-detail/project-mission/model/Task";
import { MenuItemShowNameIcon, BdwReadonlySpan, BdwRow } from "@/components";
import { viewTaskNews } from '@/service/projectDos/my-project-detail/projectTasks';
import { useSelector } from 'umi';
import moment from "moment";
import './index.less';

interface TaskProcessProps {
    task?: any
    updateTask?: any
}

interface ProjectDynamicItemProps {
    title?: string
    time?: string
    detail?: string
    className?: string
    personName?: string
    attach?: string[],
    handlerAvatar?: string
}

const { Item } = Timeline;
const TaskProcessDetail: React.FC<ProjectDynamicItemProps> = (props) => {
    const { title, time, detail = "", personName = "", attach = [], handlerAvatar = '' } = props;

    return <div className='left-label-flex'>
        <Item label={
            <BdwRow type='flex'>
                <div className='flex-1'>
                    <BdwReadonlySpan>{moment(time).format("MM-DD HH:mm")}</BdwReadonlySpan>
                </div>
            </BdwRow>
        }
            dot={
                <div style={{ 'textAlign': 'center', 'fontSize': '14px' }}>
                    {
                        handlerAvatar ? <Avatar style={{ width: '22px', height: '22px' }} src={handlerAvatar} /> :
                            <MenuItemShowNameIcon name={personName} />
                    }
                </div>
            }
        >
            <BdwRow>
                <BdwReadonlySpan>

                    <span className='mr-5 fontWed'>{personName}</span>
                    <span>{title}</span>
                    <span className='fontWed'>{detail ? ` ${detail}` : ''}</span>
                </BdwReadonlySpan>
                {
                    attach && attach.length > 0 &&
                    <div className='attachColor'>
                        {/* <BdwFileShow
              fileIds={attach}
              // @ts-ignore
              showIcon={false}
              downLoadEvent={DOWNLOAD_FILE}
            /> */}
                    </div>
                }
            </BdwRow>
        </Item>
    </div>
}

const TaskHistory: React.FC<TaskProcessProps> = (props) => {
    const { taskInfo } = useSelector((state: any) => state.projectTasks);
    // 通过taskInfo获取到taskId，用以查询任务历程信息
    const { data: taskProcessData, loading ,run:runTaskProcessData } = useRequest(() =>  viewTaskNews(taskInfo.taskId), {
        manual: true,
        refreshDeps: [taskInfo.taskId],
    })
    useEffect(()=>{
        if(taskInfo.taskId && taskInfo.taskId !== 'newAdd'){
            runTaskProcessData();
        }
    },[taskInfo])
    const handleTaskData: any = useMemo(() => {
        if (taskProcessData !== 'undefined') {
            return taskProcessData
        }
        return []
    }, [taskProcessData])
    const processElement = handleTaskData?.map((item: any, index: number) => {
        return <BdwRow key={`${index}`}>
            <TaskProcessDetail
                key={`${index}`}
                className='mt-16'
                personName={item.handlerName}
                title={item.eventMainContent}
                time={item.createTime}
                attach={item.attachments}
                detail={item.eventSubContent}
                handlerAvatar={item.handlerAvatar}

            />
        </BdwRow>
    })
    return (
        <Spin spinning={loading}>
            <div className='taskProcessBox1'>
                <Timeline>
                    {processElement}
                </Timeline>
            </div>
        </Spin>

    )
}

export default TaskHistory
