import React, { useEffect, useState } from 'react';
import { Divider, Checkbox } from 'antd';
import { BdwLeaveMessageReadonly, BdwFileShow } from '@/components';
import { cloneDeep, isString, isArray, isObject, rest } from 'lodash';
import { useDispatch } from 'umi'
import { getRichImgSrc, getText, getLocalStorage } from '@/utils/utils';
import ReactZmage from "react-zmage";
import './index.less';

interface ITaskInfoProps {
	taskBaseInfo: any
}

const TaskInformation: React.FC<ITaskInfoProps> = ({
	taskBaseInfo
}) => {
	const dispatch = useDispatch();
    let remark;

	if(taskBaseInfo?.taskExplain){
		try{
			remark = JSON.parse(taskBaseInfo.taskExplain);
		}catch{
			remark = [{
			value: taskBaseInfo.taskExplain,
			fileList:[]
		  }]
		}
	  }

	const showHasSaveTaskRemark = remark && isArray(remark) && remark?.map((item: any, index: number) => {
        const detailsArr = getRichImgSrc(item.value);
        return <div key={index}>
            <div className='detail-info-word'>{getText(item.value)}</div>
            {
                detailsArr.length > 0 && detailsArr.map((i: any, index: number) => (
                    // @ts-ignore
                    <ReactZmage key={index} backdrop='rgba(0,0,0,0.7)' src={i} />
                ))
            }
        </div>
    })

	return (
		<>
			{
				taskBaseInfo ? 
				<div className='basic-information-container'>
					<p>
						<span className='title'>名称：</span>
						<span>{taskBaseInfo?.name || '--'}</span>
					</p>
					<p>
						<div className='title'>任务说明：</div>
						<div className='project-information-add'>
							{isArray(remark) && remark?.length !== 0 ? showHasSaveTaskRemark : <span className='back-show-info'>暂无任务说明</span>}
						</div>
					</p>
					<p className='content_line'>
						<span className='title'>负责人：</span>
						<span>{taskBaseInfo?.leaderName ? <div className='form-content'>{taskBaseInfo?.leaderName}</div> : <div className='back-show-info'>未选择任务负责人</div>}</span>
					</p>
					<p className='content_line'>
						<span className='title'>关联任务：</span>
						<div>
						{
								taskBaseInfo?.associationTaskId && taskBaseInfo?.associationTaskName 
								? <div className='form-content'>{taskBaseInfo?.associationTaskName}</div> 
								: <div className='back-show-info'>暂无关联任务</div>
							}
						</div>
					</p>
					<Divider type='horizontal' />
					<p>
						<Checkbox checked={taskBaseInfo?.associationTaskEndTime || true} disabled>连接关联任务截至时间</Checkbox>
					</p>
					<p className='content_line'>
						<span className='title'>执行周期：</span>
						{
							taskBaseInfo?.executionCycle ?
							<span>{taskBaseInfo?.executionCycle}</span> :
							<span className='back-show-info'>未设置</span>
						}
					</p>
					<p>
						<span className='title'>工作调整：</span>
						<>
							<div className='back-show-info flex' >
								{taskBaseInfo?.askLeaveDays && <div className='form-content mr-5' >请假{taskBaseInfo.askLeaveDays}天</div>}
								{taskBaseInfo?.compensatoryLeaveDays && <div className='form-content mr-5'>补休{taskBaseInfo.compensatoryLeaveDays}天</div>}
								{taskBaseInfo?.depositRestDays && <div className='form-content mr-5' >存休{taskBaseInfo.depositRestDays}天</div>}
								{(!taskBaseInfo?.depositRestDays && !taskBaseInfo.compensatoryLeaveDays && !taskBaseInfo.askLeaveDays) && <div className='back-show-info'>暂无工作调整</div>}
							</div>
						</>
					</p>
					<div className='time_wrapper'>
						<p className='time_container'>
							<span className='title'>开始时间：</span>
							<span>
								{taskBaseInfo?.startTime ? <div className='form-content'>{taskBaseInfo?.startTime}</div> : <div className='back-show-info'>未设置</div>}
							</span>
						</p>
						<p className='time_container'>
							<span className='title'>截止时间：</span>
							<span>
								{taskBaseInfo?.endTime ? <div className='form-content'>{taskBaseInfo?.endTime}</div> : <div className='back-show-info'>未设置</div>}
							</span>
						</p>
					</div>
					<p>
						<span className='title'>相关资料：</span>
						{
							taskBaseInfo?.beforeDocument?.length >= 1 ?
									<>
										<Divider dashed={true} />
										<BdwFileShow attachments={taskBaseInfo?.beforeDocument} onChange={(data: any) => {
												dispatch({
													type: 'projectTasks/renewTaskInfo',
													payload: {
														beforeDocument: data,
													}
												})
										}} enableEdit={false} />
									</>
								: <div className='back-show-info'>未设置</div>
						}
					</p>
				</div> : <span>加载中</span>
			}
		</>
	)
}

export default TaskInformation;
