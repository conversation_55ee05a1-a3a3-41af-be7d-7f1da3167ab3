import React from "react";
import bdwConfig from '@/utils/bdwConfig';
import request from "@/utils/request";

const {urlPrefix} = bdwConfig

export const UPLOAD_FILE_URL = `/cloud-base-service/common/attachment/save-batch`

/**
 *  【上传文件】 有些地方不用组件进行上传，所以需要用到这个
 * */
export const uploadFile = (data: any) => {
  return request.post(UPLOAD_FILE_URL, {
    requestType: "form",
    data
  })
}

/**
 *  【获取文件信息】
 * */
export const GET_FILE_INFO_URL = `${urlPrefix.common}/attachment`
export const getFileById = (fileId: React.Key) => {
  return request.get(`${GET_FILE_INFO_URL}/${fileId}`);
}

/**
 *  【批量获取文件信息】
 * */
export const getFilesByIds = (fileIds: Array<string | number> = []) => {
  return Promise.all(fileIds ? fileIds?.map((item) => getFileById(item)) : [])
}

// 下载附件

export const DOWNLOAD_FILE= `${urlPrefix.bdw}/modules/project/ProjectManagementAction/getFile.do`

