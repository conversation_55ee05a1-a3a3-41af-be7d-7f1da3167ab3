import request from '@/utils/requestTool';
import { BASE_PATH } from '@/constants/static';
import { stringify } from 'qs';


//查询近期项目
export async function listRecentProjectApi(params: any) {
    return request(`${BASE_PATH}/project-dos/project-management/list-recent-project?${stringify(params)}`);
}
//统计近期项目
export async function recentProjectStatisticsApi() {
    return request(`${BASE_PATH}/project-dos/project-management/recent-project-statistics`);
}
