import request from '@/utils/requestTool';
import { BASE_PATH } from '@/constants/static';
import { stringify } from 'qs';

interface ITaskParamsProps {
  count: string
  name: string
  page: string
}

// 查询tab栏状态数据
export async function queryTaskStatusList() {
  return request(`${BASE_PATH}/project-dos/my-task/status`);
}

// 查询已完成的任务
export async function queryFinishedTaskList(params: ITaskParamsProps) {
  return request(`${BASE_PATH}/project-dos/my-task/list-finished?${stringify(params)}`);
}

// 查询进行中的任务
export async function queryInProgressTaskList(params: ITaskParamsProps) {
  return request(`${BASE_PATH}/project-dos/my-task/list-in-progress?${stringify(params)}`);
}

// 查询待处理的任务
export async function queryWaitDealTaskList(params: ITaskParamsProps) {
  return request(`${BASE_PATH}/project-dos/my-task/list-wait-deal?${stringify(params)}`);
}

// 查询最新的待回复的进程汇报
export async function queryTaskApplyInfo(params: { taskId: string }) {
  return request(`${BASE_PATH}/project-dos/task-report/wait-reply-report/${params.taskId}`);
}

// 查询所有项目信息
export async function queryTeamProjectList() {
  return request(`${BASE_PATH}/project-dos/team-project-management/list-team-project-base-info`);
}

// 查询团队任务信息
export async function queryTeamTaskList(params: { projectId: string }) {
  return request(`${BASE_PATH}/project-dos/team-project-management/list?${stringify(params)}`);
}