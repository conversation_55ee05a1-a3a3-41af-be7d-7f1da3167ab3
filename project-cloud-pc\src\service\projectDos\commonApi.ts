/**
 * @description 公用
 * <AUTHOR>
 * @date 2023-11-02 11:42:26
*/
import Request from 'bmc-app-react-component/es/util/SelfHostRequest';
import request from '@/utils/requestTool';
import { BASE_PATH } from '@/constants/static';
import type {PageType ,loadOrderPaginationType} from '../../type';
import { stringify } from 'qs';

// 查询所有状态
export function listStatus() {
    return request(`${BASE_PATH}/project-dos/project-management/list-status`);
}

// 查询当前登陆人信息
export function getCurrentUser() {
    return request(`${BASE_PATH}/account/system-employee/current-user`);
}

//查询员工列表 员工下拉项
export function listEmpSelectOptions() {
    return request(`${BASE_PATH}/account/system-employee/list-emp-select-options`);
}
//查询员工(简要信息)
export function listEmployee(params: PageType) {
    return request(`${BASE_PATH}/account/system-employee/list-employee?${stringify(params)}`);
}
//查询员工 根据手机号和姓名查询
export function listEmpByParams(params: PageType) {
    return request(`${BASE_PATH}/account/system-employee/list-employee-base-info?${stringify(params)}`);
}
// 查询员工
export function addressBookCommon() {
    return request(`${BASE_PATH}/account/address-book/common`);
}

//查询订单
export function loadOrderPagination(data: loadOrderPaginationType) {
    return request(`${BASE_PATH}/order/order-info/load-order-pagination?count=${data.count}&page=${data.page}`, {data:data.data,method:'POST'});
}
//根据查询参数分页查询客户
export function loadCustomListByParams(data: any) {
    return request(`${BASE_PATH}/customer/load-page-by-params?count=${data.count}&page=${data.page}`, {data:data.data,method:'POST'});
}
//上传附件
//进程汇报 Api
export async function saveBatch(data: any) {
    return request(`${BASE_PATH}/common/attachment/save-batch`,{body:data,method:'POST'});
}
//根据查询参数获取客户id列表
export async function loadCustomerByParams(data: any) {
    return request(`${BASE_PATH}/customer/load-customer-by-params`,{body:data.data,method:'POST'});
}
//查询已发布的模板
export async function getTemplateListApi(name?: string) {
    return request(`${BASE_PATH}/project-dos/project-template-management/list-released-project-template-none-pagination?projectTypeEnum=ORDER&projectLevel=COMPANY`);
}


