/**
 * @description 项目详情 -> 项目概览
 * <AUTHOR>
 * @date 2023-11-02 11:38:22
*/
import request from '@/utils/requestTool';
import { BASE_PATH } from '@/constants/static';
import { stringify } from 'qs';

//项目申请发布
export function applyRelease(data: any) {
    return request(`${BASE_PATH}/project-dos/project-management/apply-release`, {data,method:'POST'});
}
//查询项目申请信息
export function applyReleaseByProjectId(projectId: string) {
    return request(`${BASE_PATH}/project-dos/project-management/apply-release-info/${projectId}`);
}
//删除项目
export function deleteProjectApi(projectId: string) {
    return request(`${BASE_PATH}/project-dos/project-management/delete/${projectId}`,{method:'DELETE'});
}
//项目审核发布
export function auditApplyInfoApi(data: any) {
    return request(`${BASE_PATH}/project-dos/project-management/audit-apply-info`,{data,method:'PUT'});
}
//项目概览
export function projectOverviewApi(projectId: string) {
    return request(`${BASE_PATH}/project-dos/project-management/project-overview/${projectId}`);
}
//项目启动
export function startupProject(projectId: string,data?:{startupMethod?: 'AUTOMATIC_PROVISIONING'|'START_NOW'}) {
    return request(`${BASE_PATH}/project-dos/project-management/startup/${projectId}`,{method:'PUT',body:stringify(data),header: { "Content-Type": "application/x-www-form-urlencoded" }});
}
//项目暂停
export function pauseProject(projectId: string) {
    return request(`${BASE_PATH}/project-dos/project-management/pause/${projectId}`,{method:'PUT'});
}
//查询项目功能项信息
export function listProjectFunction(projectId: string) {
    return request(`${BASE_PATH}/project-dos/project-management/list-project-function/${projectId}`);
}
//复制项目
export function copyProject(projectId: string,data:{name?: string}) {
    return request(`${BASE_PATH}/project-dos/project-management/copy/${projectId}`,{data,method:'POST'});
}
//项目任务分派接受情况
export async function taskAssignmentReceiveStatus(projectId: string) {
    return request(`${BASE_PATH}/project-dos/task-management/task-assignment-receive-status/${projectId}`);
}
//项目统计
export async function projectStatisticsApi(projectId: string) {
    return request(`${BASE_PATH}/project-dos/task-management/project-statistics/${projectId}`);
}
//能否修改基本信息
export async function canUpdateBaseInfo(projectId: string) {
    return request(`${BASE_PATH}/project-dos/project-management/can-update-base-info/${projectId}`,{method:'POST'});
}


