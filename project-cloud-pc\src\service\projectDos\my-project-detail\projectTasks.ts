/**
 * @description 项目任务
 * <AUTHOR>
 * @date 2023-11-07 10:27:47
*/
import request from '@/utils/requestTool';
import { BASE_PATH } from '@/constants/static';
import { stringify } from 'qs';

//查询项目下所有任务
export async function getListTask(projectId: string) {
    return request(`${BASE_PATH}/project-dos/task-management/list-task/${projectId}`);
}
//计算工作周期时间
export async function calcWorkDays(params: any) {
    return request(`${BASE_PATH}/project-dos/task-management/calc-work-days?${stringify(params)}`);
}
//查询任务负责人数据
export async function listTaskLeaderInfo(params: any) {
    return request(`${BASE_PATH}/project-dos/task-management/list-task-leader-info?${stringify(params)}`);
}
//编辑修改项目任务
export async function editProjectTasksApi(data: any) {
    return request(`${BASE_PATH}/project-dos/task-management/edit`, { data, method: 'POST' });
}
//下移
export async function moveDownApi(taskId: string) {
    return request(`${BASE_PATH}/project-dos/task-management/move-down/${taskId}`, { method: 'PUT' });
}
//上移
export async function moveUpApi(taskId: string) {
    return request(`${BASE_PATH}/project-dos/task-management/move-up/${taskId}`, { method: 'PUT' });
}
//查询任务信息
export async function getTaskInfo(taskId: string) {
    return request(`${BASE_PATH}/project-dos/task-management/task-info/${taskId}`);
}
//查询任务结果评价
export async function taskResultEvaluation(taskId: string) {
    return request(`${BASE_PATH}/project-dos/task-management/task-result-evaluation/${taskId}`);
}
//查询所有附件格式
export async function listAttachmentFormat() {
    return request(`${BASE_PATH}/project-dos/task-management/list-attachment-format`);
}
//删除任务
export async function deleteTasksApi(taskId: string) {
    return request(`${BASE_PATH}/project-dos/task-management/delete/${taskId}`, { method: 'DELETE' });
}
//查询任务历程
export async function viewTaskNews(taskId: string) {
    return request(`${BASE_PATH}/project-dos/task-management/task-news/${taskId}`);
}
//进程汇报 Api
export async function taskReport(data: any) {
    return request(`${BASE_PATH}/project-dos/task-report`, { data, method: 'POST' });
}
//回复
export async function taskReportReply(data: any) {
    return request(`${BASE_PATH}/project-dos/task-report`, { data, method: 'PUT' });
}
//查询进程汇报
export async function getTaskReportByTaskId(taskId: string) {
    return request(`${BASE_PATH}/project-dos/task-report/${taskId}`);
}
//删除回复
export async function deleteTaskReportReply(replyId: string) {
    return request(`${BASE_PATH}/project-dos/task-report/delete-reply/${replyId}`, { method: 'DELETE' });
}
//删除汇报
export async function deleteTaskReport(reportId: string) {
    return request(`${BASE_PATH}/project-dos/task-report/delete-report/${reportId}`, { method: 'DELETE' });
}
//项目评审管理 Api
export async function editResultEvaluationCriteria(data: any) {
    return request(`${BASE_PATH}/project-dos/task-review/edit-result-evaluation-criteria`, { data, method: 'POST' });
}
//评审
export async function projectReview(data: any) {
    return request(`${BASE_PATH}/project-dos/task-review/review`, { data, method: 'PUT' });
}
//提交评价资料
export async function submitEvaluationMaterials(data: any) {
    return request(`${BASE_PATH}/project-dos/task-review/submit-evaluation-materials`, { data, method: 'POST' });
}
//查询任务结果评价信息
export async function SelectSubmitEvaluationMaterials(taskId: any) {
    return request(`${BASE_PATH}/project-dos/task-review/task-result-evaluation-info/${taskId}`);
}
//任务分派
export async function taskAssign(data: any) {
    return request(`${BASE_PATH}/project-dos/task-assign/assign`, { data, method: 'POST' });
}
//处理任务分派
export async function dealTaskAssign(data: any) {
    return request(`${BASE_PATH}/project-dos/task-assign/deal-task-assign`, { data, method: 'PUT' });
}
//任务申请 APi
export async function taskApplyType(data: any) {
    return request(`${BASE_PATH}/project-dos/task-apply`, { data, method: 'POST' });
}
//审核申请
export async function auditTaskApply(data: any) {
    return request(`${BASE_PATH}/project-dos/task-apply`, { data, method: 'PUT' });
}
//查询任务最新的申请
export async function queryTaskApply(taskId: string) {
    return request(`${BASE_PATH}/project-dos/task-apply/${taskId}`);
}
//查询功能项信息
export async function listTaskFunctionality(params: any) {
    return request(`${BASE_PATH}/project-dos/task-management/list-task-function?${stringify(params)}`);
}
//降级
export async function downgradeTask(taskId: string, data?: { autoAdjustStartTime?: boolean, useParentTaskEndTime?: boolean }) {
    return request(`${BASE_PATH}/project-dos/task-management/downgrade/${taskId}`, { method: 'PUT', body: stringify(data), header: { "Content-Type": "application/x-www-form-urlencoded" } });
}
//升级
export async function upgradeTask(taskId: string) {
    return request(`${BASE_PATH}/project-dos/task-management/upgrade/${taskId}`, { method: 'PUT', header: { "Content-Type": "application/x-www-form-urlencoded" } });
}
//关闭任务
export async function closeTask(taskId: string) {
    return request(`${BASE_PATH}/project-dos/task-management/close/${taskId}`, { method: 'PUT' });
}
//查询项目下所有任务信息
export async function listTaskSimpleInfo(projectId: string) {
    return request(`${BASE_PATH}/project-dos/task-management/list-task-simple-info/${projectId}`);
}
//编辑/新增标准参考
export async function editStandardReferenceApi(taskId: string, data: any) {
    return request(`${BASE_PATH}/project-dos/task-review/edit-standard-reference/${taskId}`, { data, method: 'POST' });
}
export async function infoDirectoryCatalogApi() {
    return request(`${BASE_PATH}/project-dos/task-review/list-upload-type`);
}
//查询通讯录
export async function selectAddressBook() {
    return request(`${BASE_PATH}/account/address-book`);
}
//查询任务提报评审信息
export async function getTaskSubmitReviewInfo(params?: any) {
    return request(`${BASE_PATH}/project-dos/task-review/get-task-submit-review-info?${stringify(params)}`);
}
//查询用户类型
export async function listUserTypeApi() {
    return request(`${BASE_PATH}/project-dos/common/list-user-type`);
}
//查询品牌供应商类型
export async function listProductProviderApi() {
    return request(`${BASE_PATH}/account/system-employee/list-supplier`);
}
//查询客户以及联系人列表
export async function loadByCustomerApi(id: string) {
    return request(`${BASE_PATH}/project-dos/common/get-customer-contact/${id}`);
}
//查询执行岗位
export async function listPositionSelectOptionsApi() {
    return request(`${BASE_PATH}/account/custom-position/list-position-select-options`);
}
//查询进程阶段
export async function listDirectoryProcessApi() {
    return request(`${BASE_PATH}/common/info-directory-catalog/list-directory?directoryIdentifyEnum=PROCESS_STAGE`);
}
//查询物料供应
export async function listMaterialSupplyApi() {
    return request(`${BASE_PATH}/project-dos/common/list-material-supply`);
}
//查询用户类型（用于设置可见范围）
export async function listUserTypeVisibilityApi() {
    return request(`${BASE_PATH}/project-dos/common/list-user-type-visibility`);
}
// 查询客户订单项目信息
export async function queryProjectInfoApi(estateId: string) {
    return request(`${BASE_PATH}/project-dos/customer-order/project-info/${estateId}`);
}
// 分派项目
export async function assignProject(data: any) {
    return request(`${BASE_PATH}/project-dos/customer-order/assign`, { data, method: 'POST' });
}
// 项目分派目录
export async function getProjectAssignCategory() {
    return request(`${BASE_PATH}/project-dos/customer-order/project-tree`);
}
// 获取最新模板
export async function getLastTemplate(params: any) {
    return request(`${BASE_PATH}/project-dos/customer-order/last-temp?projectLevel=${params?.projectLevel || ''}&rootType=${params?.rootType || ''}&subjectId=${params?.subjectId || ''}&classification=${params?.classification || ''}`);
};
// 创建项目获取模板
export async function getInitTaskInfo(data: any) {
    return request(`${BASE_PATH}/project-dos/customer-order/init-project`, { data, method: 'POST' });
};
// 编辑新增模板
export async function editOrCreateTemplate(data: any) {
    return request(`${BASE_PATH}/project-dos/customer-order/edit-temp`, { data, method: 'POST' });
}
// 批量创建项目
export async function batchAssignApi( data: any) {
    return request(`${BASE_PATH}/project-dos/customer-order/batch-assign`, { data, method: 'POST' });
}
// 拆分项目
export async function splitProjectApi( data: any) {
    return request(`${BASE_PATH}/project-dos/customer-order/split-project`, { data, method: 'POST' });
}
