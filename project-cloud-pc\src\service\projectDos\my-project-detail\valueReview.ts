/**
 * @description 价值评分
 * <AUTHOR>
 * @date 2023-11-16 16:41:35
*/
import request from '@/utils/requestTool';
import { BASE_PATH } from '@/constants/static';
import { stringify } from 'qs';

//选用价值评分
export async function chooseByVersionId(versionId: any) {
    return request(`${BASE_PATH}/project-dos/task-score/choose/${versionId}`,{method:'PUT'});
}
//查询该项目分值所有版本
export async function listScoreVersionInfo(projectId: any) {
    return request(`${BASE_PATH}/project-dos/task-score/list-score-version-info/${projectId}`);
}
//查询该评分版本下所有任务分值信息
export async function listScoreVersionTaskInfo(versionId: any) {
    return request(`${BASE_PATH}/project-dos/task-score/list-score-version-task-info/${versionId}`);
}
//查询该项目所有任务的分值信息
export async function listTaskScoreInfo(projectId: any) {
    return request(`${BASE_PATH}/project-dos/task-score/list-task-score-info/${projectId}`);
}
//设置当前分组下的任务分值
export async function setGroupTaskScore(data: any) {
    return request(`${BASE_PATH}/project-dos/task-score/set-group-task-score`,{data,method:'POST'});
}
//设置一级任务分值
export async function setOneLevelTaskScore(data: any) {
    return request(`${BASE_PATH}/project-dos/task-score/set-one-level-task-score`,{data,method:'POST'});
}
//保存版本信息
export async function saveVersionInfo(data: any) {
    return request(`${BASE_PATH}/project-dos/task-score/save-version`,{data,method:'PUT'});
}
//删除评分版本
export async function deleteVersion(versionId: any) {
    return request(`${BASE_PATH}/project-dos/task-score/delete/${versionId}`,{method:'DELETE'});
}