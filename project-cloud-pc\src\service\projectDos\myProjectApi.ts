/**
 * @description 我的项目
 * <AUTHOR>
 * @date 2023-11-02 11:38:11
*/
import request from '@/utils/requestTool';
import type { calcWorkDaysType} from '../../type';
import { BASE_PATH } from '@/constants/static';
import { stringify } from 'qs';

//项目列表快捷统计
export async function quickStatistics(params: any) {
    return request(`${BASE_PATH}/project-dos/project-management/quick-statistics?${stringify(params)}`);
}
// 查询所有项目类型
export async function listProjectType() {
    return request(`${BASE_PATH}/project-dos/project-management/list-project-type`);
}
//计算工作天数
export async function calcWorkDays(params: calcWorkDaysType) {
    return request(`${BASE_PATH}/project-dos/project-management/calc-work-days?${stringify(params)}`);
}
// 查选所有项目业务关联类型
export async function listBusinessAssociationType() {
    return request(`${BASE_PATH}/project-dos/project-management/list-business-association-type`);
}
// 查询所有项目级别
export async function listProjectLevel() {
    return request(`${BASE_PATH}/project-dos/project-management/list-project-level`);
}
// 查询项目列表
export async function listProject(params: any) {
    return request(`${BASE_PATH}/project-dos/project-management/list-project?${stringify(params)}`);
}
// 查询所有工作制度
export async function listWorkSystem() {
    return request(`${BASE_PATH}/project-dos/project-management/list-work-system`);
}
// 查询所有项目可见授权
export async function listProjectVisibleAuthorization() {
    return request(`${BASE_PATH}/project-dos/project-management/list-project-visible-authorization`);
}
//查询项目分类
export async function loadOptionalList() {
    return request(`${BASE_PATH}/project-dos/project-management/list-classifications`);
}
// 编辑新增修改项目
export async function projectManagementEdit(data: any) {
    return request(`${BASE_PATH}/project-dos/project-management/edit`, { data,method:'POST'});
}
//查询项目基本信息
export async function listProjectBaseInfo() {
    return request(`${BASE_PATH}/project-dos/project-management/list-project-base-info`);
}
//保存为项目模板
export async function saveToTemplate(projectId: string,data: any) {
    return request(`${BASE_PATH}/project-dos/project-management/save-to-template/${projectId}`,{data,method:'POST'});
}
//查询单个项目基本信息
export async function listProjectBaseInfoByProjectId(projectId: string) {
    return request(`${BASE_PATH}/project-dos/project-management/project-base-info/${projectId}`);
}
/**
 * 项目模板api
*/
//项目模板 查询所有项目级别
export async function listProjectLevelTemplateName() {
    return request(`${BASE_PATH}/project-dos/project-template-management/list-project-level-template-name`);
}
//项目模板 项目模板快捷统计
export async function templateQuickStatistics() {
    return request(`${BASE_PATH}/project-dos/project-template-management/quick-statistics`);
}
//查询项目模板列表
export async function listProjectTemplate(params: any) {
    return request(`${BASE_PATH}/project-dos/project-template-management/list-project-template?${stringify(params)}`);
}
//查询项目模板下所有任务
export async function templateListTask(projectId: string) {
    return request(`${BASE_PATH}/project-dos/project-template-management/list-task/${projectId}`);
}
//项目模板导入Excel模板
export async function importExcelTemplate(projectId: string,file: any) {
    return request(`${BASE_PATH}/project-dos/project-template-management/import-excel-template/${projectId}`,{body:file,method:'POST'},false);
}
//项目模板导出Excel
export async function exportExcelTemplate(projectId: string) {
    return request(`${BASE_PATH}/project-dos/project-template-management/export-excel-template/${projectId}`);
}
//查询已发布的模板列表
export async function listReleasedProjectTemplate(params: any) {
    return request(`${BASE_PATH}/project-dos/project-template-management/list-released-project-template?${stringify(params)}`);
}
//查询历史项目
export async function listHistoryProject(params: any) {
    return request(`${BASE_PATH}/project-dos/project-management/list-history-project?${stringify(params)}`);
}
//查询客户分类
export async function listCustomerServiceCategoryApi() {
    return request(`${BASE_PATH}/project-dos/common/list-customer-service-category`);
}