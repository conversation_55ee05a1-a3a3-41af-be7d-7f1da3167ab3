/* 系统主题色 */
@primary: #0275d8;
@lightPrimary: #5aa5e5;
@darkPrimary: #025aa5;

/* 系统辅助色 */
@info: #428bca;
@success: #5cb85c;
@warning: #f0ad4e;
@error: #d9534f;
@label: #23c6c8;
@menuHover: #f9f9f9;
@menuActive: #ebf4fc;
@nameColor: #657599;

/* 中性色 */
@importantTitle: #222222;
@title: #333333;
@content: #5c5c5c;
@help: #9f9f9f;
@border: #c0c0c0;
@divider: #eeeeee;
@backGround: #fafafa;
@inputHover: #9f9f9f;
@inputTitle: #5c5c5c;
@black: #000000;
@white: #ffffff;

/* 其他颜色 */
@embellish: #107a4a;
@importDivider: #dddddd;
@crosswalk: #f9f9f9;
@crosswalkHover: #f5f5f5;
@tableSelected: #ebf4fc;
@veryImportant: #000000;
@disabled: #c5c8cd;
@aColor: #014c8c;
@tipsBg: #DEEDFA;
@other: #e2e2e2;
@placeHolder:#c5c8cd;
.f-20 {
  font-size: 20px;
}
.f-16 {
  font-size: 16px;
}
.f-13 {
  font-size: 13px;
}
.f-18 {
  font-size: 18px;
}
.f-12 {
  font-size: 12px;
}
.m-16{
  margin: 16px;
}
.mt-2 {
  margin-top: 2px;
}
.mt-5 {
  margin-top: 5px;
}
.ml-6 {
  margin-left: 6px;
}
.ml-35{
  margin-left: 35px;
}
.ml-8 {
  margin-left: 8px;
}
.mt-8 {
  margin-top: 8px;
}
.mt-10 {
  margin-top: 10px;
}
.mt-16 {
  margin-top: 16px;
}
.mt-20 {
  margin-top: 20px;
}
.mt--10 {
  margin-top: -10px;
}
.mt--16 {
  margin-top: -16px;
}
.mb-5{
  margin-bottom: 5px;
}
.mb-10 {
  margin-bottom: 10px;
}
.mb-14 {
  margin-bottom: 14px;
}
.mb-16 {
  margin-bottom: 16px;
}
.mb-20 {
  margin-bottom: 20px;
}
.mr-3{
  margin-right: 3px;
}
.mr-5 {
  margin-right: 5px;
}
.mr-10 {
  margin-right: 10px;
}
.mr-12{
  margin-right: 12px;
}
.mr-16{
  margin-right: 16px;
}
.ml-10 {
  margin-left: 10px;
}
.ml-20 {
  margin-left: 20px;
}
.mr-20 {
  margin-right: 20px;
}
.mr-40 {
  margin-right: 40px;
}
.ml-16 {
  margin-left: 16px;
}
.p-16 {
  padding: 16px;
}
.pl-4 {
  padding-left: 4px;
}
.pl-16{
  padding-left: 16px;
}
.p-0 {
  padding: 0;
}
.pr-5{
  padding-right: 5px;
}
.pr-16 {
  padding-right: 16px;
}
.pb-10{
  padding-bottom: 10px;
}
.pb-16 {
  padding-bottom: 16px;
}
.pb-40 {
  padding-bottom: 40px;
}
.pt-16 {
  padding-top: 16px;
}
.py-16{
  padding: 0 16px;
}
.pointer {
  cursor: pointer;
}
.f-weight {
  font-weight: bold;
}
.f-weight-normal{
  font-weight: normal;
}
.c-content {
  color: @content !important;;
}
.flex-1 {
  flex: 1;
}
.flex-2 {
  flex: 2;
}
.flex-3 {
  flex: 3;
}
.flex-4 {
  flex: 4;
}
.flex-5 {
  flex: 5;
}
.flex-6 {
  flex: 6;
}
.flex-7 {
  flex: 7;
}
.flex-8 {
  flex: 8;
}
.flex-column {
  flex-direction: column;
}
.bottom-divider {
  border-bottom: 1px solid @divider;
}
.flex{
  display: flex;
}
.flex-wrap {
  flex-wrap: wrap;
}
.width-80 {
  width: 80%;
}
.width-90 {
  width: 90%;
}
.width-100 {
  width: 100%;
}
.height-100 {
  height: 100%;
}
.style-cover{
  min-width: 32px;
  height: 18px;
  line-height: 18px;
  .ant-switch-handle{
    width: 14px;
    height: 14px;
  }
}
.bg-default {
  background-color: @backGround;
}
.border-bottom {
  border-bottom: 1px solid @divider;
}
.overflow-hidden {
  overflow: hidden;
}
.pre-wrap {
  white-space: pre-wrap;
}
.font-bolder{
  font-weight: 600;
}
.skip-setting-text{
  color: #0275d8;
}
.float-left{
  float: left;
}
.align-center{
  align-items: center;
}
.readonly-text{
  line-height: 26px;
  padding-left: 4px;
}
.font-form-label{
  color: #5c5c5c;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
}
.color-5c{
  color: #5c5c5c;
}
.color-c5c8cd{
  color: #c5c8cd;
}
.fw-b {
  font-weight: bold;
}
.flex-w {
  display: flex;
  justify-content: space-between;
}
