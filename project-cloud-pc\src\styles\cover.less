@import "base";

// button样式覆盖
.ant-btn {
  height: 32px;
  padding: 0 16px;
  line-height: 28px;
  border-radius: 2px;

  &.ant-btn-primary {
    background-color: @primary;
    border-color: @primary;

    &:hover {
      background-color: @darkPrimary;
      border-color: @darkPrimary;
    }
  }
}

.ant-timeline-item {
  padding-bottom: 0 !important;
}

.gantt-schedule-timeline-calendar {
  background-color: @white  !important;
}

.gantt-schedule-timeline-calendar__list {
  background-color: @white  !important;
}

.gantt-schedule-timeline-calendar__chart {
  background-color: @white  !important;
}

.gantt-schedule-timeline-calendar__chart-calendar {
  background-color: @white  !important;
}

.gantt-schedule-timeline-calendar__chart-timeline-inner {
  background-color: @white  !important;
}

.gantt-schedule-timeline-calendar__chart-timeline-grid {
  background-color: @white  !important;
}

.gantt-schedule-timeline-calendar__list-column-header-resizer {
  background-color: @white  !important;
}

.gantt-schedule-timeline-calendar__list-column-row-expander {
  background-color: @white  !important;
}

.gantt-schedule-timeline-calendar__list-column {
  background-color: @white  !important;
}

.gantt-schedule-timeline-calendar__list-column-header {
  background-color: @white  !important;
}

.gantt-schedule-timeline-calendar__list-column-header-content {
  background-color: @white  !important;
}

.gantt-schedule-timeline-calendar__chart-timeline-items-row-item {
  margin-left: 4px;
  border-radius: 8px !important;
}

.gantt-schedule-timeline-calendar__chart-calendar-date {
  border-right: 1px solid @divider  !important;
}

.gantt-schedule-timeline-calendar__chart-timeline-grid-row-block {
  border-right: 1px solid @divider  !important;
  border-bottom: 1px solid @divider  !important;
}

.gantt-schedule-timeline-calendar__list-toggle {
  padding: 9px !important;
  background-color: transparent !important;
}

@import "./task-table-cover";

@import "../components/bdw-custom-select/index";

.ant-layout {
  flex: 1 !important;
  background-color: @white  !important;
}

.hide {
  display: none;
}

.show {
  display: block;
}

.ant-radio-group {
  &.ant-radio-group-solid {
    height: 32px;
    overflow: hidden;
    line-height: 32px;
    border: 1px solid @divider;
    border-radius: 32px;
    .f-13();
    padding: 0;

    .ant-radio-button-wrapper {
      height: 32px;
      line-height: 32px;
      border: none;
      padding: 0 30px;
      border-radius: 32px;
      box-shadow: none !important;

      &::before {
        display: none;
      }

      &:hover {
        color: @primary;
        border: none;
      }

      &.ant-radio-button-wrapper-checked {
        background-color: @primary;
        color: @white;
        box-shadow: none;
        outline: none;
        border: none;
      }
    }

  }
}

.overflow-auto {
  overflow: auto !important;
}

.ant-picker-cell-disabled {
  &.ant-picker-cell-in-range {
    &::before {
      background-color: #f5f5f5 !important;
    }
  }
}

.ant-pro-basicLayout-children-content-wrap {
  width: 100%;
  height: 100%;
}

.ant-pro-grid-content {
  height: 100%;
}

.ant-tabs {
  &.ant-tabs-card {
    .ant-tabs-nav {
      padding-left: 20px;

      .ant-tabs-nav-list {
        border-right: 1px solid @divider;
        border-radius: 2px;
      }

      .ant-tabs-tab {
        height: 37px;
        margin-right: 0 !important;
        box-sizing: border-box;
        padding: 6px 10px !important;
        background-color: @white  !important;
        border-color: @divider;
        border-right: none !important;

        .ant-tabs-tab-btn {
          color: @help;
        }

        &:hover {
          // color: @primary;
          color: #222;

          span {
            // color: @primary  !important;
            color: #222  !important;
          }
        }

        &.ant-tabs-tab-active {
          border-top: 2px solid @primary;

          .ant-tabs-tab-btn {
            color: @primary;
          }
        }
      }
    }
  }

  &.ant-tabs-top {
    .ant-tabs-nav {
      margin-bottom: 0;

      .ant-tabs-tab {
        .f-13();
        padding: 9px 12px;
        margin: 0;

        &:hover {
          color: @primary  !important;
        }

        &.ant-tabs-tab-active {
          .ant-tabs-tab-btn {
            color: #222;
            // color: @black;
            // font-weight: 600;
          }
        }
      }

      .ant-tabs-ink-bar {
        background-color: @primary  !important;
      }
    }
  }
}

.bdw-icon-button {
  color: @content  !important;
}

.ant-menu-horizontal {
  line-height: 30px !important;
  border-bottom: none !important;

  .ant-menu-item {
    .f-13();
    line-height: 30px;
    color: @help  !important;
    padding: 0 12px;

    &:hover {
      color: @content  !important;
      border-bottom-color: @primary  !important;
    }

    &.ant-menu-item-selected {
      color: @content  !important;
      border-bottom-color: @primary;
    }
  }
}

.no-border-input {
  .f-13();
  padding: 4px 0 2px 0 !important;
  border: none !important;

  &:focus {
    outline: none !important;
    box-shadow: none !important;
  }
}

// 对modal的样式进行覆盖
.ant-modal-content {
  .ant-modal-header {
    padding: 16px;

    .ant-modal-title {
      font-size: 22px;
      .f-weight();
    }
  }

  .ant-modal-close {
    .ant-modal-close-x {
      font-size: 20px;
    }
  }

  .ant-modal-body {
    padding: 16px;
  }
}

.ant-form-item-has-error {
  .ant-picker-focused {
    box-shadow: none !important;
  }
}

.ant-form-item-control-input {
  min-height: 27px !important;

  .ant-input,
  textarea.ant-input {
    min-height: 27px !important;
    box-sizing: border-box;
  }

  // 选择框
  .ant-select {
    border-bottom: 1px solid #c0c0c0;
    min-height: 27px;

    .ant-select-selector {
      height: 27px!important;
      padding: 2px 0 !important;
      align-items: center;
      .ant-select-selection-search{
        left: 0;
        right: 0;
      }

      .ant-select-selection-item {
        font-size: 13px;
        margin-top: 0;
        margin-bottom: 0;
      }

      .ant-select-selection-search .ant-select-selection-search-input {
        min-height: 27px;
        height: 27px!important;
      }
    }
  }

  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border: unset !important;
  }

}

.ant-form-item-explain-error {
  font-size: 12px;
}




.ant-drawer {
  .ant-drawer-header {
    padding: 12px 16px;
  }

  .ant-drawer-close {
    padding: 16px;
    line-height: 1;
  }

  .ant-drawer-body {
    padding: 10px 0px;
  }
}

.ant-btn.ant-btn-link {
  color: @primary;

  &:hover {
    color: @primary;
  }
}

.bdw-menu-item {
  &:hover {
    background-color: @backGround;
  }
}

#zmage {
  #zmageBackground {
    background-color: rgba(0, 0, 0, 0.7);
  }

  #zmageControl {
    background-color: @white  !important;
  }
}

.help-title {
  .f-12();
  color: @help;
}

.light-tips-title {
  .f-13();
  color: @nameColor;
}

.dark-tips-title {
  .f-13();
  color: @content;
}

.text-right {
  text-align: right;
}

.can-click {
  color: @aColor;
}

.button-cover-style {
  font-size: 12px !important;
  height: 28px !important;
  line-height: 14px !important;
}

.ant-input-cover-style {
  background-color: #fafafa !important;
}
.ant-drawer-header-title{
  position: relative;
  .ant-drawer-close{
    position: absolute;
    right: 0;
    width: 40px;
    height: 40px;
    padding: unset;
    top: 50%;
    transform: translate(0,-50%);
  }
}
.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before{
  background: unset!important;
}
