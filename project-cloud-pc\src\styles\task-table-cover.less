@import "base";
.project-task-table,.associate-task-table,.template-detail-table,.version-record-table,.create-template-table {
  border: 1px solid @divider;
  .bdw-table-header{
   padding-bottom: 0;
  }
  .ant-table {
    table-layout: fixed;
    .ant-table-thead {
      tr {
        th {
          color: @title !important;
          font-size: 13px;
          padding: 6px 0 !important;
          text-indent: 4px;
          position: static;
        }
      }
    }
    .ant-table-tbody {
      tr {
        td {
          color: @title;
          font-size: 13px;
          padding: 6px 8px 6px 6px !important;
          position: static;
        }
      }
      button.ant-table-row-expand-icon-expanded + .bdw-flex {
        .task-name-show {
          color: @black;
          font-weight: 600;
        }
        .task-index-num {
          color: @black;
          font-weight: 600;
        }
      }

    }
  }

  .ant-table-tbody > tr.ant-table-row-selected > td {
    background-color: @tableSelected !important;
  }
  .ant-table-tbody > tr.ant-table-row-selected:hover > td {
    background-color: @tableSelected !important;
  }
  .task-name-input {
    width: 100%;
    height: 28px;
    color: @black;
    font-size: 13px;
    line-height: 28px;
    border: @border;
    outline: none;
    box-shadow: none;

    &:focus {
      outline: none;
      box-shadow: none;
    }
  }
  .task-table-buttons {
    width: 100px;
  }
  .ant-table-selection-column {
    width: 24px !important;
    min-width: 24px !important;
    padding: 0;
    overflow: hidden !important;
    font-size: 0;
  }
  .ant-input{
    //padding-top:7px !important;
  }
  .ant-input:placeholder-shown,.ant-picker-input > input:placeholder-shown{
    font-size: 13px;
  }

}



