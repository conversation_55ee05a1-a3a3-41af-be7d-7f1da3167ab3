/**
 * @description 类型
 * <AUTHOR>
 * @date 2023-10-26 15:52:48
*/
// 菜单title
export interface MenuTitleType {
    title: string
}
export interface TypeDataResult {
    quickStatus: string
    quickStatusName: string
    quantity: number
}
export interface PageType {
    count?: string | number
    keywords?: string
    page?: string | number
}
export interface calcWorkDaysType {
    endDate: string,
    startDate: string,
    workSystem: string
}
export interface loadOrderPaginationType extends Omit<PageType, 'keywords'> {
    data: {
        customerName?: string
        dateInfo?: { startTime: string, stopTime: string }
        orderContractNo?: string
        phone?: string
    }
}
export interface optionType {
    name: string
    value: string
}
export interface selectOptionType {
    label: string
    value: string
}
export interface scoreVersionRecord {
    choose?: boolean
    createTime?: string,
    projectScore?: string | number,
    versionId?: string
    versionName?: string
}
export interface StandardListItem {
    // 文档名称
    des: string
    // 文档id
    id: string | number
    // 文档类型
    type: string
}
//项目负责人信息
export interface ProjectLeadType {
    leaderId?: string,
    leaderName?: string,
}
//当前登录人信息
export interface CurrentUserInfoType {
    userId?: string;
    userName?: string
}
//当前版本id信息
export interface currentVersionInfoType {
    versionId?: string | null
    isNew?: boolean
}
//时间调整
interface workItemType {
    checked: boolean,
    day: string
}
export interface defaultTimeAdjustType {
    askLeaveDays: workItemType,
    compensatoryLeaveDays: workItemType,
    depositRestDays: workItemType,
}
export interface projectListInfoType {
    name?: string
    projectId?: string
}
//项目任务列表
export interface taskInfoListType {
    children?: taskInfoListType
    editorId?: string,
    editorName?: string,
    endTime?: string,
    executionCycle?: string,
    leaderId?: string,
    leaderName?: string,
    process?: string,
    received?: boolean,
    startTime?: string,
    statusCode?: string,
    statusName?: string,
    taskId?: string,
    title?: string,
}
//项目任务列表调整
export interface currentTaskInfoListType extends taskInfoListType{
    indexKey?: number[],
    parentId?: string,
    parentTitle?: string,
    parentDetail?:taskInfoListType[],
    value?: string
}
//文件预览
export interface documentType {
    attachmentType?: string,
    createUserId?: string,
    fileName?: string,
    fileSize?: string,
    id?: string,
    uploadTime?: string,
    url?: string
}
//项目任务信息
export interface taskInfoType {
    associationTaskEndTime?: boolean,
    associationTaskId?: string,
    document?: documentType[],
    endTime?: string,
    executionCycle?: string,
    leaderId?: string,
    leaderName?: string,
    startTime?: string,
    taskExplain?: string,
    taskId?: string,
    title?: string
}
//当前任务信息
export interface currentTaskType extends taskInfoListType, taskInfoType {
    indexKey?: number[],
    parentId?: string,
    parentTitle?: string,
    parentDetail?:taskInfoListType[],
    value?: string
}
//上传附件要求
export interface projectFileType{
    AUDIO?: string
    CAD?: string
    EXCEL?: string
    IMG?: string
    PDF?: string
    PPT?: string
    TXT?: string
    VIDEO?: string
    WORD?: string
    XMIND?: string
    ZIP?: string
}