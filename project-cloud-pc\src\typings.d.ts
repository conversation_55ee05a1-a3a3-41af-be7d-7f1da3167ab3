declare module 'slash2';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.bmp';
declare module '*.tiff';
declare module 'omit.js';
declare module 'react-file-viewer';

interface Window {
  reloadAuthorized: () => void;
}

declare const REACT_APP_ENV: 'test' | 'dev' | 'pre' | false;

type Optional<T> = T | null | undefined

export interface Action{
  type: string,
  payload?: any
}
