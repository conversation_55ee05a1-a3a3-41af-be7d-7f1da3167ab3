import {message} from "antd";

export class IgnoreError extends Error{

}

export function safe<F extends (...param: Array<any>) => Promise<void>|void>(func: F) {
  return async (...args: Parameters<F>): Promise<void> => {
    try{
      await func(...args)
    }catch (e) {
      if(e instanceof IgnoreError){
        console.warn("忽略的异常",e.message)
      }else{
        console.error("执行错误",e)
        message.error(e.message??"执行错误")
      }
    }
  }
}
