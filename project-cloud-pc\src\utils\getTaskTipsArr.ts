

interface TipsObjType {
  id: number,
  content: string
}

export function getTaskTipsArr(task: any) {
  const taskTipsArr: TipsObjType[] = []
  let TipsOption: TipsObjType
  if(!task?.executionCycle){
    TipsOption = {id:1,content:'未设置工期'}
    taskTipsArr.push(TipsOption)
  }
  // @ts-ignore
  if(!task?.startTime){
    TipsOption = {id:2,content:'未设置任务起始时间'}
    taskTipsArr.push(TipsOption)
  }
  // @ts-ignore
  if(!task?.endTime){
    TipsOption = {id:3,content:'未设置任务截止时间'}
    taskTipsArr.push(TipsOption)
  }
  if(!task?.taskExplain?.length){
    TipsOption = {id:4,content:'未设置任务说明'}
    taskTipsArr.push(TipsOption)
  }
  if(!(task?.document?.length)){
    TipsOption = {id:5,content:'未提交任务相关资料'}
    taskTipsArr.push(TipsOption)
  }
  if(!task?.isExistResultEvaluation){
    TipsOption = {id:6,content:'未设置评价标准'}
    taskTipsArr.push(TipsOption)
  }
  return taskTipsArr
}
