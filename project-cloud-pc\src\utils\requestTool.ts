import { fetch } from 'umi-request';
import { history } from 'umi';
import { TOKEN_PREFIX, COMPANY_CODE } from '@/constants/token';
import { getToken } from '@/auth';
import { message, notification } from 'antd';

const codeMessage: any = {
  200: '服务器成功返回请求数据',
  201: '新建或修改数据成功',
  202: '一个请求已进入后台排队',
  204: '删除数据成功',
  400: '发出的请求有错误，服务器没有进行新建或修改数据操作',
  403: '得到授权，但访问被禁止',
  404: '资源未找到',
  406: '请求的格式不可得',
  410: '请求的资源被永久删除，且不会再得到的',
  422: '当创建一个对象时，发生一个验证错误',
  500: '服务器发生错误',
  502: '网关错误',
  503: '服务不可用',
  504: '网关超时',
};


const checkStatus = (response: any) => {

  if (response.status >= 200 && response.status < 300) {
    return response;
  }
  const errortext = codeMessage[response.status] || response.statusText;
  const error: any = new Error(errortext);
  error.name = response.status;
  error.response = response;
  throw error;
};

// 配置request请求时默认参数
export default function request(url: string, options?: any,showMessage = true) {
  const defaultOptions = {
    credentials: 'include', 
    headers: {
      companyCode: COMPANY_CODE,
      Authorization: TOKEN_PREFIX + getToken()?.access_token,
      token: TOKEN_PREFIX + getToken()?.access_token,
    }
  };
  const newOptions: any = { ...defaultOptions, ...options };

  if (options?.header) {
    newOptions.headers = {
      ...newOptions.headers,
      ...options.header
    };
  }

  if (
    newOptions.method === 'POST' ||
    newOptions.method === 'PUT' ||
    newOptions.method === 'DELETE'
  ) {
    if (!(newOptions.body instanceof FormData)) {
      newOptions.headers = {
        Accept: 'application/json',
        'Content-Type': 'application/json; charset=utf-8',
        ...newOptions.headers,
      };
      if(!options?.header){
        newOptions.body = JSON.stringify(newOptions.body);
      }
    } else {
      newOptions.headers = {
        Accept: 'application/json',
        ...newOptions.headers,
      };
    }
  }
  return new Promise((resolve, reject) => {
    fetch(url, newOptions)
      .then(checkStatus)
      .then((response: any) => {
        return response.json().then((res: any) => {
          if (res.code == 0) {
            resolve(res.data);
          } else {
            //兼容错误编码为10190的请求不使用统一错误提示
            if (res.code !== 10190 && showMessage) {
              message.error(res.message)
            }
            reject(res);
          }
        });
      })
      .catch((e: any) => {
        const status = e.name;
        if (status >= 500 && status <= 504) {
          // 请求错误
          notification.open({
            message: '请求错误',
            type: 'error',
            description: '错误代码' + status,
          });
          // history.push('exception?code=' + status);
          return;
        }
        if ([400, 401].includes(status)) {
          e.response.json().then((json: any) => {
            // 请求错误
            notification.open({
              message: '请求错误',
              type: 'error',
              description: '错误代码' + status,
            });
          });
          // history.push('exception?code=' + status);
          return;
        }
        // env should not be used
        if (status === 403) {
          console.log('得到授权，但访问被禁止');
          notification.open({
            message: '请求错误',
            type: 'error',
            description: '得到授权，但访问被禁止',
          });
          return;
        }
        if (status >= 404 && status < 422) {
          notification.open({
            message: '请求错误',
            type: 'error',
            description: '错误代码' + status,
          });
          // history.push('exception?code=' + status);
          return;
        }
      });
  })
}
