import {useRequest} from "ahooks";
import {useMemo} from "react";

export const useGetRequestReturnSingleFileData = (fun:() => Promise<any>,options: any) => {
  const {data: returnData} = useRequest(fun,options)
  const handleData = useMemo(() => {
    if(returnData) {
      const {id,originalFileName,url} = returnData;
      return {uid:id, id,name: originalFileName, url, status: 'done'};
    }
    return undefined;
  },[returnData])

  return handleData
}

export const useGetRequestReturnMultipleFileData = (fun:() => Promise<any>,options: any) => {
  const {data: returnData} = useRequest(fun,options)
  const handleData = useMemo(() => {
    if(returnData) {
      return returnData.map((item: any) => {
        return {
          uid: item.id,
          id: item.id,
          name: item.originalFileName,
          status: 'done',
          url: item.url
        }
      })
    }
    return undefined;
  },[returnData])

  return handleData
}
