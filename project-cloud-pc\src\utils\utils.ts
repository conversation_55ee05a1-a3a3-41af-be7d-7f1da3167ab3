import { message } from "antd";
import { cloneDeep, uniqBy } from "lodash";
import moment from "moment";
import axios from 'axios';
import { TOKEN_PREFIX, COMPANY_CODE } from '@/constants/token';
import { getToken } from '@/auth';
import { BASE_PATH } from '@/constants/static';
import { v4 as uuidv4 } from 'uuid';

/**
 * 等待一定时间后运行
 * @param time 睡眠的时间,单位毫秒
 */
export async function sleep(time: number): Promise<unknown> {
  return new Promise((resolve) => {
    setTimeout(resolve, time)
  })
}

export interface BdwResult<T> {
  code: number
  msg?: string
  data?: T
}

export interface OldBdwResult<T> {
  EXE_RES_STAT: boolean
  EXE_RES_MSG?: string
  EXE_RES_DATA?: T
}

export interface BdwTableResult<T> {
  griModel: T[]
  page: number
  record: number
  rows: number
  total: number
}


export type BdwRequest<T> = (...param: Array<any>) => Promise<BdwResult<T>>
export type OldBdwRequest<T> = (...param: Array<any>) => Promise<OldBdwResult<T>>

export type UnpackPromise<T> = T extends Promise<infer P> ? P : unknown
export type UnpackBdwResult<T> = T extends BdwResult<infer P> ? P : unknown
export type UnpackOldBdwResult<T> = T extends OldBdwResult<infer P> ? P : unknown

export type UnpackBdwResultPromise<T> = UnpackBdwResult<UnpackPromise<T>>
export type UnpackOldBdwResultPromise<T> = UnpackOldBdwResult<UnpackPromise<T>>


export function bdwRequest<F extends BdwRequest<any>>(func: F) {
  return async (...args: Parameters<F>): Promise<UnpackBdwResultPromise<ReturnType<F>>> => {
    const res = await func(...args);
    if (res.code !== 0) {
      if (res.msg) {
        throw new Error(res.msg)
      }
    }
    return res.data;
  }
}

export function bdwRequestIgnoreError<F extends BdwRequest<any>>(func: F) {
  return async (...args: Parameters<F>): Promise<UnpackBdwResultPromise<ReturnType<F>>> => {
    const res = await func(...args);
    if (res.code !== 0) {
      if (res.msg && res.data) {
        throw new Error(res.msg)
      }
    }
    return res.data;
  }
}

export function oldBdwRequest<F extends OldBdwRequest<any>>(func: F) {
  return async (...args: Parameters<F>): Promise<UnpackOldBdwResultPromise<ReturnType<F>>> => {
    const res = await func(...args);
    if (!res.EXE_RES_STAT) {
      throw new Error(res.EXE_RES_MSG)
    }
    return res.EXE_RES_DATA;
  }
}

// 根据fileName获取到后缀
export const getFileTypeByFileName = (fileName: string) => {
  if (!fileName) {
    return ""
    // throw new Error("请上传有文件名的文件")
  }
  return fileName.substring(fileName.lastIndexOf('.') + 1);
}


// 根据传入的accept，检验是否符合上传规则
export const beforeUploadExamineFormat = (file: any, accept: string) => {
  const acceptArray = accept ? accept.split(",") : [];
  const handleAcceptArray = acceptArray.map((item) => item.replace(".", ""));
  // 代表不进行校验
  if (handleAcceptArray.length === 0) {
    return true
  }
  // 需要校验的时候
  const fileType = getFileTypeByFileName(file.name)
  if (!handleAcceptArray.includes(fileType)) {
    return false
  }
  return true
}

// 讲图片妆花为base64
export const getBase64 = (img: any, callback: any) => {
  const reader = new FileReader();
  reader.addEventListener("load", () => callback(reader.result));
  reader.readAsDataURL(img);
}

interface GetWorkDayParams {
  startTime: moment.Moment | string,
  endTime: moment.Moment | string
  type?: WorkTimeType
}

export const bdwGetWorkDay = (params: GetWorkDayParams = { startTime: "", endTime: "", type: "FullDay" }) => {
  // 获取到传入的三个参数 开始时间 截至时间 工作标准类型
  const { startTime, endTime, type } = params;

  // 定义变量用来暂存传入的开始时间和截止时间
  let startTimeCalculateUseVal
  let endTimeCalculateUseVal

  // 根据工作标准类型判断开始时间和截止时间是否是 休息日
  // 如果是休息日时，将其修改为最近的一个工作日，且开始时间的小时分钟置为9：00，截止时间的小时分钟置为18：00
  switch (type) {
    // 标准 5 天工作制
    case "StandardWorkDay":
      // 如果开始时间为休息日 更新开始时间
      // 如果开始时间 isoWeekday 得到的值为6 代表当前开始时间是周六; 计算时，把开始时间后延，置为最近的一个工作日（加上2）
      if (moment(startTime).isoWeekday() === 6) {
        startTimeCalculateUseVal = moment(moment(startTime).add(2, "days").format('YYYY-MM-DD 09:00:00'))
      }
      // 如果开始时间 isoWeekday 得到的值为7 代表当前开始时间是周日; 计算时，把开始时间后延，置为最近的一个工作日（加上1）
      else if (moment(startTime).isoWeekday() === 7) {
        startTimeCalculateUseVal = moment(moment(startTime).add(1, "days").format('YYYY-MM-DD 09:00:00'))
      }
      // 开始时间为标准 5 天工作制下的工作日
      else {
        startTimeCalculateUseVal = moment(startTime)
      }

      // 如果当前截止时间为休息日 更新截止时间
      // 如果截止时间 isoWeekday 得到的值为6 代表当前截止时间是周六; 计算时，把截止时间前推，置为最近的一个工作日（减去1）
      if (moment(endTime).isoWeekday() === 6) {
        endTimeCalculateUseVal = moment(moment(startTime).subtract(1, "days").format('YYYY-MM-DD 18:00:00'))
      }
      // 如果截止时间 isoWeekday 得到的值为7 代表当前截止时间是周日; 计算时，把截止时间前推，置为最近的一个工作日（减去2）
      else if (moment(endTime).isoWeekday() === 7) {
        endTimeCalculateUseVal = moment(moment(endTime).subtract(2, "days").format('YYYY-MM-DD 18:00:00'))
      }
      // 截止时间为标准 5 天工作制下的工作日
      else {
        endTimeCalculateUseVal = moment(endTime)
      }
      break;
    // 6 天工作制
    case "SixDayWorkDay":
      // 如果开始时间为休息日 更新开始时间
      // 如果开始时间 isoWeekday 得到的值为7 代表当前开始时间是周日; 计算时，把开始时间后延，置为最近的一个工作日（加上1）
      if (moment(startTime).isoWeekday() === 7) {
        startTimeCalculateUseVal = moment(moment(startTime).add(1, "days").format('YYYY-MM-DD 09:00:00'))
      }
      // 开始时间为 6 天工作制下的工作日
      else {
        startTimeCalculateUseVal = moment(startTime)
      }

      // 如果当前截止时间为休息日 更新截止时间
      // 如果截止时间 isoWeekday 得到的值为7 代表当前截止时间是周日; 计算时，把截止时间前推，置为最近的一个工作日（减去1）
      if (moment(endTime).isoWeekday() === 7) {
        endTimeCalculateUseVal = moment(moment(endTime).subtract(1, "days").format('YYYY-MM-DD 18:00:00'))
      }
      // 截止时间为 6 天工作制下的工作日
      else {
        endTimeCalculateUseVal = moment(endTime)
      }
      break;
    case "FullDay":
    default:
      startTimeCalculateUseVal = moment(startTime)
      endTimeCalculateUseVal = moment(endTime)
  }

  // 定义变量记录处理后的开始时间和截止时间之间存在的整天数(包含了休息日)
  // 开始时间和截止时间的处理方法描述如下
  // 将开始时间和截止时间的小时分钟部分均置为09：00，如果不处理直接使用MomentJS API得到的天数结果是错误的
  // 小时分钟部分拎出来单独计算
  // 后续涉及解决MomentJS第三方库自身缺陷 此处使用let进行定义
  let diffResultFullDayNum = moment(endTimeCalculateUseVal.format('YYYY-MM-DD 09:00'))
    .diff(moment(startTimeCalculateUseVal.format('YYYY-MM-DD 09:00')), 'days');

  // 定义变量，调用momentJS API计算更新后的开始时间和截止时间之间的 分钟数
  // @ts-ignore
  const diffResultMin = moment(endTimeCalculateUseVal.format('YYYY-MM-DD 09:00')).
    diff(moment(startTimeCalculateUseVal.format('YYYY-MM-DD 09:00')), 'minutes');

  const hourAndMinutesOfStartTime = moment(startTimeCalculateUseVal).format('HH:mm')
  const hourAndMinutesOfEndTime = moment(endTimeCalculateUseVal).format('HH:mm')
  let endHourSameDiffMinutes = moment(endTimeCalculateUseVal).diff(moment(startTimeCalculateUseVal), "minutes")

  // 引用momentJS库计算实际天数时 需要规避momentJS库本身的Bug缺陷
  // 缺陷举例
  // 当开始时间和截止时间的小时分钟 完全一致时
  // 如开始时间为 2020-10-11 9：00  截止时间为 2020-10-12 9：00
  // 计算天数的结果可能为0 可能为1
  // 规避方法：获取到截止时间和开始时间之间的分钟数 diffResultMin 和 1440（完整1天的分钟数：24小时*60分钟）取余，看结果是否为0
  // 这种情况下, 截止时间和开始时间之间的分钟数 diffResultMin 一定是 1440 的整倍数
  // 取余结果为0 则不改变用momentJS计算得到的天数;取余结果不为0 则改变用momentJS计算得到的天数(+1)
  console.log('endHourSameDiffMinutes111111111', endHourSameDiffMinutes)
  // 由于开始时间和截止时间的小时分钟部分相同时，得到结果可能取余1440（一整天的分钟数），结果可能为539,也可能为1439，差一分钟为 一天的有效工作时间 或一整天时间
  if (endHourSameDiffMinutes % 1440 === 539 || endHourSameDiffMinutes % 1440 === 1439) {
    endHourSameDiffMinutes += 1
  }
  console.log('endHourSameDiffMinutes', endHourSameDiffMinutes)
  // 如果开始时间和截止时间的小时分钟部分完全相同时
  // 且endHourSameDiffMinutes取余1440结果不为0时 加一天
  if (hourAndMinutesOfStartTime === hourAndMinutesOfEndTime) {
    if (endHourSameDiffMinutes % 1440 !== 0) {
      diffResultFullDayNum += 1
    }
  }
  // 如果开始时间和截止时间的小时分钟部分恰好分别为一天有效工作时间的两个端点
  // 那endHourSameDiffMinutes取余结果应该为540 那么加一天
  if (
    (hourAndMinutesOfStartTime === '09:00' && hourAndMinutesOfEndTime === '18:00') ||
    (hourAndMinutesOfStartTime === '18:00' && hourAndMinutesOfEndTime === '09:00')
  ) {
    if (endHourSameDiffMinutes % 1440 === 540) {
      diffResultFullDayNum += 1
    }
  }

  // 定义字段记录当前工作日标准下 diffResultFullDayNum 中的有效工作日 (只计算了整天数)
  let effectiveWorkday = 0
  // 根据 工作日标准类型 排除 diffResultFullDayNum 中的休息日
  switch (type) {
    // 标准 5 天工作制
    case "StandardWorkDay":
      if (diffResultFullDayNum && diffResultFullDayNum > 0) {
        for (let i = 0; i < diffResultFullDayNum; i += 1) {
          // 如果当前判断的这一天不是周六和周日 那么有效工作日就加一天
          if (moment(startTimeCalculateUseVal).add(i, "days").isoWeekday() !== 6 &&
            moment(startTimeCalculateUseVal).add(i, "days").isoWeekday() !== 7) {
            effectiveWorkday += 1;
          }
        }
      }
      break;
    // 6 天工作制
    case "SixDayWorkDay":
      // if (endTimeCalculateUseVal.isoWeekday() === 6){
      //   diffResultFullDayNum -= 1
      // }
      if (diffResultFullDayNum && diffResultFullDayNum > 0) {
        for (let i = 0; i < diffResultFullDayNum; i += 1) {
          // if(i === 0){
          //   if ( moment(startTimeCalculateUseVal).add(i, "days").isoWeekday() !== 7) {
          //     effectiveWorkday += 1;
          //   }
          // }else{
          // 如果当前判断的这一天不是周日 那么有效工作日就加一天
          if (moment(startTimeCalculateUseVal).add(i, "days").isoWeekday() !== 1) {
            effectiveWorkday += 1;
          }
          // }
        }
      }
      break;
    case "FullDay":
    default:
      effectiveWorkday = diffResultFullDayNum
  }

  // console.log('计算得到的整天数（未排除休息日前）为：',diffResultFullDayNum)
  // console.log('正常情况下的有效工作日effectiveWorkday',effectiveWorkday)

  // 自定义保留小数时 只舍不入的方法
  // num为需要进行处理的数，decimal为精度，其值为整数，表示保留几位小数
  const ownFuncToFixed = (num: any, decimal: any) => {
    let numVal = num.toString();
    let index = numVal.indexOf('.')
    if (index !== -1) {
      numVal = numVal.substring(0, decimal + index + 1)
    } else {
      numVal = numVal.substring(0)
    }
    return parseFloat(numVal).toFixed(decimal)
  }

  // 计算起始时间和截止时间的 小时分钟部分 的天数
  // 定义变量记录开始时间和截止时间小时分钟部分计算得到的数值(单位:分钟)
  // 计算方法: 小时数*60+分钟数
  // 举例: 9:30 ===> 9*60+30 = 570
  const startTimeOfMinutesVal = Number(moment(startTimeCalculateUseVal).format('HH')) * 60 + Number(moment(startTimeCalculateUseVal).format('mm'))
  const endTimeOfMinutesVal = Number(moment(endTimeCalculateUseVal).format('HH')) * 60 + Number(moment(endTimeCalculateUseVal).format('mm'))
  // 定义变量记录小时分钟部分转化为天数后的数值（暂不进行小数位的取舍）
  let timeOfHourToDay
  // 根据两者的大小判断开始时间和截止时间小时分钟部分的时刻在一天中的先后关系
  // 同时计算出两者之差转化为天数后的值
  // 540 代表一天中有效工作时间（9小时）的分钟数（9*60）
  // 截止时间的小时分钟时刻 晚于 开始时间的小时分钟时刻
  if (endTimeOfMinutesVal > startTimeOfMinutesVal) {
    timeOfHourToDay = (endTimeOfMinutesVal - startTimeOfMinutesVal) / 540
  } else if (endTimeOfMinutesVal < startTimeOfMinutesVal) {
    effectiveWorkday -= 1
    timeOfHourToDay = (540 - (startTimeOfMinutesVal - endTimeOfMinutesVal)) / 540
  } else {
    timeOfHourToDay = 0
  }

  // 定义变量记录小时分钟差值转换为天数后得到的小数 进行小数位取舍后的值
  const timeOfHourToDayAfterDeal = ownFuncToFixed(timeOfHourToDay, 2)

  // console.log('小时分钟转换为差值后的天数',timeOfHourToDayAfterDeal)

  // 定义返回值（通过计算得到的工期）
  const periodResultByCalculate = Number(effectiveWorkday) + Number(timeOfHourToDayAfterDeal)

  console.log('返回的工期值为', periodResultByCalculate)

  return periodResultByCalculate
}



interface GetWorkEndTime {
  startTime?: string | moment.Moment,
  type?: WorkTimeType
  total: number | string | undefined
}

export const bdwGetEndTime = (params: GetWorkEndTime) => {
  // 获取传入的参数，并定义其初始值
  const { startTime = moment(), type = "FullDay", total: totalParam = 0 } = params ?? {};
  // 定义变量用来暂存传入的开始时间
  let startTimeCalculateUseVal

  // 根据工作标准类型判断开始时间是否是 休息日
  // 如果是休息日时，将其修改为最近的一个工作日，且开始时间的小时分钟置为9：00
  switch (type) {
    // 标准 5 天工作制
    case "StandardWorkDay":
      // 如果开始时间为休息日 更新开始时间
      // 如果开始时间 isoWeekday 得到的值为6 代表当前开始时间是周六; 计算时，把开始时间后延，置为最近的一个工作日（加上2）
      if (moment(startTime).isoWeekday() === 6) {
        startTimeCalculateUseVal = moment(moment(startTime).add(2, "days").format('YYYY-MM-DD 09:00:00'))
      }
      // 如果开始时间 isoWeekday 得到的值为7 代表当前开始时间是周日; 计算时，把开始时间后延，置为最近的一个工作日（加上1）
      else if (moment(startTime).isoWeekday() === 7) {
        startTimeCalculateUseVal = moment(moment(startTime).add(1, "days").format('YYYY-MM-DD 09:00:00'))
      }
      // 开始时间为标准 5 天工作制下的工作日
      else {
        startTimeCalculateUseVal = moment(startTime)
      }
      break;
    // 6 天工作制
    case "SixDayWorkDay":
      // 如果开始时间为休息日 更新开始时间
      // 如果开始时间 isoWeekday 得到的值为7 代表当前开始时间是周日; 计算时，把开始时间后延，置为最近的一个工作日（加上1）
      if (moment(startTime).isoWeekday() === 7) {
        startTimeCalculateUseVal = moment(moment(startTime).add(1, "days").format('YYYY-MM-DD 09:00:00'))
      }
      // 开始时间为 6 天工作制下的工作日
      else {
        startTimeCalculateUseVal = moment(startTime)
      }
      break;
    case "FullDay":
    default:
      startTimeCalculateUseVal = moment(startTime)
  }

  // 判断传入参数中的工期是否为NaN 并检查其类型是否为Number
  if (Number.isNaN(Number(totalParam))) {
    return startTimeCalculateUseVal;
  }
  // 如果传入的工期类型为string 就以10为基数进行取整
  const total = (typeof totalParam === "string") ? parseInt(totalParam, 10) : totalParam

  // 定义变量用于记录 计算得到的截止时间的值
  let endTimeByCalculate

  // 如果工期大于0
  if (total > 0) {
    // 将工期整数部分与小数部分拆开算 如2.5天拆成2天和0.5天进行计算
    // 工期小数部分
    // 工期原始值 - 工期向下取整后的值 = 工期小数部分
    const periodOfDecimal = Number((total - Math.floor(total)).toFixed(2))

    // 工期整数部分
    // Math.trunc()取给定数字的整数部分
    const periodOfInteger = Math.trunc(total)

    // 计算第一步
    // 将起始时间加上工期整数部分的值 得到计算截止时间值的第一步的暂存值
    // 以天为单位先加
    const firstStepOfEndTime = moment(startTimeCalculateUseVal).add(periodOfInteger, "days")

    // 如果小数部分不为0
    if (periodOfDecimal !== 0) {
      // 小数部分算出来的分钟数
      const periodOfDecimalToMin = periodOfDecimal * 9 * 60;
      // 计算第二步
      // 整数部分加上天之后 再加上小数部分算出的分钟数
      // 将第一步计算的暂存值 加上 小数部分转换成的分钟数的值
      // 定义变量记录第二步计算的暂存值
      const secondStepOfEndTime = moment(firstStepOfEndTime).add(periodOfDecimalToMin, 'minutes');

      // console.log('secondStepOfEndTime',secondStepOfEndTime)
      // console.log(moment('2020-11-12 01:56').diff(moment('2020-11-11 18:00'),'hours'))

      // 将第二步计算结果的暂存值 与 计算结果代表的那一天的18：00进行比较  看是否超过了18:00
      // 没超过18点直接保留计算值
      // 步骤一 超过了18点 将计算得到的值 diff 18点 获得分钟数
      // 步骤二 然后将 secondStepOfEndTime 的天数加1 并 设置小时分钟为 09：00
      // 步骤三 然后将得到的结果再加上 步骤一得到的分钟数

      // 将计算结果当天的18点 与 计算结果相减 得到毫秒数 两种情况  大于0 和 小于等于0
      // 定义变量记录第二步计算结果那天的18点
      const firstStepOfEndTimeOfEighteen = moment(firstStepOfEndTime).set({ 'hour': 18, 'minute': 0 })
      const diffMilliseconds = moment(firstStepOfEndTimeOfEighteen).diff(moment(secondStepOfEndTime), 'milliseconds');

      // console.log('测试一个日期值是否在另一个日起值后面',moment('2020-11-10 09:01').isAfter(moment('2020-11-10 09:00'),'minutes'))
      // console.log('secondStepOfEndTimeOfEighteen',firstStepOfEndTimeOfEighteen)
      // console.log('secondStepOfEndTime',secondStepOfEndTime)
      // console.log('diffMilliseconds的值为', diffMilliseconds)
      // console.log(moment(secondStepOfEndTime).startOf('h'))

      // 第三步
      // 超出了18：00
      if (diffMilliseconds <= 0) {
        // 定义变量记录 超出18点后的时间与18点之间的分钟数
        // 步骤一
        const targetTimeDiffMinutes = moment(secondStepOfEndTime).diff(moment(firstStepOfEndTimeOfEighteen), 'minutes')
        // 步骤二
        // 定义变量记录 第二步结果超出18点后时 应该更新的时间结果
        // 可能涉及到筛除非工作日
        const thirdStepOfEndTime = moment(firstStepOfEndTime).set({
          'hours': 9,
          'minutes': 0
        }).add(1, "days").add(targetTimeDiffMinutes, 'minutes')

        endTimeByCalculate = moment(thirdStepOfEndTime)
      } else {
        // 没有超过18：00
        // 如果小数部分为 0
        endTimeByCalculate = moment(secondStepOfEndTime)
      }
    } else {
      if (moment(startTimeCalculateUseVal).format('HH:mm') === '09:00') {
        endTimeByCalculate = moment(firstStepOfEndTime).subtract(1, "days").set({ hours: 18, minutes: 0 })
      } else {
        endTimeByCalculate = moment(firstStepOfEndTime)
      }
    }
  }
  // 定义变量记录 计算后得到的截止时间筛除非工作日后应该后延的准确的工作时间
  let endTimeByCalculatePrecise

  // console.log('初步计算结果（未排除休息日时）',endTimeByCalculate)
  // console.log('工作标准类型',type)

  // 定义变量记录工期值 判断是否满足了工期要求来筛除休息日 其值需要累加改变并与工期值进行比较 用let定义
  // 初始值为0 工期存在小数部分时 把小数部分代表的那一天当成一整天去检验是否为当前工作制标准类型下的休息日
  let periodVirtualValue = 0
  // 记录实际验证过是否为休息日的天数
  let practicalTime = 0;
  // 记录截止时间前包含了几个休息日
  let restDayNum = 0
  switch (type) {
    case 'StandardWorkDay':
      do {
        practicalTime += 1;
        // 如果给加一天，isoWeekday 后的值为7 ，当前为周六
        // 如果给加一天，isoWeekday 后的值为1 ，当前为周日
        if (moment(startTimeCalculateUseVal).add(practicalTime, "days").isoWeekday() !== 7 &&
          moment(startTimeCalculateUseVal).add(practicalTime, "days").isoWeekday() !== 1) {
          periodVirtualValue += 1;
        } else {
          restDayNum += 1
        }
      } while (periodVirtualValue < total)
      endTimeByCalculatePrecise = moment(endTimeByCalculate).add(restDayNum, "days")
      break;
    case 'SixDayWorkDay':
      do {
        practicalTime += 1;
        // 如果给加一天，isoWeekday 后的值为1 ，当前为周日
        if (moment(startTimeCalculateUseVal).add(practicalTime, "days").isoWeekday() !== 1) {
          periodVirtualValue += 1;
        } else {
          restDayNum += 1
        }
      } while (periodVirtualValue < total)
      endTimeByCalculatePrecise = moment(endTimeByCalculate).add(restDayNum, "days")
      if (moment(endTimeByCalculatePrecise).isoWeekday() === 7) {
        endTimeByCalculatePrecise = moment(endTimeByCalculatePrecise).add(1, "days")
      }
      break;
    case 'FullDay':
    default:
      endTimeByCalculatePrecise = moment(endTimeByCalculate)
  }
  // console.log('返回的截止时间计算结果',endTimeByCalculatePrecise)
  return moment(endTimeByCalculatePrecise)
}

export type WorkTimeType = "FullDay" | "SixDayWorkDay" | "StandardWorkDay";

export interface GetWorkTimeParams {
  startTime?: string
  endTime?: string
  type: WorkTimeType
  total?: number
}


export class Signal {
  s: number = 0

  get = () => {
    return this.s;
  }

  p = () => {
    this.s += 1
  }

  v = () => {
    this.s -= 1
  }
}

// 判断这个对象数组中是否含有这个对象
export const judgeArrayHasObject = (obj: Object, arr: Array<object>): boolean => {
  const filterTheArr = arr && arr.filter((item) => JSON.stringify(item) === JSON.stringify(obj));
  if (filterTheArr && filterTheArr.length > 0) {
    return true
  }
  return false
}

/**
 *  工具函数，判断显示时间格式。
 *  如果距离现在是x分钟前，返回x分钟前
 *  如果是x小时，返回x小时前
 *  超过一天，那么就返回 具体时间
 * */
export const bdwShowTimeFunction = (time: string | moment.Moment) => {
  if (!time) {
    return ""
  }
  const nowTime = new Date().getTime();
  const diffTime = moment(nowTime).diff(moment(time), "hour");

  if (diffTime <= 1) {
    return moment(time).from(nowTime);
  } else if (1 < diffTime && diffTime <= 24) {
    return moment(time).from(nowTime);
  } else {
    return moment(time).format("MM-DD HH:mm")
  }
}


export function flattenFun<T>(arr: T[], childKey = "children"): T[] {
  return arr.reduce((result, item) => {
    return [...result, item, ...(flattenFun((item[childKey] as T[]) ?? [], childKey))]
  }, [] as T[]);
}

// 格式化函数
export const formatSelectOptions = (arr: any, flag = false) => {
  if (arr && arr.length) {
    if (flag) {
      return arr.map((item: any) => ({
        value: item.value,
        label: item.name
      }))
    } else {
      return arr.map((item: any) => ({
        value: item.name,
        label: item.value
      }))
    }


  } else {
    return [];
  }
}
/**
 * @description 文件下载
 * @param fileUrl 文件地址
 * @param fileName 文件名
 */
export const fileDownload = (fileUrl?: string | null, fileName?: string | null) => {
  if (fileUrl && fileName) {
    fetch(fileUrl).then((res) => {
      res.blob().then(blob => {
        const a = document.createElement('a');
        const href = window.URL.createObjectURL(blob);
        a.setAttribute('href', href);
        a.setAttribute('download', fileName);
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(href);
        document.body.removeChild(a);
      });
    }).catch((err) => {
      message.warn(`请检查文件地址是否正确!`);
    });
  }

};
/**
 * @description 文件导出
 * @param {*}
 * @public
*/
export const fileExport = (fileUrl?: string | null) => {
  if (fileUrl) {
    axios({
      method: 'get',
      url: BASE_PATH + fileUrl,
      headers: {
        companyCode: COMPANY_CODE,
        Authorization: TOKEN_PREFIX + getToken()?.access_token,
      },
      responseType: 'blob',
    }).then(res => {
      const fileNames = decodeURIComponent(res.headers['content-disposition']?.split('=')[1]);
      const blob = new Blob([res.data], { type: 'application/vnd.ms-excel;charset=UTF-8' });
      const a = document.createElement('a');
      const href = window.URL.createObjectURL(blob);
      a.setAttribute('href', href);
      a.setAttribute('download', fileNames);
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(href);
      document.body.removeChild(a);
    }).catch((err) => {
      message.warn(err.message);
    });
  }
};
export function initTaskIndex<T>(arr?: T[], parent?: T, value = "value", childKey = "children", indexKey = "index", parentId = "parentId", parentTitle = "parentTitle", parentDetail = "parent") {
  arr?.forEach((it, index) => {
    // eslint-disable-next-line no-param-reassign
    it[indexKey] = [...((parent?.[indexKey]) ?? []), index]
    //@ts-ignore
    it[parentId] = parent?.taskId;
    //@ts-ignore
    it[parentTitle] = parent ? parent?.name : null;
    it[parentDetail] = parent ? cloneDeep(parent) : null;
    //@ts-ignore
    it[value] = it.taskId
    initTaskIndex(it[childKey], it)
  })
}
export function initIndex<T>(arr?: Array<T>, parent?: T, childKey = "children", indexKey = "index") {
  arr?.forEach((it, index) => {
    // eslint-disable-next-line no-param-reassign
    it[indexKey] = [...((parent?.[indexKey]) ?? []), index]
    initIndex(it[childKey], it)
  })
}
//添加树节点
export const addTaskItem = (taskId: string, tree: any, obj: any, index?: number) => {
  try {
    tree.forEach((item: any) => {
      if (item.taskId == taskId) {
        if (item.children) {
          // if (index !== undefined) {
          //   item.children.splice(index + 1, 0, obj)
          // } else {
          //   item.children.push(obj)
          // }
          item.children.push(obj)
        } else {
          item.children = [obj];
        }
        throw new Error('添加成功!')
      } else {
        if (item.children) {
          addTaskItem(taskId, item.children, obj, index);
        }
      }
    })
  } catch (e) {
    // console.error(e)
  }

}
//删除树节点
export const deleteTaskItem = (tree: any, taskId: string,) => {
  if (!tree || !tree.length) {
    return
  }
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].taskId == taskId) {
      tree.splice(i, 1);
      break;
    }
    deleteTaskItem(tree[i].children, taskId);
  }
}
//查找过滤树节点
export const filterTaskItem = (tree: any, key: string, keyType: string, callback?: (id: string, obj?: any) => void) => {
  if (!(tree && tree.length)) return
  const arr: any[] = [];
  for (const i of tree) {
    const subs = filterTaskItem(i.children, key, keyType, callback);
    if ((subs && subs.length) || (i[keyType] && i[keyType].indexOf(key) !== -1)) {
      i.children = subs;
      callback?.(i.taskId, i)
      arr.push(i);
    }
  }
  return arr;
}
//查找树节点
export const findTaskItem = (tree: any, taskId: string, callback?: (data: any) => void) => {
  if (!(tree && tree.length)) return
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].taskId == taskId) {
      return callback?.(tree[i]);
    }
    if (tree[i].children) {
      findTaskItem(tree[i].children, taskId, callback);
    }
  }
}
//更新树节点
export const renewTaskItem = (tree: any, taskId: string, obj: any) => {
  if (!(tree && tree.length)) return
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].taskId == taskId) {
      tree[i] = obj;
      break;
    }
    if (tree[i].children) {
      renewTaskItem(tree[i].children, taskId, obj);
    }
  }
}
//扁平化
export function flatten<T>(arr: T[], childKey = "children"): T[] {
  return arr.reduce((result, item) => {
    return [...result, item, ...(flatten((item[childKey] as T[]) ?? [], "children"))]
  }, [] as T[]);
}
export function setLocalStorage<T>(data: T, name: string): void {
  window.localStorage.setItem(name, JSON.stringify(data));
}
export function getLocalStorage(data: string) {
  if (window.localStorage.getItem(data)) {
    // @ts-ignore
    return JSON.parse(window.localStorage.getItem(data));
  } else {
    return {}
  }
}
// 防抖
export const handleDebonce = (fn: Function, wait: number) => {
  let timmer: any;
  return function (...args: any) {
    if (timmer) clearTimeout(timmer);
    timmer = setTimeout(function () {
      fn(...args);
    }, wait);
  };
};

// 处理团队任务数据
const rebuildData = (orgList: any[], taskList: any[]) => {
  if (!Array.isArray(orgList)) return

  for (let i = 0; i < orgList.length; i++) {
    orgList[i].name = orgList[i].orgName

    if (orgList[i].children) {
      rebuildData(orgList[i].children, taskList)
    } else if (!Array.isArray(orgList[i].children) || orgList[i].children.length == 0) {
      // 找到最底层的子部门后，将员工及员工下的任务加到最底层的子部门上
      orgList[i].children = taskList.filter(item => item.orgId.split('-')[0] == orgList[i].orgId)
    }
  }
  return orgList;
}
export const rebuildTeamTaskData = (teamTaskObj: {
  employees: any[]
  organizations: any[]
}) => {
  const { organizations, employees } = teamTaskObj;

  // 所有的任务数据
  let taskList = employees.map(item => ({
    ...item,
    orgId: item.orgId + '-' + Math.random(),  // 任务数据的orgId和部门数据的orgId相同，为了保证orgId的唯一性，需要给它加点料
    children: item.teamProjectTasks?.length ? item.teamProjectTasks.map((taskItem: any) => ({
      ...taskItem,
      name: taskItem.taskName, // 仅用于列表解析用
      orgId: taskItem.taskId // 仅用于列表解析用
    })) : []
  }));

  // 所有的部门数据（uniqBy(organizations, 'id')为做去重处理）
  return rebuildData(uniqBy(organizations, 'id'), taskList);
}

// 获取只看与我相关数据
export const handleGetMineEmployeesData = (myUserId: string, employeesList: any[]) => {
  let taskList: any[] = []

  // 过滤任务列表中任务leaderId和当前用户id相同的数据
  if (employeesList.length) {
    taskList = employeesList.map((item) => ({
      ...item,
      teamProjectTasks: item.teamProjectTasks?.filter((projectItem: any) => projectItem.leaderId == myUserId)
    }))
  }

  // 返回过滤后不为空的任务数据
  return taskList.length ? taskList.filter(item => item.teamProjectTasks.length !== 0) : taskList
}
export const getRichImgSrc = (str: string) => {
  if (!Boolean(str)) return []
  let data: any[] = [];
  // @ts-ignore
  str.replace(/<img [^>]*src=['"]([^'"]+)[^>]*>/g, function (match: string, capture) {
    data.push(capture);
  });
  return data;
}
export const getText = (richCont: string) => {
  if (!Boolean(richCont)) return ''
  let regx = /<\/?[a-zA-Z]+(\s+[a-zA-Z]+=".*")*>/g;
  return richCont.replaceAll(regx, '');
}
// 随机生成一个字符串 不重复
export const randomlyGenerateId = () => {
  let idStr = Date.now().toString(36);
  idStr += Math.random().toString(36).substr(3);
  return idStr;
}
// 找到目标id，并往children里添加数据
export const findAndAdd = (tree: any[], targetId: string | number, newItem: any) => {
  // 递归函数用于查找目标id并添加数据
  function recursiveFindAndAdd(nodes: any[]) {
    nodes?.forEach((node: any) => {
      if (node?.id === targetId) {
        if (!node?.children) {
          node.children = [];
        }
        node?.children?.push(newItem);
      } else if (node?.children && node?.children?.length > 0) {
        recursiveFindAndAdd(node?.children);
      }
    });
  };
  recursiveFindAndAdd([...tree]);
  return tree;
}
// 找到父级id
export const findParent = (treeData: any[], id: string | number) => {
  for (let i = 0; i < treeData.length; i++) {
    if (treeData[i]?.id == id) {
      return treeData[i]?.parentId || null
    }
    if (treeData[i]?.children?.length > 0) {
      const parentId: string = findParent(treeData[i]?.children, id);
      if (parentId) return parentId;
    }

  }
  return null;
};
// 为树形数据添加parentId
export const addParentIds = (nodes: any[], parentId = null) => {
  nodes?.forEach((node: any) => {
    node.parentId = parentId;
    if (node?.children?.length) {
      addParentIds(node?.children, node?.taskId)
    }
  });
  return nodes;
};
// 根据目标id找到树形数据里的目标数据
export const findDataById = (data: any, targetId: string, id = 'id') => {
  for (let i = 0; i < data?.length; i++) {
    if (data[i]?.[id] == targetId) {
      return data[i];
    };
    if (data[i]?.children?.length) {
      const found: any = findDataById(data[i]?.children, targetId);
      if (found) {
        return found;
      }
    }
  }
  return null;
};
// 找到目标数据并进行替换
export const findAndUpdateById = (tree: any, targetId: string, newObj: any) => {
  function modifyNode(nodes: any[]) {
    if (!nodes || nodes?.length === 0) return;
    nodes?.forEach((node: any) => {
      if (node?.id === targetId) {
        Object.assign(node, newObj);
      }
      if (node?.children?.length) {
        modifyNode(node?.children)
      }
    });
  };
  modifyNode(tree);
  return tree;
}
// 根据id移除数据
export const deleteNodeById = (treeData: any[], targetId: string) => {
  function findAndDelete(nodes: any, id: string) {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (node?.id === id) {
        //  删除找到的节点
        nodes?.splice(i, 1);
        return true;
      }
      if (node?.children && findAndDelete(node?.children, id)) {
        return true;
      }
    }
    return false;
  }
  const result = findAndDelete([...treeData], targetId);
  return result ? treeData : null
}


// 计算数据
export const recalculateValues = (node: any) => {
  if (node?.children?.length) {
    node?.children?.forEach((child: any) => {
      child.projectRatio = parseFloat((((Number(child?.ratio || 0)) * (Number(node?.projectRatio || 0))) / 100)?.toFixed(3));
      // 继续递归更新子节点
      recalculateValues(child);
    });
  }
};
// 树形数据只保留自己需要的属性
export const extractProps = (nodes: any, props: string[]) => {
  return nodes?.map((node: any) => {
    const obj: any = {};
    props?.forEach((prop: string) => {
      if (node?.hasOwnProperty(prop)) {
        if (prop === 'id') {
          // id这里要判断是否是数字，如果是数字，则保留，否则置空
          const curId = node?.taskId;
          if (!curId) {
            // 不是数字
            obj.taskId = null;
          } else {
            obj.taskId = node[prop];
          }
        } else {
          obj[prop] = node[prop];
        }

      }
    });
    if (node?.children?.length) {
      obj.children = extractProps(node?.children, props);
    };
    return obj;
  })
};
// 递归增加某个属性
const traverseTree = (nodes: any[], callback: Function) => {
  nodes?.forEach((node: any) => {
    callback(node);
    if (node?.children?.length) {
      traverseTree(node?.children, callback);
    }
  });
};
// 递归增加id
export const addFieldToTree = (tree: any[]) => {
  traverseTree(tree, (node: any) => {
    node.id = node?.taskId || '';
  })
};
// 处理下标
export function initTaskIndex1<T>(arr?: Array<T>, parent?: T, childKey = "children", indexKey = "index") {
  arr?.forEach((it, index) => {
    // eslint-disable-next-line no-param-reassign
    it[indexKey] = [...((parent?.[indexKey]) ?? []), index]
    initTaskIndex1(it[childKey], it)
  })
};
// 添加唯一的Key
export const addKey = (list: any) => {
  list.forEach((item: any) => {
    if (item.classificationVos?.length) {
      item.classificationVos.forEach((ii: any) => {
        if (!ii.groupIds) ii.groupIds = uuidv4();
        if (ii.projects?.length) {
          ii.projects.forEach((iii: any) => {
            if (!iii.onlyId) iii.onlyId = uuidv4();
          })
        }
      })
    }
  })
};
// 递归增加项目id
export const addProjectTaskIdFun = (tree: any[], projectId: string) => {
  traverseTree(tree, (node: any) => {
    node.projectId = projectId || '';
  })
};
// 按照自定义的属性进行合并
export const ngOnInit = (
  data: any,
  type: any,
  otherType?: any,
  otherTypeT?: any,
) => {
  // 合并相同项
  let res = [];
  let narr = [];
  for (let i = 0; i < data.length; i++) {
    let n = res.indexOf(data[i][type]);
    if (n == -1) {
      res.push(data[i][type]);
      let obj = {
        [type]: data[i][type],
        children: [data[i]],
      };
      if (otherType) {
        obj = {
          ...obj,
          [otherType]: data[i][otherType],
        };
      }
      if (otherTypeT) {
        obj = {
          ...obj,
          [otherTypeT]: data[i][otherTypeT],
        };
      }
      narr.push(obj);
    } else {
      narr[n].children.push(data[i]);
    }
  }
  return narr;
};