# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Asebter azewwar
previous_label=Azewwar
next.title=Asebter d-iteddun
next_label=Ddu ɣer zdat

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Asebter
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=ɣef {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} n {{pagesCount}})

zoom_out.title=Semẓi
zoom_out_label=Semẓi
zoom_in.title=Semɣeṛ
zoom_in_label=Semɣeṛ
zoom.title=Semɣeṛ/Semẓi
presentation_mode.title=Uɣal ɣer Uskar Tihawt
presentation_mode_label=Askar Tihawt
open_file.title=Ldi Afaylu
open_file_label=Ldi
print.title=Siggez
print_label=Siggez
download.title=Sader
download_label=Azdam
save.title=Sekles
save_label=Sekles
bookmark.title=Timeẓri tamirant (nɣel neɣ ldi ɣef usfaylu amaynut)
bookmark_label=Askan amiran

# Secondary toolbar and context menu
tools.title=Ifecka
tools_label=Ifecka
first_page.title=Ddu ɣer usebter amezwaru
first_page_label=Ddu ɣer usebter amezwaru
last_page.title=Ddu ɣer usebter aneggaru
last_page_label=Ddu ɣer usebter aneggaru
page_rotate_cw.title=Tuzzya tusrigt
page_rotate_cw_label=Tuzzya tusrigt
page_rotate_ccw.title=Tuzzya amgal-usrig
page_rotate_ccw_label=Tuzzya amgal-usrig

cursor_text_select_tool.title=Rmed afecku n tefrant n uḍris
cursor_text_select_tool_label=Afecku n tefrant n uḍris
cursor_hand_tool.title=Rmed afecku afus
cursor_hand_tool_label=Afecku afus

scroll_page.title=Seqdec adrurem n usebter
scroll_page_label=Adrurem n usebter
scroll_vertical.title=Seqdec adrurem ubdid
scroll_vertical_label=Adrurem ubdid
scroll_horizontal.title=Seqdec adrurem aglawan
scroll_horizontal_label=Adrurem aglawan
scroll_wrapped.title=Seqdec adrurem yuẓen
scroll_wrapped_label=Adrurem yuẓen

spread_none.title=Ur sedday ara isiɣzaf n usebter
spread_none_label=Ulac isiɣzaf
spread_odd.title=Seddu isiɣzaf n usebter ibeddun s yisebtar irayuganen
spread_odd_label=Isiɣzaf irayuganen
spread_even.title=Seddu isiɣzaf n usebter ibeddun s yisebtar iyuganen
spread_even_label=Isiɣzaf iyuganen

# Document properties dialog box
document_properties.title=Taɣaṛa n isemli…
document_properties_label=Taɣaṛa n isemli…
document_properties_file_name=Isem n ufaylu:
document_properties_file_size=Teɣzi n ufaylu:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KAṬ ({{size_b}} ibiten)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MAṬ ({{size_b}} iṭamḍanen)
document_properties_title=Azwel:
document_properties_author=Ameskar:
document_properties_subject=Amgay:
document_properties_keywords=Awalen n tsaruţ
document_properties_creation_date=Azemz n tmerna:
document_properties_modification_date=Azemz n usnifel:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Yerna-t:
document_properties_producer=Afecku n uselket PDF:
document_properties_version=Lqem PDF:
document_properties_page_count=Amḍan n yisebtar:
document_properties_page_size=Tuγzi n usebter:
document_properties_page_size_unit_inches=deg
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=s teɣzi
document_properties_page_size_orientation_landscape=s tehri
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Asekkil
document_properties_page_size_name_legal=Usḍif
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Taskant Web taruradt:
document_properties_linearized_yes=Ih
document_properties_linearized_no=Ala
document_properties_close=Mdel

print_progress_message=Aheggi i usiggez n isemli…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Sefsex

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Sken/Fer agalis adisan
toggle_sidebar_notification2.title=Ffer/Sekn agalis adisan (isemli yegber aɣawas/ticeqqufin yeddan/tissiwin)
toggle_sidebar_label=Sken/Fer agalis adisan
document_outline.title=Sken isemli (Senned snat tikal i wesemɣer/Afneẓ n iferdisen meṛṛa)
document_outline_label=Isɣalen n isebtar
attachments.title=Sken ticeqqufin yeddan
attachments_label=Ticeqqufin yeddan
layers.title=Skeen tissiwin (sit sin yiberdan i uwennez n meṛṛa tissiwin ɣer waddad amezwer)
layers_label=Tissiwin
thumbs.title=Sken tanfult.
thumbs_label=Tinfulin
current_outline_item.title=Af-d aferdis n uɣawas amiran
current_outline_item_label=Aferdis n uɣawas amiran
findbar.title=Nadi deg isemli
findbar_label=Nadi

additional_layers=Tissiwin-nniḍen
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Asebter {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Asebter {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Tanfult n usebter {{page}}

# Find panel button title and messages
find_input.title=Nadi
find_input.placeholder=Nadi deg isemli…
find_previous.title=Aff-d tamseḍriwt n twinest n deffir
find_previous_label=Azewwar
find_next.title=Aff-d timseḍriwt n twinest d-iteddun
find_next_label=Ddu ɣer zdat
find_highlight=Err izirig imaṛṛa
find_match_case_label=Qadeṛ amasal n isekkilen
find_match_diacritics_label=Qadeṛ ifeskilen
find_entire_word_label=Awalen iččuranen
find_reached_top=Yabbeḍ s afella n usebter, tuɣalin s wadda
find_reached_bottom=Tebḍeḍ s adda n usebter, tuɣalin s afella
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} seg {{total}} n tmeɣṛuḍin
find_match_count[two]={{current}} seg {{total}} n tmeɣṛuḍin
find_match_count[few]={{current}} seg {{total}} n tmeɣṛuḍin
find_match_count[many]={{current}} seg {{total}} n tmeɣṛuḍin
find_match_count[other]={{current}} seg {{total}} n tmeɣṛuḍin
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Ugar n {{limit}} n tmeɣṛuḍin
find_match_count_limit[one]=Ugar n {{limit}} n tmeɣṛuḍin
find_match_count_limit[two]=Ugar n {{limit}} n tmeɣṛuḍin
find_match_count_limit[few]=Ugar n {{limit}} n tmeɣṛuḍin
find_match_count_limit[many]=Ugar n {{limit}} n tmeɣṛuḍin
find_match_count_limit[other]=Ugar n {{limit}} n tmeɣṛuḍin
find_not_found=Ulac tawinest

# Error panel labels
error_more_info=Ugar n telɣut
error_less_info=Drus n isalen
error_close=Mdel
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Izen: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Tanebdant: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Afaylu: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Izirig: {{line}}

# Predefined zoom values
page_scale_width=Tehri n usebter
page_scale_fit=Asebter imaṛṛa
page_scale_auto=Asemɣeṛ/Asemẓi awurman
page_scale_actual=Teɣzi tilawt
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading=Asali…
loading_error=Teḍra-d tuccḍa deg alluy n PDF:
invalid_file_error=Afaylu PDF arameɣtu neɣ yexṣeṛ.
missing_file_error=Ulac afaylu PDF.
unexpected_response_error=Aqeddac yerra-d yir tiririt ur nettwaṛǧi ara.

rendering_error=Teḍra-d tuccḍa deg uskan n usebter.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Tabzimt {{type}}]
password_label=Sekcem awal uffir akken ad ldiḍ afaylu-yagi PDF
password_invalid=Awal uffir mačči d ameɣtu, Ɛreḍ tikelt-nniḍen.
password_ok=IH
password_cancel=Sefsex

printing_not_supported=Ɣuṛ-k: Asiggez ur ittusefrak ara yakan imaṛṛa deg iminig-a.
printing_not_ready=Ɣuṛ-k: Afaylu PDF ur d-yuli ara imeṛṛa akken ad ittusiggez.
web_fonts_disabled=Tisefsiyin web ttwassensent; D awezɣi useqdec n tsefsiyin yettwarnan ɣer PDF.

# Editor
editor_free_text2.title=Aḍris
editor_free_text2_label=Aḍris
editor_ink2.title=Suneɣ
editor_ink2_label=Suneɣ

free_text2_default_content=Bdu tira...

# Editor Parameters
editor_free_text_color=Initen
editor_free_text_size=Teɣzi
editor_ink_color=Ini
editor_ink_thickness=Tuzert
editor_ink_opacity=Tebrek

# Editor aria
editor_free_text2_aria_label=Amaẓrag n uḍris
editor_ink2_aria_label=Amaẓrag n usuneɣ
editor_ink_canvas_aria_label=Tugna yettwarnan sɣur useqdac
