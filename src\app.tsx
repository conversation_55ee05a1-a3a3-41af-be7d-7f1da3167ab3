import '@grapecity/wijmo.styles/wijmo.css';
import '@grapecity/wijmo.touch';
import './assets/iconfont/iconfont.css';
import './assets/iconfont/iconfont';

// 路由修改钩子，可以在这里动态修改路由
export function patchRoutes({ routes }: { routes: any }) {
  console.log('应用路由已加载:', routes);
}

// 路由变化钩子，可以在这里监听路由变化
export function onRouteChange({ location, routes, action }: { 
  location: { pathname: string }, 
  routes: any, 
  action: string 
}) {
  console.log('路由变化:', location.pathname);
} 