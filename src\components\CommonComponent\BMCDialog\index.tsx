import React from 'react';
import {
	Box,
	Dialog,
	DialogProps,
	DialogTitle,
	IconButton,
} from '@mui/material';
import { SxProps } from '@mui/system';
import { Theme } from '@mui/material/styles';
import { Close } from '@mui/icons-material';

const omit = require('lodash/omit');

const BMCDialog: React.FC<BMCDialogProps> = (props) => {
	const {
		position,
		fullHeight = false,
		fullScreen = false,
		fitSide = false,
		showCloseBtn = true,
		clickBackdropToClose = true,
		withTitle = true,
	} = props;

	const subProps = omit(props, [
		'onCloseClick',
		'children',
		'title',
		'fitSide',
		'sx',
		'fullHeight',
		'position',
		'width',
		'height',
		'onClose',
		'clickBackdropToClose',
		'showCloseBtn',
		'withTitle',
	]) as DialogProps;

	const sx: SxProps<Theme> = {
		right: fullScreen ? 0 : position === 'left' ? 'auto' : 0,
		left: fullScreen ? 0 : position === 'right' ? 'auto' : 0,
		'& .MuiDialogTitle-root, .MuiDialogContent-root, .MuiDialogActions-root':
			{
				px: '2rem',
			},
		'& .MuiDialogTitle-root': {
			boxShadow: '0 1px 3px rgba(0, 0, 0, 0.08)',
			color: '#333333',
			fontWeight: 600,
			fontSize: '1rem',
			py: '0.5rem',
		},
		'& .MuiDialogContent-root': {
			// minHeight: '25rem'
			py: '1rem !important',
		},
		'& .MuiDialogActions-root': {
			boxShadow: '0 -1px 3px rgba(0, 0, 0, 0.08)',
			pt: '0.5rem',
			pb: '0.75rem',
		},
		'& .MuiDialog-paper': {
			borderRadius: 0,
			height: fullHeight ? '100vh' : props.height,
			maxHeight: fullHeight ? '100vh' : undefined,
			margin: fitSide ? 0 : undefined,
			width: props.width,
			maxWidth: props.width ? '100vw' : undefined,
		},
	};

	const handleClose = (event: {}, reason: 'backdropClick' | 'escapeKeyDown') => {
		if (clickBackdropToClose) {
			props.onClose?.(event, reason);
		} else {
			if (reason !== 'backdropClick') {
				props.onClose?.(event, reason);
			}
		}
	};

	return (
		<Dialog sx={sx} {...subProps} onClose={handleClose}>
			{withTitle && (
				<DialogTitle>
					<Box
						sx={{
							display: 'flex',
							alignItems: 'center',
							justifyContent: 'space-between',
							height: '2rem',
						}}
					>
						<span>{props.title}</span>
						{showCloseBtn && (
							<IconButton onClick={props.onCloseClick}>
								<Close />
							</IconButton>
						)}
					</Box>
				</DialogTitle>
			)}{' '}
			{props.children}
		</Dialog>
	);
};

export interface BMCDialogProps extends Omit<DialogProps, 'title' | 'sx'> {
	// 右上角关闭图标点击事件
	onCloseClick?: () => void;
	// 是否显示右上角关闭按钮, 默认 true
	showCloseBtn?: boolean;
	// 标题
	title?: string | React.ReactNode;
	// 对话框位置, 默认 center
	position?: 'center' | 'left' | 'right';
	// 是否占满高度, 默认 false
	fullHeight?: boolean;
	// 是否贴边, 默认 false
	fitSide?: boolean;
	// 对话框宽
	width?: number | string;
	// 对话框高
	height?: number | string;
	// 点击遮罩是否关闭对话框, 默认 true
	clickBackdropToClose?: boolean;
	// 是否渲染标题
	withTitle?: boolean;
}

export default BMCDialog;
