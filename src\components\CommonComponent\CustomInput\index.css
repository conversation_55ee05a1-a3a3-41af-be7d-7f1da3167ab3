/* Common underline style - replacing .commonUnderLine() mixin */
.customInput-container input:focus::after,
.customInput-container select:focus::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 2px;
  background-color: #1976d2;
}

.customInput-container {
  width: 100%;
  box-sizing: border-box;
}

.customInput-container input::-webkit-input-placeholder {
  font-size: 12px;
}

.customInput-container input {
  font-size: 12px;
  padding: 0;
}

.customInput-container select {
  font-size: 12px;
}

/* Common flex styles - replacing .commonFlex() mixin */
.customInput-container .customInput-renderInput,
.customInput-container .customInput-renderSelect,
.customInput-container .customInput-renderInputAndText,
.customInput-container .customInput-renderSelectAndText {
  font-size: 12px;
  width: 100%;
  min-height: 2rem;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  flex-shrink: 0;
  box-sizing: border-box;
  position: relative;
  line-height: 1.4375em;
}

.customInput-container .customInput-renderInput > span:nth-of-type(1),
.customInput-container .customInput-renderSelect > span:nth-of-type(1),
.customInput-container .customInput-renderInputAndText > span:nth-of-type(1),
.customInput-container .customInput-renderSelectAndText > span:nth-of-type(1) {
  flex-shrink: 0;
  color: #666666;
}

/* Hidden underline styles - replacing .hiddenUnderlineCommonFlex() mixin */
.customInput-container .customInput-renderInput-hidden,
.customInput-container .customInput-renderSelect-hidden,
.customInput-container .customInput-renderInputAndText-hidden,
.customInput-container .customInput-renderSelectAndText-hidden {
  font-size: 12px;
  width: 100%;
  min-height: 2rem;
  display: flex;
  flex-shrink: 0;
  box-sizing: border-box;
  position: relative;
  line-height: 1.4375em;
}

.customInput-container .customInput-renderInput-hidden > span:nth-of-type(1),
.customInput-container .customInput-renderSelect-hidden > span:nth-of-type(1),
.customInput-container
  .customInput-renderInputAndText-hidden
  > span:nth-of-type(1),
.customInput-container
  .customInput-renderSelectAndText-hidden
  > span:nth-of-type(1) {
  width: 4.25rem;
  flex-shrink: 0;
  color: #666666;
}

/* Input specific styles */
.customInput-container .customInput-renderInput input,
.customInput-container .customInput-renderInput-hidden input {
  width: 100%;
  border: none;
}

.customInput-container .customInput-renderInput input:focus,
.customInput-container .customInput-renderInput-hidden input:focus {
  outline: none;
}

/* Select specific styles */
.customInput-container .customInput-renderSelect select,
.customInput-container .customInput-renderSelect-hidden select {
  width: 80%;
  border: none;
}

.customInput-container .customInput-renderSelect select:focus,
.customInput-container .customInput-renderSelect-hidden select:focus {
  outline: none;
}

/* Radio specific styles */
.customInput-container .customInput-renderRadio,
.customInput-container .customInput-renderRadio-hidden {
  font-size: 12px;
  width: 100%;
  min-height: 2rem;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.customInput-container .customInput-renderRadio > span:first-of-type,
.customInput-container .customInput-renderRadio-hidden > span:first-of-type {
  width: 4.25rem;
  flex-shrink: 0;
  color: #666666;
}

.customInput-container .customInput-renderRadio span,
.customInput-container .customInput-renderRadio-hidden span {
  padding: 0;
}

/* Input and Text specific styles */
.customInput-container .customInput-renderInputAndText,
.customInput-container .customInput-renderInputAndText-hidden {
  line-height: 0;
}

.customInput-container .customInput-renderInputAndText input,
.customInput-container .customInput-renderInputAndText-hidden input {
  border: none;
}

.customInput-container .customInput-renderInputAndText input:focus,
.customInput-container .customInput-renderInputAndText-hidden input:focus {
  outline: none;
}

/* Select and Text specific styles */
.customInput-container .customInput-renderSelectAndText select,
.customInput-container .customInput-renderSelectAndText-hidden select {
  border: none;
}

.customInput-container .customInput-renderSelectAndText select:focus,
.customInput-container .customInput-renderSelectAndText-hidden select:focus {
  outline: none;
}
