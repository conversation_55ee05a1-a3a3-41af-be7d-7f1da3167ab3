@import '../../../variable.less';

.commonFlex() {
  font-size: 12px;
  width: 100%;
  min-height: 2rem;
  //height: 2rem;
  border-bottom: 1px solid @formBorderColor;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  box-sizing: border-box;
  position: relative;
  line-height: 1.4375em;
  .commonUnderLine();


  > span:nth-of-type(1) {
    width: 4.25rem;
    flex-shrink: 0;
    color: @formFontColor;
  }
}

.customInput-container {
  width: 100%;
  box-sizing: border-box;

  input::-webkit-input-placeholder {
    font-size: 12px;
  }

  input {
    font-size: 12px;
    padding: 0;
  }

  select {
    font-size: 12px;
  }

  .customInput-renderInput {
    .commonFlex();

    input {
      width: 100%;
      border: none;
    }

    input:focus {
      outline: none;
    }
  }

  .customInput-renderSelect {
    .commonFlex();

    select {
      width: 80%;
      border: none;
    }

    select:focus {
      outline: none;
    }
  }

  .customInput-renderRadio {
    .commonUnderLine();
    font-size: 12px;
    width: 100%;
    min-height: 2rem;
    border-bottom: 1px solid @formBorderColor;
    display: flex;
    align-items: center;
    flex-shrink: 0;

    > span:first-of-type {
      width: 4.25rem;
      flex-shrink: 0;
      color: @formFontColor;
    }

    span {
      padding: 0;
    }
  }

  .customInput-renderInputAndText {
    .commonFlex();

    input {
      border: none;
    }

    input:focus {
      outline: none;
    }

    line-height: 0;
  }

  .customInput-renderSelectAndText {
    .commonFlex();

    select {
      //width: 6rem;
      border: none;
    }

    select:focus {
      outline: none;
    }

    //> div:last-of-type {
    //  flex-grow: 1;
    //  text-align: right;
    //}
  }
}
