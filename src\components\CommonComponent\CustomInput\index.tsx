/**
 * @description 表单中通用的渲染input、select、radio等输入组件
 * <AUTHOR>
 * @date 2022/04/07
 * @record 1
 */
import React, { ReactNode } from 'react';
import {
  Autocomplete,
  FormControl,
  FormControlLabel,
  FormHelperText,
  InputBase,
  Radio,
  RadioGroup,
  Stack,
  Tooltip,
  SxProps, Typography, Box
} from '@mui/material';
import { Clear, ExpandMore, Add, InfoOutlined } from '@mui/icons-material';
import './index.css';
// import { AutocompleteRenderOptionState } from "@mui/material/Autocomplete/Autocomplete";
import { ThemeColor } from '@/constants/common';

/**
 * 自定义输入组件的属性接口
 * 继承了SimpleFieldProps和SimpleProps的部分属性，并添加了额外的自定义属性
 */
export interface CustomInputProps extends Record<string, any> {
  /** 是否支持多选（用于select类型） */
  multiple?: boolean;
  /** 输入框的值 */
  value?: string | string[] | null | number;
  /** 输入框类型 */
  type?: 'input' | 'select' | 'radio' | 'selectAndText' | 'inputAndText' | 'readonlyItem';
  /** 选项列表（用于select和radio类型） */
  options?: any[];
  /** 输入类型 */
  inputType?: 'number' | 'text'
  /** 标签文本 */
  label?: string | ReactNode;
  /** 占位文本 */
  placeholder?: string;
  /** 单位文本（用于inputAndText类型） */
  unit?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 选项值的字段名 */
  valueFieldName?: string;
  /** 值变化时的回调函数 */
  onChange?: (val: any, selected?: any) => void;
  /** 自定义样式 */
  sx?: SxProps;
  /** 是否只读 */
  readOnly?: boolean
  /** 选择框宽度 */
  selectWidth?: string
  /** 添加按钮配置 */
  showButton?: {
    /** 是否显示按钮 */
    state?: boolean
    /** 按钮颜色 */
    color?: string
    /** 按钮点击事件 */
    effect?: () => void
    /** 按钮图标 */
    icon?: ReactNode
  }
  /** select框最前面的文字/图标 */
  startText?: ReactNode
  /** 失去焦点事件 */
  onBlur?: any
  /** 标签方向 */
  labelDirection?: 'row' | 'column'
  /** 是否隐藏下划线 */
  hiddenUnderline?: boolean
  /** 标签颜色 */
  labelColor?: string
  /** 自定义选项渲染函数 */
  renderOption?: (
    props: React.HTMLAttributes<HTMLLIElement>,
    option: any,
    state: any,
  ) => React.ReactNode;
  /** 单位颜色 */
  unitColor?: string
}

/**
 * 自定义输入组件
 * 根据type属性渲染不同类型的输入控件
 * @param props 组件属性
 * @returns 自定义输入组件
 */
const CustomInput = (props: CustomInputProps) => {
  const {
    value = '',
    options = [],
    label = '',
    placeholder = '',
    unit = '',
    type = 'input',
    inputType = 'text',
    onChange,
    disabled = false,
    multiple = false,
    sx,
    helperText,
    error,
    tips,
    required = false,
    valueFieldName = 'value',
    description,
    component,
    children,
    selectWidth,
    showButton,
    startText = null,
    onBlur,
    labelDirection = 'row',
    hiddenUnderline = false,
    labelColor,
    unitColor,
    ...other
  } = props;
  const { readonly, readOnly } = props
  const { inputProps, ...selectOther } = other;
  const { state, color, effect, icon } = showButton || {};

  const tempSx =
    labelDirection === 'row'
      ? { flexGrow: 1 }
      : { width: '100%', height: '2rem' };

  /**
   * 渲染普通输入框
   * 包含标签和输入控件
   * @returns 输入框JSX
   */
  const renderInput = () => {
    return (
      <Stack
        direction={labelDirection === 'row' ? 'row' : 'column'}
        alignItems={labelDirection === 'row' ? 'center' : 'flex-start'}
        className={
          hiddenUnderline
            ? 'customInput-renderInput-hidden'
            : 'customInput-renderInput'
        }
      >
        {label && (
          <Stack
            direction={'row'}
            sx={{
              fontSize: '12px',
              alignItems: 'center',
              minWidth: '4.25rem',
              color: labelColor ? labelColor : '#666666',
            }}
          >
            {label}
            {required && (
              <Typography sx={{ ml: 0.5, color: ThemeColor.main, lineHeight: '1' }}>
                *
              </Typography>
            )}
          </Stack>
        )}
        <InputBase
          disabled={disabled}
          onChange={handleChange}
          onBlur={onBlur}
          placeholder={placeholder}
          value={value ?? ''}
          type={inputType}
          sx={{ lineHeight: 0, ...tempSx }}
          {...other}
        />
      </Stack>
    )
  }

  /**
   * 渲染下拉选择框
   * 基于Material UI的Autocomplete组件
   * 支持单选和多选模式
   * @returns 选择框JSX
   */
  const renderSelect = () => {
    return (
      <Stack
        className={
          hiddenUnderline
            ? 'customInput-renderSelect-hidden'
            : 'customInput-renderSelect'
        }
        direction={labelDirection === 'row' ? 'row' : 'column'}
        alignItems={labelDirection === 'row' ? 'center' : 'flex-start'}
      >
        {label && (
          <Stack
            direction={'row'}
            sx={{
              fontSize: '12px',
              alignItems: 'center',
              minWidth: '4.25rem',
              color: labelColor ? labelColor : '#666666',
            }}
          >
            {label}
            {required && (
              <Typography sx={{ ml: 0.5, color: ThemeColor.main, lineHeight: '1' }}>
                *
              </Typography>
            )}
          </Stack>
        )}
        <Autocomplete
          size={'small'}
          limitTags={3}
          onBlur={onBlur}
          disabled={disabled}
          multiple={multiple}
          value={computeValue()}
          popupIcon={readOnly || showButton ? null : <ExpandMore />}
          clearIcon={<Clear sx={{ fontSize: '12px' }} />}
          sx={{
            flexGrow: 1,
            '.MuiChip-sizeSmall': { padding: 0 },
            // '.MuiButtonBase-root': { height: 24 },
            '.MuiChip-label': { fontSize: 12 },
            // '.MuiInputBase-root':{lineHeight:0,}
            '.MuiChip-deleteIcon': {
              color: 'rgba(0,0,0,0.15)',
            },
            '.MuiChip-root': { backgroundColor: 'rgba(0,0,0,0.05)' },
            display: 'flex',
            alignItems: 'center',
            ...tempSx,
          }}
          renderInput={(param) => {
            if (multiple) {
              return (
                <InputBase
                  inputProps={{ ...param.inputProps, ...other.inputProps }}
                  placeholder={computeValue()?.length > 0 ? '' : placeholder}
                  {...param.InputProps}
                  fullWidth
                />
              );
            } else {
              return (
                <InputBase
                  inputProps={{ ...param.inputProps, ...other.inputProps }}
                  placeholder={placeholder}
                  {...param.InputProps}
                  fullWidth
                  startAdornment={startText}
                />
              );
            }
          }}
          options={options ?? []}
          onChange={handleChange}
          isOptionEqualToValue={(o: any, v: any) =>
            o[valueFieldName] === v[valueFieldName]
          }
          {...selectOther}
        />
        {state && (
          <Stack
            sx={{ alignItems: 'flex-end', cursor: 'pointer' }}
            onClick={effect}
          >
            {icon}
          </Stack>
        )}
      </Stack>
    )
  }

  /**
   * 渲染单选框组
   * 基于Material UI的RadioGroup组件
   * @returns 单选框组JSX
   */
  const renderRadio = () => {
    return (
      <Stack
        className={
          hiddenUnderline
            ? 'customInput-renderRadio-hidden'
            : 'customInput-customInput-renderRadio'
        }
        direction={labelDirection === 'row' ? 'row' : 'column'}
        alignItems={labelDirection === 'row' ? 'center' : 'flex-start'}
      >
        {label && (
          <Stack
            direction={'row'}
            sx={{
              fontSize: 12,
              alignItems: 'center',
              minWidth: '4.25rem',
              color: labelColor ? labelColor : '#666666',
            }}
          >
            {label}
            {required && (
              <Typography sx={{ ml: 0.5, color: ThemeColor.main, lineHeight: '1' }}>
                *
              </Typography>
            )}
            {tips && (
              <Tooltip title={tips}  >
                <InfoOutlined sx={{ fontSize: 14, color: '#666', ml: 1, cursor: "pointer", }} />
              </Tooltip>
            )}
          </Stack>
        )}
        <FormControl disabled={disabled} sx={{ fontSize: 12 }}>
          <RadioGroup value={value ?? ''} onChange={handleChange} row>
            {options.map((i, index) => (
              <FormControlLabel
                key={index}
                value={i[valueFieldName]}
                control={
                  <Radio
                    size={'small'}
                    sx={{
                      '& .MuiSvgIcon-root': { fontSize: 16 },
                    }}
                  />
                }
                label={i.label}
                sx={{
                  '.MuiFormControlLabel-label': { fontSize: 12, marginLeft: 0 },
                  '&.MuiFormControlLabel-root': { marginLeft: 0 },
                }}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Stack>
    );
  };

  /**
   * 渲染带单位的输入框
   * 在输入框末尾添加单位文本
   * @returns 带单位的输入框JSX
   */
  const renderInputAndText = () => {
    return (
      <Stack
        className={
          hiddenUnderline
            ? 'customInput-renderSelectAndText-hidden'
            : 'customInput-renderSelectAndText'
        }
        direction={labelDirection === 'row' ? 'row' : 'column'}
        alignItems={labelDirection === 'row' ? 'center' : 'flex-start'}
      >
        {label && (
          <Stack
            direction={'row'}
            sx={{
              fontSize: '12px',
              alignItems: 'center',
              minWidth: '4.25rem',
              color: labelColor ? labelColor : '#666666',
            }}
          >
            {label}
            {required && (
              <Typography sx={{ ml: 0.5, color: ThemeColor.main, lineHeight: '1' }}>
                *
              </Typography>
            )}
          </Stack>
        )}
        <Autocomplete
          disabled={disabled}
          multiple={multiple}
          onBlur={onBlur}
          size={'small'}
          value={computeValue()}
          popupIcon={<ExpandMore />}
          clearIcon={<Clear sx={{ fontSize: '12px' }} />}
          sx={{
            width: '60%',
            '.MuiAutocomplete-option': { fontSize: '34px' },
            '.MuiInputBase-root': { lineHeight: 0 },
            ...tempSx,
          }}
          renderInput={(param) => (
            <InputBase
              sx={{ width: '100%' }}
              inputProps={param.inputProps}
              placeholder={placeholder}
              {...param.InputProps}
              fullWidth
              startAdornment={startText}
              endAdornment={
                <Typography sx={{ color: unitColor ? unitColor : 'inherit' }}>
                  {unit}
                </Typography>
              }
            />
          )}
          options={options}
          noOptionsText={'无选项'}
          onChange={handleChange}
          isOptionEqualToValue={(o, v) =>
            o[valueFieldName] === v[valueFieldName]
          }
          {...selectOther}
        />
      </Stack>
    );
  };

  /**
   * 渲染带单位的选择框
   * 在选择框右侧添加单位文本
   * @returns 带单位的选择框JSX
   */
  const renderSelectAndText = () => {
    return (
      <Stack
        className={
          hiddenUnderline
            ? 'customInput-renderSelect-hidden'
            : 'customInput-renderSelect'
        }
        direction={labelDirection === 'row' ? 'row' : 'column'}
        alignItems={labelDirection === 'row' ? 'center' : 'flex-start'}
      >
        {
          label && <Stack direction={'row'} sx={{ alignItems: 'center', width: '4.25rem', color: '#666666', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
            <Typography noWrap sx={{ whiteSpace: 'nowrap' }}>{label}</Typography>
            {required && <Typography sx={{ ml: 0.5, color: ThemeColor.main }}>*</Typography>}
          </Stack>
        }
        <Autocomplete
          disabled={disabled}
          multiple={multiple}
          onBlur={onBlur}
          size={'small'}
          value={computeValue()}
          popupIcon={<ExpandMore />}
          clearIcon={<Clear sx={{ fontSize: '12px' }} />}
          sx={{
            width: '60%',
            '.MuiAutocomplete-option': { fontSize: '34px' },
            '.MuiInputBase-root': { lineHeight: 0 },
          }}
          renderInput={(param) => (

            <InputBase
              sx={{ width: '100%' }}
              inputProps={param.inputProps}
              placeholder={placeholder}
              {...param.InputProps}
              fullWidth
              startAdornment={startText}
              endAdornment={<Typography>{unit}</Typography>}
            />
          )}
          options={options}
          noOptionsText={'无选项'}
          onChange={handleChange}
          isOptionEqualToValue={(o, v) => o[valueFieldName] === v[valueFieldName]}
          {...other}
        />
      </Stack>
    );
  };

  const readonlyItem = () => {
    return (
      <Stack
        direction={labelDirection === 'row' ? 'row' : 'column'}
        alignItems={labelDirection === 'row' ? 'center' : 'flex-start'}
        sx={{ minHeight: '1.75rem' }}
      >
        {label && (
          <Typography
            p={1}
            sx={{
              color: labelColor ? labelColor : '#666666',
              p: 0,
              minWidth: '4.25rem',
            }}
          >
            {label}
          </Typography>
        )}
        <Box sx={{ color: '#171717', fontSize: 12, flex: 1 }}>{value}</Box>
      </Stack>
    );
  };

  /**
   * 处理输入值变化的事件
   * 根据输入类型处理值并调用onChange回调
   * @param e 事件对象
   * @param val 新值
   */
  const handleChange = (e: any, val?: any) => {
    let data = e.target.value;
    switch (type) {
      case 'input':
      case 'radio':
      case 'inputAndText':
        onChange?.(data.trim());
        break;
      case 'select':
      case 'selectAndText':
        onChange?.(!multiple ? val?.[valueFieldName] : val?.map((i: any) => i[valueFieldName]), val);
        break;
      default:
        break;
    }
  };

  /**
   * 计算选择框的当前值
   * 处理单选和多选两种情况
   * @returns 格式化后的当前值
   */
  const computeValue = () => {
    if (multiple) {
      // 确保 value 是数组类型才调用 includes
      return Array.isArray(value) && value.length > 0
        ? options?.filter((i) => value.includes(i[valueFieldName])) || []
        : [];
    } else {
      return (value && options?.find((i) => value === i[valueFieldName])) || null;
    }
  };

  return (
    <Stack className='customInput-container' sx={{ ...sx, width: selectWidth ? selectWidth : '100%' }}>
      {type === 'input' && renderInput()}
      {type === 'select' && renderSelect()}
      {type === 'radio' && renderRadio()}
      {type === 'inputAndText' && renderInputAndText()}
      {type === 'selectAndText' && renderSelectAndText()}
      {type === 'readonlyItem' && readonlyItem()}
      {helperText && <FormHelperText error={error} sx={{ color: '#d32f2f', whiteSpace: 'nowrap' }}>{helperText}</FormHelperText>}
    </Stack>
  )
}

export default CustomInput
