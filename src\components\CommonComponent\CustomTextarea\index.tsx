import React from 'react';
import { FormH<PERSON>perText, Stack, SxProps, Typography } from '@mui/material';
import './index.css'

interface CustomTextareaProps {
  onChange?: (text: string ) => void;
  hideTool?: boolean;
  value?: string | null;
  minHeight?: number;
  maxHeight?: number;
  height?: number;
  enterKeypress?: any;
  label?: any;
  placeholder?: string;
  error?: any;
  sx?: SxProps;
  required?: boolean;
  helperText?: any;

}
/**
 * @description 自定义textarea
 * <AUTHOR>
 * @date 2024-08-30
 */
function CustomTextarea(props: CustomTextareaProps) {
  const {
    value,
    label,
    sx,
    required = false,
    placeholder,
    onChange,
    error,
    helperText,
    minHeight = 15,
    maxHeight = 1000,
    height = 250,
    enterKeypress,
  } = props;
  return (
    <Stack
      className={'customTextarea'}
      sx={{
      'textarea': {
        height: height + 'px',
        maxHeight: maxHeight + 'px',
        minHeight: minHeight + 'px',
      },
      ...sx
    }}>
      {
        label && <Stack direction={'row'} sx={{ alignItems: 'center', color:error ? '#d32f2f' : '#666666', fontSize: 12, height: '2rem' ,minHeight:'2rem'}}>
          {label}
          {required && <Typography sx={{ ml: 0.3, color: 'primary.main' }}>*</Typography>}
        </Stack>
      }
      <textarea value={value ?? ''} onChange={(e) => onChange?.(e.target.value)} placeholder={placeholder} />
      {helperText && error && <FormHelperText error={error} sx={{ color: '#d32f2f', whiteSpace: 'nowrap' }}>{helperText}</FormHelperText>}
    </Stack>
  );
}

export default CustomTextarea;
