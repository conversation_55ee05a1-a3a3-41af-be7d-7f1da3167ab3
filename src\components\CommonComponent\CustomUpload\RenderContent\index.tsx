import React, { useRef } from 'react';
import {
  allFileType,
  attachmentType, hiddenReviewBtn,
  interceptFileType,
} from '@/components/CommonComponent/UplaodConmmon/CommonMethod';
import { DeleteOutline, Download, Visibility } from '@mui/icons-material';
import { Box, Stack, Typography, SxProps } from '@mui/material';
import { fileDownload } from '@/util/tool';
import { Attachment } from '@/typings';
import CustomFancyBox, { CustomFancyBoxOptions } from '@/components/CommonComponent/UplaodConmmon/CustomFancyBox';
import Uncheck from '@/assets/img/unchecked.png';
import NO_PIC from '@/assets/img/UploadImg/NO_PIC.jpg';
import ImgComponent from '@/components/CommonComponent/UplaodConmmon/ImgComponent';


export interface ImageUploadProps {
  PdfViewReadonly?: boolean;
  contentPosition?: 'flex-start' | 'center';
  fileType?: 'image' | 'pdf' | 'zip' | 'office' | 'video' | 'audio' | 'all';
  specificFileType?: string;
  deletePermission?: boolean;
  downloadPermission?: boolean;
  uploadPermission?: boolean;
  downloadAllPermission?: boolean
  idArray?: string[];
  tableWidth?: number;
  readOnly?: boolean;
  quantityLimit?: number
  listMaxHeight?: string
  sx?: any
  deleteDataBase?: boolean
  uploadUrl?: string
  getUrl?: string
  deleteUrl?: string
  typeNotAllowed?: string[];
  typeAllowed?: string[];
  fileSizeLimit?: number;
  multiple?: boolean;
  label?: string | React.ReactNode
  tableView?: boolean
  labelSx?: SxProps
  showDate?: boolean
  hiddenPrompt?: boolean
  columns?: any[]
  uploadText?: string
}

interface RenderContentProps extends ImageUploadProps {
  value?: any
  readonly?: boolean
  cardWidth?: number
  cardScale?: number
  onChange?: (val?: any, method?: string) => void
  downloadPermission?: boolean
  deletePermission?: boolean
  component?: any
  hiddenPrompt?: boolean
}

function RenderContent(props: RenderContentProps) {

  const anchorRef = useRef<Map<any, HTMLAnchorElement | null> | null>(new Map())

  const {
    value,
    readonly,
    cardScale = 1,
    cardWidth = 120,
    onChange,
    downloadPermission,
    deletePermission,
    component,
    hiddenPrompt,
  } = props;

  /**
   * @description 附件下载
   * @param e
   * @param item 点击的那项数据
   */
  const handleDownload = (e: any, item: Attachment) => {
    e.stopPropagation();
    fileDownload(item.url, item.fileName);
  };

  /**
   * @description 传id到状态里面去
   * @param e
   * @param item 点击的那项数据
   */
  const handleDelete = (e: any, item: Attachment) => {
    e.stopPropagation();
    onChange?.(item, 'delete');
  };

  // 点击图片可以关闭此图片
  const clickPicture = (item: Attachment, index?: number) => {
    // @ts-ignore
    // document.querySelectorAll(`#a${item.id}`)[0]?.click();
    console.log(anchorRef.current)
    const map = anchorRef.current
    const anchor = map?.get(index)
    if (anchor) {
      anchor.click()
    }
  };



  return (
    <React.Fragment>
      <CustomFancyBox options={CustomFancyBoxOptions}>
        {component}
        {
          value?.length > 0 ? value?.map((item: any, index: number) => {
            return item && <Box
              className={'customUpload-filePreview'}
              key={item.id ?? index}
              sx={{ width: cardWidth, height: cardWidth / cardScale }}
            >
              {hiddenReviewBtn(item) && <a
                key={item.url}
                data-fancybox='gallery'
                data-download-src={item.url}
                data-caption={item.fileName}
                href={item.url}
                id={`a${item.id}`}
                ref={(node) => {
                  const map = anchorRef.current
                  if (node) {
                    map?.set(index, node)
                  } else {
                    map?.delete(index)
                  }
                }}
              />}

              <Box title={item.fileName} className={'customUpload-imgContainer'}
                onClick={() => clickPicture(item, index)}>
                {
                  allFileType.Video.includes(interceptFileType(item)) ?
                    <video onClick={() => clickPicture(item, index)} src={item?.url} /> : <ImgComponent
                      src={allFileType.Pic.includes(interceptFileType(item)) ? item.url : attachmentType(item)}
                      defaultImg={NO_PIC}
                    />
                }

                {
                  !readonly && <Box className={'customUpload-pictureMask'}>
                    {
                      hiddenReviewBtn(item) && <span title='预览' onClick={() => clickPicture(item, index)}>
                        <Visibility sx={{ fontSize: 18 }} />
                      </span>
                    }
                    {
                      deletePermission && <span title='删除' onClick={(e) => handleDelete(e, item)}>
                        <DeleteOutline sx={{ fontSize: 18 }} />
                      </span>
                    }
                    {
                      downloadPermission && <span title='下载' onClick={(e) => handleDownload(e, item)}>
                        <Download sx={{ fontSize: 18 }} />
                      </span>
                    }
                  </Box>
                }
              </Box>
            </Box>;
          }) : (!hiddenPrompt && <Stack sx={{ width: '5rem', height: '5rem', marginTop: '1rem' }}>
            <img src={Uncheck} alt={'图片加载失败'} />
            <Typography color={'#666666'} fontSize={12}>暂无已上传文件</Typography>
          </Stack>)
        }
      </CustomFancyBox>
    </React.Fragment>
  );
}

export default RenderContent;
