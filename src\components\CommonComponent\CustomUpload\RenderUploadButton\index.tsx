import React from 'react';
import { PhotoCamera,CameraAltOutlined } from '@mui/icons-material';
import { Box } from '@mui/material';
interface RenderUploadButtonProps {
  cardWidth?: number
  cardScale?: number
  onChange?: (val?: any) => void
  fileType?: any
  uploadPermission?: boolean
  specificFileType?: string;
}

function RenderUploadButton(props: RenderUploadButtonProps) {
  const { cardScale = 1, cardWidth = 120, onChange, fileType = 'all', uploadPermission, specificFileType } = props;
  /**
   * @description 判断传入文件类型
   */
  const fileTypeGroup: any = {
    all: '',
    image: 'image/*',
    pdf: 'application/pdf',
    video: 'video/*',
    audio: 'audio/*',
    office: 'application/msword,application/vnd.ms-excel,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.presentationml.presentation',
    zip: 'application/zip',
    dwg: 'image/vnd.dwg',
  };

  /**
   * @description 判断传入的是数组还是字符串
   */
  const handleFileType = () => {
    if (typeof fileType !== 'object') {
      return fileTypeGroup[fileType];
    } else {
      return fileType?.map((item: any) => fileTypeGroup[item]) + '';
    }
  };
  return (
    <React.Fragment>
      {uploadPermission && (
        <Box
          className={'customUpload-uploadButtons'}
          sx={{ width: cardWidth, height: cardWidth / cardScale, }}
        >
          {/* <input type="file" /> */}
          <Box >
            <input
              type='file'
              id={'customUpload-uploadInput'}
              title='点击上传'
              accept={specificFileType ?? handleFileType()}
              name='upload'
              onChange={(e) => onChange?.(e)}
              placeholder=''
              multiple
            />
            <Box title='点击上传' sx={{ marginTop: '5px' }}>
              <CameraAltOutlined sx={{ fontSize: 20 * cardScale }} />
            </Box>
          </Box>
          <div>上传</div>
        </Box>
      )}
    </React.Fragment>
  );
}

export default RenderUploadButton;
