.customUpload-previewContainer {
  min-height: 20vh;
  max-height: 30vh;
  display: flex;
  flex-flow: row wrap;
  overflow-y: auto!important;
  align-content: flex-start;
  position: relative;
  flex-wrap: wrap;
}

/* 图片显示区域滚动条的设置 */
.customUpload-previewContainer::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

.customUpload-previewContainer::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: #8a8a8aa8;
}

.customUpload-title {
  font-size: 12px;
  height: 2rem;
  line-height: 2rem;
  color: #333333;
  display: flex;
  align-items: center;
}

.customUpload-uploadButtons {
  box-sizing: border-box;
  position: relative;
  width: var(--card-width, 6rem);
  height: var(--card-width, 6rem);
  border: 2px dashed #eeeeee;
  border-radius: 4px;
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0.2rem;
  //background-color: #fbfbfb;
  //&:hover{
  //  border-color: rgba(2,117,216,0.5);
  //  transition: all 0.5s;
  //}
  // div:nth-child(1) {}

  div:nth-child(2) {
    color: #8c8c8c;
    font-size: 12px;
  }
}

/* 上传的默认样式的隐藏 */
#customUpload-uploadInput {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  display: inline-block;
  font-size: 0;
  border: 0;
}

.customUpload-filePreview {
  box-sizing: border-box;
  width: var(--card-width, 6rem);
  height: var(--card-width, 6rem);
  border: 1px solid #eeeeee;
  margin: 0.2rem;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  a{
    cursor: zoom-in;
  }
  .customUpload-imgContainer {
    width: 85%;
    height: 85%;
    position: relative;
    cursor: zoom-in;
    img {
      width: 100%;
      height: 100%;
      transition: all 0.5s;
      object-fit: contain;
    }

    video {
      width: 100%;
      height: 100%;
      transition: all 0.5s;
      object-fit: contain;
    }

    .customUpload-pictureMask {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.45);
      opacity: 0;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      justify-content: space-around;
      align-items: center;

      span {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s;
      }

      span:hover {
        color: white;
      }
    }
  }

  .customUpload-imgContainer:hover .customUpload-pictureMask {
    opacity: 1;
  }
}

.customUpload-hoverImgContainer {
  width: 85%;
  height: 85%;
  position: relative;
  transition: all 0.5s;
  overflow: hidden;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
    transition: all 0.5s;
    object-fit: contain;
  }
}

.customUpload-hoverImgContainer:hover {
  transform: scale(2);
  z-index: 12;
  transition: all 0.5s;
}

/* 图片字体过长的隐藏设置 */
.customUpload-cardContent {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


