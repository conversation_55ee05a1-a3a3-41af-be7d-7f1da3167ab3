/**
 * @description 上传图片
 * <AUTHOR>
 */
import React, { ReactNode, useEffect, useState } from 'react';
import './index.less';
import RenderPreview from '@/components/CommonComponent/UplaodConmmon/RenderPreview';
import RenderDeleteDialog from '@/components/CommonComponent/UplaodConmmon/RenderDeleteDialog';
import RenderContent from '@/components/CommonComponent/CustomUpload/RenderContent';
import RenderUploadButton from '@/components/CommonComponent/CustomUpload/RenderUploadButton';
import { Box } from '@mui/material';
import { onBeforeUpload } from "@/components/CommonComponent/UplaodConmmon/CommonMethod";
import LoadingUtil from '@/util/LoadingUtil';
import Message from '@/util/Message';
import { Attachment } from '@/typings';
import { useCloudbase } from '@/components/Context/CloudBaseContext/CloudbaseContext';
import { allFileType } from '@/components/CommonComponent/UplaodConmmon/CommonMethod';

// 参数类型限制
export interface CustomUploadProps {
  value?: any[];
  titleName?: string | ReactNode;
  fileType?: 'image' | 'pdf' | 'zip' | 'office' | 'video' | 'audio' | 'all' | ('image' | 'pdf' | 'zip' | 'office' | 'video' | 'audio' | 'all')[];
  specificFileType?: string;
  PdfViewReadonly?: boolean;
  onChange?: (val: any) => void;
  idArray?: string[];
  cardWidth?: number;
  cardScale?: number;
  minHeight?: string;
  maxHeight?: string;
  readOnly?: boolean;
  //图片数量限制
  quantityLimit?: number;
  uploadUrl?: string
  getUrl?: string
  deleteUrl?: string
  //是否删除数据库的数据
  deleteDataBase?: boolean
  typeNotAllowed?: string[];
  typeAllowed?: string[];
  fileSizeLimit?: number;
  deletePermission?: boolean;
  downloadPermission?: boolean;
  uploadPermission?: boolean;
  downloadAllPermission?: boolean
  hiddenPrompt?: boolean
}

const CustomUpload = (props: CustomUploadProps) => {

  // 获取云开发实例
  const { cloudbaseApp } = useCloudbase();

  const {
    quantityLimit = 1000,
    value = [],
    titleName,
    fileType = 'image',
    PdfViewReadonly = false,
    onChange,
    idArray = [],
    cardWidth = 120,
    cardScale = 1,
    minHeight = '10vh',
    maxHeight = '30vh',
    readOnly = true,
    deleteDataBase = true,
    typeNotAllowed = [],
    typeAllowed = [],
    fileSizeLimit,
    deletePermission = true,
    downloadPermission = true,
    uploadPermission = true,
    downloadAllPermission = false,
    //查看指定類型 specificFileType = '.dwg,.png'
    specificFileType,
    hiddenPrompt = true
  } = props;

  // 控制弹框的开关
  const [open, setOpen] = useState<boolean>(false);
  // 储存要删除的一项
  const [saveItem, setSaveItem] = useState<any>('');

  const [permission, setPermission] = useState({
    upload: uploadPermission,
    delete: deletePermission,
    download: downloadPermission,
    downloadAll: downloadAllPermission,
  });

  useEffect(() => {
    //如果是只讀，所有操作全部禁用
    if (readOnly) {
      setPermission({
        upload: false,
        delete: false,
        download: false,
        downloadAll: false,
      });
    }
  }, []);


  useEffect(() => {
    //为了不再卸载组件时还在渲染
    let isUnmounted = false;
    if (idArray !== null && idArray.length === 0) {
      value ? onChange?.([...value]) : onChange?.([]);
    } 
    return () => {
      isUnmounted = true;
    };
  }, [JSON.stringify(idArray)]);


  /**
   * @description 判断禁止上传的类型和当前传入的文件类型是否相同
   * @param arr 当前传的数组
   */
  const haveSame = (arr: string[]) => {
    switch (true) {
      case typeNotAllowed.length > 0:
        return arr
          .map((item) => typeNotAllowed.includes(item))
          .every((i) => !i);
      case typeAllowed.length > 0:
        return arr
          .map((item) => typeAllowed.includes(item))
          .every((i) => i);
      default:
        return true;
    }
  };
  /**
   * @description 判断图片的大小是否符合条件
   * @param arr 当前传的数组
   */
  const handleFileSize = (arr: number[]) => {
    if (fileSizeLimit) {
      const size = fileSizeLimit * 1024 * 1024;
      return arr.every((item) => item < size);
    }
    return true;
  };

  /**
   * @description 根据文件类型获取存储目录
   * @param file 文件对象
   * @returns 存储目录名
   */
  const getStorageDir = (file: File): string => {
    const fileExt = file.name.split('.').pop()?.toLowerCase() || '';

    if (allFileType.Pic.includes(fileExt)) {
      return 'images';
    } else if (allFileType.Doc.includes(fileExt)) {
      return 'documents';
    } else if (allFileType.Ppt.includes(fileExt)) {
      return 'presentations';
    } else if (allFileType.Excel.includes(fileExt)) {
      return 'spreadsheets';
    } else if (allFileType.Video.includes(fileExt)) {
      return 'videos';
    } else if (allFileType.Rar.includes(fileExt)) {
      return 'archives';
    } else {
      return 'others';
    }
  };

  /**
   * @description 生成安全的文件名
   * @param fileName 原始文件名
   * @returns 安全的文件名
   */
  const generateSafeFileName = (fileName: string): string => {
    // 保留中文、字母、数字、下划线和点，其他特殊字符替换为下划线
    const safeName = fileName.replace(/[^\u4e00-\u9fa5a-zA-Z0-9._-]/g, '_');
    const timestamp = Date.now();
    return `${timestamp}-${safeName}`;
  };

  /**
   * @description 图片的路径上传
   * @param e event事件
   */
  const handleOnChange = async (e: any) => {
    e.preventDefault();
    let file = e.target.files;
    let fileList: any[] = Array.from(file);

    //提取文件后缀
    const uploadType = fileList.map((item: any) => {
      let itemIndex = item?.name?.lastIndexOf('.') + 1;
      return itemIndex === 0 ? '' : item?.name?.slice(itemIndex).toLowerCase();
    });

    if (!haveSame(uploadType)) {
      Message.warn('上传失败,包含禁止上传类型文件');
      return;
    }

    if (!handleFileSize(fileList.map((i) => i?.size))) {
      Message.warn('上传失败,包含文件大小超出限制的文件');
      return;
    }

    if (fileList.length + (value?.length ?? 0) > quantityLimit) {
      Message.warn('上传失败,超出数量限制');
      return;
    }

    if (!cloudbaseApp) {
      Message.warn('上传失败,云开发实例未初始化');
      return;
    }

    LoadingUtil.load({ openInfo: true, messages: '上传中' });

    try {
      const uploadPromises = fileList.map(async (file) => {
        const newImg = await onBeforeUpload(file) as File;
        const storageDir = getStorageDir(file);
        const safeFileName = generateSafeFileName(file.name);
        const cloudPath = `${storageDir}/${safeFileName}`;
        try {
          const uploadResult = await cloudbaseApp.uploadFile({
            cloudPath,
            filePath: newImg as unknown as string
          });

          if (!uploadResult.fileID) {
            throw new Error('Upload failed: No fileID returned');
          }
          return {
            fileID: uploadResult.fileID,
            id: uploadResult.fileID,
            //@ts-ignore
            url: uploadResult.download_url,
            fileName: file.name,
            fileSize: file.size,
            fileType: file.type
          };
        } catch (uploadError: any) {
          console.error('File upload error:', uploadError);
          throw new Error(`Failed to upload file ${file.name}: ${uploadError?.message || 'Unknown error'}`);
        }
      });

      const uploadResults = await Promise.all(uploadPromises);
      const uniqueImg = [...new Map([...uploadResults, ...(value || [])]?.map((item) => [item.fileID, item])).values()];

      if ([...uploadResults, ...(value || [])]?.length > uniqueImg?.length) {
        Message.success('上传成功,已过滤重复的文件');
      } else {
        Message.success('上传成功');
      }

      onChange?.(uniqueImg);
    } catch (error: any) {
      Message.warn('上传失败: ' + (error?.message || '未知错误'));
      console.error('Upload error:', error);
    } finally {
      LoadingUtil.load({ openInfo: false, messages: '上传完成' });
      e.target.value = '';
    }
  };

  /**
   * @description 点击确定按钮删除对应的一项
   */
  async function determineDelete() {
    if (!saveItem?.id) {
      Message.warn('删除失败,未找到要删除的文件');
      return;
    }

    if (!cloudbaseApp) {
      Message.warn('删除失败,云开发实例未初始化');
      return;
    }

    try {
      if (deleteDataBase) {
        const deleteResult = await cloudbaseApp.deleteFile({
          fileList: [saveItem.id]
        });
        console.log(deleteResult);
      }

      const deleteData = value.filter((item: Attachment) => {
        return item.id !== saveItem.id;
      });

      onChange?.(deleteData);
      Message.success('删除成功');
    } catch (error) {
      Message.warn('删除失败');
      console.error('Delete error:', error);
    } finally {
      setOpen(false);
    }
  }

  const contentOnchange = (val?: Attachment, method?: string) => {
    if (method === 'delete') {
      setOpen(true);
      setSaveItem(val);
    }
  };

  return (
    <React.Fragment>
      {titleName && <Box className='customUpload-title'>{titleName}</Box>}
      <Box
        className={'customUpload-previewContainer'}
        sx={{ 
          minHeight: minHeight, 
          maxHeight: maxHeight,
          '--card-width': `${cardWidth}px`  // 添加 CSS 变量
        }}
      >
        <RenderContent
          value={value}
          cardWidth={cardWidth}
          cardScale={cardScale}
          onChange={contentOnchange}
          downloadPermission={permission.download}
          deletePermission={permission.delete}
          readonly={readOnly}
          hiddenPrompt={hiddenPrompt}
          component={<RenderUploadButton
            onChange={handleOnChange}
            fileType={fileType}
            cardScale={cardScale}
            cardWidth={cardWidth}
            uploadPermission={permission.upload}
            specificFileType={specificFileType}
          />}
        />
      </Box>

      {/*渲染删除弹框*/}
      <RenderDeleteDialog
        item={saveItem}
        onChange={determineDelete}
        open={open}
        close={() => setOpen(false)}
      />
    </React.Fragment>
  );
}

export default CustomUpload;
