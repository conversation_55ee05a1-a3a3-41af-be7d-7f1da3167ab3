/**
 * @description 加载时间过长的工具类
 * <AUTHOR>
 * @date 2022/8/29
 */

import React, { useState, useEffect } from 'react';
import Backdrop from '@mui/material/Backdrop';
import CircularProgress from '@mui/material/CircularProgress';
import { Stack } from '@mui/material';
import LoadingUtil from '@/util/LoadingUtil';

function Loading() {
	const [open, setOpen] = useState(false);
	const [message, setMessage] = useState('保存中');
	const [color, setColor] = useState<any>({});
	useEffect(() => {
		LoadingUtil.load = handleToggle;
	}, []);

	/**
	 * @param param 开关loading页面参数
	 */
	const handleToggle = (param?: LoadingParam) => {
		setMessage(param?.messages ?? '保存中,请稍等');
		setOpen(param?.openInfo ?? false);
		setColor(param?.color ?? {});
	};

	return open ? (
		<div>
			<Backdrop
				sx={{
					// color: '#fff',
					color:
						color?.textColor?.length > 0
							? color?.textColor
							: '#2b6bff',
					zIndex: (theme) => theme.zIndex.drawer + 999,
					display: 'flex',
					flexDirection: 'column',
				}}
				open={open}
			>
				<Stack pb={1}>{message}</Stack>
				{/*<CircularProgress color='inherit' />*/}
				<CircularProgress
					sx={{
						color:
							color?.iconColor?.length > 0
								? color?.iconColor
								: 'inherit',
					}}
				/>
			</Backdrop>
		</div>
	) : null;
}

interface LoadingParam {
	openInfo?: boolean;
	messages?: string;
	color?: {
		textColor?: any;
		iconColor?: any;
	};
}

export default Loading;
