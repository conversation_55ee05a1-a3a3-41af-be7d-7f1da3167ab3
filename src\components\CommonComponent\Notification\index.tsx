/**
 * <AUTHOR>
 * @date 2022/12/23
 */
import React, { useEffect, useRef, useState } from 'react';
import { Alert, Snackbar } from '@mui/material';
import Message, {
	IMessageOption,
	MessageOption,
	MessagePosition,
} from '@/util/Message';
import { nanoid } from 'nanoid';

export default (props: NotificationProps) => {
	// 是否是主窗体
	const isMaster = props.isMaster ?? false;

	const [message, setMessage] = useState<MessageItem>();
	// 缓存子应用的事件
	const callbackMapping = useRef<Record<string, Function>>({});
	// 是否在 Iframe 中
	const isInIframe = window.self !== window.self.top;

	function genOption(option: MessageOption): IMessageOption {
		if (typeof option === 'string') {
			return {
				message: option,
				duration: 2000,
				position: 'top-center',
			};
		}
		return {
			message: option.message || '',
			duration: option.duration || 2000,
			position: option.position || 'top-center',
			onClose: option.onClose,
		};
	}

	// 获取最顶级的窗体
	function getTopWindow() {
		let topWindow = window.self as Window;
		while (topWindow !== topWindow.top) {
			const top = topWindow.top;
			if (!top) {
				break;
			}
			topWindow = top;
		}
		return topWindow;
	}

	function showMessage(option: MessageOption, type: MessageType) {
		const op = genOption(option);
		const newMsg = { ...op, type };
		// 在子窗体中，不直接显示提示，而是向上级窗体透传
		if (isInIframe) {
			const { position, duration, message, onClose } = newMsg;
			const transMessage: TransMessageItem = {
				type,
				position,
				duration,
				message,
			};
			if (Boolean(onClose)) {
				// 生成唯一事件key，缓存
				const key = nanoid(8);
				transMessage.key = key;
				callbackMapping.current[key] = onClose!;
			}
			getTopWindow().postMessage(
				{
					event: 'NOTIFICATION',
					data: transMessage,
				},
				'*',
			);
		} else {
			// 不在子窗体中，直接显示
			setMessage((pre) => {
				pre?.onClose?.();
				return newMsg;
			});
		}
	}

	// 主窗体通过此方法显示提示
	function showMessageFromChild(
		transMessageItem: TransMessageItem,
		source: Window,
	) {
		const { message, key, duration, position, type } = transMessageItem;
		const messageOpt: MessageItem = {
			message,
			duration,
			position,
			type,
		};
		if (Boolean(key)) {
			// 如果有关闭事件，注册关闭事件，关闭时向子窗体推送关闭回调
			messageOpt.onClose = () => {
				source.postMessage(
					{
						event: 'NOTIFICATION_CALLBACK',
						data: key,
					},
					'*',
				);
			};
		}
		setMessage((pre) => {
			pre?.onClose?.();
			return messageOpt;
		});
	}

	function setupMessage() {
		Message.info = (option) => showMessage(option, 'info');
		Message.error = (option) => showMessage(option, 'error');
		Message.warn = (option) => showMessage(option, 'warning');
		Message.success = (option) => showMessage(option, 'success');
	}

	function handleClose() {
		setMessage(undefined);
		message?.onClose?.();
	}

	function handlePosition(position: MessagePosition) {
		const [vertical, horizontal] = position.split('-');
		return {
			horizontal,
			vertical,
		} as any;
	}

	useEffect(setupMessage, []);

	useEffect(() => {
		const listener = (e: any) => {
			// 收到主窗体的关闭回调，执行刚刚缓存的事件，执行完成后移除
			const { data, event } = e.data || {};
			if (event === 'NOTIFICATION_CALLBACK' && Boolean(data)) {
				const mapping = { ...callbackMapping.current };
				mapping[data]?.();
				delete mapping[data];
				callbackMapping.current = mapping;
			}
		};
		isInIframe && window.addEventListener('message', listener);
		return () => {
			isInIframe && window.removeEventListener('message', listener);
		};
	}, []);

	useEffect(() => {
		// 主窗体收到子窗体传递的提示
		const listener = (e: any) => {
			const source = e.source;
			const { event, data } = e.data || {};
			if (event !== 'NOTIFICATION' || !data) {
				return;
			}
			showMessageFromChild(data, source);
		};
		isMaster && window.addEventListener('message', listener);
		return () => {
			isMaster && window.removeEventListener('message', listener);
		};
	});

	return (
		<>
			{Boolean(message) && (
				<Snackbar
					open
					onClose={handleClose}
					anchorOrigin={handlePosition(message!.position!)}
					autoHideDuration={message!.duration}
					disableWindowBlurListener
				>
					<Alert
						severity={message!.type}
						variant="filled"
						onClose={handleClose}
					>
						{message!.message}
					</Alert>
				</Snackbar>
			)}
		</>
	);
};

type MessageType = 'success' | 'error' | 'info' | 'warning';

interface MessageItem extends IMessageOption {
	type: MessageType;
}

interface TransMessageItem extends Omit<MessageItem, 'onClose'> {
	key?: string;
}

interface NotificationProps {
	isMaster?: boolean;
}
