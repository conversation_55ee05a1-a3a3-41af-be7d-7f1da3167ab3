interface IPdfView {
  // 文件地址
  src: string,
}

const PdfView = (props: IPdfView) => {
  const {src} = props;
  const baseUrl = window.location.origin + window.location.pathname;
  let url = encodeURIComponent(src ?? '')
  return (
    <iframe
      style={{width:"100%",height:'100%'}}
      id="pdf-view-container"
      data-source={src}
      className='pdf-view-container'
      src={`${baseUrl}/pdfjs/web/viewer.html?file=${url}`}/>
  )
}

export default PdfView;
