/**
 * @description 左侧容器列设置组件
 * <AUTHOR>
 * @date 2022/04/14
 */

import React, { useRef, useState } from 'react';
import { SimpleCustomGridEnum, businessColumnProps, GridColumnProps } from '../enum';
import { Stack, Typography, TextField, Popover } from '@mui/material'
import ViewColumnOutlinedIcon from '@mui/icons-material/ViewColumnOutlined';

interface ColumnsSetProps {
  initBusinessColumn?: businessColumnProps[],
  open_column_set_method?: string,
  onClick?: () => void,
  columnSet?: GridColumnProps[],
  onChange?: (columns: GridColumnProps[]) => void,
}

const ColumnsSet = (props: ColumnsSetProps) => {

  const { initBusinessColumn, open_column_set_method, onClick, columnSet, onChange } = props || {}


  //下拉状态开关
  const [open, setOpen] = useState<boolean>(false);

  const [anchorEl, setAnchorEl] = React.useState<HTMLInputElement | null | HTMLDivElement>(null);


  //搜索框关键字
  const [keyWord, setKeyWord] = useState<string>();

  const column_set_hide = initBusinessColumn?.filter(i => i.column_set_hide)?.map(i => i.binding)

  const ref = useRef(null)

  //改变选项的勾选
  const changeValue = (item: GridColumnProps) => {
    const newColumnSet = columnSet?.map(i => {
      if (i.binding === item.binding) {
        return { ...i, visible: !i.visible }
      }
      return i
    }) ?? []
    onChange?.(newColumnSet)
  };

  //查找包含关键字的选项
  const searchKeyWord: React.ChangeEventHandler<HTMLTextAreaElement | HTMLInputElement> = (e) => {
    setKeyWord(e.target.value);
  };

  const handleClose = () => {
    setOpen(false)
    setAnchorEl(null)
  }

  const handleClick = (e: any) => {
    if (open_column_set_method === 'Dialog') {
      onClick?.()
    } else {
      e.stopPropagation()
      setOpen(!open);
      setAnchorEl(e.target);
    }
  }

  return (
    <Stack
      ref={ref}
      sx={{
        fontSize: 12,
        position: 'relative',
        width: 60,
        alignItems: 'center'
      }}>
      <Stack
        direction={'row'}
        alignItems={'center'}
        onClick={handleClick}
        sx={{
          ":hover": {
            backgroundColor: '#f2f2f2',
            cursor: 'pointer'
          },
          '>svg': {
            fontSize: '1.5rem',
            width: '1rem',
            height: '1rem',
            mr: 0.2,
          },
        }}
      >
        <ViewColumnOutlinedIcon sx={{ fontSize: '1.2rem', mr: 0.5 }} />
        <Typography component={'span'} sx={{ lineHeight: 1.75 }}>{SimpleCustomGridEnum.COLUMN_SET}</Typography>
      </Stack>
      {
        <Popover
          open={open}
          anchorEl={anchorEl}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left'
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left'
          }}
          onClose={() => { handleClose() }}
        >
          <Stack sx={{
            px: 1.5,
            pt: 1,
            minWidth: 150,
            height: 300,
            bgcolor: 'whitesmoke'
          }}>
            <TextField variant="outlined" autoComplete='off' sx={{
              '& legend': {
                m: '0!important', width: '0!important',
              },
            }} value={keyWord} placeholder='可以输入关键词过滤列' onChange={searchKeyWord} />
            <Stack
              className='overflow-stack'
              sx={{
                mt: 1,
                overflow: 'auto'
              }}>
              {
                columnSet?.map((item, index) => {                  
                  if (column_set_hide?.includes(item.binding ?? '')) {
                    return null
                  }
                  return <Stack key={index}
                    direction={'row'}
                    m={'0 5px 5px'}
                    display={item.header?.includes(keyWord ?? '') ? 'flex' : 'none'}
                    alignItems={'center'}>
                    <input type="checkbox"
                      onChange={changeValue.bind(null, item)}
                      checked={item.visible} />
                    <Stack style={{ marginLeft: 4, fontSize: 12 }}  >{item.header}</Stack>
                  </Stack>
                })
              }
            </Stack>
          </Stack>
        </Popover>
      }
    </Stack>
  );
};

export default ColumnsSet;
