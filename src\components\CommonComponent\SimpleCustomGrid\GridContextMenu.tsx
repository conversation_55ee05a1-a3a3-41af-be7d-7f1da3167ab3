import { Menu } from '@grapecity/wijmo.input';
import {
  GroupRow,
  AllowSorting,
} from '@grapecity/wijmo.grid';
import {
  DataType,
  SortDescription,
  CollectionViewGroup,
  PropertyGroupDescription,
} from '@grapecity/wijmo';

/**
 *  表格右键菜单栏
 *  整合 8 类常用便捷操作菜单选项
 */
export class FlexGridContextMenu {
  constructor(grid: any) {
    let host = grid.hostElement,
      menu = this.buildMenu(grid);
    host.addEventListener(
      'contextmenu',
      (e: any) => {
        // 选中的单元格或列 被点击
        let sel = grid.selection,
          ht = grid.hitTest(e),
          row = ht.getRow();
        switch (ht.panel) {
          case grid.cells:
            let colIndex = ht.col;
            // 如果这是一个分组表头，则选中分组列
            if (
              row instanceof GroupRow &&
              row.dataItem instanceof CollectionViewGroup
            ) {
              let gd = row.dataItem.groupDescription;
              if (gd instanceof PropertyGroupDescription) {
                let col = grid.getColumn(gd.propertyName);
                if (col && col.index > -1) {
                  colIndex = col.index;
                }
              }
            }
            grid.select(ht.row, colIndex);
            break;
          case grid.columnHeaders:
            grid.select(sel.row, ht.col);
            break;
          case grid.rowHeaders:
            grid.select(ht.row, sel.col);
            break;
          default:
            return; // 无效面板，忽略
        }
        // 为当前列展示菜单
        if (grid.selection.col > -1) {
          e.preventDefault(); // 取消浏览器默认右键菜单
          menu.show(e); // 并显示此快捷菜单
        }
      },
      true,
    );
  }

  // 构建菜单面板
  buildMenu(grid: any) {
    // 构建排序面板及快捷菜单项
    let menu = new Menu(document.createElement('div'), {
      owner: grid.hostElement,
      displayMemberPath: 'header',
      subItemsPath: 'items',
      commandParameterPath: 'cmd',
      dropDownCssClass: 'ctx-menu',
      openOnHover: true,
      closeOnLeave: true,
      itemsSource: true ? [
        {
          header: '排序',
          items: [
            { header: '升序', cmd: 'SRT_ASC' },
            { header: '降序', cmd: 'SRT_DESC' },
            { header: '不排序', cmd: 'SRT_NONE' },
            { header: '-' },
            { header: '取消所有排序', cmd: 'SRT_CLR' },
          ],
        },
        { header: '-' },
        { header: '冻结/取消', cmd: 'PIN' },
        { header: '-' },
        { header: '合适宽度', cmd: 'ASZ' },
        { header: '全表合适列宽', cmd: 'ASZ_ALL' },
      ] : [
        {
          header: '排序',
          items: [
            { header: '升序', cmd: 'SRT_ASC' },
            { header: '降序', cmd: 'SRT_DESC' },
            { header: '不排序', cmd: 'SRT_NONE' },
            { header: '-' },
            { header: '取消所有排序', cmd: 'SRT_CLR' },
          ],
        },
        { header: '-' },
        { header: '冻结/取消', cmd: 'PIN' },
        { header: '-' },
        { header: '合适宽度', cmd: 'ASZ' },
        { header: '全表合适列宽', cmd: 'ASZ_ALL' },
        { header: '-' },
        { header: '分组/取消分组', cmd: 'GRP' },
        { header: '清除所有分组', cmd: 'GRP_CLR' },
        // { header: '-' },
      ],
      command: {
        // 启用或禁用菜单命令
        canExecuteCommand: (cmd: any) => {
          let view = grid.collectionView,
            col = grid.columns[grid.selection.col];
          switch (cmd) {
            case 'SRT_ASC':
              return col.currentSort != '+';
            case 'SRT_DESC':
              return col.currentSort != '-';
            case 'SRT_NONE':
              return col.currentSort != null;
            case 'SRT_CLR':
              return view.sortDescriptions.length > 0;
            case 'PIN':
              if (col._hdr === '客户头像') {
                return false;
              }
              return true; // 切换冻结
            case 'ASZ':
            case 'ASZ_ALL':
              return true;
            case 'GRP':
              return col.dataType != DataType.Number; // 数值列不做分组
            case 'GRP_CLR':
              return view.groupDescriptions.length > 0;
          }
          return true;
        },
        // 执行菜单命令
        executeCommand: (cmd: any) => {
          let view = grid.collectionView,
            cols = grid.columns,
            col = cols[grid.selection.col],
            sd = view.sortDescriptions,
            gd = view.groupDescriptions;
          switch (cmd) {
            case 'SRT_ASC':
            case 'SRT_DESC':
            case 'SRT_NONE':
              if (grid.allowSorting != AllowSorting.MultiColumn) {
                sd.clear();
              } else {
                for (let i = 0; i < sd.length; i++) {
                  if (sd[i].property == col.binding) {
                    sd.removeAt(i);
                    break;
                  }
                }
              }
              if (cmd != 'SRT_NONE') {
                sd.push(new SortDescription(col.binding, cmd == 'SRT_ASC'));
              }
              break;
            case 'SRT_CLR':
              sd.clear();
              break;
            case 'PIN':
              let fCols = grid.frozenColumns;
              if (col.index >= fCols) {
                // 冻结
                cols.moveElement(col.index, fCols, false);
                cols.frozen++;
              } else {
                // 取消冻结
                cols.moveElement(col.index, fCols - 1, false);
                cols.frozen--;
              }
              break;
            case 'ASZ':
              if (col.index === 0) {
                break;
              }
              grid.autoSizeColumn(col.index);
              break;
            case 'ASZ_ALL':
              // grid.autoSizeColumns(0, grid.columns.length - 1);
              grid.autoSizeColumns(1, grid.columns.length - 1);
              break;
            case 'GRP':
              // 移除分组
              for (let i = 0; i < gd.length; i++) {
                if (gd[i].propertyName == col.binding) {
                  gd.removeAt(i);
                  return; // 完成移除则返回
                }
              }
              // 添加分组
              gd.push(new PropertyGroupDescription(col.binding));
              break;
            case 'GRP_CLR':
              gd.clear();
              break;
          }
          // 为选中的表格单元格存储光标聚焦 (TFS 439964)
          grid.refresh();
          let sel = grid.selection,
            cell = grid.cells.getCellElement(sel.row, sel.col);
          if (cell) {
            cell.focus();
          }
        },
      },
    });
    // 完成
    return menu;
  }
}

