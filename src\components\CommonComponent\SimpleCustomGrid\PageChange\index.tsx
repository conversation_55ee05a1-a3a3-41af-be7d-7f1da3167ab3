import React, { useState } from 'react';
import { Stack, Typography, FormControl, MenuItem as MuiMenuItem, Select } from '@mui/material';
import { FirstPage, LastPage, KeyboardArrowLeft, KeyboardArrowRight } from '@mui/icons-material';

interface PageChangeProps {
  /**
   * 总页数
   */
  totalPage?: string | number,
  /**
   * 每页显示条数
   */
  count?: string | number,
  /**
   * 当前页
   */
  page?: string | number,
  /**
   * 总条数
   */
  total?: string | number,
  /**
   * 分页变化回调函数
   */
  onChange?: (val: { count: string | number, page: string | number }) => void,
  /**
   * 兼容旧版本API
   * @deprecated 使用 onChange 替代
   */
  pageCountChange?: (val: { count: string | number, page: string | number }) => void,
  /**
   * 每页条数选项列表
   */
  countList?: number[]
}

const PageChange = (props: PageChangeProps) => {
  const { total, totalPage, page, count, onChange, pageCountChange, countList = [10, 20, 50, 100, 200] } = props;

  // 将所有数值转换为字符串以保持一致性
  const totalStr = String(total || 0);
  const totalPageStr = String(totalPage || 0);
  const pageStr = String(page || 1);
  const countStr = String(count || 100);

  const [pageSize, setPageSize] = useState(countStr);

  // 页码变化处理函数
  const handlePageChange = (val: { count: string | number, page: string | number }) => {
    // 优先使用新API，兼容旧API
    if (onChange) {
      onChange(val);
    } else if (pageCountChange) {
      pageCountChange(val);
    }
  };

  return <Stack direction={'row'} justifyContent={'space-between'} mr={2}>
    <Stack />
    <Stack direction={'row'} alignItems={'center'}>
      <Stack direction={'row'} alignItems={'center'} mx={1} fontSize={12}>
        每页显示
        <FormControl sx={{ m: 1 }}>
          <Select value={pageSize} onChange={(e) => {
            setPageSize(e.target.value);
            handlePageChange({ count: e.target.value, page: 1 });
          }}>
            {countList.map(item => (
              <MuiMenuItem key={item} value={item}>{item}</MuiMenuItem>
            ))}
          </Select>
        </FormControl>
        条
      </Stack>

      <FirstPage sx={{ color: pageStr !== '1' ? 'inherit' : 'grey', cursor: pageStr !== '1' ? 'pointer' : 'inherit' }}
        onClick={() => {
          if (pageStr !== '1') {
            handlePageChange({ count: pageSize, page: '1' });
          }
        }} />
      <KeyboardArrowLeft sx={{ color: pageStr !== '1' ? 'inherit' : 'grey', cursor: pageStr !== '1' ? 'pointer' : 'inherit' }}
        onClick={() => {
          if (pageStr !== '1') {
            handlePageChange({
              count: pageSize,
              page: parseInt(pageStr) - 1,
            });
          }
        }} />
      <Typography
        fontWeight={'bold'}>{(totalStr && totalStr !== '0') ? (parseInt(pageStr) - 1) * parseInt(pageSize) + 1 : 0}</Typography>
      <Typography>至</Typography>
      <Typography
        fontWeight={'bold'}>{(totalStr && totalStr !== '0') ? Math.min(parseInt(pageStr) * parseInt(pageSize), parseInt(totalStr)) : 0}</Typography>
      <Typography m={0.5}>共计
        <Typography component={'span'} fontWeight={'bold'}>{totalStr}</Typography>
      </Typography>
      <KeyboardArrowRight
        sx={{ color: totalPageStr !== pageStr ? 'inherit' : 'grey', cursor: totalPageStr !== pageStr ? 'pointer' : 'inherit' }}
        onClick={() => {
          if (totalPageStr !== pageStr) {
            handlePageChange({
              count: pageSize,
              page: parseInt(pageStr) + 1,
            });
          }
        }} />
      <LastPage
        sx={{ color: totalPageStr !== pageStr ? 'inherit' : 'grey', cursor: totalPageStr !== pageStr ? 'pointer' : 'inherit' }}
        onClick={() => {
          if (pageStr !== totalPageStr) {
            handlePageChange({
              count: pageSize,
              page: parseInt(totalPageStr),
            });
          }
        }} />
    </Stack>
  </Stack>;
};

export default PageChange;
