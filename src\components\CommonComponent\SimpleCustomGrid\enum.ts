
const bgColor = '#dddddd4c';
const color = '#1976d2fe';

//选择标签的形状
export const tagShape = [
    {
        value: '11',
        label: {
            color: '#666666',
            backgroundColor: bgColor,
            border: '1px solid transparent',
            borderRadius: '0',
        },
    },
    {
        value: '12',
        label: {
            color: '#666666',
            borderRadius: '0.2rem',
            backgroundColor: bgColor,
            border: '1px solid transparent',
        },
    },
    {
        value: '13',
        label: {
            color: '#666666',
            borderRadius: '2rem',
            backgroundColor: bgColor,
            border: '1px solid transparent',
        },
    },
    {
        value: '21',
        label: {
            color: color,
            backgroundColor: bgColor,
            border: '1px solid transparent',
            borderRadius: '0',
        },
    },
    {
        value: '22',
        label: {
            color: color,
            borderRadius: '0.2rem',
            backgroundColor: bgColor,
            border: '1px solid transparent',
        },
    },
    {
        value: '23',
        label: {
            color: color,
            borderRadius: '2rem',
            backgroundColor: bgColor,
            border: '1px solid transparent',
        },
    },
    {
        value: '31',
        label: {
            color: color,
            border: '1px solid #1976d233',
            borderRadius: '0',
        },
    },
    {
        value: '32',
        label: {
            color: color,
            borderRadius: '0.2rem',
            border: '1px solid #1976d233',
        },
    },
    {
        value: '33',
        label: {
            color: color,
            borderRadius: '2rem',
            border: '1px solid #1976d233',
        },
    },
    {
        value: '41',
        label: {
            color: color,
            border: '1px solid #1976d233',
            backgroundColor: bgColor,
            borderRadius: '0',
        },
    },
    {
        value: '42',
        label: {
            color: color,
            borderRadius: '0.2rem',
            border: '1px solid #1976d233',
            backgroundColor: bgColor,
        },
    },
    {
        value: '43',
        label: {
            color: color,
            borderRadius: '2rem',
            border: '1px solid #1976d233',
            backgroundColor: bgColor,
        },
    },
];

/**
* 树转数组
* @param arr
* @returns
*/
export function flatTreeInKey(arr: any[] = [], key: string): any[] {
    if (!Array.isArray(arr)) {
        return [];
    }
    const res: any[] = [];
    res.push(...arr);
    for (const item of arr) {
        if (item[key] && item[key]?.length) {
            res.push(...flatTreeInKey(item[key], key));
        }
    }
    return res;
}

export function removeGroupPrefix(str?: string | null) {
    if (!str) {
        return '-';
    }
    if (typeof str !== 'string') {
        return '-';
    }
    return str ? str?.replace(/C\d{2,3}-/, '') : '';
}

export enum SimpleCustomGridEnum {
    NO_DATA = '查询数据范围内无记录',
    CREATE_GROUP = '将需要分组统计查看的列拖动到此分组栏，查看分组统计透视表',
    NO_DATA_TABLE = '暂无分类表格',
    COLUMN_SET = '列设置',

}

//客户列设置数据
export interface GridColumnProps {
    /**
     * 列标题字段
     */
    binding?: string;
    /**
     * 列标题
     */
    header?: string;
    /**
     * 宽度
     */
    width?: any;
    /**
     * 最小宽度
     */
    minWidth?: number;
    /**
     * 最大宽度
     */
    maxWidth?: number;
    /**
     * 是否允许通过单击列标题进行排序
     */
    allowSorting?: boolean;
    /**
     * 是否可以拖动换行 默认可以直接拖动
     */
    allowDragging?: boolean;
    /**
     * 是否允许调整列宽
     */
    allowResizing?: boolean;
    /**
     * 列的文本位置，左，居中，右 'left' 'center' 'right'
     */
    align?: string;
    /**
     * 是否显示列
     */
    visible?: boolean;
}

export interface businessColumnProps {
    /**
     * 列标题字段
     */
    binding?: string;
    /**
     * 是否不要这一列筛选器
     */
    filter_btn?: boolean;
    /**
     * 是否不要冻结图标
     */
    freeze_btn?: boolean;
    /**
     * 是否加粗这列文字
     */
    bold?: boolean;
    /**
     * 是否在工作通道隐藏该列
     */
    dimension_hide?: boolean;
    /**
     * 是否在列设置中直接隐藏改列的控制隐藏
     */
    column_set_hide?: boolean;
    /**
     * 数字以数字的颜色展示
     */
    numberColor?: string;
    /**
     * 是否在olap中隐藏展示
     */
    hideOlap?: boolean;
    /**
     * 是否是富文本展示
     */
    rich_text?: boolean;
    /**
     * 是否以标签形式展示
     */
    labelShow?: boolean;
    /**
     * 需要去除列中的Cxx- 值列
     */
    removeCxxBinding?: boolean;
}
