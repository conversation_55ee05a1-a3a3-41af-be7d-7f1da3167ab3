div[style*='z-index: 10000'] {
    display: none !important;
}

.simple-custom #simple-custom-grid {
    border: none;
    margin-bottom: 8px;
    flex: 1;
}

.simple-custom .wj-grouppanel .wj-groupmarker {
    padding: 4px 16px;
}

/* 整个表格的边框*/


.simple-custom div::-webkit-scrollbar {
    width: 7.5px;
    height: 7.5px;
}

.simple-custom div::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px #E1E1E1;
    background-color: #E1E1E1;
    border-radius: 10px;
}

.simple-custom div::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 5px #8c8c8c;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}


.simple-custom div::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
}

.simple-custom #simple-custom-grid .wj-group b {
    max-width: 230px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    vertical-align: top;
}

.simple-custom #simple-custom-grid .wj-cell {
    border: none;
    border-right: 1px solid #eaecef;
    border-bottom: 1px solid #eaecef;
    padding: 8px 8px 8px 8px;
    color: #4A545E;
    font-size: 12px;
    line-height: 1.2;
}

.simple-custom #simple-custom-grid .wj-cell.wj-header {
    background: #F5F7F9;
    color: #171717;
    font-size: 12px;
    font-weight: bold;
}

.simple-custom #simple-custom-grid .wj-btn .wj-btn-glyph .wj-elem-detail {
    display: none;
}

.simple-custom #simple-custom-grid .wj-content {
    border-radius: 0;
}

#simple-custom-grid .wj-cells .wj-cell.wj-state-active {
    background-color: #e5ebfc;
}


.simple-custom #simple-custom-grid .wj-cell.wj-group {
    background-color: white !important;
    color: #2c4a77 !important;
}

.simple-custom  .wj-grouppanel {
    display: block;
    background: #fbfbfb;
    padding: 15px;
    height: 46px;
    min-height: 1em;
}

.simple-custom #simple-custom-grid .wj-pager .wj-input-group .wj-form-control {
    text-align: center;
    border-left: none;
    border-right: none;
}

.simple-custom #simple-custom-grid .wj-control .wj-input-group {
    display: flex;
    align-items: center;
    border-collapse: inherit;
    background-color: #fbfbfb;
    width: inherit;

}

.simple-custom #simple-custom-grid .wj-control .wj-input-group .wj-form-control {
    display: block;
    padding: 0;
    border: 0;
    width: inherit;
    height: inherit;
    color: inherit;
    background: 0 0;
    min-height: inherit;

}

.simple-custom #simple-custom-grid .wj-control .wj-input-group .wj-input-group-btn {
    display: flex;
    width: inherit;
    height: inherit;
    min-width: inherit;
    white-space: nowrap;
    vertical-align: inherit;
}

.simple-custom #simple-custom-grid .wj-cell.wj-alt {
    background: #F4F7FA;
}

.simple-custom #simple-custom-grid .wj-cells .wj-cell.wj-state-selected {
    background: #D3E2ED;
    color: black;
}

.simple-custom #simple-custom-grid .wj-cells .wj-cell.wj-state-multi-selected {
    background: #D3E2ED;
    color: #4A545E;
}

.simple-custom #simple-custom-grid .wj-rowheaders .wj-header.wj-state-multi-selected {
    border-right: 0 solid #eaecef;
}

.simple-custom #simple-custom-grid .wj-colheaders .wj-header.wj-state-multi-selected {
    border-bottom: 0 solid #eaecef;
}

.simple-custom #simple-custom-grid .wj-cell.wj-header.wj-state-multi-selected {
    background: #9EB3C9;
    color: #FCFEFF;
    font-weight: 700;
}

.simple-custom .wj-grouppanel .wj-groupmarker:hover {
    background: #9EB3C9;
}

.simple-custom #simple-custom-grid .wj-cell input.wj-column-selector.wj-column-selector-group {
    transform: scale(1.1);
    margin-right: 6px;
}




/* flex-grid-filter */

.simple-custom #simple-custom-grid .wj-sort-buttons {
    font-size: 12px;
}

.simple-custom #simple-custom-grid .wj-listbox .wj-listbox-item>label {
    font-size: 12px;
}

.simple-custom #simple-custom-grid .wj-listbox-item {
    font-size: 12px;
}

.simple-custom #simple-custom-grid .wj-columnfiltereditor .wj-filtertype {
    margin: 10px 0;
}

.simple-custom #simple-custom-grid .wj-listbox-item>label {
    display: flex;
    align-items: center;
}

.simple-custom #simple-custom-grid .wj-listbox-item>label>span {
    margin-left: 3px;
}

.simple-custom #simple-custom-grid .wj-listbox .wj-listbox-item>label {
    align-items: center;
}

.simple-custom #simple-custom-grid .wj-columnfiltereditor .wj-control {
    font-size: 12px;
}

.simple-custom #simple-custom-grid .wj-content.wj-dropdown,
.simple-custom #simple-custom-grid .wj-content.wj-inputnumber,
.simple-custom #simple-custom-grid .wj-content.wj-inputmask,
.simple-custom #simple-custom-grid .wj-content.wj-calendar-outer,
.simple-custom #simple-custom-grid .wj-content.wj-pager {
    height: 24px;
}

/* 自定义样式*/

.simple-custom {
    width: inherit;
}


.simple-custom-group-paging {
    /* width: 200px; */
    padding: 10px;
    display: flex;
    align-items: center;
    background-color: #fbfbfb;
    white-space: nowrap;
    justify-content: space-between;
}

.simple-custom-group-paging>div:nth-of-type(1) {
    display: flex;
    align-items: center;
}



.simple-custom-group-paging-select {
    outline: none;
    height: 25px;
    margin: 0 5px;
    background-color: #fbfbfb;
}


.simple-custom-group-paging-first {
    display: flex;
    margin: 0 10px 0 20px;
    cursor: pointer;
}

.simple-custom-group-paging-first>div:nth-of-type(1) {
    border-left: 2px solid;
    height: 1.25em;
    align-self: center;
    margin-right: 1px;
}

.simple-custom-group-paging-first>div:nth-of-type(2) {
    width: 10px;
    height: 10px;
    border-left-width: 2px;
    border-top-width: 2px;
    border-bottom-width: 0;
    border-right-width: 0;
    border-style: solid;
    transform: rotate(-45deg);
    align-self: center;
}

.simple-custom-group-paging-last {
    display: flex;
    margin: 0 10px;
    cursor: pointer;
}

.simple-custom-group-paging-last>div:nth-of-type(1) {
    width: 10px;
    height: 10px;
    border-left-width: 0;
    border-top-width: 2px;
    border-bottom-width: 0;
    border-right-width: 2px;
    border-style: solid;
    transform: rotate(45deg);
    align-self: center;
}

.simple-custom-group-paging-last>div:nth-of-type(2) {
    border-right: 2px solid;
    height: 1.25em;
    align-self: center;
    margin-left: 1px;
}

.simple-custom-group-paging-next {
    width: 10px;
    height: 10px;
    border-left-width: 0;
    border-top-width: 2px;
    border-bottom-width: 0;
    border-right-width: 2px;
    border-style: solid;
    transform: rotate(45deg);
    align-self: center;
    cursor: pointer;
}

.simple-custom-group-paging-prev {
    width: 10px;
    height: 10px;
    border-left-width: 2px;
    border-top-width: 2px;
    border-bottom-width: 0;
    border-right-width: 0;
    border-style: solid;
    transform: rotate(-45deg);
    align-self: center;
    cursor: pointer;

}

.simple-custom-grid-no-data-to-display {
    position: absolute;
    top: 95px;
    left: 50%;
    font-size: 12px;
    transform: translate(-50%,0);
    text-align: center;
}

#simple-custom-grid .simple-custom-click-row {
    background: #D3E2ED !important;
}

.simple-custom #simple-custom-grid .flex-grid .wj-filter-on .wj-glyph-filter {
    color: #2b6bff;
    transform: scale(1);
}

.simple-custom #simple-custom-grid .wj-glyph-filter {
    transform: scale(0.9);
}

.simple-custom #simple-custom-grid .wj-elem-pin {
    transform: scale(0.9);
}

.simple-custom-icon {
    margin-right: 10px;
    display: flex;
    align-items: center;
}

.simple-custom-icon>img {
    width: 12px;
    height: 32px;
}

.simple-custom-icon>img:hover {
    cursor: pointer;
}

.simple-custom-icon-box {
    display: flex;
    align-items: center;
    padding: 10px;
}

.simple-custom #simple-custom-grid .simple-custom-small-cell {
    padding: 6px;
}

.simple-custom #simple-custom-grid .simple-custom-big-cell {
    padding: 12px;
}


.simple-custom-small-flexgrid {
    height: 320px;
}

.simple-custom-big-flexgrid {
    height: 450px;
}


.simple-custom-avatar-cell {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.simple-custom #simple-custom-grid .simple-custom-table-no-border {
    border: none;
}

.simple-custom #simple-custom-grid .simple-custom-table-all-border {
    /* border:0.5px solid #eaecef; */
    box-shadow: inset 0 0 0 0.1px #eaecef;
}

.simple-custom #simple-custom-grid .simple-custom-table-bottom-border {
    border: none;
    border-bottom: 1px solid #eaecef;
}

.simple-custom #simple-custom-grid .simple-custom-table-right-border {
    border: none;
    border-right: 1px solid #eaecef;
}

.simple-custom #simple-custom-grid .current-table-font-bold {
    font-weight: bold;
}

.simple-custom #simple-custom-grid .table-avatar {
    width: 24px;
    height: 24px;
    border-radius: 3px;
    margin-right: 16px;
}

.simple-custom #simple-custom-grid .column-header-right-border {
    border-right: 1px dashed #b4bbcd8c;
}

.simple-custom #simple-custom-grid .customer-name-item {
    display: flex;
    flex: 1;
    align-items: center;
}

.simple-custom #simple-custom-grid .wj-state-disabled {
    color: #2B6BFF !important;
}


.simple-custom #simple-custom-grid .wj-glyph-right {
    top: 1px;
    border-top: .5em solid transparent;
    border-bottom: .5em solid transparent;
    border-left: .5em solid;
}

.simple-custom #simple-custom-grid .wj-glyph-down-right {
    top: 0;
    border-bottom: .75em solid;
    border-left: .75em solid transparent;
}

.simple-custom #simple-custom-grid .dimension-opacity-column {
    opacity: 0.3;
}

.simple-custom #simple-custom-grid .import-number-color {
    color: #23527c
}