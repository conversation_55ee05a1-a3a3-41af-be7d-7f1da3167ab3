/**
 * @description 通用表格组件
 * <AUTHOR>
 * @date 2025/03/13
 */

import * as React from 'react';
import ReactDOM from 'react-dom';
import { compact, isEqual } from 'lodash';
// wijmo 组件
import * as wijmo from '@grapecity/wijmo';
import { CollectionView, SortDescription } from '@grapecity/wijmo';
import { Selector } from '@grapecity/wijmo.grid.selector';
import { Menu, MenuItem, MenuSeparator } from '@grapecity/wijmo.react.input';
import { CellType, GroupRow, SelectionMode, CellRange } from '@grapecity/wijmo.grid';
import { DetailVisibilityMode, FlexGridDetailProvider } from '@grapecity/wijmo.grid.detail';
import { FlexGrid, FlexGridColumn } from '@grapecity/wijmo.react.grid';
import { GroupPanel } from '@grapecity/wijmo.react.grid.grouppanel';

//mui组件
import { Stack, Typography, Divider, Popover } from '@mui/material';

//表格数据列过滤器
import { FlexGridFilter } from '@grapecity/wijmo.react.grid.filter';
import * as gridFilter from '@grapecity/wijmo.grid.filter';

// 导入部分，添加MUI图标
import FilterAltOffIcon from '@mui/icons-material/FilterAltOff';

// 自定义组件
import { FlexGridContextMenu } from './GridContextMenu';
import ColumnsSet from './ColumnSet';

//样式
import './index.css';
//默认头像图片
import Empty_Data from './Empty_Data.png';

import { tagShape, flatTreeInKey, removeGroupPrefix, SimpleCustomGridEnum, GridColumnProps, businessColumnProps } from './enum';

//客户列表的state定义
interface IsState {
  contextMenu: any; //右键菜单
  detailCell: any; //详情单元格
  flexGrid: any; //整个表格，flexGrid组件
  groupIndex: any; //分组的数组下标
  // selectPagingValues?: number[]; //分页展示的数组
  data: any; //表格数据源
  selectedItem: any; // 点击当前行选择中的数据记录，不是点击勾选框的数据记录
  selectedItems: any[]; //存储勾选多选框的数据行
  clickSelectedItem: any,//当前单击的那个单元格，为了获取其兄弟节点，设置选中效果
  customerNotes?: any; //点击的当前单元格的富文本内容 用popver 弹出展示
  anchorPosition?: any; //点击当前单元格富文本内容的在屏幕上的 x,y位置，用于popver定位
  tempGroup: any[];//临时存储当前分组
}

//客户列表的props
interface GridProps {
  /**
   * 数据源相关
   */
  /**
   * 表格展示数据源
   */
  itemsSource?: any[];
  /**
   * 真实数据源
   */
  realItemsSource?: any[];
  /**
   * 行记录
   */
  rowStyle?: any[];

  /**
   * 列及展示配置
   */
  /**
   * 表格列设置数据
   */
  columnSet?: GridColumnProps[];
  /**
   * 初始化列业务展示设置包括加粗，隐藏，数字颜色，标签展示，富文本展示，去除Cxx- 值列
   */
  initBusinessColumn?: businessColumnProps[];
  /**
   * 冻结第几列
   * @default 2
   */
  frozenColumns?: number;
  /**
   * 是否允许冻结列
   * @default 'Both'
   */
  allowPinning?: string;
  /**
   * 打开列设置的方式 是用组件里popver打开，还是回调出自定义打开
   */
  open_column_set_method?: string;

  /**
   * 表格样式配置
   */
  /**
   * 表格行高
   */
  tableRowHeight?: 28 | 32 | 40;
  /**
   * 斑马线效果
   */
  zebraCrossing?: 1 | 0;
  /**
   * 表格边框
   */
  tableBorder?: 'NO_BORDERS' | 'ALL_BORDERS' | 'BOTTOM_BORDER' | 'RIGHT_BORDER';
  /**
   * 标签样式
   */
  tagStyle?: string;
  /**
   * 初始是否正在加载
   * @default 0
   */
  isLoadingList?: number;
  /**
   * 这张表的统数单位 户 条
   */
  unit?: any;

  /**
   * 分组相关配置
   */
  /**
   * 默认是否显示分组
   */
  defaultShowGroup?: boolean;
  /**
   * 默认分组
   */
  defaultGroups?: string[];
  /**
   * 分组条件
   */
  showGroupCondition?: string[];
  /**
   * 分组过滤器是否显示百分比
   * @default true
   */
  groupFilterListShowPrecent?: boolean;
  /**
   * 是否隐藏分组面板
   */
  hideGroupPanel?: boolean;

  /**
   * 选择和操作相关
   */
  /**
   * 是否启用选择器
   */
  enableSelector?: boolean;
  /**
   * 会打开多选框的那一行，但是会隐藏多选框可见，当批量操作的时候，会让多选框可见
   * @default false
   */
  hiddenCheck?: boolean;
  /**
   * 是否处于批量操作中
   * @default false
   */
  isBatchOperate?: boolean;
  /**
   * 勾选行
   */
  changeItem?: any;
  /**
   * 是否有导出excel
   * @default false
   */
  exportExcel?: boolean;
  /**
   * 当前是那张表
   */
  listCode?: any;

  /**
   * 是否显示详情
   */
  showDetail?: boolean;

  /**
   * 详情组件
   */
  DetailComp?: React.ComponentType<{ selectedItem: any }>;

  /**
   * 分页组件
   */
  pageComp?: React.ReactNode;

  /**
   * 回调事件
   */
  /**
   * 选中多选框回调返回选中的行
   */
  onChange?: (value: any) => void;
  /**
   * 单击表格回调选中的行
   */
  onClick?: (value: any) => void;
  /**
   * 双击表格回调事件
   */
  onDbClick?: (value: any) => void;
  /**
   * 修改了表格布局之后回调事件
   */
  updateView?: (value?: any, group?: string[], selectColumnBinding?: string) => void;
  /**
   * 表格初始化回调事件
   */
  callbackFlexGrid?: (value?: any) => void;
  /**
   * 自定义点击表格单元格回调事件
   */
  onOperation?: (value?: any) => void;
  /**
   * 回调出点击了列设置
   */
  openColumnSet?: () => void;
  /**
   * 自定义格式化单元格
   */
  formatItem?: (gridPanel: any, cellRange: any) => void;
  /**
   * 列设置改变
   */
  columnSetChange?: (columns: GridColumnProps[]) => void;

}
class SimpleCustomGrid extends React.Component<GridProps, IsState> {
  static defaultProps: Partial<GridProps> = {
    tableRowHeight: 28,
    zebraCrossing: 0,
    defaultShowGroup: false,
    defaultGroups: [],
    tableBorder: 'BOTTOM_BORDER',
    enableSelector: false,
    groupFilterListShowPrecent: true,
    hideGroupPanel: false,
  }
  constructor(props: GridProps) {
    super(props);
    // 此时尚未加载表格数据！！
    const { itemsSource } = props;

    this.state = {
      contextMenu: null, // 构建并绑定右键菜单
      detailCell: null, //详情单元格
      flexGrid: null, //整个表格，flexGrid组件
      groupIndex: null, //分组的时候的数组下标
      data: new CollectionView(itemsSource, {
        pageSize: itemsSource ? itemsSource.length : 10,
        // 暂时关闭初始化分组
        groupDescriptions: this.props.defaultShowGroup ? (this.props?.defaultGroups ?? []) : [],
      }),
      // 点击当前行选择中的数据记录，不是点击勾选框的数据记录
      selectedItem: null,
      //存储多选框选中行
      selectedItems: [],
      clickSelectedItem: null,
      customerNotes: null,
      tempGroup: [],

    };
  }

  // 表格过滤器
  customFilter: any = null;
  //定义个变量存储控制翻页的对象
  collNavigator: any = null;
  //多选框
  selector: any = null;
  //分组容器
  groupPanel: any = null;


  /**
   * 在表格（grid）初始化后发生
   * @arg {Object] gridPanel - 包含展示区域的 表格面板。
   */
  handleInitialized = (gridPanel: any) => {
    // 表格存入到 state 并表格初始化分页数
    this.setState({ flexGrid: gridPanel });

    // 构建并绑定右键菜单
    new FlexGridContextMenu(gridPanel);

    if (this.props.hiddenCheck) {
      gridPanel.rowHeaders.columns.defaultSize = 0
    }

    //构建多选框
    this.selector = this.props.enableSelector ? new Selector(gridPanel, {
      showCheckAll: false,
      itemChecked: () => {
        this.setState(
          {
            selectedItems: gridPanel.rows.filter((r: any) => r.isSelected && r?.visible),
          },
          () => {
            let temp: any[] = []; //临时存放变量
            this.state.selectedItems?.forEach((i) => {
              temp.push({ ...i.dataItem });
            });
            //返回数据
            this.props.onChange?.(temp);
          },
        );
      }
    }) : null;

    if (this.props.showDetail) {
      // 创建详情面板 Provider
      const detailProvider = new FlexGridDetailProvider(gridPanel, {});

      //详情面板不设置最高宽度
      detailProvider.maxHeight = null;

      // 默认是点击行头的展开详情按钮打开查看
      // 选中一行时打开详情面板
      detailProvider.detailVisibilityMode = DetailVisibilityMode.Code;

      // 设置创建详情单元的回调函数
      detailProvider.createDetailCell = () => {
        const detailCell = document.createElement('div');
        detailCell.className = 'grid-record-detail-panel';
        this.setState({
          detailCell: detailCell,
        });
        return detailCell;
      };

      // 设置收起单元格的回调函数
      detailProvider.disposeDetailCell = () => {
        this.setState({ detailCell: null });
      };
    }


    // 双击表格事件触发处理：数据行上双击，打开详情信息面板
    gridPanel.hostElement.addEventListener('dblclick', (e: any) => {
      let ht = gridPanel.hitTest(e.pageX, e.pageY);

      if (!gridPanel.rows[ht.row]?._detail) {
        //区分详情单元格的一行
        //为了区分是分组的那一行还是详情单元格的哪一行
        if (!(gridPanel.rows[ht.row] instanceof GroupRow)) {
          if (
            CellType[ht.cellType] !== 'ColumnHeader' &&
            CellType[ht.cellType] !== 'RowHeader'
          ) {  //不是列标题和行标题
            if (gridPanel.rows[ht.row]?.dataItem?.customerId) {//有数据
              if (CellType[ht.cellType] === 'Cell') {
                this.props.onDbClick?.(gridPanel.rows[ht.row]?.dataItem);
              }
            }
          }
        }
      }
    });

    //单击表格事件
    gridPanel.hostElement.addEventListener('click', (e: any) => {
      let ht = gridPanel.hitTest(e.pageX, e.pageY);
      //单击的是单元格
      if (CellType[ht.cellType] === 'Cell') {
        if (!gridPanel.rows[ht.row]?._detail) {
          //单击的不是详情面板行
          if (!(gridPanel.rows[ht.row] instanceof GroupRow)) {
            //单击的不是分组标题行 处理点击富文本，弹出模态框显示
            if ([...this.props.initBusinessColumn?.filter(i => !!i.rich_text)?.map(i => i.binding) ?? [], 'notes'].includes(gridPanel.columns[ht.col].binding)) {
              this.setState({
                anchorPosition: {
                  top: e.clientY,
                  left: e.clientX
                },
                customerNotes: gridPanel.rows[ht.row]?.dataItem[gridPanel.columns[ht.col].binding]
              })
            }
            //回调点击事件
            this.props.onClick?.(gridPanel.rows[ht.row]?.dataItem);
          }
        }
      }

      //点击某些文字功能的回调
      if (CellType[ht.cellType] === 'Cell') {
        if (e.target.dataset.type) {
          this.props.onOperation?.({ ...gridPanel.rows[ht.row]?.dataItem, dataCode: e.target.dataset.type })
        }
      }
    });
  };

  /**
   * 初始化表格过滤器
   * @param  cgFilter - 自定义表格过滤器
   */
  initializedGridFilter = (cgFilter: any) => {
    this.customFilter = cgFilter;

    //包含选中的项
    this.customFilter.filterColumns = this.props.initBusinessColumn?.filter(item => item.filter_btn).map(item => item.binding) || [];
    //Both and Condition
    this.customFilter.defaultFilterType = gridFilter.FilterType.Both;
    this.customFilter.showSortButtons = true;
    // 设置不需要过滤的列

  };

  /**
   * 初始化表格分组面板
   * @param {Object} groupPanel
   */
  initializedGroupPanel = (groupPanel: any) => {
    //分组的样式设置
    groupPanel.hostElement.style.flex = 1;
    groupPanel.hostElement.style.padding = '8px';
    groupPanel.hostElement.firstElementChild.firstElementChild.style.color = '#8a8a8a';
    groupPanel.hostElement.firstElementChild.firstElementChild.style.lineHeight =
      '30px';
    groupPanel.hostElement.firstElementChild.firstElementChild.style.overflow =
      'normal';
    this.groupPanel = groupPanel

    //绑定右键点击事件
    groupPanel.hostElement.addEventListener('contextmenu', (e: any) => {
      let groupDescription = groupPanel.hitTest(e);
      let cv = groupPanel.collectionView;
      if (groupDescription) {
        this.setState({
          groupIndex: cv.groupDescriptions.indexOf(groupDescription),
        });
        this.state.contextMenu.show(e);
      }
      e.preventDefault();
    });
  };

  /**
   * 分组面板组件初始化
   * @arg {Object] s   当分组面板触发打开右键菜单
   *
   */
  onGroupPanelMenuClick = (s: any) => {
    let grid = this.state.flexGrid;
    let cv = grid.collectionView,
      groupIndex = this.state.groupIndex;
    switch (s.selectedIndex) {
      case 0: // 展开所有
        grid.collapseGroupsToLevel(groupIndex + 1);
        break;
      case 1: // 收起所有
        grid.collapseGroupsToLevel(groupIndex);
        break;
      case 3: // 升序排序
      case 4: // 降序排序
      case 5: // 清除排序
        cv.deferUpdate(() => {
          cv.sortDescriptions.clear();
        });
        break;
      case 7: // 移除分组
        cv.groupDescriptions.removeAt(groupIndex);
        break;
    }
  };

  /**
   * 在一个单元格创建并格式化时处理
   * @arg {Object] gridPanel - 包含展示区域的 表格面板。
   * @arg {Object] cellRange - 受交互事件影响的单元格范围。
   */
  handleFormatItem = (gridPanel: any, cellRange: any) => {

    // 获取所有记录行、列定义及所有单元格（实际也是GridPanel 实例）
    const { rows, columns, cells } = gridPanel;
    // 当前单元格、列索引、行、表格面板（GridPanel）
    const { cell, col, row, panel } = cellRange;

    //实现奇偶数的斑马线效果 为了解决多选框单元格的背景色
    if (panel === gridPanel.rowHeaders && col === 0) {
      if (rows[row]?.dataItem) {
        //区分详情行
        if (!rows[row].dataItem?.items) {
          //区分分组行
          if (this.props.zebraCrossing) {
            cell.style.backgroundColor = row % 2 === 1 ? '#F4F7FA' : 'white';
          } else {
            cell.style.backgroundColor = 'white';
          }
        }
      }
    }

    //根据配置取消列标题固定钉子
    if (panel === gridPanel.columnHeaders) {
      if (!this.props.initBusinessColumn?.find(i => i.binding === columns[col]?.binding)?.freeze_btn) {
        cell.innerHTML = cell.innerHTML.replace(
          '<span class="wj-glyph-pin"></span>',
          '',
        );
      }
    }

    //列标题的虚线
    if (panel === gridPanel.columnHeaders) {
      cell.classList.add('column-header-right-border')
    }

    if (panel === gridPanel.columnHeaders) {
      const groupRows = rows.filter((i: any) => i instanceof GroupRow)
      if (groupRows.length) {
        const lastLevelGroupRows = groupRows.filter((i: any) => i.level === gridPanel.collectionView.groupDescriptions?.length - 1)
        const isCollapseRow = lastLevelGroupRows?.filter((i: any) => !i.isCollapsed)
        if (groupRows?.length) {
          if (isCollapseRow?.length) {
            cell.classList?.remove('dimension-opacity-column')
          } else {
            if (cell.classList?.contains?.('wj-groupmarker')) {
            } else {
              cell.classList?.add('dimension-opacity-column')
            }
          }
        } else {
          cell.classList?.remove('dimension-opacity-column')
        }
      }
    }

    //分组标题行
    if (rows[row] instanceof GroupRow) {

      if (panel !== gridPanel.columnHeaders && panel !== gridPanel.rowHeaders) {

        const { name, level, _path: path, items, groupDescription } = rows[row]?.dataItem || {}
        const { collectionView } = gridPanel || {}
        const { groups, groupDescriptions } = collectionView || {}

        if (this.props.groupFilterListShowPrecent) {

          const flatGroups = flatTreeInKey(groups, 'groups')
          if (level === 0) {
            //计算当前分组行占总数的百分比
            let precent = 0
            let showPrecent = ''
            precent = this.props.realItemsSource?.length ? items?.length / this.props?.realItemsSource?.length : 0
            showPrecent = this.props.groupFilterListShowPrecent ? this.props.realItemsSource?.length ? ' ' + (precent * 100).toFixed(2) + '%' : '' : ''

            let div = `<span class="process-diemension-0 process-diemension-number process-diemension-number-total" >${items?.length ?? '-'}</span><span class="process-diemension-0-precent process-diemension-number process-diemension-number-total">${showPrecent ?? '-'}</span>`

            const closingTagIndex = cell.innerHTML.indexOf('</b>')
            cell.innerHTML = cell.innerText ? removeGroupPrefix(cell.innerHTML.substring(0, closingTagIndex, closingTagIndex + 5)) + div : ''

          } else {
            //计算当前分组行占上一级分组行总数的百分比
            const targetParent = flatGroups?.find(i => {
              return groupDescriptions[level]?.propertyName === groupDescription?.propertyName && (i?._path === path?.replace('/' + (name + ''), '') || i?._path?.split('')?.reverse()?.join('') === path?.split('')?.reverse()?.join('')?.replace('' + (name?.split('')?.reverse()?.join('') + '/'), ''))
            }) //判断当前项的 path替换掉 /name 和父级的 path相等的元素 并且 当前项的分组层级在整个分组数数据中的下标位置的propertyName 和自身 groupDescription?.propertyName 相等
            const targetParentItemsLength = targetParent ? targetParent.items?.length : 0
            const precent = targetParentItemsLength ? items?.length / targetParentItemsLength : 0
            const showPrecent = this.props.groupFilterListShowPrecent ? targetParentItemsLength ? ' ' + (precent * 100).toFixed(2) + '%' : '' : ''

            let div = `<span class="process-diemension-0 process-diemension-number process-diemension-number-total" >${items?.length ?? '-'}</span><span class="process-diemension-0-precent process-diemension-number process-diemension-number-total">${showPrecent ? showPrecent : '-'}</span>`

            const closingTagIndex = cell.innerHTML.indexOf('</b>')
            cell.innerHTML = cell.innerText ? removeGroupPrefix(cell.innerHTML.substring(0, closingTagIndex, closingTagIndex + 5)) + div : ''

          }
        } else {
          let div = `<span class="process-diemension-0 process-diemension-number process-diemension-number-total" >${items?.length ?? '-'}</span>`
          const closingTagIndex = cell.innerHTML.indexOf('</b>')
          cell.innerHTML = cell.innerText ? removeGroupPrefix(cell.innerHTML.substring(0, closingTagIndex, closingTagIndex + 5)) + div : ''
        }

      }
    }

    //处理行高变化带来的文字垂直居中的问题
    if (this.props.tableRowHeight === 28) {
      if (cell instanceof HTMLDivElement) {
        cell.classList.remove('simple-custom-big-cell');
        cell.classList.add('simple-custom-small-cell');
      }
    } else if (this.props.tableRowHeight === 32) {
      if (cell instanceof HTMLDivElement) {
        cell.classList.remove('simple-custom-small-cell');
        cell.classList.remove('simple-custom-big-cell');
      }
    } else if (this.props.tableRowHeight === 40) {
      if (cell instanceof HTMLDivElement) {
        cell.classList.remove('simple-custom-small-cell');
        cell.classList.add('simple-custom-big-cell');
      }
    }

    //处理修改表格边框
    if (this.props.tableBorder === 'NO_BORDERS') {
      if (cell instanceof HTMLDivElement) {
        cell.classList.remove('simple-custom-table-all-border', 'simple-custom-table-bottom-border', 'simple-custom-table-right-border');
        cell.classList.add('simple-custom-table-no-border');
      }
    } else if (this.props.tableBorder === 'ALL_BORDERS') {
      if (cell instanceof HTMLDivElement) {
        cell.classList.remove('simple-custom-table-no-border', 'simple-custom-table-bottom-border', 'simple-custom-table-right-border');
        cell.classList.add('simple-custom-table-all-border');
      }
    } else if (this.props.tableBorder === 'BOTTOM_BORDER') {
      if (cell instanceof HTMLDivElement) {
        cell.classList.remove('simple-custom-table-no-border', 'simple-custom-table-all-border', 'simple-custom-table-right-border');
        cell.classList.add('simple-custom-table-bottom-border');
      }
    } else if (this.props.tableBorder === 'RIGHT_BORDER') {
      if (cell instanceof HTMLDivElement) {
        cell.classList.remove('simple-custom-table-no-border', 'simple-custom-table-all-border', 'simple-custom-table-bottom-border');
        cell.classList.add('simple-custom-table-right-border');
      }
    }



    if (cells === panel) {
      //为了区分增加的一行是detail的一行，还是group的一行，还是表格数据的一行
      if (rows[row].dataItem) {
        //为了区分增加的一行是detail详情表格的那一行，还是group分组的那一行，还是表格数据的一行
        if (!rows[row].dataItem?.items) {


          //置顶
          if (columns[col]?.binding === 'topTime') {
            let div = `<span data-type="fixed-top" class="iconfont  fixed-customer-plan ${rows[row].dataItem.topTime ? 'icon-FIXED_FILLS' : 'icon-FIXED'} ${rows[row].dataItem.topTime ? 'fixed-customer-plan-color' : ''} "></span>`
            cell.innerHTML = div
            cell.style.paddingLeft = '8px'
          }

          //标签形式展示的列
          if (this.props.initBusinessColumn?.filter(i => i.labelShow)?.map(i => i.binding)?.includes(columns[col]?.binding ?? '')) {
            const labelSet = tagShape.find((i) => i.value === this.props.tagStyle)?.label;
            const border = labelSet?.border;
            const borderRadius = labelSet?.borderRadius;
            const backgroundColor = labelSet?.backgroundColor;
            let valueText = rows[row].dataItem?.[columns[col].binding]
            const temp = columns[col].binding === 'promotionDocument' ? compact(rows[row].dataItem.promotionDocument?.split(',')) : compact([removeGroupPrefix(valueText)])
            let div = ''
            temp?.forEach((i: any) => {
              div += this.decorationLabelCell(backgroundColor, borderRadius, border, i)
            })
            cell.style.color = '';
            cell.style.paddingTop = 0;
            cell.style.paddingBottom = 0;
            cell.style.display = 'flex';
            cell.style.alignItems = 'center'
            const align = this.props.columnSet?.find(i => i.binding === columns[col]?.binding)?.align
            cell.innerHTML = valueText && removeGroupPrefix(valueText) !== '-' ? `<div class="customer-name-item" style="justify-content:${align === 'center' ? 'center' : align === 'right' ? 'flex-end' : 'flex-start'}">
            ${div}
            </div>` : '-';
          }

          //备注 ,跟单备注
          if (['notes', 'trackNote'].includes(columns[col]?.binding ?? "")) {
            let valueText = rows[row].dataItem?.[columns[col].binding]
            const show = "<div class='rich-text-echo'>" + (valueText ?? '-') + "</div>"
            cell.innerHTML = show
          }

          //去除配合标签过滤添加CXX-的列
          if (this.props.initBusinessColumn?.filter(i => i.removeCxxBinding)?.map(i => i.binding)?.includes(columns[col]?.binding ?? '')) {
            let valueText = rows[row].dataItem?.[columns[col].binding]
            cell.style.color = '';
            cell.innerHTML = removeGroupPrefix(valueText) ?? '-';
          }

          //置顶的背景色
          if (rows[row].dataItem.topTime) {
            cell.classList.add('fixed-customer-row')
          } else {
            cell.classList.remove('fixed-customer-row')
          }

          /**
           * 读取行样式设置
           */
          if (this.props.rowStyle?.length !== 0) {
            const temp = this.props.rowStyle?.find(i => i.businessId === rows[row].dataItem.customerId && i.relationId === rows[row].dataItem.estateId);
            if (temp) {
              cell.style.backgroundColor = ` rgba(${temp.rowStyle?.backgroundColor?.r},
                   ${temp.rowStyle?.backgroundColor?.g},
                   ${temp.rowStyle?.backgroundColor?.b},
                   1)`;
            } else {
              cell.style.backgroundColor = '';
            }
          } else {
            //为了取消行背景色
            cell.style.backgroundColor = '';
          }

          //根据配置加粗字体
          if (this.props.initBusinessColumn?.find(i => i.binding === columns[col]?.binding)?.bold) {
            cell.classList.add('current-table-font-bold')
          }

          //添加重要数字类样式
          if (this.props.initBusinessColumn?.find(i => i.binding === columns[col]?.binding)?.numberColor) {
            cell.classList.add('import-number-color')
          }

          //查找所有单元格中值为空时替换为-
          if (!cell.innerHTML) {
            cell.innerHTML = '-';
          }

          //单元格没有数据时
          if (cell.innerHTML === 'undefined' || cell.innerHTML === 'null') {
            cell.innerHTML = '-';
          }

          //自定义格式化单元格
          this.props.formatItem?.(gridPanel, cellRange)


        }
      }
    }
  };

  /**
   * 显示渲染标签
   * @param backgroundColor z
   * @param borderRadius
   * @param border
   * @param content
   * @returns
   */
  decorationLabelCell = (backgroundColor: any, borderRadius: any, border: any, content: any) => {
    return `<div style='
    background-color:${backgroundColor ? '#337ab71A' : ''};
    width:fit-content;
    text-align:center;padding:2px 8px;
    border-radius:${borderRadius};
    border:${border?.replace(border?.slice(border?.indexOf('#')), "#337ab733")};
    margin-right:4px;
    color:#337ab7;'
    >${content ?? '-'}</div>`
  }

  shouldComponentUpdate(nextProps: any, nextState: any) {
    const grid = this.state.flexGrid;
    const { itemsSource } = this.props;
    const nextItemSource = nextProps.itemsSource;

    if (itemsSource) {

      // 比较新旧 props 传入的数据源是否已经改变，决定是否重新渲染组件
      const isChangedItems = isEqual(itemsSource, nextItemSource);

      // 更新表格 itemsSource（如果数据源已经更改）
      if (!isChangedItems) {
        // 在表格数据发生更新后，需要更新动态列及关联的分组面板
        console.log('更新表格 itemsSource（如果数据源已经更改）！');

        grid.itemsSource = new CollectionView(nextProps.itemsSource, {
          pageSize: nextProps.itemsSource.length,
          groupDescriptions: nextProps.showGroupCondition ?? [],
        });
        if (nextProps.showGroupCondition?.length) {
          this.state.flexGrid?.collapseGroupsToLevel(this.state.flexGrid.collectionView?.groupDescriptions?.length - 1)
        }
        //回调出空数组
        this.props.onChange?.([]);

      }

      //判断行样式填充背景色改变
      if (!isEqual(this.props.rowStyle, nextProps.rowStyle)) {
        if (grid?.itemsSource) {
          grid.collectionView.refresh();
        }
      }

      //判断表格边框改变
      if (this.props.tableBorder !== nextProps.tableBorder) {
        if (grid?.itemsSource) {
          grid.collectionView.refresh();
        }
      }

      //判断表格是否打开默认分组改变
      if (this.props.defaultShowGroup !== nextProps.defaultShowGroup) {
        if (nextProps.defaultShowGroup) {
          grid.itemsSource = new CollectionView(nextProps.itemsSource, {
            pageSize: nextProps.itemsSource.length,
            groupDescriptions: nextProps.defaultGroups?.length ? nextProps.defaultGroups : [],
          });
        } else if (this.props.defaultShowGroup === true && nextProps.defaultShowGroup === false) {
          grid.itemsSource = new CollectionView(nextProps.itemsSource, {
            pageSize: nextProps.itemsSource.length,
            groupDescriptions: [],
          });
        }
      }

      //判断默认分组打开，在调整默认分组，不会改变分组
      if (!isEqual(this.props.defaultGroups, nextProps.defaultGroups)) {
        if (nextProps.defaultShowGroup) {
          grid.itemsSource = new CollectionView(nextProps.itemsSource, {
            pageSize: nextProps.itemsSource.length,
            groupDescriptions: nextProps.defaultGroups ?? [],
          });
        }
      }

      //判断打开新表格的时候要展示的初始分组
      if (!isEqual(this.props.showGroupCondition, nextProps.showGroupCondition)) {
        if (grid?.itemsSource) {//如果表格已有分组和要更新的分组不一样
          if (!isEqual(grid.collectionView.groupDescriptions?.map((i: any) => i?.propertyName), nextProps.showGroupCondition)) {
            grid.itemsSource = new CollectionView(nextProps.itemsSource, {
              pageSize: nextProps.itemsSource.length,
              groupDescriptions: nextProps.showGroupCondition,
            });
          }
        }
      }

      //修改工具栏标签样式
      if (this.props.tagStyle !== nextProps.tagStyle) {
        if (grid?.itemsSource) {
          grid.collectionView.refresh();
        }
      }

      //显示百分比
      if (this.props.groupFilterListShowPrecent !== nextProps.groupFilterListShowPrecent) {
        if (grid?.itemsSource) {
          grid.collectionView.refresh();
        }
      }

      //是否在批量处理中
      if (this.props.isBatchOperate !== nextProps.isBatchOperate) {
        if (grid?.itemsSource) {
          if (this.props.hiddenCheck) {
            if (nextProps.isBatchOperate) {
              grid.rowHeaders.columns.defaultSize = 45
            } else {
              grid.rowHeaders.columns.defaultSize = 0
            }
            grid.collectionView.refresh(true);
          }
        }
      }

    }

    return true;
  }


  /**
   * 分组更新内容刷新之后
   * @param control
   */
  groupPanelRefreshed = (control: any) => {
    //避免卡顿问题，在此收起展开分组  会因为多选框的打开和隐藏影响

    if (control.collectionView?.groupDescriptions?.length) { //用一个存储分组与当前分组作比较，（其他动作也会触发这个方法）在分组没有变化时，不执行collapseGroupsToLevel方法
      const tempControlGroup = control.collectionView?.groupDescriptions?.map((i: any) => i.propertyName)
      if (!isEqual(this.state.tempGroup, tempControlGroup)) {
        this.state.flexGrid?.collapseGroupsToLevel(control.collectionView?.groupDescriptions?.length - 1)
        this.setState({ tempGroup: tempControlGroup })
      }
    } else {
      if (this.state.tempGroup.length) {
        this.setState({ tempGroup: [] })
      }
    }

  };

  //分组更新内容刷新之前
  groupPanelRefreshing = (control: any, event: any) => {

    control.collectionView?.groupDescriptions?.forEach((item: any) => {
      if (!control.collectionView?.sortDescriptions?.find((i: any) => i.property === item.propertyName)) {
        this.state.flexGrid?.collectionView?.sortDescriptions?.push(new SortDescription(item.propertyName, true))
      }
    })

    const tempControlGroup = control.collectionView?.groupDescriptions?.map((i: any) => i.propertyName)
    if (!isEqual(this.state.tempGroup, tempControlGroup)) {
      const filterSort = this.state.tempGroup.filter(i => !tempControlGroup.includes(i))
      if (filterSort?.length) {
        const index = control.collectionView?.sortDescriptions.findIndex((i: any) => filterSort.includes(i.property))
        if (index !== -1) {
          control.collectionView?.sortDescriptions.removeAt(index)
        }
      }
    }

    //拖动分组后，控制焦点重回第一行第一列 该行代码必须放置在清除排序效果之后，
    this.state.flexGrid?.select(new CellRange(0, 0), true)

  }

  /**
   *
   * @param flexGrid 表格
   */
  updatedView = (flexGrid: any) => {

    this.props.callbackFlexGrid?.(flexGrid);
    this.props.onClick?.(flexGrid.selectedItems?.[0]?.customerId)
    const customerArchiveTableColumnDtoList: any[] = [];
    let selectColumn = ''
    flexGrid?.columns?.map((item: any, index: number) => {
      if (index === flexGrid?.selectedRanges?.[0]?.col) {
        selectColumn = item?.binding
      }
      let temp = {
        align: item?.align,
        binding: item?.binding,
        header: item?.header,
        width: item?.width,
        sortIndex: index,
        visible: item.visible,
        allowSorting: item.allowSorting,
        allowResizing: item.allowResizing,
        allowDragging: item.allowDragging,
      };
      customerArchiveTableColumnDtoList.push(temp);
    });

    //设置隐藏的列，在分组取消后，会重新在列上展示,这里又设置回隐藏
    const hideColumn = this.props.initBusinessColumn?.filter(i => i.column_set_hide)?.map(i => i.binding)
    if (hideColumn && hideColumn?.length) {
      const target = flexGrid.columns.filter((i: any) => hideColumn.includes(i.binding))
      target.forEach((i: any) => {
        i.visible = false
      })
    }
    /**
     * 当前表格的分组
     */
    const groupCondition = flexGrid?.collectionView?.groupDescriptions.map((item: any) => {
      return item._bnd._key;
    });
    if (this.props.itemsSource && this.props.itemsSource.length) {
      this.props.updateView?.(customerArchiveTableColumnDtoList, groupCondition, selectColumn);
    }

    if (flexGrid._activeCell) {
      this.setState({ clickSelectedItem: flexGrid._activeCell?.parentNode?.children }, () => {
        [...flexGrid._activeCell?.parentNode?.children].forEach((item) => {
          item.classList.add('simple-custom-click-row');
        });
      });
    }

    const { rows } = flexGrid

    //在工作通道，如果不是处于批量操作中，隐藏多选框
    let temp: HTMLInputElement[] = Array.from(document.querySelectorAll('.simple-custom .wj-column-selector'))
    if (this.props.hiddenCheck) {
      if (!this.props.isBatchOperate) {
        temp.forEach(i => {
          i.style.visibility = 'hidden'
        })
      } else {
        temp.forEach(i => {
          i.style.visibility = 'normal'
        })
      }
    }


    //如果存在这个勾选数据，就每次在表格布局更新完之后，又更新一次当前布局的勾选数据
    if (this.props.changeItem?.length && !rows.some((i: any) => i.isSelected)) {
      this.props.onChange?.(rows.filter((i: any) => i.isSelected).map((i: any) => i.dataItem))
    } else if (this.props.changeItem?.length === 0 && rows.some((i: any) => i.isSelected)) { //如果没有勾选数据，但是有选中的行，就再回调
      this.props.onChange?.(rows.filter((i: any) => i.isSelected).map((i: any) => i.dataItem))
    }

  };

  //两者结合，选中一行的效果
  /**
   * 在刷新网格中的时候
   */
  updatingView = () => {
    if (this.state.clickSelectedItem) {
      [...this.state.clickSelectedItem].forEach((item: any) => {
        item.classList.remove('simple-custom-click-row');
      });
    }
  };

  /**
   * 在筛选器即将变更时
   * @param s
   * @param e
   */
  filterChanging = (s: any, e: any) => {
    let edt = s.activeEditor
    //拿到ele
    let lbHost = edt.hostElement.querySelector('[wj-part=div-values]')
    //根据ele获取到listBox的控件
    let lb = wijmo.Control.getControl(lbHost)
    if (lb) {
      //@ts-ignore
      lb.itemFormatter = (index, content) => {
        return removeGroupPrefix(content)
      }
    }
  }

  handleClearFilterAll = () => {
    this.customFilter.clear?.()
  }

  componentDidMount() {

    // 自定义表格过滤条件
    let gridCellFilter = wijmo.culture.FlexGridFilter,
      Operator = gridFilter.Operator;
    gridCellFilter.apply = '应用';
    gridCellFilter.cancel = '关闭';
    gridCellFilter.clear = '清除';
    gridCellFilter.apply = '应用';
    gridCellFilter.conditions = '按条件过滤';
    gridCellFilter.ascending = '↑升序';
    gridCellFilter.descending = '↓降序';
    gridCellFilter.header = '显示下列值的项目';
    gridCellFilter.search = '搜索';
    gridCellFilter.or = '或';
    gridCellFilter.and = '和';
    gridCellFilter.values = '按值过滤';
    gridCellFilter.selectAll = '搜索全部';
    gridCellFilter.null = '-';

    // 字符文本类
    gridCellFilter.stringOperators = [
      { name: '(未设置)', op: null },
      { name: '包含', op: Operator.CT },
      { name: '开头', op: Operator.BW },
      { name: '结尾', op: Operator.EW },
      { name: '相同', op: Operator.EQ },
      { name: '不同', op: Operator.NE },
      { name: '大于', op: Operator.GT },
      { name: '小于', op: Operator.LT },
    ];
    // 数值
    gridCellFilter.numberOperators = [
      { name: '(未设置)', Operator: null },
      { name: '相同', op: Operator.EQ },
      { name: '不同', op: Operator.NE },
      { name: '大于', op: Operator.GT },
      { name: '小于', op: Operator.LT },
    ];
    //
    gridCellFilter.dateOperators = [
      { name: '(未设置)', op: null },
      { name: '相等', op: Operator.EQ },
      { name: '早于', op: Operator.LT },
      { name: '晚于', op: Operator.GT },
    ];
    //
    gridCellFilter.booleanOperators = [
      { name: '(未设置)', op: null },
      { name: '是', op: Operator.EQ },
      { name: '否', op: Operator.NE },
    ];


  }

  render() {
    try {
      return (
        <Stack className='simple-custom' sx={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          width: '100%',
          position: this.props.itemsSource?.length === 0 ? 'relative' : 'static',
          '& .fixed-customer-plan': {
            ml: 0.5,
            ':hover': {
              cursor: 'pointer'
            }
          },
          '& .fixed-customer-plan-color': {
            color: '#2b6bff'
          },
          '& .fixed-customer-row': {
            bgcolor: '#F9FAFC'
          },
          '& .show-last-update-time': {
            color: '#2b6bff',
            ml: 2,
            fontSize: 13,
            ':hover': {
              cursor: 'pointer'
            }
          },
          '& .table-avatar': {
            cursor: 'pointer'
          },
          '& .count-precent': {
            color: '#2b6bff',
            position: 'absolute',
            left: (this.state.flexGrid?.collectionView?.groupDescriptions?.length ?? 0) * 25 + 260
          },
          '& .table-rich-text-echo': {
            width: '16px',
            height: '16px'
          },
          '& .img-phnom-penh': {
            // border: '1px solid #f9bb03'
          },
          '& .activityTargetNum': {
            color: '#8489A0',
            ml: '16px'
          },
          '& .activityReachRate': {
            color: '#8489A0',
            ml: '16px'
          },
          '& .process-diemension-number': {
            display: 'inline-block',
            width: '60px',
            textAlign: 'right',
            position: 'absolute',
          },
          '& .process-diemension-number-total': {
            color: '#477efe'
          },
          '& .process-diemension-0': {
            color: '#477efe',
            left: (this.state.flexGrid?.collectionView?.groupDescriptions?.length ?? 0) * 25 + 260
          },
          '& .process-diemension-0-precent': {
            color: '#477efe',
            fontWeight: 'normal',
            left: (this.state.flexGrid?.collectionView?.groupDescriptions?.length ?? 0) * 25 + 330
          },
          '& .process-diemension-1': {
            color: '#464feb',
            left: (this.state.flexGrid?.collectionView?.groupDescriptions?.length ?? 0) * 25 + 320
          },
          '& .process-diemension-1-precent': {
            color: '#8489A0',
            fontWeight: 'normal',
            left: (this.state.flexGrid?.collectionView?.groupDescriptions?.length ?? 0) * 25 + 380
          },
          '& .process-diemension-2': {
            color: '#000080',
            left: (this.state.flexGrid?.collectionView?.groupDescriptions?.length ?? 0) * 25 + 420
          },
          '& .process-diemension-2-precent': {
            color: '#8489A0',
            fontWeight: 'normal',
            left: (this.state.flexGrid?.collectionView?.groupDescriptions?.length ?? 0) * 25 + 480
          },
          '& .process-diemension-3': {
            color: '#420080',
            left: (this.state.flexGrid?.collectionView?.groupDescriptions?.length ?? 0) * 25 + 520
          },
          '& .process-diemension-3-precent': {
            color: '#8489A0',
            fontWeight: 'normal',
            left: (this.state.flexGrid?.collectionView?.groupDescriptions?.length ?? 0) * 25 + 580
          },
          '& .process-diemension-4': {
            color: '#cf344e',
            left: (this.state.flexGrid?.collectionView?.groupDescriptions?.length ?? 0) * 25 + 660
          },
          '& .process-diemension-4-precent': {
            color: '#8489A0',
            fontWeight: 'normal',
            left: (this.state.flexGrid?.collectionView?.groupDescriptions?.length ?? 0) * 25 + 720
          },
          '& .cancel-import-focus': {
            color: '#2b6bff',
            cursor: 'pointer'
          },
          '& p': {
            m: 0
          }
        }}>
          {!this.props.hideGroupPanel && <Stack sx={{
            flexDirection: 'row', alignItems: 'center', fontSize: 12, bgcolor: '#fbfbfb',
            position: 'sticky',
            top: 0,
            zIndex: 2,
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
          }}>
            <GroupPanel
              className='simple-custom-group-panel'
              hideGroupedColumns={false}
              grid={this.state.flexGrid}
              initialized={this.initializedGroupPanel}
              placeholder={SimpleCustomGridEnum.CREATE_GROUP}
              refreshing={this.groupPanelRefreshing}
              refreshed={this.groupPanelRefreshed}
            />
            {!!this.state.flexGrid?.collectionView?.groupDescriptions?.length && <Typography sx={{ px: 1, color: '#b0b8c4' }}>右键点击分组块，收起展开分组</Typography>}
            <Divider variant='middle' textAlign='center' orientation='vertical' sx={{ height: '50%' }} />
            {
              this.props.exportExcel && <>
                <Divider variant='middle' textAlign='center' orientation='vertical' sx={{ height: '50%', mr: 1 }} />
                <Divider variant='middle' textAlign='center' orientation='vertical' sx={{ height: '50%', mx: 1 }} />
              </>
            }
            {this.props.pageComp ? this.props.pageComp : null}
            <Stack sx={{ cursor: 'pointer', ':hover': { color: 'primary.main' }, flexDirection: 'row', alignItems: 'center' }} onClick={this.handleClearFilterAll}>
              <FilterAltOffIcon sx={{ ml: 1, fontSize: '1rem' }} titleAccess="清空筛选漏斗条件" />
              <Typography component={'span'} sx={{ fontSize: '12', ml: 0.4, cursor: 'pointer', ':hover': { color: 'primary.main' } }}>清空筛选条件</Typography>
            </Stack>
            <Divider variant='middle' textAlign='center' orientation='vertical' sx={{ height: '50%', mx: 1 }} />
            <Stack sx={{
              alignItems: 'center', bgcolor: '#fbfbfb', justifyContent: 'center',
            }}>
              <ColumnsSet initBusinessColumn={this.props.initBusinessColumn} columnSet={this.props.columnSet} onClick={() => { this.props.openColumnSet?.() }} onChange={this.props.columnSetChange} open_column_set_method={this.props.open_column_set_method} />
            </Stack>
            {!this.props.pageComp && <Divider variant='middle' textAlign='center' orientation='vertical' sx={{ height: '50%', ml: 1 }} />}
            {!this.props.pageComp && <Typography sx={{ px: 1, }}>共计{this.props.realItemsSource?.length}{this.props.unit ? this.props.unit : ''}</Typography>}
          </Stack>}
          <Stack sx={{ flex: 1, overflow: 'auto' }}>
            <FlexGrid
              isReadOnly //只读单元格
              id='simple-custom-grid'
              className='flex-grid'
              draggable="false"
              selectionMode={this.props?.enableSelector ? SelectionMode.Row : SelectionMode.Row} //选择一行 ，添加了Selector之后，不起作用
              autoGenerateColumns={false}
              rows={{ defaultSize: this.props.tableRowHeight ?? 32 }}
              frozenColumns={this.props.frozenColumns !== undefined ? this.props.frozenColumns : 2}
              formatItem={this.handleFormatItem}
              initialized={this.handleInitialized}
              alternatingRowStep={this.props.zebraCrossing ?? 0} //斑马线效果设置，默认为1，关闭属性也为1
              showSelectedHeaders={'All'}
              itemsSource={this.state.data || []}
              preserveSelectedState={false}
              headersVisibility={this.props?.enableSelector ? 'All' : "Column"} // 不显示首列
              updatedView={this.updatedView} //在表格刷新内容布局之后发生
              updatingView={this.updatingView} //在表格刷新内容布局之前发生
              stickyHeaders={true} //设置一个值，该值确定当用户滚动文档时列标题是否应保持可见
              allowPinning={'SingleColumn'} //设置一个值,确定网格是否应向列标题添加pin按钮，以及pin按钮的行为方式,固定列
              allowSorting='MultiColumn' //用户可以一次按多个列队网格进行排序，单击列标题对列进行排序或翻转排序方向
            //按住ctrl键并单击可删除该列的排序，ctrl+shift+click（单击）可删除所有排序
            >
              {this.props?.columnSet?.map(
                (column: GridColumnProps, index: number) => (
                  <FlexGridColumn key={index} {...column} />
                )
              )}
              <FlexGridFilter
                initialized={this.initializedGridFilter}
                filterChanging={this.filterChanging} />
            </FlexGrid>
          </Stack>
          {this.props.itemsSource?.length === 0 && (
            <Stack className='simple-custom-grid-no-data-to-display' sx={{
              '& img': {
                width: '120px',
                height: '120px',
              }
            }}>
              <img src={Empty_Data} />
              <Typography color={'#c5c8cd'}>{this.props.isLoadingList ? SimpleCustomGridEnum.NO_DATA : ''}</Typography>
            </Stack>
          )}
          <Menu
            style={{ display: 'none' }}
            dropDownCssClass='ctx-menu'
            initialized={(cm: any) => this.setState({ contextMenu: cm })}
            itemClicked={this.onGroupPanelMenuClick}
          >
            <MenuItem>
              <span className='wj-glyph-down-right' /> 展开分组
            </MenuItem>
            <MenuItem>
              <span className='wj-glyph-right' /> 收起分组
            </MenuItem>
            <MenuSeparator />
            <MenuItem>
              <span className='wj-glyph-up' /> 升序排序
            </MenuItem>
            <MenuItem>
              <span className='wj-glyph-down' /> 降序排序
            </MenuItem>
            <MenuItem>
              <span className='wj-glyph-circle' /> 取消排序
            </MenuItem>
            <MenuSeparator />
            <MenuItem>
              <span>&times;</span> 移除分组
            </MenuItem>
          </Menu>
          {
            !!this.state.customerNotes && <Popover
              open={!!this.state.customerNotes}
              anchorReference='anchorPosition'
              anchorPosition={this.state.anchorPosition}
              onClose={() => {
                this.setState({
                  anchorPosition: {},
                  customerNotes: null,
                })
              }}
            >
              <Stack sx={{
                minWidth: '300px',
                minHeight: '150px',
                maxWidth: '500px',
                maxHeight: '300px',
                overflowY: 'auto',
                boxSizing: 'border-box',
                p: 2
              }}>
                <Stack sx={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Typography className='iconfont icon-details1' sx={{ fontSize: 18, color: 'primary.main' }}></Typography>
                  <Typography sx={{ fontSize: 14, fontWeight: 'bold', ml: 1 }}>详细备注</Typography>
                </Stack>
                <Stack sx={{ ml: '25px', mt: 1, lineHeight: 1.75, '& p': { my: 0 } }} dangerouslySetInnerHTML={{ __html: this.state.customerNotes }}></Stack>
              </Stack>
            </Popover>
          }
          {/* 根据初始参数判断是否生成订单详情面板 */}
          {Boolean(this.state.detailCell && this.props.showDetail) &&
            ReactDOM.createPortal(
              this.props.DetailComp ?
                React.createElement(this.props.DetailComp, { selectedItem: this.state.selectedItem }) :
                <Stack className="grid-detail-default">详情内容</Stack>,
              this.state.detailCell
            )}
        </Stack>
      );
    } catch {

    }

  }
}

export default SimpleCustomGrid
