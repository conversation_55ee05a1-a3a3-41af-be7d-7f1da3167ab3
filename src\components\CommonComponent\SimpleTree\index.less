@import '../../../../node_modules/rc-tree/assets/index.less';

@treePrefixCls: ~'rc-tree';

.leaf-empty-icon {
  width: 0.75em;
  height: 0.75em;
}

.@{treePrefixCls} {
  &-title {
    font-size: 0.75rem;
  }

  &-switcher {
    display: flex !important;
    height: 100% !important;
    width: fit-content !important;
    background-position: unset !important;
    background-image: none !important;
    align-items: center;
    font-size: 1.5rem;
  }

  &-node-content-wrapper {
    display: flex !important;
    align-items: center;
    height: fit-content !important;
    flex-grow: 1;
    padding: 3px 0 !important;
    min-height: 1.5rem !important;
  }

  &-node-selected {
    background-color: #dde7f9;
    box-shadow: none;
    opacity: 1;
  }

  &-treenode-selected {
    background-color: #dde7f9;
    color: #2b6bff;
  }

  &-treenode {
    display: flex;
    align-items: center;
    line-height: unset !important;

    &:hover {
      background-color: #dde7f9 !important;
    }
  }

  .filter-node {
    > .@{treePrefixCls}-node-content-wrapper {
      > .@{treePrefixCls}-title {
        color: initial !important;
      }
    }
  }
}
