import React, { useRef, useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import {
    InputAdornment,
    OutlinedInput,
    Stack,
    styled,
    Typography,
} from '@mui/material';
import Tree, { BasicDataNode, TreeNodeProps } from 'rc-tree';
import {
    Clear,
    DoNotDisturbAlt,
    ExpandMore,
    Search,
} from '@mui/icons-material';
import './index.less';


const SwitchIcon = (node: TreeNodeProps) => {
    const { isLeaf, expanded } = node;

    if (isLeaf) {
        return <span className="leaf-empty-icon" />;
    }

    return (
        <ExpandMore
            sx={{
                width: '0.75em',
                height: '0.75em',
                cursor: 'pointer',
                transform: expanded ? undefined : 'rotate(-90deg)',
            }}
        />
    );
};

export interface SimpleTreeProps {
    value?: string | null;
    options?: SimpleTreeOption[];
    onChange?: (id: string, node?: SimpleTreeOption) => void;
    className?: string;
    //打开多选框
    checkAble?: boolean;
    //多选时是否选中子节点
    checkStrictly?: boolean;
    //多选时的值
    multipleValue?: string[] | null;
    multipleOnchange?: (val: any, node: any) => void;
    //是否展开所有
    defaultExpandAll?: boolean;
    expandAllValue?: string[];
    showIcon?: boolean;
    defaultExpandParent?: boolean;
    placeholder?: string;
    showInput?: boolean;
    keyword?: string;
    fieldNames?: any
}

export interface SimpleTreeOption extends BasicDataNode {
    id: string;
    label: string;
    children?: SimpleTreeOption[];
}


const SimpleTree = forwardRef((props: SimpleTreeProps, ref) => {
    const {
        value,
        options = [],
        placeholder = '',
        onChange,
        className,
        checkAble = false,
        checkStrictly = false,
        multipleValue,
        multipleOnchange,
        defaultExpandAll = false,
        expandAllValue = [],
        showIcon = false,
        defaultExpandParent = true,
        showInput = true,
        keyword,
        fieldNames = {
            key: 'id',
            title: 'label',
        }
    } = props;

    const flatRef = useRef<SimpleTreeOption[]>(flatTreeToArray(options));

    useImperativeHandle(ref, () => ({
        handleSearchInputChange: handleSearchInputChange,
        handleClear: handleClear
    }))

    const [expanded, setExpanded] = useState<string[]>(
        value ? [value] : expandAllValue ?? [],
    );
    const [inputValue, setInputValue] = useState<string>('');
    const [searchResult, setSearchResult] = useState<string[]>([]);
    const [autoExpand, setAutoExpand] = useState<boolean>(true);
    const [searchFilterOption, setSearchFilterOption] = useState<any>([]);

    function flatTreeToArray(options: SimpleTreeOption[]) {
        let optionArr: { id: string; label: string }[] = [];
        options?.forEach((parent) => {
            const { id, label, children = [] } = parent;
            optionArr.push({ id, label });
            if (children?.length) {
                optionArr.push(...flatTreeToArray(children));
            }
        });
        return optionArr;
    }

    function handleFilter(node: any) {
        if (!searchResult.length) {
            return false;
        }
        return searchResult.includes(node.id);
    }

    function handleExpand(ids: any[]) {
        setAutoExpand(false);
        setExpanded(ids);
    }

    function handleSelect(ids: any[], { node, selected }: any) {
        !checkAble &&
            onChange?.(ids[0], selected ? node : undefined);
    }

    const onCheck = (checkedKeys: any, e: any) => {
        checkStrictly
            ? multipleOnchange?.(checkedKeys.checked, e?.checkedNodes)
            : multipleOnchange?.(checkedKeys, e.checkedNodes);
    };

    function renderTree() {
        return (
            <Tree
                treeData={searchFilterOption?.length ? searchFilterOption : options}
                fieldNames={fieldNames}
                showIcon={showIcon}
                switcherIcon={SwitchIcon}
                autoExpandParent={autoExpand}
                expandedKeys={expanded}
                selectedKeys={value ? [value] : []}
                onSelect={handleSelect}
                onExpand={handleExpand}
                filterTreeNode={handleFilter}
                checkable={checkAble}
                // defaultExpandedKeys={expandAllValue}
                defaultExpandAll={defaultExpandAll ?? false}
                checkStrictly={checkStrictly}
                onCheck={onCheck}
                defaultExpandParent={defaultExpandParent}
                // @ts-ignore
                checkedKeys={multipleValue ?? []}
            // disabled={true}
            />
        );
    }

    function handleSearchInputChange(event: any) {
        const input = event.target.value;

        setInputValue(input);
        if (!input) {
            setSearchResult([]);
            setExpanded([]);
            setSearchFilterOption([]);
            return;
        }
        const idArr = flatTreeToArray(options).filter(({ label }) => label.indexOf(input) !== -1).map(({ id }) => id);
        setSearchFilterOption(filterTree(options, input) ?? [])
        setSearchResult(idArr);
        setExpanded(idArr);
        setAutoExpand(true);
    }

    function filterTree(data: SimpleTreeOption[], keyWord: string) {
        const results: any[] = [];

        data.forEach((node) => {
            const children = node?.children ? filterTree(node.children, keyWord) : [];
            if (node.label.includes(keyWord) || children.length > 0) {
                results.push({
                    ...node, children: children.length > 0 ? children : node.children,
                });
            }
        });
        return results;
    }

    function handleClear() {
        setInputValue('');
        setSearchResult([]);
        // setExpanded([]);
        setSearchFilterOption([]);
    }

    function renderEmpty() {
        return (
            <Stack
                sx={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    color: '#888',
                    minHeight: 100,
                }}
            >
                <DoNotDisturbAlt />
                <Typography>无结果</Typography>
            </Stack>
        );
    }

    return (
        <Stack
            id="simpleTree-container"
            className={className}
            sx={{
                padding: 1.5,
                overflowY: 'auto',
                '& legend': {
                    m: '0!important',
                    width: '0!important',
                },
            }}
            spacing={1}
        >
            {showInput && (
                <OutlinedInput
                    value={inputValue}
                    placeholder={placeholder}
                    endAdornment={
                        inputValue && (
                            <InputAdornment position="end">
                                <Clear
                                    sx={{
                                        fontSize: '1.1em',
                                        cursor: 'pointer',
                                    }}
                                    onClick={handleClear}
                                />
                            </InputAdornment>
                        )
                    }
                    startAdornment={
                        <InputAdornment position="start">
                            <Search sx={{ fontSize: '1.1em' }} />
                        </InputAdornment>
                    }
                    onChange={handleSearchInputChange}
                />
            )}

            {(inputValue || keyword) && searchResult.length === 0
                ? renderEmpty()
                : renderTree()}
        </Stack>
    );
})


export default styled(SimpleTree)<SimpleTreeProps>({});


