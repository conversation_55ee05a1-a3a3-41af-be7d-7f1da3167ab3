import WORD from '@/assets/img/UploadImg/WORD.png';
import EXCEL from '@/assets/img/UploadImg/EXCEL.png';
import PDF from '@/assets/img/UploadImg/PDF.png';
import RAR from '@/assets/img/UploadImg/RAR.png';
import PPT from '@/assets/img/UploadImg/PPT.png';
import TXT from '@/assets/img/UploadImg/TXT.png';
import VIDEO from '@/assets/img/UploadImg/VIDEO.png';
import OTHER from '@/assets/img/UploadImg/leaf-light-blue.png';
import Compressor from "compressorjs";

export const allFileType = {
  Doc: ['doc', 'docx'],
  Ppt: ['ppt', 'pptx'],
  Excel: ['xls', 'xlsx'],
  Pic: ['jpg', 'png', 'bmp', 'gif', 'svg', 'webp', 'pcx', 'tif', 'jpeg', 'tag', 'raw'],
  Video: ['avi', 'mp4', 'm4v', 'mov', 'wmv', 'asf', 'asx', 'rm', 'rmvb', 'mpg', 'mpeg', 'mpe', '3gp', 'mov'],
  Rar: ['rar', '7z', 'zip'],
};

export const attachmentType = (item: any) => {
  switch (true) {
    case  allFileType.Doc.includes(interceptFileType(item)) :
      return WORD;
    case allFileType.Excel.includes(interceptFileType(item)) :
      return EXCEL;
    case interceptFileType(item) === 'pdf':
      return PDF;
    case  allFileType.Rar.includes(interceptFileType(item)):
      return RAR;
    case allFileType.Ppt.includes(interceptFileType(item)):
      return PPT;
    case interceptFileType(item) === 'txt':
      return TXT;
    case allFileType.Pic.includes(interceptFileType(item)):
      return item.url;
    case allFileType.Video.includes(interceptFileType(item)):
      return VIDEO;
    default:
      return OTHER;
  }
};

/**
 * @description 截取文件类型
 * @param val 需要转换的数据
 */
export const interceptFileType = (val: any) => {
  //判断位置
  let itemIndex = val?.fileName?.lastIndexOf('.') + 1;
  return itemIndex === 0 ? '' : val?.fileName?.slice(itemIndex).toLowerCase();
};


/**
 * @description 上传前压缩文件大小
 * @param file 文件
 * @param quality 图片压缩比，0-1，数字越小，图片压缩越小
 */
export const onBeforeUpload = (file: any, quality?: number) => {
  return new Promise((resolve, reject) => {
      if (!allFileType.Pic.includes(interceptFileType(file))) {
        resolve(file)
      } else {
        new Compressor(file, {
          quality: quality || 0.6,
          convertSize: Infinity,
          success(result) {
            let img = new File([result], file.name, {type: file.type})
            resolve(img)
          },
          error(error: Error) {
            console.log('图片压缩失败 :', error)
            reject(error)
          }
        });
      }
    }
  )
}


export const videoCover = (item: any, index: number) => {
  const videoElement: any = document.createElement('video')
  const imgElement: any = document.getElementById(`img-element-${index}`)

  let imgUrl
  videoElement.src = item?.url
  videoElement.currentTime = 1
  videoElement.setAttribute('crossOrigin', 'Anonymous')
  if (imgElement) {
    videoElement?.addEventListener('loadeddata', function () {
      let canvas = document.createElement('canvas')
      let context = canvas.getContext('2d')
      canvas.width = videoElement?.videoWidth
      canvas.height = videoElement?.videoHeight
      context?.drawImage(videoElement, 0, 0, canvas.width, canvas.height)
      imgUrl = canvas?.toDataURL('image/jpeg')
      imgElement.src = imgUrl
    })
    // console.log(imgUrl, videoElement,videoElement.src)
    console.log(imgUrl, imgElement)
  }
}


/**
 * @description 隐藏预览按钮
 * @param row 数据项
 */
export const hiddenReviewBtn = (row: any): boolean => {
  const typeArr = [...allFileType.Doc, ...allFileType.Ppt, ...allFileType.Excel]
  return !typeArr.includes(interceptFileType(row))
}

