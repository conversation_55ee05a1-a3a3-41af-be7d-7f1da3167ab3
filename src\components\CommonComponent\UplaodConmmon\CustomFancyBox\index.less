// 确保预览弹窗在最上层
.fancybox__container {
  z-index: 9999 !important;
}

// 确保预览弹窗的背景遮罩在最上层
.fancybox__backdrop {
  z-index: 9998 !important;
  background: rgba(0, 0, 0, 0.08) !important;
}

// 确保预览弹窗的内容在最上层
.fancybox__content {
  z-index: 9999 !important;
  padding: 0 !important;
}

// 确保预览弹窗的工具栏在最上层
.fancybox__toolbar {
  z-index: 10000 !important;
  background: rgba(0, 0, 0, 0.3) !important;
  border-radius: 4px !important;
  padding: 8px !important;
}

// 确保预览弹窗的缩略图在最上层
.fancybox__thumbs {
  z-index: 9999 !important;
  background: rgba(0, 0, 0, 0.3) !important;
}

// 优化图片显示
.fancybox__content > .fancybox__image {
  max-width: 95vw !important;
  max-height: 95vh !important;
  object-fit: contain !important;
  background: transparent !important;
  image-rendering: -webkit-optimize-contrast !important;
  image-rendering: crisp-edges !important;
  transform: translateZ(0) !important;
  backface-visibility: hidden !important;
  -webkit-backface-visibility: hidden !important;
  -webkit-transform: translateZ(0) !important;
  will-change: transform !important;
}

// 优化按钮样式
.f-button {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 4px !important;
  color: white !important;
  transition: all 0.3s ease !important;

  &:hover {
    background: rgba(255, 255, 255, 0.2) !important;
  }
}

// 优化缩略图样式
.fancybox__thumb {
  border-radius: 4px !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  image-rendering: -webkit-optimize-contrast !important;
  image-rendering: crisp-edges !important;

  &:hover {
    border-color: rgba(255, 255, 255, 0.5) !important;
  }

  &.is-active {
    border-color: white !important;
  }
} 