/**
 * @description 公用文件预览组件
 * <AUTHOR>
 * @date 2023-11-14
 */

import React, {useEffect, useRef} from "react";
import {Fancybox as NativeFancybox} from '@fancyapps/ui'
import "@fancyapps/ui/dist/fancybox/fancybox.css"
import {Stack} from "@mui/material";

interface CustomFancyBoxProps{
  options?:any
  del?:any
  children?:any
}
export const CustomFancyBoxOptions = {
  Carousel: {
    infinite: false
  },
  Toolbar: {
    display: {
      // right:["download",'close']
    }
  },
  Thumbs: false
}
function CustomFancyBox(props: CustomFancyBoxProps) {
  const containerRef = useRef(null)

  useEffect(() => {
    const container = containerRef.current
    const del = props.del || "[data-fancybox]"
    const options = props.options || {}
    NativeFancybox.bind(container, del, options)

    return () => {
      NativeFancybox.unbind(container)
      NativeFancybox.close()
    }

  }, [])
  return <Stack sx={{flexDirection: 'row', flexGrow: 1, flexWrap: 'wrap'}} ref={containerRef}>{props.children}</Stack>
}

export default CustomFancyBox
