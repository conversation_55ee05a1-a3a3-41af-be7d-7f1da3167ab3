import React, { CSSProperties, SyntheticEvent, useState } from "react";
import NO_PIC from "@/assets/img/UploadImg/NO_PIC.jpg";

interface CommonImgProps extends React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement> {
  src: any
  //默认占位图片
  defaultImg?: any
  alt?: string
  style?: CSSProperties
}

/**
 * @description 图片加载失败的时候，使用默认图片
 * <AUTHOR>
 * @date 2024-05-23
 */
function ImgComponent(props: CommonImgProps) {
  const {src, defaultImg = NO_PIC, style, alt = '', ...other} = props;
  const [error, setError] = useState<boolean>(true)

  const handleImgError = (e: SyntheticEvent<HTMLImageElement>) => {
    setError(false)
    error && (e.currentTarget.src = defaultImg)
  };

  return (
    <img
      src={src}
      // onLoad={handleImgLoad}
      onError={handleImgError}
      alt={alt}
      style={style}
      {...other}
    />
  );
}

export default ImgComponent;
