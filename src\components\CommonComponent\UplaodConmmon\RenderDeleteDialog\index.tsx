import React, { createRef, useEffect, useRef, useState } from 'react';
import { Button, DialogActions, DialogContent, Stack } from '@mui/material';
import BMCDialog from '@/components/CommonComponent/BMCDialog';

import {
  allFileType,
  attachmentType,
  interceptFileType,
} from '@/components/CommonComponent/UplaodConmmon/CommonMethod';
import NO_PIC from '@/assets/img/UploadImg/NO_PIC.jpg';
import ImgComponent from '@/components/CommonComponent/UplaodConmmon/ImgComponent';

interface RenderDeleteDialogProps {
  item?: any
  onChange?: (val?: boolean) => void
  open?: boolean
  close?: any
}

function RenderDeleteDialog(props: RenderDeleteDialogProps) {
  const { item, onChange, open = false, close } = props;

  return (
    <BMCDialog
      fullWidth={true}
      open={open}
      onClose={close}
      title={'是否要刪除？'}
      onCloseClick={close}
    >
      <DialogContent sx={{ pt: '20px!important' }}>
        <Stack sx={{ fontSize: 14, pb: 2 }}>
          {/*文件名:{item && item?.fileName?.slice(item?.fileName?.lastIndexOf('.') + 1)}*/}
          文件名:{item && item?.fileName}
        </Stack>
        {
          allFileType.Video.includes(interceptFileType(item)) ?
            <video src={item?.url} style={{ objectFit: 'contain', maxWidth: '34rem', maxHeight: '23rem' }} /> :
            <ImgComponent src={allFileType.Pic.includes(interceptFileType(item)) ? item.url : attachmentType(item)}
                          defaultImg={NO_PIC} style={{ objectFit: 'contain', maxWidth: '34rem', maxHeight: '23rem' }} />
        }
      </DialogContent>
      <DialogActions>
        <Button autoFocus onClick={close}>
          取消
        </Button>
        <Button variant='contained' onClick={() => onChange?.()} autoFocus>
          确定
        </Button>
      </DialogActions>
    </BMCDialog>
  );
}

export default RenderDeleteDialog;
