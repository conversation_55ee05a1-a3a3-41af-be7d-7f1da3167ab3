/**
 * @description 渲染预览类容
 * <AUTHOR>
 * @date 2023-02-13
 */
import React, { useState, useEffect } from 'react';
import { Box, Button, Stack, Typography } from '@mui/material';
import { Document, Page, pdfjs } from 'react-pdf';
import {
  allFileType,
  attachmentType,
  interceptFileType,
} from '@/components/CommonComponent/UplaodConmmon/CommonMethod';
import { Close } from '@mui/icons-material';
import axios from 'axios';
import PdfView from '@/components/CommonComponent/PdfView/PdfView';

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;

interface RenderPreviewProps {
  item?: any
  PdfViewReadonly?: any
  onChange?: (val: boolean) => void
  open?: boolean
}

function RenderPreview(props: RenderPreviewProps) {
  const { item, PdfViewReadonly, onChange, open } = props;
  // pdf只读模式下的预览
  const [page, setPage] = useState(1);
  const [pageTotal, setPageTotal] = useState(1);
  const [pdfBlob, setPdfBlob] = useState<any>(null);

  useEffect(() => {
    // 获取pdf文件进行转码
    if (item?.attachmentType === 'PDF') {
      axios({
        url: `${item.url}`,
        method: 'get',
        responseType: 'blob',
      }).then((res) => {
        console.log(res);
        let reader = new FileReader();
        reader.readAsDataURL(res.data);
        reader.addEventListener('load', () => {
          let base64 = reader.result as string;
          setPdfBlob(base64);
        });
      });
      if (!pdfBlob) return;
    }
  }, []);


  const closeButton = () => {
    onChange?.(false);
  };

  /**
   * @description 循环展示所有的pdf页面
   * @param num 页数
   */
  const renderPdf = (num: number) => {
    const page: any[] = [];
    for (let i = 2; i <= num; i++) {
      page.push(
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <Document file={pdfBlob} key={i}>
              <Page pageNumber={i} />
            </Document>
          </Box>
          <Typography style={{ display: 'flex', justifyContent: 'center' }}>
            page {i} of {pageTotal}
          </Typography>
        </Box>,
      );
    }
    return page;
  };

  /**
   * @description 获取到pdf的总页数
   * @param pages 页数
   */
  const onDocumentComplete = (pages: any) => {
    setPageTotal(pages);
  };


  const renderPreview = () => {
    if (interceptFileType(item) == 'pdf') {
      return !PdfViewReadonly ? (
        <Stack sx={{width:'90vw',height:'90vh'}}>
          <PdfView src={item?.url}/>
        </Stack>
        // <Box sx={{position:'a'}}>
        //   {/* <Box sx={{ display: 'flex', justifyContent: 'center' }}>
        //     <Document
        //       // file={window.location.origin +'http://*************/static/20211220/85d1acbac7684ea78db80fca5db1140f.pdf'}
        //       file={pdfBlob}
        //       onLoadSuccess={onDocumentComplete}
        //     >
        //       <Page pageNumber={1} />
        //     </Document>
        //   </Box>
        //   <Typography style={{ display: 'flex', justifyContent: 'center' }}>
        //     page {page} of {pageTotal}
        //   </Typography>
        //   {pageTotal > 1 && renderPdf(pageTotal)} */}
        //
        // </Box>
      ) : (
        <iframe
          title='pdf'
          src={item.url}
          style={{
            width: '90vw',
            height: '90vh',
          }}
        />);
    } else if (allFileType.Video.includes(interceptFileType(item))) {
      return <video src={item.url} controls />;
    } else {
      return <img
        src={allFileType.Pic.includes(interceptFileType(item)) ? item.url : attachmentType(item)}
        alt='图片加载失败'
        onClick={closeButton}
      />;
    }
  };

  return (
    <Stack>
      {
        open && (
          <Box
            sx={{
              position: 'fixed',
              top: 0,
              left: 0,
              width: '100%',
              height: '100vh',
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              zIndex: 9998,
            }}
          >
            <Button
              sx={{
                zIndex: 9999,
                cursor: 'pointer',
                position: 'fixed',
                right: '1rem',
                top: '1rem',
                border: 0,
                backgroundColor: 'transparent',
                fontSize: ' 2rem',
                color: 'white',
              }}
              title={'点击关闭'}
              onClick={closeButton}
              variant={'text'}
            >
              <Close />
            </Button>
            <Box
              sx={{
                position: 'fixed',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                inset: 0,
                transition: 'all 0.3s',
                'img': {
                  zIndex: 9999,
                },
              }}
            >
              {/* 判断预览是否是只读页面 */}
              {renderPreview()}
            </Box>
          </Box>
        )
      }
    </Stack>
  );
}

export default RenderPreview;
