import React, { createContext, useContext, useState, useEffect } from "react";

// 正确导入 cloudbase 核心库
import cloudbase from "@cloudbase/js-sdk";
import Message from "@/util/Message";
import { getValidToken } from "@/util/tokenUtils";

interface CloudbaseContextType {
    cloudbaseApp: cloudbase.app.App | null;
}

const CloudbaseContext = createContext<CloudbaseContextType | null>(null);

/**
 * 重定向到Cloudbase认证页面
 * @param {cloudbase.auth.App} auth - Cloudbase认证实例
 * @param {string} redirectUrl - 认证成功后的重定向URL
 */
export const redirectToAuth = (auth: any, redirectUrl: string) => {
    if (!auth.hasLoginState()) {
        const encodedUrl = encodeURIComponent(redirectUrl);
        window.location.href = `/__auth/?env_id=${process.env.ENV_ID}&client_id=${process.env.ENV_ID}&config_version=env&redirect_uri=${encodedUrl}`;
    }
};

export function redirectToLogin() {
    window.parent.postMessage(
        {
            messageType: "REDIRECT_URL",
            confirmRedirect: true,
        },
        "*"
    );
    Message.error("当前登录状态已过期，请重新登录");
    const doc = top || window;
    doc.location.href = "/cloud-home-page";
}


export const CloudbaseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [cloudbaseApp, setCloudbaseApp] = useState<cloudbase.app.App | null>(null);

    // useEffect(() => {
    //     try {
    //         const initialize = async () => {
    //             const app = cloudbase.init({
    //                 env: "begingroup-1g6s82e205f983c0", // 替换为你的云开发环境 ID                     
    //             });
    //             const auth = app.auth({
    //                 persistence: "local",
    //             });
    //             if (process.env.NODE_ENV === 'development') {
    //                 await auth.signIn({
    //                     username: '13281128729',
    //                     password: 'Zyh123456',
    //                     // username: 'develop',
    //                     // password: '222222N!',
    //                     // username: '13917085688',
    //                     // password: 'Init@123456'
    //                 });
    //                 //develop 222222n!
    //             } else {
    //                 // 进行认证重定向
    //                 redirectToAuth(auth, window.location.href);
    //             }
    //             setCloudbaseApp(app);
    //         };
    //         initialize();
    //     } catch (error) {
    //         console.error(error);
    //     }
    // }, []);

    useEffect(() => {
        const initialize = async () => {
            const app = cloudbase.init({
                env: process.env.ENV_ID || '',
                timeout: 90000,
            });

            const auth = app.auth({ persistence: "local" });
            const loginState = auth.hasLoginState();
            const existingToken = getValidToken('token')


            // 如果有有效登录状态和token，直接使用
            if (existingToken && loginState) {
                setCloudbaseApp(app);
                return;
            }

            // 需要重新认证
            if (process.env.NODE_ENV === 'development') {
                await auth.signIn({
                    username: '17318631581',
                    password: 'Qwer1234',
                });
                // 获取access_token
                const { accessToken } = await auth.getAccessToken();
                if (accessToken) {
                    localStorage.setItem('token', accessToken);
                }
                setCloudbaseApp(app);
            } else {
                redirectToLogin();
                // redirectToAuth(auth, window.location.href);
            }

            // // 获取access_token
            // const { accessToken } = await auth.getAccessToken();
            // if (accessToken) {
            //   localStorage.setItem('token', accessToken);
            // }
            // setCloudbaseApp(app);
        };

        initialize();
    }, []);


    return (
        <CloudbaseContext.Provider value={{ cloudbaseApp }}>
            {children}
        </CloudbaseContext.Provider>
    );
};

export const useCloudbase = () => {
    const context = useContext(CloudbaseContext);
    if (!context) {
        throw new Error("useCloudbase must be used within a CloudbaseProvider");
    }
    return context;
};
