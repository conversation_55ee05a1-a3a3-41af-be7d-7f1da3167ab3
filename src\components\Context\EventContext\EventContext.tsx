/**
 * 事件管理器
 * 提供跨组件或跨窗口的事件通信机制，主要用于处理窗口消息
 * <AUTHOR>
 * @date 2025-03-23
 */
import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from "react";

/**
 * 消息处理函数类型
 * 接收消息事件并处理
 */
type MessageHandler = (e: any) => void

/**
 * 事件类型枚举
 * 定义支持的事件类型
 */
export enum EventType {
    MESSAGE = 'message',
    RESIZE = 'resize',
    SCROLL = 'scroll',
    FOCUS = 'focus',
    BLUR = 'blur',
    LOAD = 'load',
    BEFOREUNLOAD = 'beforeunload',
    OFFLINE = 'offline',
    ONLINE = 'online',
    STORAGE = 'storage',
    HASHCHANGE = 'hashchange',
    POPSTATE = 'popstate',
    // 可以根据需要添加更多窗口事件
}

/**
 * 事件监听器接口
 * 用于存储不同类型事件的监听函数
 */
interface EventListeners {
    [key: string]: Set<MessageHandler>;
}

/**
 * 事件上下文类型接口
 * 定义事件管理器提供的方法
 */
interface EventContextType {
    /** 添加消息监听器(兼容旧API) */
    addListener: (listener: MessageHandler) => void;
    /** 移除消息监听器(兼容旧API) */
    removeListener: (listener: MessageHandler) => void;
    /** 发送消息(兼容旧API) */
    sendMessage: (message: any) => void;
    /** 添加指定类型的事件监听器 */
    addEventListener: (eventType: EventType | string, listener: MessageHandler) => void;
    /** 移除指定类型的事件监听器 */
    removeEventListener: (eventType: EventType | string, listener: MessageHandler) => void;
    /** 触发指定类型的事件 */
    dispatchEvent: (eventType: EventType | string, data: any) => void;
}

/**
 * 事件上下文
 * 用于在组件树中传递事件管理器
 */
const EventContext = createContext<EventContextType | null>(null)

/**
 * 事件提供者组件
 * 创建事件上下文并提供事件管理功能
 * @param children 子组件
 * @returns React组件
 */
export const EventProvider = (({ children }: { children: React.ReactNode }) => {
    /** 旧版API的消息监听器集合(兼容性考虑) */
    const [messageListeners, setMessageListeners] = useState<Set<MessageHandler>>(new Set());
    
    /** 所有事件类型的监听器集合 */
    const eventListeners = useRef<EventListeners>({
        [EventType.MESSAGE]: new Set<MessageHandler>(),
    });
    
    /**
     * 添加消息监听器(兼容旧API)
     * @param listener 监听器函数
     */
    const addListener = useCallback((listener: MessageHandler) => {
        if (typeof listener === 'function') {
            setMessageListeners(prevListeners => {
                const newListeners = new Set([...prevListeners, listener]);
                // 同步更新ref中的消息监听器
                eventListeners.current[EventType.MESSAGE] = newListeners;
                return newListeners;
            });
        }
    }, []);

    /**
     * 移除消息监听器(兼容旧API)
     * @param listener 要移除的监听器函数
     */
    const removeListener = useCallback((listener: MessageHandler) => {
        if (typeof listener === 'function') {
            setMessageListeners(prevListeners => {
                const newListeners = new Set(prevListeners);
                newListeners.delete(listener);
                // 同步更新ref中的消息监听器
                eventListeners.current[EventType.MESSAGE] = newListeners;
                return newListeners;
            });
        }
    }, []);

    /**
     * 添加指定类型的事件监听器
     * @param eventType 事件类型
     * @param listener 监听器函数
     */
    const addEventListener = useCallback((eventType: EventType | string, listener: MessageHandler) => {
        if (typeof listener !== 'function') return;
        
        // 如果是消息事件，同时更新旧API的状态(兼容性考虑)
        if (eventType === EventType.MESSAGE) {
            addListener(listener);
            return;
        }
        
        // 确保事件类型的监听器集合存在
        if (!eventListeners.current[eventType]) {
            eventListeners.current[eventType] = new Set<MessageHandler>();
        }
        
        // 添加监听器
        eventListeners.current[eventType].add(listener);
    }, [addListener]);

    /**
     * 移除指定类型的事件监听器
     * @param eventType 事件类型
     * @param listener 要移除的监听器函数
     */
    const removeEventListener = useCallback((eventType: EventType | string, listener: MessageHandler) => {
        if (typeof listener !== 'function') return;
        
        // 如果是消息事件，同时更新旧API的状态(兼容性考虑)
        if (eventType === EventType.MESSAGE) {
            removeListener(listener);
            return;
        }
        
        // 确保事件类型的监听器集合存在
        if (eventListeners.current[eventType]) {
            eventListeners.current[eventType].delete(listener);
        }
    }, [removeListener]);

    /**
     * 事件处理器
     * 处理所有类型的事件
     * @param eventType 事件类型
     * @param e 事件对象
     */
    const handleEvent = useCallback((eventType: EventType | string, e: any) => {
        // 确保事件类型的监听器集合存在
        const listeners = eventListeners.current[eventType];
        if (listeners) {
            listeners.forEach(listener => listener(e));
        }
    }, []);

    /**
     * 发送消息(兼容旧API)
     * 在开发环境下直接发送，生产环境下验证同源
     * @param message 消息事件
     */
    const sendMessage = useCallback((message: MessageEvent) => {
        if (process.env.NODE_ENV === 'development') {
            messageListeners.forEach(listener => listener(message));
        } else {
            if (message.origin === window.origin) { //校验两个窗口域一样
                messageListeners.forEach(listener => listener(message));
            }
        }
    }, [messageListeners]);

    /**
     * 触发指定类型的事件
     * @param eventType 事件类型
     * @param data 事件数据
     */
    const dispatchEvent = useCallback((eventType: EventType | string, data: any) => {
        handleEvent(eventType, data);
    }, [handleEvent]);

    // 注册全局消息事件监听(兼容旧API)
    useEffect(() => {
        const messageHandler = (e: MessageEvent) => {
            handleEvent(EventType.MESSAGE, e);
        };
        
        window.addEventListener('message', messageHandler);
        return () => {
            window.removeEventListener('message', messageHandler);
        };
    }, [handleEvent]);

    // 动态注册所有支持的窗口事件
    useEffect(() => {
        // 事件处理器映射
        const handlers: Record<string, (e: Event) => void> = {};
        
        // 为每个枚举中的事件类型创建处理器并注册
        Object.values(EventType).forEach((eventType) => {
            // 跳过message事件，因为已经单独处理了
            if (eventType === EventType.MESSAGE) return;
            
            // 创建事件处理器
            handlers[eventType] = (e: Event) => {
                handleEvent(eventType, e);
            };
            
            // 注册事件监听
            window.addEventListener(eventType, handlers[eventType]);
        });
        
        // 清理函数
        return () => {
            Object.values(EventType).forEach((eventType) => {
                if (eventType === EventType.MESSAGE) return;
                window.removeEventListener(eventType, handlers[eventType]);
            });
        };
    }, [handleEvent]);

    // 提供事件上下文
    return (
        <EventContext.Provider 
            value={{ 
                addListener, 
                removeListener, 
                sendMessage, 
                addEventListener, 
                removeEventListener, 
                dispatchEvent 
            }}
        >
            {children}
        </EventContext.Provider>
    );
});

/**
 * 事件管理器Hook
 * 用于在组件中获取事件管理器功能
 * @returns 事件管理器方法
 * @throws 如果不在EventProvider内使用则抛出错误
 */
export const useEventManager = (): EventContextType => {
    const context = useContext(EventContext);
    if (!context) {
        throw new Error('useEventManager must be used within an EventProvider');
    }
    return context;
};