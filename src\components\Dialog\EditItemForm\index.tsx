import React, { useEffect } from 'react';
import { use<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { Stack, DialogActions, Button, DialogContent } from '@mui/material';
import { findSecondLevelNode, flatTree, findRootNode } from '@/util/tool';
import CustomInput from '@/components/CommonComponent/CustomInput';
import BMCDialog from '@/components/CommonComponent/BMCDialog';
import LoadingUtil from '@/util/LoadingUtil';
import Message from '@/util/Message';
import { useCloudbase } from '@/components/Context/CloudBaseContext/CloudbaseContext';
import { TreeNode } from '@/components/MainContainer';
import { DICTIONARY_ATTRIBUTE_OPTIONS_MAP, DICTIONARY_IDENTIFY_ENUM } from 'bmc-common-resource';
interface EditItemFormProps {
    onClose: () => void;
    options?: TreeNode[];
    selectedItem?: TreeNode;
    onSuccess?: () => void;  // 添加成功回调
    tenantId?: string;
}

interface FormData {
    tenant_id: string;
    company_changed: boolean;
    dictionary_group: any;
    identify: string | null;
    attribute: string | null;
    description: string;
    pid: string;
    type: string;
    icon_id: string;
    is_synced: boolean;
    rule_trigger_event: string;
    belong_person: any;
    sort_number: number;
    name: string;
    directory_category: string;
    status: string;
}

/**
 * 编辑
 * @param props 
 * @returns 
 */
const EditItemForm = (props: EditItemFormProps) => {

    const { selectedItem, options = [], tenantId } = props;

    const form = useForm<FormData>();

    const { control, handleSubmit } = form;

    const { cloudbaseApp } = useCloudbase();

    const flatOptions = flatTree(options)
    //当前节点的根节点
    const rootNode = selectedItem?.pid ? findRootNode(flatOptions, selectedItem.pid, flatOptions) : null;

    // 当前节点是否是第二级节点
    const isSecondLevelNode = !!options?.find((i => i.id === selectedItem?.pid))

    // 获取属性选项
    const tempAttributeOptions = selectedItem?.data?.identify ? DICTIONARY_ATTRIBUTE_OPTIONS_MAP[selectedItem.data.identify as DICTIONARY_IDENTIFY_ENUM] : [];

    // 已存在的属性选项
    const alreadyExistAttributeOptions = flatOptions.filter((item) => item.data?.identify === selectedItem?.data?.identify).map((item) => item.data?.attribute);

    // 可用的属性选项
    const attributeOptions = tempAttributeOptions?.filter((item) => {
        if (selectedItem?.data?.attribute === item.value) {
            return true
        }
        return !alreadyExistAttributeOptions.includes(item.value)
    })?.map((item) => ({ label: item.label + '(' + item.value + ')', value: item.value })) || [];

    // 状态选项
    const statusOptions = [
        { label: '启用', value: 'ENABLED' },
        { label: '停用', value: 'DISABLED' }
    ];

    // 字典项分类选项
    const typeOptions = [
        { label: '系统字典', value: 'SYSTEM_DIRECTORY' },
        { label: '业务字典', value: 'SERVICE_DIRECTORY' }
    ];

    const onSubmit = (data: FormData) => {
        // TODO: 处理表单提交
        const formData = {
            ...data,
            tenant_id: tenantId,
            pid: selectedItem?.pid,
            _id: selectedItem?.id,
            dictionary_group: {
                _id: rootNode?._id,
            },
        }
        LoadingUtil.load({ openInfo: true, messages: '保存中...' })
        cloudbaseApp && cloudbaseApp.callFunction({
            name: 'information-dictionary-crud',
            data: {
                action: 'update',
                data: formData
            }
        }).then((res) => {
            console.log(res)
            LoadingUtil.load({ openInfo: false })
            if (res?.result?.code === 0) {
                Message.success('保存成功')
                props.onSuccess?.()  // 调用成功回调
                props.onClose()      // 关闭弹窗
            } else {
                Message.error(res?.result?.message)
            }
        }).catch((err) => {
            Message.error(err.message)
            LoadingUtil.load({ openInfo: false })
        })
        console.log(formData)
    }

    //删除
    const handleDelete = () => {
        LoadingUtil.load({ openInfo: true, messages: '删除中...' })
        cloudbaseApp && cloudbaseApp.callFunction({
            name: 'information-dictionary-crud',
            data: {
                action: 'delete',
                data: {
                    id: selectedItem?.id
                }
            }
        }).then((res) => {
            console.log(res)
            LoadingUtil.load({ openInfo: false })
            if (res?.result?.code === 0) {
                Message.success('保存成功')
                props.onSuccess?.()  // 调用成功回调
                props.onClose()      // 关闭弹窗
            } else {
                Message.error(res?.result?.message)
            }
        }).catch((err) => {
            Message.error(err.message)
            LoadingUtil.load({ openInfo: false })
        })
    }

    useEffect(() => {
        form.reset({
            identify: selectedItem?.data?.identify || null,
            name: selectedItem?.label,
            status: selectedItem?.data?.status,
            description: selectedItem?.data?.description,
            directory_category: selectedItem?.data?.directory_category,
            sort_number: selectedItem?.data?.sort_number,
            dictionary_group: rootNode?.label,
            type: selectedItem?.data?.type,
            attribute: selectedItem?.data?.attribute || null,
        })
    }, [selectedItem])


    return <BMCDialog
        title={isSecondLevelNode ? "编辑字典项" : "编辑字典记录"}
        open={true}
        onClose={props?.onClose}
        onCloseClick={props?.onClose}
        height={600}
        width={750}
    >
        <DialogContent>
            <FormProvider {...form}>
                <Stack spacing={2}>
                    {/* 第一行：名称和序号 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="name"
                            control={control}
                            rules={{ required: '此项必填' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项名称"
                                    error={!!error}
                                    helperText={error?.message}
                                    required={true}
                                    value={value}
                                    onChange={onChange}
                                    placeholder="文本"
                                />
                            )}
                        />
                    </Stack>
                    {/* 第二行：字典项标识和字典项分类 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="identify"
                            control={control}
                            render={({ field: { value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项标识"
                                    type="input"
                                    value={value}
                                    readOnly={true}
                                    placeholder="请录入选项标识或选项值"
                                />
                            )}
                        />
                        {!isSecondLevelNode && <Controller
                            name="attribute"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="唯一编码"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={attributeOptions}
                                    placeholder="请选择"
                                />
                            )}
                        />}
                    </Stack>
                    {/* 第三行：父级ID和状态 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="type"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项分类"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={typeOptions}
                                    placeholder="请录入选项标识或选项值"
                                />
                            )}
                        />
                        <Controller
                            name="status"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项状态"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={statusOptions}
                                    placeholder="请录入选项标识或选项值"
                                />
                            )}
                        />
                    </Stack>
                    {/* 第四行：序号 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="sort_number"
                            control={control}
                            rules={{ required: '此项必填' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="序号"
                                    error={!!error}
                                    helperText={error?.message}
                                    required={true}
                                    type="input"
                                    inputType="number"
                                    value={value}
                                    onChange={onChange}
                                    placeholder="1"
                                />
                            )}
                        />
                        <Controller
                            name="dictionary_group"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项分组"
                                    type="input"
                                    value={value}
                                    readOnly={true} />
                            )}
                        />
                    </Stack>
                    {/* 第五行：归属人员和规则触发事件 */}
                    <Stack direction="row" sx={{ display: 'none' }} spacing={2}>
                        <Controller
                            name="rule_trigger_event"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="规则触发事件"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={[]}
                                    placeholder="请录入选项标识或选项值"
                                />
                            )}
                        />
                    </Stack>
                    {/* 描述 */}
                    <Controller
                        name="description"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                            <CustomInput
                                labelDirection="column"
                                label="字典项描述"
                                value={value}
                                onChange={onChange}
                                placeholder="文本"
                            />
                        )}
                    />
                </Stack>
            </FormProvider>
        </DialogContent>
        <DialogActions>
            <Button variant="outlined" color="primary" onClick={props.onClose}>取消</Button>
            {/* <Button variant="outlined" color="primary" onClick={handleDelete}>删除</Button> */}
            <Button variant="contained" color="primary" onClick={handleSubmit(onSubmit)}>保存</Button>
        </DialogActions>
    </BMCDialog>

};

export default EditItemForm;
