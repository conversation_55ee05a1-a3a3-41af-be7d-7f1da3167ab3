import React, { useEffect } from 'react';
import { use<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { Stack, DialogActions, Button, DialogContent } from '@mui/material';
import CustomInput from '@/components/CommonComponent/CustomInput';
import BMCDialog from '@/components/CommonComponent/BMCDialog';
import { flatTree, findRootNode } from '@/util/tool';
import LoadingUtil from '@/util/LoadingUtil';
import Message from '@/util/Message';
import { useCloudbase } from '@/components/Context/CloudBaseContext/CloudbaseContext';
import { DICTIONARY_ATTRIBUTE_OPTIONS_MAP, DICTIONARY_IDENTIFY_ENUM } from 'bmc-common-resource';
import { TreeNode } from '@/components/MainContainer';
interface NewAddItemChildLevelFormProps {
    onClose: () => void;
    options?: TreeNode[];
    selectedItem?: TreeNode;
    onSuccess?: () => void;
    tenantId?: string;
}

interface FormData {
    tenant_id: string;
    company_changed: boolean;
    dictionary_group: any;
    identify: string | null;
    description: string;
    pid: string;
    type: string;
    icon_id: string;
    is_synced: boolean;
    rule_trigger_event: string;
    belong_person: any;
    sort_number: number;
    name: string;
    attribute: string;
    directory_category: string;
    status: string;
}

const NewAddItemChildLevelForm = (props: NewAddItemChildLevelFormProps) => {
    const { options, selectedItem, tenantId } = props;
    const form = useForm<FormData>();
    const { control, handleSubmit } = form;
    const { cloudbaseApp } = useCloudbase();

    // 状态选项
    const statusOptions = [
        { label: '启用', value: 'ENABLED' },
        { label: '停用', value: 'DISABLED' }
    ];

    // 字典项分类选项
    const typeOptions = [
        { label: '系统字典', value: 'SYSTEM_DIRECTORY' },
        { label: '业务字典', value: 'SERVICE_DIRECTORY' }
    ];


    const flatOptions = flatTree(options)

    //当前节点的根节点
    const rootNode = selectedItem?.pid ? findRootNode(flatOptions, selectedItem.pid, flatOptions) : null;

    // 获取属性选项
    const tempAttributeOptions = selectedItem?.data?.identify ? DICTIONARY_ATTRIBUTE_OPTIONS_MAP[selectedItem.data.identify as DICTIONARY_IDENTIFY_ENUM] : [];

    // 已存在的属性选项
    const alreadyExistAttributeOptions = flatOptions.filter((item) => item.data?.identify === selectedItem?.data?.identify).map((item) => item.data?.attribute);

    // 可用的属性选项
    const attributeOptions = tempAttributeOptions?.filter((item) => !alreadyExistAttributeOptions.includes(item.value))?.map((item) => ({ label: item.label + '(' + item.value + ')', value: item.value }));

    const onSubmit = (data: FormData) => {
        console.log('Form data:', data);
        // TODO: 处理表单提交
        const formData = {
            ...data,
            attribute: data?.attribute,
            tenant_id: tenantId,
            pid: selectedItem?.id,
            dictionary_group: {
                _id: rootNode?._id,
            },
            directory_category: "DIRECTORY_RECORD",
        }
        // TODO: 处理表单提交
        LoadingUtil.load({ openInfo: true, messages: '保存中...' })
        cloudbaseApp && cloudbaseApp.callFunction({
            name: 'information-dictionary-crud',
            data: {
                action: 'create',
                data: formData
            }
        }).then((res) => {
            console.log(res)
            LoadingUtil.load({ openInfo: false })
            if (res?.result?.code === 0) {
                Message.success('保存成功')
                props.onSuccess?.()  // 调用成功回调
                props.onClose()      // 关闭弹窗
            } else {
                Message.error(res?.result?.message)
            }
        }).catch((err) => {
            Message.error(err.message)
            LoadingUtil.load({ openInfo: false })
        })
        return
    };

    useEffect(() => {
        form.reset({
            type: 'SYSTEM_DIRECTORY',
            status: 'ENABLED',
            sort_number: (selectedItem?.children?.length ?? 0) + 1,
            dictionary_group: rootNode?.label,
            identify: selectedItem?.data?.identify ? selectedItem?.data?.identify : null
        })
    }, []);

    return <BMCDialog
        title="新增字典记录"
        open={true}
        onClose={props.onClose}
        onCloseClick={props.onClose}
        height={600}
        width={750}
    >
        <DialogContent>
            <FormProvider {...form}>
                <Stack spacing={2}>
                    {/* 第一行：名称和序号 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="name"
                            control={control}
                            rules={{ required: '此项必填' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项名称"
                                    error={!!error}
                                    helperText={error?.message}
                                    required={true}
                                    value={value}
                                    onChange={onChange}
                                    placeholder="文本"
                                />
                            )}
                        />
                    </Stack>
                    {/* 第二行：字典项标识和唯一编码 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="identify"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项标识"
                                    type={"input"}
                                    value={value}
                                    readOnly={true}
                                    onChange={onChange}
                                    placeholder="请字典项标识"
                                />
                            )}
                        />
                        <Controller
                            name="attribute"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="唯一编码"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={attributeOptions}
                                    placeholder="请选择"
                                />
                            )}
                        />
                    </Stack>
                    {/* 第三行：字典项分类和状态 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="type"
                            control={control}
                            rules={{ required: '此项必填' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项分类"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={typeOptions}
                                    placeholder="请选择"
                                    error={!!error}
                                    helperText={error?.message}
                                />
                            )}
                        />
                        <Controller
                            name="status"
                            control={control}
                            rules={{ required: '此项必填' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项状态"
                                    type="select"
                                    value={value}
                                    required={true}
                                    onChange={onChange}
                                    options={statusOptions}
                                    placeholder="请选择"
                                    error={!!error}
                                    helperText={error?.message}
                                />
                            )}
                        />
                    </Stack>
                    {/* 第四行：序号 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="sort_number"
                            control={control}
                            rules={{ required: '此项必填' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="序号"
                                    error={!!error}
                                    helperText={error?.message}
                                    required={true}
                                    type="input"
                                    inputType="number"
                                    value={value}
                                    onChange={onChange}
                                    placeholder="1"
                                />
                            )}
                        />
                        <Controller
                            name="dictionary_group"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项分组"
                                    type="input"
                                    value={value}
                                    readOnly={true} />
                            )}
                        />
                    </Stack>
                    {/* 第五行：归属人员和规则触发事件 */}
                    <Stack direction="row" sx={{ display: 'none' }} spacing={2}>
                        <Controller
                            name="rule_trigger_event"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="规则触发事件"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={[]}
                                    placeholder="请录入选项标识或选项值"
                                />
                            )}
                        />
                    </Stack>
                    {/* 描述 */}
                    <Controller
                        name="description"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                            <CustomInput
                                labelDirection="column"
                                label="字典项描述"
                                value={value}
                                onChange={onChange}
                                placeholder="文本"
                            />
                        )}
                    />
                </Stack>
            </FormProvider>
        </DialogContent>
        <DialogActions>
            <Button variant="outlined" color="primary" onClick={props.onClose}>取消</Button>
            <Button variant="contained" color="primary" onClick={handleSubmit(onSubmit)}>保存</Button>
        </DialogActions>
    </BMCDialog>
};

export default NewAddItemChildLevelForm;
