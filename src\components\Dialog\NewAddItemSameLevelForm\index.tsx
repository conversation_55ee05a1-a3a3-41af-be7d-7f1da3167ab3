import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { Stack, DialogActions, Button, DialogContent } from '@mui/material';
import CustomInput from '@/components/CommonComponent/CustomInput';
import BMCDialog from '@/components/CommonComponent/BMCDialog';
import { findRootNode, flatTree } from '@/util/tool';
import LoadingUtil from '@/util/LoadingUtil';
import Message from '@/util/Message';
import { useCloudbase } from '@/components/Context/CloudBaseContext/CloudbaseContext';
import { DICTIONARY_ATTRIBUTE_OPTIONS_MAP, DICTIONARY_IDENTIFY_OPTIONS } from 'bmc-common-resource';
import { DICTIONARY_IDENTIFY_ENUM } from 'bmc-common-resource';
import { TreeNode } from '@/components/MainContainer';

interface NewAddItemSameLevelFormProps {
    onClose: () => void;
    selectedItem: TreeNode;
    tenantId?: string;
    onSuccess?: () => void;
    options?: TreeNode[];
}

interface FormData {
    tenant_id: string;
    company_changed: boolean;
    dictionary_group: any;
    identify: string | null;
    description: string;
    pid: string;
    type: string;
    icon_id: string;
    is_synced: boolean;
    rule_trigger_event: string;
    belong_person: any;
    sort_number: number;
    name: string;
    attribute: string | null;
    directory_category: string;
    status: string;
}


/**
 * 新增同级字典项 或新增同级字典记录
 * @param props 
 * @returns 
 */
const NewAddItemSameLevelForm: React.FC<NewAddItemSameLevelFormProps> = ({
    onClose,
    selectedItem,
    tenantId,
    onSuccess,
    options
}) => {
    const form = useForm<FormData>();

    // 扁平化选项
    const flatOptions = flatTree(options);

    // 当前节点的父节点
    const parentNode = flatOptions.find((item) => item.id === selectedItem?.pid)

    // 判断是否是第二级节点
    const isSecondLevelNode = !parentNode?.pid

    // 已存在的标识选项
    const alreadyIdentifyOptions = flatOptions.filter((i) => DICTIONARY_IDENTIFY_OPTIONS.map(i => i.value)?.includes(i.data?.identify as DICTIONARY_IDENTIFY_ENUM)).map((i) => i.data?.identify);

    // 可用的标识选项
    const identifyOptions = DICTIONARY_IDENTIFY_OPTIONS.filter((i) => !alreadyIdentifyOptions?.includes(i.value))?.map((i) => ({ label: i.label + '(' + i.value + ')', value: i.value }));

    // 获取属性选项
    const tempAttributeOptions = parentNode?.data?.identify ? DICTIONARY_ATTRIBUTE_OPTIONS_MAP[parentNode.data.identify as DICTIONARY_IDENTIFY_ENUM] : [];

    // 已存在的属性选项
    const alreadyExistAttributeOptions = flatOptions.filter((item) => item.data?.identify === parentNode?.data?.identify).map((item) => item.data?.attribute);

    // 可用的属性选项
    const attributeOptions = tempAttributeOptions?.filter((item) => !alreadyExistAttributeOptions.includes(item.value))?.map((item) => ({ label: item.label + '(' + item.value + ')', value: item.value }));

    // 当前节点的根节点
    const rootNode = parentNode?.pid ? findRootNode(flatOptions, parentNode.pid, flatOptions) : parentNode;

    const { control, handleSubmit } = form;

    const { cloudbaseApp } = useCloudbase();

    // 状态选项
    const statusOptions = [
        { label: '启用', value: 'ENABLED' },
        { label: '停用', value: 'DISABLED' }
    ];

    // 字典项分类选项
    const typeOptions = [
        { label: '系统字典', value: 'SYSTEM_DIRECTORY' },
        { label: '业务字典', value: 'SERVICE_DIRECTORY' }
    ];

    const onSubmit = (data: FormData) => {
        LoadingUtil.load({ openInfo: true, messages: '保存中...' })
        cloudbaseApp?.callFunction({
            name: 'information-dictionary-crud',
            data: {
                action: 'create',
                data: {
                    ...data,
                    tenant_id: tenantId,
                    pid: isSecondLevelNode ? undefined : parentNode?.id,
                    directory_category: isSecondLevelNode ? 'DIRECTORY_ITEM' : 'DIRECTORY_RECORD',
                    dictionary_group: {
                        _id: rootNode?._id,
                        name: rootNode?.label
                    },
                    //如果父节点是字典项，则需要设置属性
                    attribute: parentNode?.data?.identify ? data.attribute : isSecondLevelNode ? data?.identify : null,
                }
            }
        }).then((res: any) => {
            LoadingUtil.load({ openInfo: false })
            if (res?.result?.code === 0) {
                Message.success('保存成功')
                onSuccess?.()  // 调用成功回调
                onClose()      // 关闭弹窗
            } else {
                LoadingUtil.load({ openInfo: false })
                Message.error(res?.result?.message)
            }
        }).catch((err: any) => {
            LoadingUtil.load({ openInfo: false })
            Message.error(err.message)
        })
    }

    useEffect(() => {
        form.reset({
            type: 'SYSTEM_DIRECTORY',
            status: 'ENABLED',
            sort_number: (parentNode?.children?.length ?? 0) + 1,
            dictionary_group: rootNode?.label,
            identify: parentNode?.data?.identify ? parentNode?.data?.identify : null,
        })
    }, []);

    return <BMCDialog
        title={parentNode?.data?.identify ? "新增字典记录" : "新增字典项"}
        open={true}
        onClose={onClose}
        onCloseClick={onClose}
        height={600}
        width={750}
    >
        <DialogContent>
            <FormProvider {...form}>
                <Stack spacing={2}>
                    {/* 第一行：名称和序号 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="name"
                            control={control}
                            rules={{ required: '此项必填' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项名称"
                                    error={!!error}
                                    helperText={error?.message}
                                    required={true}
                                    value={value}
                                    onChange={onChange}
                                    placeholder="文本"
                                />
                            )}
                        />
                    </Stack>
                    {/* 第二行：字典项标识和唯一编码 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="identify"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项标识"
                                    type={parentNode?.data?.identify ? "input" : "select"}
                                    value={value}
                                    readOnly={!!parentNode?.data?.identify}
                                    onChange={onChange}
                                    options={identifyOptions}
                                    placeholder="请字典项标识"
                                />
                            )}
                        />
                        {parentNode?.data?.identify && <Controller
                            name="attribute"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="唯一编码"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={attributeOptions}
                                    placeholder="请选择"
                                />
                            )}
                        />}
                    </Stack>
                    {/* 第四行：字典项分类和状态 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="type"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项分类"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={typeOptions}
                                    placeholder="请录入选项标识或选项值"
                                />
                            )}
                        />
                        <Controller
                            name="status"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项状态"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={statusOptions}
                                    placeholder="请录入选项标识或选项值"
                                />
                            )}
                        />
                    </Stack>
                    {/* 第五行：序号和分组 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="sort_number"
                            control={control}
                            rules={{ required: '此项必填' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="序号"
                                    error={!!error}
                                    helperText={error?.message}
                                    required={true}
                                    type="input"
                                    inputType="number"
                                    value={value}
                                    onChange={onChange}
                                    placeholder="1"
                                />
                            )}
                        />
                        <Controller
                            name="dictionary_group"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项分组"
                                    type="input"
                                    value={value}
                                    readOnly={true}
                                />
                            )}
                        />
                    </Stack>
                    {/* 第六行：规则触发事件 */}
                    <Stack direction="row" sx={{ display: 'none' }} spacing={2}>
                        <Controller
                            name="rule_trigger_event"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="规则触发事件"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={[]}
                                    placeholder="请录入选项标识或选项值"
                                />
                            )}
                        />
                    </Stack>
                    {/* 描述 */}
                    <Controller
                        name="description"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                            <CustomInput
                                labelDirection="column"
                                label="字典项描述"
                                value={value}
                                onChange={onChange}
                                placeholder="文本"
                            />
                        )}
                    />
                </Stack>
            </FormProvider>
        </DialogContent>
        <DialogActions>
            <Button variant="outlined" color="primary" onClick={onClose}>取消</Button>
            <Button variant="contained" color="primary" onClick={handleSubmit(onSubmit)}>保存</Button>
        </DialogActions>
    </BMCDialog>
};

export default NewAddItemSameLevelForm;
