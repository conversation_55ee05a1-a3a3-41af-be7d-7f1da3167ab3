import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON>, <PERSON><PERSON><PERSON>ider, Controller } from 'react-hook-form';
import { Stack, Typography, DialogActions, Button, DialogContent, FormControlLabel, Switch } from '@mui/material';
import CustomInput from '@/components/CommonComponent/CustomInput';
import BMCDialog from '@/components/CommonComponent/BMCDialog';
import { useCloudbase } from '@/components/Context/CloudBaseContext/CloudbaseContext';
import Message from '@/util/Message';
import LoadingUtil from '@/util/LoadingUtil';
import { DICTIONARY_IDENTIFY_OPTIONS, DICTIONARY_IDENTIFY_ENUM } from 'bmc-common-resource';

// 扩展标识选项 - 添加新的标识类型
const EXTENDED_IDENTIFY_OPTIONS = [
    ...DICTIONARY_IDENTIFY_OPTIONS,
    // 在这里添加新的标识选项
    { label: '产品', value: 'PRODUCT' },
    { label: '订单', value: 'ORDER' },
    { label: '客户', value: 'CUSTOMER' },
    { label: '供应商', value: 'SUPPLIER' },
    { label: '项目', value: 'PROJECT' },
    // 可以继续添加更多...
];
import { flatTree } from '@/util/tool';
import { TreeNode } from '@/components/MainContainer';
interface NewAddItemFormProps {
    onClose: () => void;
    options?: TreeNode[];
    tenantId?: string;
    onSuccess?: () => void;  // 添加成功回调
}

interface FormData {
    tenant_id: string;
    company_changed: boolean;
    dictionary_group: any;
    identify: string;
    description: string;
    pid: string;
    type: string;
    icon_id: string;
    is_synced: boolean;
    rule_trigger_event: string;
    belong_person: any;
    sort_number: number;
    name: string;
    attribute: string;
    directory_category: string;
    status: string;
}

/**
 * 新增字典项
 * @param props 
 * @returns 
 */
const NewAddItemForm = (props: NewAddItemFormProps) => {

    const { options, tenantId } = props;

    const form = useForm<FormData>();

    const { cloudbaseApp } = useCloudbase()

    const { control, handleSubmit } = form;

    const [groupOptions, setGroupOptions] = useState<any[]>([]);

    const flatOptions = flatTree(options);

    // 状态选项
    const statusOptions = [
        { label: '启用', value: 'ENABLED' },
        { label: '停用', value: 'DISABLED' }
    ];

    // 字典项分类选项
    const typeOptions = [
        { label: '系统字典', value: 'SYSTEM_DIRECTORY' },
        { label: '业务字典', value: 'SERVICE_DIRECTORY' }
    ];



    const alreadyIdentifyOptions = flatOptions.filter((i) => EXTENDED_IDENTIFY_OPTIONS.map(i => i.value)?.includes(i.data?.identify as DICTIONARY_IDENTIFY_ENUM)).map((i) => i.data?.identify);

    const identifyOptions = EXTENDED_IDENTIFY_OPTIONS.filter((i) => !alreadyIdentifyOptions?.includes(i.value))?.map((i) => ({ label: i.label + '(' + i.value + ')', value: i.value }));


    const onSubmit = (data: FormData) => {
        LoadingUtil.load({ openInfo: true, messages: '保存中...' })
        const targetGroup = groupOptions.find((i: any) => i.value === data.dictionary_group)
        cloudbaseApp && cloudbaseApp.callFunction({
            name: 'information-dictionary-crud',
            data: {
                action: 'create',
                data: {
                    ...data,
                    dictionary_group: {
                        _id: targetGroup?.value,
                        name: targetGroup?.label || data.dictionary_group
                    },
                    tenant_id: tenantId,
                    attribute: data.identify,
                }
            }
        }).then((res) => {
            console.log(res)
            LoadingUtil.load({ openInfo: false })
            Message.success('保存成功')
            if (res?.result?.code === 0) {
                props.onSuccess?.()  // 调用成功回调
                props.onClose()      // 关闭弹窗
            }
        }).catch((err) => {
            Message.error(err.message)
            LoadingUtil.load({ openInfo: false })
        })
    };


    useEffect(() => {
        form.reset({
            type: 'SYSTEM_DIRECTORY',
            status: 'ENABLED',
            sort_number: 1,
            directory_category: 'DIRECTORY_ITEM'
        })
    }, [])

    useEffect(() => {
        if (!cloudbaseApp) return;
        try {
            cloudbaseApp && cloudbaseApp.models.dictionary_group.list({
                filter: {
                    where: {
                        tenant_id: {
                            $eq: tenantId
                        }
                    }
                },
                pageNumber: 1,
                pageSize: 200,
            }).then((res) => {
                setGroupOptions(res.data.records?.map((i: any) => ({ label: i.name, value: i._id })))
            })
        } catch (error) {
            console.error(error)
        }
    }, [cloudbaseApp])

    const handleGroupInputChange = (e: object, val: string, reason: string, onChange: any) => {
        if (reason === 'input') {
            onChange(val)
        }
    }

    return <BMCDialog
        title="新增字典项"
        open={true}
        onClose={props.onClose}
        onCloseClick={props.onClose}
        height={600}
        width={750}
    >
        <DialogContent>
            <FormProvider {...form}>
                <Stack spacing={2}>
                    {/* 第一行：名称和序号 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="name"
                            control={control}
                            rules={{ required: '此项必填' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项名称"
                                    error={!!error}
                                    helperText={error?.message}
                                    required={true}
                                    value={value}
                                    onChange={onChange}
                                    placeholder="文本"
                                />
                            )}
                        />

                    </Stack>
                    {/* 第二行：字典项标识 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="identify"
                            control={control}
                            rules={{ required: '此项必填' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项标识"
                                    type="select"
                                    required={true}
                                    error={!!error}
                                    helperText={error?.message}
                                    value={value}
                                    onChange={onChange}
                                    options={identifyOptions}
                                    placeholder="请选择字典标识"
                                />
                            )}
                        />

                    </Stack>
                    {/* 第三行：父级ID和状态 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="type"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项分类"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={typeOptions}
                                    placeholder="请录入选项标识或选项值"
                                />
                            )}
                        />
                        <Controller
                            name="status"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项状态"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={statusOptions}
                                    placeholder="请录入选项标识或选项值"
                                />
                            )}
                        />
                    </Stack>
                    {/* 第四行：序号 */}
                    <Stack direction="row" spacing={2}>
                        <Controller
                            name="sort_number"
                            control={control}
                            rules={{ required: '此项必填' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="序号"
                                    error={!!error}
                                    helperText={error?.message}
                                    required={true}
                                    type="input"
                                    inputType="number"
                                    value={value}
                                    onChange={onChange}
                                    placeholder="1"
                                />
                            )}
                        />
                        <Controller
                            name="dictionary_group"
                            control={control}
                            rules={{ required: '此项必填' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="字典项分组"
                                    type="select"
                                    error={!!error}
                                    helperText={error?.message}
                                    required={true}
                                    value={value}
                                    options={groupOptions}
                                    freeSolo={true}
                                    onChange={onChange}
                                    inputValue={groupOptions?.find((i: any) => i?.value === value)?.label ?? value ?? ''}
                                    onInputChange={(e: object, val: string, reason: string) => handleGroupInputChange(e, val, reason, onChange)}
                                    placeholder="请选择或输入字典项分组"
                                />
                            )}
                        />
                    </Stack>
                    {/* 第五行：归属人员和规则触发事件 */}
                    <Stack direction="row" sx={{ display: 'none' }} spacing={2}>
                        <Controller
                            name="rule_trigger_event"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="规则触发事件"
                                    type="select"
                                    value={value}
                                    onChange={onChange}
                                    options={[]}
                                    placeholder="请录入选项标识或选项值"
                                />
                            )}
                        />
                    </Stack>
                    {/* 描述 */}
                    <Controller
                        name="description"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                            <CustomInput
                                labelDirection="column"
                                label="字典项描述"
                                value={value}
                                onChange={onChange}
                                placeholder="文本"
                            />
                        )}
                    />
                </Stack>
            </FormProvider>
        </DialogContent>
        <DialogActions>
            <Button variant="outlined" color="primary" onClick={props.onClose}>取消</Button>
            <Button variant="contained" color="primary" onClick={handleSubmit(onSubmit)}>保存</Button>
        </DialogActions>
    </BMCDialog>
};

export default NewAddItemForm;
