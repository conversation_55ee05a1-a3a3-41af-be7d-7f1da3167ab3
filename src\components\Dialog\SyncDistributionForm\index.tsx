import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { Stack, DialogActions, Button, DialogContent } from '@mui/material';
import CustomInput from '@/components/CommonComponent/CustomInput';
import BMCDialog from '@/components/CommonComponent/BMCDialog';
import { useCloudbase } from '@/components/Context/CloudBaseContext/CloudbaseContext';
import Message from '@/util/Message';
import LoadingUtil from '@/util/LoadingUtil';
import { getTenantList, syncDictionaryList } from '@/services';

interface SyncDistributionFormProps {
    onClose: () => void;
    tenantId?: string;
    onSuccess?: () => void;
}

interface FormData {
    target_tenants: string[];
    sync_mode: string;
    dictionary_groups: string[];
    sync_type: string;
    description: string;
    auto_sync: boolean;
    conflict_resolution: string;
}
const groupOptions = [
    { label: '分组1', value: 'group1' },
    { label: '分组2', value: 'group2' },
    { label: '分组3', value: 'group3' }
];

// 同步模式选项
// const syncModeOptions = [
//     { label: '全量同步', value: 'FULL_SYNC' },
//     { label: '增量同步', value: 'INCREMENTAL_SYNC' },
//     { label: '选择性同步', value: 'SELECTIVE_SYNC' }
// ];

// 同步类型选项
// const syncTypeOptions = [
//     { label: '立即同步', value: 'IMMEDIATE' },
//     { label: '定时同步', value: 'SCHEDULED' },
//     { label: '手动同步', value: 'MANUAL' }
// ];

// 冲突解决策略选项
// const conflictResolutionOptions = [
//     { label: '源优先', value: 'SOURCE_PRIORITY' },
//     { label: '目标优先', value: 'TARGET_PRIORITY' },
//     { label: '手动处理', value: 'MANUAL_RESOLVE' }
// ];

/**
 * 同步分发表单
 * @param props 
 * @returns 
 */
const SyncDistributionForm = (props: SyncDistributionFormProps) => {

    const { tenantId } = props;

    const form = useForm<FormData>();

    const { cloudbaseApp } = useCloudbase()

    const { control, handleSubmit } = form;

    const [tenantOptions, setTenantOptions] = useState<any[]>([]);

    useEffect(() => {
        getTenantList().then((res) => {
            console.log('租户列表数据:', res);
            if (res?.code === 200) {
                // 根据实际数据结构映射
                setTenantOptions(res.data.map((i: any) => ({
                    label: i.corpName,  // 使用公司名称作为显示标签
                    value: i.corpCode   // 使用公司代码作为值
                }))?.filter((i: any) => i.value !== tenantId))
            }
        }).catch((error) => {
            console.error('获取租户列表失败:', error);
            // 如果请求失败，使用默认数据
            setTenantOptions([]);
        })
    }, [])





    const onSubmit = (data: FormData) => {
        LoadingUtil.load({ openInfo: true, messages: '同步分发中...' })
        console.log('表单数据:', data);
        const formData: any = {
            "source_tenant_id": "master",
            "target_tenants": data?.target_tenants
        }

        syncDictionaryList(formData).then((res) => {
            console.log(res)
            LoadingUtil.load({ openInfo: false })
            if (res?.code === 200) {
                Message.success('同步分发成功')
                props.onSuccess?.()  // 调用成功回调
                props.onClose()      // 关闭弹窗
            } else {
                Message.error(res?.message)
            }
        }).catch((err) => {
            Message.error(err.message)
            LoadingUtil.load({ openInfo: false })
        })

    };

    useEffect(() => {
        form.reset({
            sync_mode: 'FULL_SYNC',
            sync_type: 'IMMEDIATE',
            conflict_resolution: 'SOURCE_PRIORITY',
            auto_sync: false,
            target_tenants: [],
            dictionary_groups: []
        })
    }, [form])


    return <BMCDialog
        title="同步分发"
        open={true}
        onClose={props.onClose}
        onCloseClick={props.onClose}
        height={650}
        width={800}
    >
        <DialogContent>
            <FormProvider {...form}>
                <Stack spacing={2}>
                    {/* 第一行：目标租户 */}
                    <Controller
                        name="target_tenants"
                        control={control}
                        rules={{ required: '选择目标租户' }}
                        render={({ field: { onChange, value }, fieldState: { error } }) => (
                            <CustomInput
                                labelDirection="column"
                                label="目标租户"
                                type="select"
                                multiple={true}
                                error={!!error}
                                helperText={error?.message}
                                required={true}
                                value={value}
                                onChange={onChange}
                                options={tenantOptions}
                                placeholder="选择要同步的目标租户"
                            />
                        )}
                    />

                    {/* 第二行：同步模式和同步类型 */}
                    {/* <Stack direction="row" spacing={2}>
                        <Controller
                            name="sync_mode"
                            control={control}
                            rules={{ required: '选择同步模式' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="同步模式"
                                    type="select"
                                    error={!!error}
                                    helperText={error?.message}
                                    required={true}
                                    value={value}
                                    onChange={onChange}
                                    options={syncModeOptions}
                                    placeholder="选择同步模式"
                                />
                            )}
                        />
                        <Controller
                            name="sync_type"
                            control={control}
                            rules={{ required: '选择同步类型' }}
                            render={({ field: { onChange, value }, fieldState: { error } }) => (
                                <CustomInput
                                    labelDirection="column"
                                    label="同步类型"
                                    type="select"
                                    error={!!error}
                                    helperText={error?.message}
                                    required={true}
                                    value={value}
                                    onChange={onChange}
                                    options={syncTypeOptions}
                                    placeholder="选择同步类型"
                                />
                            )}
                        />
                    </Stack> */}

                    {/* 第三行：字典分组选择 */}
                    {/* <Controller
                        name="dictionary_groups"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                            <CustomInput
                                labelDirection="column"
                                label="字典分组"
                                type="select"
                                multiple={true}
                                value={value}
                                onChange={onChange}
                                options={groupOptions}
                                placeholder="选择要同步的字典分组（不选择则同步全部）"
                            />
                        )} 
                    /> */}

                    {/* 第四行：冲突解决策略 */}
                    {/* <Controller
                        name="conflict_resolution"
                        control={control}
                        rules={{ required: '选择冲突解决策略' }}
                        render={({ field: { onChange, value }, fieldState: { error } }) => (
                            <CustomInput
                                labelDirection="column"
                                label="冲突解决策略"
                                type="select"
                                error={!!error}
                                helperText={error?.message}
                                required={true}
                                value={value}
                                onChange={onChange}
                                options={conflictResolutionOptions}
                                placeholder="选择冲突解决策略"
                            />
                        )}
                    /> */}

                    {/* 第五行：自动同步开关 */}
                    {/* <Stack direction="row" alignItems="center" spacing={2}>
                        <Typography variant="body2" sx={{ minWidth: '80px' }}>自动同步:</Typography>
                        <Controller
                            name="auto_sync"
                            control={control}
                            render={({ field: { onChange, value } }) => (
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={value}
                                            onChange={(e) => onChange(e.target.checked)}
                                        />
                                    }
                                    label={value ? "开启" : "关闭"}
                                />
                            )}
                        />
                    </Stack> */}

                    {/* 描述 */}
                    {/* <Controller
                        name="description"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                            <CustomInput
                                labelDirection="column"
                                label="同步描述"
                                value={value}
                                onChange={onChange}
                                placeholder="输入同步分发的描述信息"
                            />
                        )}
                    /> */}
                </Stack>
            </FormProvider>
        </DialogContent>
        <DialogActions>
            <Button variant="outlined" color="primary" onClick={props.onClose}>取消</Button>
            <Button variant="contained" color="primary" onClick={handleSubmit(onSubmit)}>开始同步</Button>
        </DialogActions>
    </BMCDialog>
};

export default SyncDistributionForm;
