/**
 * 字典管理左侧树形结构组件
 * 提供树形展示、节点选择、节点操作等功能
 */

import SimpleTree from '@/components/CommonComponent/SimpleTree';
import { Stack, Typography, Button, IconButton, Tooltip, OutlinedInput, InputAdornment } from '@mui/material';
import { useState, useRef, useMemo, useEffect, useCallback } from 'react';
import { cloneDeep, debounce } from 'lodash';
import EditItemForm from '@/components/Dialog/EditItemForm';
import NewAddItemChildLevelForm from '@/components/Dialog/NewAddItemChildLevelForm';
import NewAddItemSameLevelForm from '@/components/Dialog/NewAddItemSameLevelForm';
import folderIcon from '@/assets/img/used-normal-folder.png';
import leftFolderIcon from '@/assets/img/leaf-simple-gray.png';
import { Search, Clear } from '@mui/icons-material';
import './index.css';

// Add type declarations for SVG elements
declare global {
    namespace JSX {
        interface IntrinsicElements {
            svg: React.SVGProps<SVGSVGElement>;
            use: React.SVGProps<SVGUseElement>;
            img: React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>;
        }
    }
}

/**
 * 左侧树组件属性接口
 */
interface LeftTreeProps {
    options: any[]                  // 树形数据选项
    checkAble?: boolean            // 是否可多选
    checkStrictly?: boolean        // 是否严格模式
    onRefresh?: () => void;        // 刷新回调
    tenantId?: string              // 租户ID
    onSelect?: (node: any) => void; // 节点选择回调
}

/**
 * 左侧树组件
 */
const LeftTree = (props: LeftTreeProps) => {
    const { checkAble = false, checkStrictly = true, options, onRefresh, tenantId, onSelect } = props;

    // 状态定义
    const [value, setValue] = useState<any>([]);           // 当前选中的节点值
    const [selectedItem, setSelectedItem] = useState<any>(); // 当前选中的节点
    const [editOpen, setEditOpen] = useState(false);       // 编辑对话框显示状态
    const [addChildOpen, setAddChildOpen] = useState(false); // 添加子节点对话框显示状态
    const [addSameLevelOpen, setAddSameLevelOpen] = useState(false); // 添加同级节点对话框显示状态
    const [searchValue, setSearchValue] = useState('');    // 搜索输入值
    const [filteredOptions, setFilteredOptions] = useState<any[]>([]); // 过滤后的选项

    // 创建防抖的搜索函数
    const debouncedSearch = useMemo(
        () => debounce((value: string) => {
            setFilteredOptions(filterTree(cloneDeep(options), value));
        }, 10),
        [options]
    );
    
    // 组件卸载时取消未执行的防抖函数
    useEffect(() => {
        return () => {
            debouncedSearch.cancel();
        };
    }, [debouncedSearch]);

    // 只在第一次加载数据时选中第一个根节点
    useEffect(() => {
        if (options?.length > 0 && !selectedItem) {
            const firstRootNode = options[0];
            setValue([firstRootNode.id]);
            setSelectedItem(firstRootNode);
            onSelect?.(firstRootNode);
        }
    }, [options]);

    // 当options变化且已有选中节点时，尝试保持选中状态
    useEffect(() => {
        if (options?.length > 0 && selectedItem) {
            // 在options中查找当前选中节点的对应节点
            const findNode = (nodes: any[]): any => {
                for (const node of nodes) {
                    if (node.id === selectedItem.id) {
                        return node;
                    }
                    if (node.children) {
                        const found = findNode(node.children);
                        if (found) return found;
                    }
                }
                return null;
            };

            const foundNode = findNode(options);
            if (foundNode) {
                setValue([foundNode.id]);
                setSelectedItem(foundNode);
                onSelect?.(foundNode);
            }
        }
    }, [options]);

    // 优化节点操作函数
    const operate = useCallback((e: React.MouseEvent<HTMLButtonElement, MouseEvent>, item: any, type: 'sameLevel' | 'childLevel' | 'edit') => {
        if (type === 'edit') {
            setEditOpen(true)
        } else if (type === 'sameLevel') {
            setAddSameLevelOpen(true)
        } else if (type === 'childLevel') {
            setAddChildOpen(true)
        }
    }, []);

    // 优化节点选择函数
    const handleOnchange = useCallback((checked: string[], node: any) => {
        if (node?.id) {
            setValue(checked)
            setSelectedItem(node)
            onSelect?.(node)
        }
    }, [onSelect]);

    // 优化搜索处理函数
    const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setSearchValue(value);
        debouncedSearch(value);
    }, [debouncedSearch]);

    // 优化清除搜索函数
    const handleClearSearch = useCallback(() => {
        setSearchValue('');
        setFilteredOptions([]);
        debouncedSearch.cancel();
    }, [debouncedSearch]);

    // 搜索过滤函数
    const filterTree = useCallback((data: any[], keyword: string) => {
        if (!keyword) return data;

        const results: any[] = [];
        const search = (nodes: any[]): any[] => {
            const nodeResults: any[] = [];
            for (const node of nodes) {
                const children = node.children ? search(node.children) : [];
                if (node.label.toLowerCase().includes(keyword.toLowerCase()) || children.length > 0) {
                    nodeResults.push({
                        ...node,
                        children: children.length > 0 ? children : node.children
                    });
                }
            }
            return nodeResults;
        };
        return search(data);
    }, []);

    // 优化树节点渲染函数
    const renderTreeNode = useCallback((item: any, index: number) => {
        return (
            <Stack key={index + (value?.[0] ?? '')}
                id={`dictionaryTree-title`}
                sx={{
                    width: "100%",
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                }}>
                <Typography className={'custom-title'} sx={{ whiteSpace: "pre-wrap", mr: 1, fontWeight: 'bold' }}>
                    {item?.label}
                </Typography>
                {!!item?.pid && item?.pid !== '' && (
                    <Stack
                        id={`dictionaryTree-title-button`}
                        sx={{
                            flexDirection: "row",
                            gap: 1,
                            mr: 1,
                            button: {
                                p: 0.3,
                                minWidth: 0,
                                textAlign: 'center'
                            },
                            svg: {
                                fontSize: 20,
                                height: 20,
                                width: 20,
                                color: '#2b6bff',
                                fill: '#2b6bff'
                            },
                            display: 'flex',
                            visibility: selectedItem?.id === item?.id ? 'visible' : 'hidden',
                        }}>
                        <Tooltip title="编辑">
                            <IconButton size="small" onClick={(e) => operate(e, item, "edit")}>
                                <span className="iconfont icon-Edit" style={{ fontSize: 20, color: '#2b6bff' }} />
                            </IconButton>
                        </Tooltip>
                        <Tooltip title="新增同级">
                            <IconButton size="small" onClick={(e) => operate(e, item, "sameLevel")}>
                                <span className="iconfont icon-ADD_ITEM" style={{ fontSize: 20, color: '#2b6bff' }} />
                            </IconButton>
                        </Tooltip>
                        <Tooltip title="新增子级">
                            <IconButton size="small" onClick={(e) => operate(e, item, "childLevel")}>
                                <span className="iconfont icon-ADD_SUBSET" style={{ fontSize: 20, color: '#2b6bff' }} />
                            </IconButton>
                        </Tooltip>
                    </Stack>
                )}
            </Stack>
        );
    }, [value, selectedItem, operate]);

    // 优化树节点图标渲染函数
    const renderTreeNodeIcon = useCallback((item: any) => {
        return (
            <Stack sx={{ width: !!item.children?.length ? 20 : 16, height: !!item.children?.length ? 20 : 16, mr: 1 }}>
                <img src={!!item.children?.length ? folderIcon : leftFolderIcon} alt="folder icon" />
            </Stack>
        );
    }, []);

    /**
     * 处理树节点选项，添加图标和操作按钮
     * @param data 树形数据
     * @returns 处理后的树形数据
     */
    const handleFlatOption = useCallback((data: any) => {
        data?.forEach((item: any, index: number) => {
            // 构建节点标题
            item.title = renderTreeNode(item, index);
            // 设置节点图标
            item.icon = renderTreeNodeIcon(item);
            // 递归处理子节点
            if (item.children) {
                handleFlatOption(item.children);
            }
        });
        return data;
    }, [renderTreeNode, renderTreeNodeIcon]);

    // 处理后的树形选项
    const processedOptions = useMemo(() => {
        const dataToProcess = searchValue ? filteredOptions : options;
        return handleFlatOption(cloneDeep(dataToProcess));
    }, [options, filteredOptions, handleFlatOption]);

    const treeRef = useRef<any>(null);

    /**
     * 获取所有展开节点的ID
     * @param data 树形数据
     * @returns 展开节点ID数组
     */
    const handleFlatExpandOption = (data: any[] = []) => {
        const flatOption: string[] = [];
        data?.forEach((item) => {
            flatOption.push(item.id);
            if (item.children) {
                handleFlatOption(item.children);
            }
        });
        return flatOption;
    };

    // 树节点样式
    const treeNodeStyle = {
        '.rc-tree-checkbox.rc-tree-checkbox-disabled': {
            width: 0,
            display: 'none!important',
        },
    };

    return <Stack id="dictionaryTree-container" sx={{
        ...treeNodeStyle,
        backgroundColor: '#ffffff',
        flex: 1,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        '& #simpleTree-container': {
            height: '100%',
            flex: 1,
            overflow: 'auto',
            minHeight: 0,    
            '& .rc-tree': {
                overflow: 'auto',
                height: '100%',
                '& .rc-tree-node-content-wrapper': {
                    width: '100%',
                    minWidth: 0,
                    '& .custom-title': {
                        whiteSpace: 'normal',
                        wordBreak: 'break-all',
                        flex: 1,
                        minWidth: 0
                    }
                }
            }
        }
    }}>
        {/* 搜索输入框 */}
        <Stack sx={{ p: 1, pb: 0.5 }}>
            <OutlinedInput
                size="small"
                value={searchValue}
                placeholder="搜索..."
                onChange={handleSearchChange}
                sx={{
                    '& .MuiOutlinedInput-input': {
                        py: 0.5
                    }
                }}
                startAdornment={
                    <InputAdornment position="start">
                        <Search sx={{ fontSize: '1.1em', color: 'text.secondary' }} />
                    </InputAdornment>
                }
                endAdornment={
                    searchValue && (
                        <InputAdornment position="end">
                            <Clear
                                sx={{ fontSize: '1.1em', cursor: 'pointer', color: 'text.secondary' }}
                                onClick={handleClearSearch}
                            />
                        </InputAdornment>
                    )
                }
            />
        </Stack>
        {/* 树形组件 */}
        <Stack sx={{ flex: 1, overflow: 'auto', minHeight: 0, height: '100%' }}>
            <SimpleTree
                expandAllValue={handleFlatExpandOption(options ?? []) ?? []}
                checkStrictly={checkStrictly}
                defaultExpandAll={true}
                checkAble={checkAble}
                defaultExpandParent={true}
                options={processedOptions}
                onChange={(val, node) => {
                    !checkAble && handleOnchange([val], node);
                }}
                value={value?.[0]}
                showIcon={true}
                showInput={false}
                className={'dictionaryTree'}
                ref={treeRef}
                fieldNames={{
                    key: 'id',
                    title: 'title',
                }}
            />
        </Stack>
        {/* 编辑对话框 */}
        {editOpen && <EditItemForm tenantId={tenantId} selectedItem={selectedItem} options={options} onClose={() => setEditOpen(false)} onSuccess={onRefresh} />}
        {/* 添加子节点对话框 */}
        {addChildOpen && <NewAddItemChildLevelForm tenantId={tenantId} selectedItem={selectedItem} options={options} onClose={() => setAddChildOpen(false)} onSuccess={onRefresh} />}
        {/* 添加同级节点对话框 */}
        {addSameLevelOpen && <NewAddItemSameLevelForm tenantId={tenantId} selectedItem={selectedItem} options={options} onClose={() => setAddSameLevelOpen(false)} onSuccess={onRefresh} />}
    </Stack>
}

export default LeftTree;


