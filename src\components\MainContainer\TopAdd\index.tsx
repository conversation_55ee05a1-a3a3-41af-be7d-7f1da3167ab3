import React, { useState } from 'react';
import { Stack, Typography, Button, Box } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import SyncIcon from '@mui/icons-material/Sync';
import NewAddMainForm from '@/components/Dialog/NewAddMainForm';
import SyncDistributionForm from '@/components/Dialog/SyncDistributionForm';


interface TopAddProps {
    options?: any[];
    tenantId?: string;
    onSuccess?: () => void;
}

const TopAdd = (props: TopAddProps) => {

    const { options, tenantId, onSuccess } = props;

    const [open, setOpen] = useState(false);
    const [syncOpen, setSyncOpen] = useState(false);

    return <Stack sx={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        height: '48px',
        alignItems: 'center',
        mb: 1
    }}>
        <Typography sx={{ fontSize: '14px', fontWeight: 'bold' }}>信息字典管理</Typography>

        <Stack direction={'row'} spacing={2}>
            <Button variant="contained" color="primary" sx={{ whiteSpace: 'nowrap' }} startIcon={<AddIcon />} onClick={() => setOpen(true)}>新增字典</Button>
            {tenantId === 'master' && <Button variant="contained" color="primary" sx={{ whiteSpace: 'nowrap' }} startIcon={<SyncIcon />} onClick={() => setSyncOpen(true)}>同步分发</Button>}
        </Stack>

        {open && <NewAddMainForm onClose={() => setOpen(false)} options={options} tenantId={tenantId} onSuccess={onSuccess} />}
        {syncOpen && <SyncDistributionForm onClose={() => setSyncOpen(false)} tenantId={tenantId} onSuccess={onSuccess} />}
    </Stack>;
};

export default TopAdd;
