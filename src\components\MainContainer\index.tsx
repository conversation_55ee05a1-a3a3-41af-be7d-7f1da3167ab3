/**
 * 字典管理主容器组件
 * 包含左侧树形结构和右侧表格展示
 */

import { Stack, Paper, Button } from '@mui/material'
import SimpleTree from '@/components/CommonComponent/SimpleTree'
import SimpleCustomGrid from '@/components/CommonComponent/SimpleCustomGrid'
import { useEffect, useState } from 'react'
import TopAdd from './TopAdd'
import { useCloudbase } from '../Context/CloudBaseContext/CloudbaseContext'
import Message from '@/util/Message';
import LoadingUtil from '@/util/LoadingUtil';
import LeftTree from './LeftTree';
import { TestData } from './data'
import { getChildren, replaceKeyInTree, convertTreeToArray } from '../../util/tool'
import request from '@/util/request'
import { getDictionaryList } from '@/services'
/**
 * 字典记录接口定义
 */
interface DictionaryRecord {
    id: string;                     // ID
    _id: string;                    // 记录ID
    name: string;                   // 名称
    value: string;                  // 值
    description?: string;           // 描述
    tenantId?: string;              // 租户ID
    createTime?: Date;              // 创建时间
    updateTime?: Date;              // 更新时间
    dictionary_group?: {            // 字典组信息
        name: string;               // 字典组名称
        _id: string;
        sort_number: number;
    };
    identify?: string | null;              // 标识
    attribute?: string | null;            // 属性
    pid?: string | undefined;       // 父节点ID
    type?: string;                  // 类型
    directory_category?: string;    // 目录类别
    status?: string;                // 状态
    sort_number?: number;           // 排序号
}

/**
 * 树节点接口定义
 */
export interface TreeNode {
    label: string;                  // 节点标签
    children: TreeNode[];           // 子节点
    _id?: string;                   // 节点ID
    id?: string;                   // 节点ID 必要字段
    data?: DictionaryRecord;        // 节点数据
    pid?: string | undefined;       // 父节点ID
}

/**
 * 将扁平数据转换为树形结构
 * @param data 扁平数据数组
 * @returns 树形结构数组
 */
const convertToTree = (data: DictionaryRecord[]): TreeNode[] => {
    // 按字典组分组
    const groupMap = new Map<string, DictionaryRecord[]>();
    data.forEach(item => {
        if (item.dictionary_group) {
            const groupName = item.dictionary_group.name;
            if (!groupMap.has(groupName)) {
                groupMap.set(groupName, []);
            }
            groupMap.get(groupName)?.push(item);
        }
    });

    // 构建树形结构
    return Array.from(groupMap.entries()).map(([groupName, items]) => {
        // 获取字典组的ID
        const groupId = items[0]?.dictionary_group?._id;

        // 找出该组下的顶级节点（没有父节点的节点）并按sort_number排序
        const rootNodes = items
            .filter(item => !item.pid)
            .sort((a, b) => (a.sort_number ?? 0) - (b.sort_number ?? 0));

        // 递归构建子节点
        const buildChildren = (parentId: string): TreeNode[] => {
            return items
                .filter(item => item.pid === parentId)
                .sort((a, b) => (a.sort_number ?? 0) - (b.sort_number ?? 0))
                .map(item => ({
                    label: item.name,
                    _id: item._id,
                    id: item._id,
                    pid: item.pid,
                    data: item,
                    children: buildChildren(item._id)
                }));
        };

        // 构建顶级节点
        return {
            label: groupName,
            _id: groupId,
            id: groupId,
            pid: '',
            children: rootNodes.map(node => ({
                label: node.name,
                _id: node._id,
                id: node._id,
                pid: groupId,
                data: node,
                children: buildChildren(node._id)
            }))
        };
    });
};

/**
 * 字典管理主容器组件
 */
const MainContainer = () => {
    // 状态定义
    const [treeData, setTreeData] = useState<any[]>([]);        // 树形数据
    const [allData, setAllData] = useState<any[]>([]);          // 所有原始数据
    const [gridData, setGridData] = useState<any[]>([]);        // 表格数据
    const [tenantId, setTenantId] = useState<string>('');        // 租户ID
    const [selectedNode, setSelectedNode] = useState<any>(null); // 当前选中节点

    const { cloudbaseApp } = useCloudbase();

    // useEffect(() => {
    //     const newData = TestData.map((item) => {
    //         if (item.pid === '0') {
    //             const children = getChildren(TestData, item.id);
    //             return {
    //                 ...item,
    //                 children
    //             }
    //         } else {
    //             return null
    //         }
    //     }).filter(Boolean)

    //     // 替换pid为old_pid，保留原始关系
    //     replaceKeyInTree(newData, 'pid', 'old_pid');

    //     // 将树结构转换为层级数组
    //     const convertData = convertTreeToArray(newData, 'children');
    //     const [level1, level2, level3, level4, level5] = convertData;

    //     // 创建一级节点ID到new_id的映射
    //     const level1IdToNewIdMap: Record<string, string> = {};
    //     level1.forEach((item: any) => {
    //         if (item && item.id && item.new_id) {
    //             level1IdToNewIdMap[item.id] = item.new_id;
    //         }
    //     });

    //     // 创建所有节点的ID到父节点ID的映射
    //     const idToParentIdMap: Record<string, string> = {};
    //     const allNodes = [...(level2 || []), ...(level3 || []), ...(level4 || []), ...(level5 || [])];
    //     allNodes.forEach((node: any) => {
    //         if (node && node.id && node.old_pid) {
    //             idToParentIdMap[node.id] = node.old_pid;
    //         }
    //     });

    //     // 递归查找节点的根节点ID
    //     const findRootNodeId = (nodeId: string): string | null => {
    //         let currentId = nodeId;
    //         while (currentId) {
    //             // 如果当前节点是一级节点，返回其new_id
    //             if (level1IdToNewIdMap[currentId]) {
    //                 return level1IdToNewIdMap[currentId];
    //             }
    //             // 否则继续向上查找父节点
    //             currentId = idToParentIdMap[currentId];
    //         }
    //         return null;
    //     };

    //     // 为节点添加dictionary_group信息
    //     const processLevel = (level: any[] | undefined): any[] => {
    //         if (!level || !Array.isArray(level)) return [];

    //         return level.map((item: any) => {
    //             if (!item) return null;

    //             // 查找根节点ID
    //             const rootNodeId = findRootNodeId(item.id);

    //             // 如果找到根节点ID，添加dictionary_group
    //             if (rootNodeId) {
    //                 // 构建完整的数据对象
    //                 return {
    //                     tenant_id: "product_center",  // 添加租户ID
    //                     type: "SYSTEM_DIRECTORY",  // 添加类型
    //                     status: "ENABLED",  // 添加状态
    //                     dictionary_group: {
    //                         _id: rootNodeId
    //                     },
    //                     identify: item.identify || null,
    //                     attribute: item.attribute || null,
    //                     description: item.label || "",
    //                     sort_number: item.sortNumber || 1,
    //                     name: item.label || "",
    //                     directory_category: item.category || "DIRECTORY_RECORD",
    //                     // 不设置pid，让云函数处理父子关系
    //                     old_id: item.id,  // 保存原始ID，用于后续处理
    //                     old_pid: item.old_pid  // 保存原始父ID，用于后续处理
    //                 };
    //             }

    //             return null;
    //         }).filter(Boolean);
    //     };

    //     // 处理各层级节点
    //     const processedLevel2 = processLevel(level2);
    //     const processedLevel3 = processLevel(level3);
    //     const processedLevel4 = processLevel(level4);
    //     const processedLevel5 = processLevel(level5);

    //     // 合并所有处理后的节点
    //     const allProcessedNodes = [
    //         ...processedLevel2,
    //         ...processedLevel3,
    //         ...processedLevel4,
    //         ...processedLevel5
    //     ].filter(Boolean);
    //     console.log(allProcessedNodes);
    //     // 导入数据到云数据库
    //     if (cloudbaseApp && allProcessedNodes.length > 0) {
    //         LoadingUtil.load({ openInfo: true, messages: '导入数据中...' });
    //         cloudbaseApp.callFunction({
    //             name: 'information-dictionary-crud',
    //             data: {
    //                 action: 'importCreateMany',
    //                 data: allProcessedNodes
    //             }
    //         }).then((res: any) => {
    //             LoadingUtil.load({ openInfo: false });
    //             if (res?.result?.code === 0) {
    //                 Message.success(`成功导入 ${res.result.data.count} 条数据`);
    //                 // 刷新数据
    //                 refreshData();
    //             } else {
    //                 Message.error(res?.result?.message || '导入失败');
    //             }
    //         }).catch((err: any) => {
    //             LoadingUtil.load({ openInfo: false });
    //             Message.error(err.message || '导入失败');
    //         });
    //     }
    // }, [cloudbaseApp])
    const importDataToCloud = () => {
        const newData = TestData.map((item) => {
            if (item.pid === '0') {
                const children = getChildren(TestData, item.id);
                return {
                    ...item,
                    children
                }
            } else {
                return null
            }
        }).filter(Boolean)

        // 替换pid为old_pid，保留原始关系
        replaceKeyInTree(newData, 'pid', 'old_pid');

        // 将树结构转换为层级数组
        const convertData = convertTreeToArray(newData, 'children');
        const [level1, level2, level3, level4, level5] = convertData;

        // 创建一级节点ID到new_id的映射
        const level1IdToNewIdMap: Record<string, string> = {};
        level1.forEach((item: any) => {
            if (item && item.id && item.new_id) {
                level1IdToNewIdMap[item.id] = item.new_id;
            }
        });

        // 创建所有节点的ID到父节点ID的映射
        const idToParentIdMap: Record<string, string> = {};
        const allNodes = [...(level2 || []), ...(level3 || []), ...(level4 || []), ...(level5 || [])];
        allNodes.forEach((node: any) => {
            if (node && node.id && node.old_pid) {
                idToParentIdMap[node.id] = node.old_pid;
            }
        });

        // 递归查找节点的根节点ID
        const findRootNodeId = (nodeId: string): string | null => {
            let currentId = nodeId;
            while (currentId) {
                // 如果当前节点是一级节点，返回其new_id
                if (level1IdToNewIdMap[currentId]) {
                    return level1IdToNewIdMap[currentId];
                }
                // 否则继续向上查找父节点
                currentId = idToParentIdMap[currentId];
            }
            return null;
        };

        // 为节点添加dictionary_group信息
        const processLevel = (level: any[] | undefined): any[] => {
            if (!level || !Array.isArray(level)) return [];

            return level.map((item: any) => {
                if (!item) return null;

                // 查找根节点ID
                const rootNodeId = findRootNodeId(item.id);

                // 如果找到根节点ID，添加dictionary_group
                if (rootNodeId) {
                    // 构建完整的数据对象
                    return {
                        tenant_id: "product_center",  // 添加租户ID
                        type: "SYSTEM_DIRECTORY",  // 添加类型
                        status: "ENABLED",  // 添加状态
                        dictionary_group: {
                            _id: rootNodeId
                        },
                        identify: item.identify || null,
                        attribute: item.attribute || null,
                        description: item.label || "",
                        sort_number: item.sortNumber || 1,
                        name: item.label || "",
                        directory_category: item.category || "DIRECTORY_RECORD",
                        // 不设置pid，让云函数处理父子关系
                        old_id: item.id,  // 保存原始ID，用于后续处理
                        old_pid: item.old_pid  // 保存原始父ID，用于后续处理
                    };
                }

                return null;
            }).filter(Boolean);
        };

        // 处理各层级节点
        const processedLevel2 = processLevel(level2);
        const processedLevel3 = processLevel(level3);
        const processedLevel4 = processLevel(level4);
        const processedLevel5 = processLevel(level5);

        // 合并所有处理后的节点
        const allProcessedNodes = [
            ...processedLevel2,
            ...processedLevel3,
            ...processedLevel4,
            ...processedLevel5
        ].filter(Boolean);
        console.log(allProcessedNodes);
        // 导入数据到云数据库
        if (cloudbaseApp && allProcessedNodes.length > 0) {
            LoadingUtil.load({ openInfo: true, messages: '导入数据中...' });
            cloudbaseApp.callFunction({
                name: 'information-dictionary-crud',
                data: {
                    action: 'importCreateMany',
                    data: allProcessedNodes
                }
            }).then((res: any) => {
                LoadingUtil.load({ openInfo: false });
                if (res?.result?.code === 0) {
                    Message.success(`成功导入 ${res.result.data.count} 条数据`);
                    // 刷新数据
                    refreshData();
                } else {
                    Message.error(res?.result?.message || '导入失败');
                }
            }).catch((err: any) => {
                LoadingUtil.load({ openInfo: false });
                Message.error(err.message || '导入失败');
            });
        }
    }
    /**
     * 获取选中节点的直接子节点数据
     * @param node 选中的节点
     * @returns 子节点数据数组
     */
    const getDirectChildren = (node: any) => {
        if (!node) return [];
        // 如果是根节点（字典组），返回该组下的所有二级节点
        if (!node.pid) {
            return allData
                .filter(item => item.dictionary_group?._id === node._id && !item.pid)
                .sort((a, b) => (a.sort_number ?? 0) - (b.sort_number ?? 0))?.map((item: any) => {
                    return {
                        ...item,
                        status: item.status === 'ENABLED' ? '启用' : '禁用',
                        type: item.type === 'SYSTEM_DIRECTORY' ? '系统字典' : '业务字典',
                        childrenCount: allData.filter(child => child.pid === item._id).length,
                        createdAt: new Date(item.createdAt).toLocaleString()
                    }
                })
        }
        // 否则返回直接子节点
        return allData
            .filter(item => item.pid === node._id)
            .sort((a, b) => (a.sort_number ?? 0) - (b.sort_number ?? 0))?.map((item: any) => {
                return {
                    ...item,
                    status: item.status === 'ENABLED' ? '启用' : '禁用',
                    type: item.type === 'SYSTEM_DIRECTORY' ? '系统字典' : '业务字典',
                    childrenCount: allData.filter(child => child.pid === item._id).length,
                    createdAt: new Date(item.createdAt).toLocaleString()
                }
            })
    };

    /**
     * 刷新数据的函数
     * 从服务器获取最新数据并更新状态
     */
    const refreshData = async () => {
        if (!cloudbaseApp || !tenantId) return;
        LoadingUtil.load({ openInfo: true, messages: '加载中...' })
        try {
            let allData: DictionaryRecord[] = [];
            // let currentPage = 1;
            // let hasMore = true;
            // const pageSize = 200;

            // // 分页获取所有数据
            // while (hasMore) {
            //     const res = await cloudbaseApp.callFunction({
            //         name: 'information-dictionary-crud',
            //         data: {
            //             action: 'list',
            //             data: {
            //                 pageNumber: currentPage,
            //                 pageSize: pageSize,
            //                 tenantId
            //             }
            //         }
            //     });
            //     if (res?.result.code === 0) {
            //         const { records, total } = res.result.data;
            //         allData = [...allData, ...records];
            //         // 判断是否还有更多数据
            //         hasMore = allData.length < total;
            //         currentPage++;
            //     } else {
            //         console.error('获取字典数据失败:', res?.result.message);
            //         break;
            //     }
            // }

            const res = await getDictionaryList();
            if (res.code === 200) {
                console.log(res);

                allData = res.data;
            };
            console.log('allData', allData);
            // 按dictionary_group中的sort_number字段排序，由小到大
            allData.sort((a, b) => {
                const sortA = a.dictionary_group?.sort_number || 0;
                const sortB = b.dictionary_group?.sort_number || 0;
                return sortA - sortB;
            });

            // 转换为树形结构
            const treeData = convertToTree(allData);

            // 设置状态
            setTreeData(treeData);
            setAllData(allData);
            // 如果有选中的节点，更新表格数据
            if (selectedNode) {
                setGridData(getDirectChildren(selectedNode));
            } else {
                // 如果没有选中的节点，默认选中第一个根节点
                if (treeData.length > 0) {
                    setSelectedNode(treeData[0]);
                    setGridData(getDirectChildren(treeData[0]));
                }
            }
            LoadingUtil.load({ openInfo: false })
        } catch (error) {
            console.error('获取字典数据失败:', error);
            Message.error('获取数据失败');
            LoadingUtil.load({ openInfo: false })
        }
    };

    // 表格列配置
    const columns = [
        {
            binding: 'sort_number',
            header: '序号',
            width: 80,
        },
        {
            binding: 'name',
            header: '名称',
            width: 150
        },
        {
            binding: 'attribute',
            header: '唯一编码',
            width: 150
        },
        {
            binding: 'type',
            header: '分类',
            width: 120
        },
        {
            binding: 'status',
            header: '状态',
            width: 100
        },
        {
            binding: 'childrenCount',
            header: '子项数',
            width: 100
        },
        {
            binding: 'createdAt',
            header: '创建时间',
            width: 180
        },
        {
            binding: 'description',
            header: '描述',
            width: '*'
        }
    ];

    // 初始加载数据
    useEffect(() => {
        tenantId && refreshData();
    }, [tenantId]);

    /**
     * 获取用户租户信息
     */
    const getUserTenant = async () => {
        try {
            // const user = await cloudbaseApp?.auth()?.getUserInfo();
            const results = await request.get("/account/getLoginUserTenant");
            if (results.code === 200) {
                setTenantId(results.data?.latestVisitTenant?.tenantId);
            }
            // console.log('res', results);
            // //@ts-ignore
            // let result = await cloudbaseApp?.callContainer({
            //     name: 'account',
            //     method: 'POST',
            //     path: '/getLoginUserInfo',
            //     data: {
            //         userId: user?.uid
            //     }
            // })
            // setTenantId(result.data.tenantInfo.corpCode);
        } catch (error) {
            console.error('获取用户所在商家时出错:', error);
        }
    }

    // 初始化时获取用户租户
    useEffect(() => {
        cloudbaseApp && getUserTenant()
    }, [cloudbaseApp])

    return (
        <Stack direction="row" sx={{
            height: '100vh',
            width: '100%',
            overflow: 'hidden'
        }}>
            {/* 左侧树形结构 */}
            <Paper
                sx={{
                    minWidth: '400px',
                    maxWidth: '400px',
                    flex: '0 0 320px',
                    p: 2,
                    borderRight: 1,
                    borderColor: 'divider',
                    display: 'flex',
                    flexDirection: 'column',
                    overflow: 'hidden'
                }}
            >
                {/* <Button variant="contained" color="primary" onClick={importDataToCloud}>测试数据</Button> */}
                <TopAdd tenantId={tenantId} options={treeData} onSuccess={refreshData} />
                <LeftTree
                    options={treeData}
                    tenantId={tenantId}
                    onRefresh={refreshData}
                    onSelect={(node) => {
                        setSelectedNode(node);
                        setGridData(getDirectChildren(node));
                    }}
                />
            </Paper>
            {/* 右侧表格 */}
            <Paper
                sx={{
                    flex: 1,
                    p: 2,
                    overflow: 'hidden',
                    minWidth: 0
                }}
            >
                <SimpleCustomGrid
                    itemsSource={gridData}
                    realItemsSource={gridData}
                    columnSet={columns}
                    unit={'条'}
                />
            </Paper>
        </Stack>
    );
}

export default MainContainer;