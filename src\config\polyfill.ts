/**
 * 浏览器兼容性垫片
 * 提供解决不同浏览器问题的polyfills
 * <AUTHOR>
 * @date 2024/12/20
 */

/**
 * 修复Chrome浏览器触摸事件的passive警告
 * 
 * Chrome默认将触摸事件监听器设置为passive=true，这可能导致页面无法正常滚动
 * 此polyfill通过重写addEventListener方法，为特定的事件类型默认设置passive=false
 * 
 * 涉及的事件类型:
 * - touchstart, touchmove, touchend, touchcancel: 触摸事件
 * - scroll: 滚动事件
 * - pointerdown, pointermove, pointerup, pointercancel: 指针事件
 * - wheel: 滚轮事件
 */
(function () {
    if (typeof EventTarget !== 'undefined') {
        const originalEventListener = EventTarget.prototype.addEventListener
        EventTarget.prototype.addEventListener = function (type, fn, capture) {
            if (['touchstart', 'touchmove', 'touchend', 'touchcancel', 'scroll', 'pointerdown', 'pointermove', 'pointerup', 'pointercancel', 'wheel'].includes(type)) {
                if (typeof capture !== 'boolean') {
                    capture = capture || {}
                    capture.passive = false;
                }
                if (capture === false) { //tinymce中的capture给了默认的false
                    let newCapture: any = {}
                    newCapture.passive = false;
                    return originalEventListener.call(this, type, fn, newCapture)
                }
            }
            return originalEventListener.call(this, type, fn, capture)
        }
    }
})();