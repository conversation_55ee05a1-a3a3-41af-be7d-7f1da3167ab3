/**
 * 主题配置文件
 * 定义全局Material-UI组件的样式覆盖和默认属性
 * <AUTHOR>
 * @date 2022/05/10
 */
import { ThemeOptions } from '@mui/material/styles';

/**
 * 主题配置对象
 * 包含调色板定义和各个Material-UI组件的样式覆盖
 */
const themeConfig: ThemeOptions = {
  // 调色板配置，定义主色和次色
  palette: {
    primary: {
      light: '#477efe',
      main: '#2b6bff',
      dark: '#0f57ff',
      contrastText: '#fff',
    },
    secondary: {
      light: '#f73378',
      main: '#f50057',
      dark: '#ab003c',
      contrastText: '#000',
    },
  },
  // 组件样式覆盖
  components: {
    // 背景层组件
    MuiBackdrop: {
      styleOverrides: {
        root: { backgroundColor: 'rgba(255,255,255,0.08)' },
      },
    },
    // 对话框组件
    MuiDialog: {
      defaultProps: {
        disableEnforceFocus: true,
        PaperProps: {
          sx: {
            minWidth: '480px',
          },
        },
      },
    },
    // 自动完成组件
    MuiAutocomplete: {
      styleOverrides: {
        option: {
          fontSize: '12px',
        },
        noOptions: {
          fontSize: '12px',
        },
      },
    },
    // 输入基础组件
    MuiInputBase: {
      styleOverrides: {
        input: {
          fontSize: 12,
        },
        root: {
          borderRadius: '0 !important',
        },
      },
    },
    // 选择器组件
    MuiSelect: {
      styleOverrides: {
        nativeInput: {
          fontSize: 12,
        },
        select: {
          paddingTop: 0,
          paddingBottom: 0,
        },
      },
    },
    // 按钮组件
    MuiButton: {
      defaultProps: {
        variant: 'outlined',
        size: 'small',
      },
      styleOverrides: {
        root: {
          borderRadius: 0,
          boxShadow: 'none',
          ':hover': {
            boxShadow: 'none',
          },
          '&.Mui-disabled': {
            color: 'white',
            backgroundColor: '#2b6bff9a'
          }
        },

      },
      variants: [
        {
          props: {
            size: 'small',
          },
          style: {
            fontSize: '0.75rem',
          },
        },
        {
          props: {
            size: 'medium',
          },
          style: {
            fontSize: '0.875rem',
          },
        },
        {
          props: {
            size: 'large',
          },
          style: {
            fontSize: '1rem',
          },
        },
        // {
        //   props: {
        //     variant: 'outlined',
        //   },
        //   style: {
        //     color: '#333',
        //     border: '1px solid #d9d9d9',
        //     ':hover': {
        //       border: '1px solid #d9d9d9',
        //     },
        //   },
        // },
      ],
    },
    // 列表组件
    MuiList: {
      styleOverrides: {
        root: {
          padding: '0.25rem 0',
        },
      },
    },
    // 菜单项组件
    MuiMenuItem: {
      styleOverrides: {
        root: {
          fontSize: 12,
        },
      },
    },
    // 树形组件项
    // @ts-ignore
    MuiTreeItem: {
      styleOverrides: {
        label: {
          fontSize: 12,
        },
      },
    },
    // 排版组件
    MuiTypography: {
      styleOverrides: {
        root: {
          fontSize: 12,
          whiteSpace: 'nowrap',
        },
      },
    },
    // 复选框组件
    MuiCheckbox: {
      defaultProps: {
        size: 'small',
      },
    },
    // 轮廓输入组件
    MuiOutlinedInput: {
      styleOverrides: {
        input: {
          paddingTop: 4,
          paddingBottom: 5,
        },
      },
    },
  },
};

export default themeConfig;
