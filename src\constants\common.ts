//主题颜色设置
export const ThemeColor = {
  dark: '#0f57ff',
  //在没有固定颜色的时候统一使用的默认主色
  main: '#2b6bff',
  light: '#477efe',
  bgMain: '#2b6bff1a',
  borderMain: "#2b6bff33"
};
//默认标签颜色
export const tagDefaultColor = '#1976d2';
export const tagDefaultAttr = {
  width: "7rem",
  height: '1.5rem',
  color: '#1976d2'
}

export const ColorPickerInitColor = '#333333';

export const tagSmallAttr = {
  width: "6rem",
  height: '1.25rem',
}

export const commonChipSx = {
  mr: 1,
  mt: 1,
  fontSize: 12,
  // width: tagDefaultAttr.width,
  width: 'calc((100% - 40px)/5)',
  // minWidth:'calc((100% - 40px)/5)',
  height: tagDefaultAttr.height,
  '.MuiChip-deleteIcon ': { fontSize: 14, m: 0 },
  '.MuiChip-label': { lineHeight: tagDefaultAttr.height, },
};
//选择颜色面板的默认颜色设置
export const ColorPickerDefaultColor = ['#333333', '#ff6900', '#fcb900', '#7bdcb5', '#00D084', '#8ed1fc', '#0693e3', '#abb8c3', '#eb144c', '#f78da7', '#9900ef', '#1d999f',];

export const primaryColor = '#0f57ff'

//基础搜索框的尺寸大小
export enum BaseInputSx {
  selectAndInputHeight = '1.75rem',
  selectAndInputWidth = '16rem'
}

export enum dispatchFormColor {
  radioAndCheckboxColor = '#2b6bff'
}

//新增订单
export const addOrderFormSx = {
  richText_height: 200,
  richText_bgColor_readonly: '#fbfbfb!important',
  richText_height_readonly: 150,
}

//长文本文字样式
export const longtextStyle = {
  letterSpacing: '1px',
  lineHeight: '1.5rem'
}

export const TARGET_RESULT_PARAM = {
  customer: 'customer',
  order: 'order',
  no: 'no'
}

export const TimeFormat = 'yyyy-MM-dd HH:mm'

export const developUrl = 'http://*************'

const isDevelop = process.env.NODE_ENV === 'development'

/**
 * 自身应用路径
 */
export const customerHandleAppUrl = isDevelop ? 'http://*************/customer-handle/' : '/customer-handle/'

//活动页面的路径
// export const promotionTargetUrl = '/promotion/';
export const promotionTargetUrl = isDevelop ? 'http://*************/enable-promotions/' : '/enable-promotions/';


//地图页面地址
// export const mapTargetUrl = process.env.NODE_ENV === 'development' ?  'http://192.168.0.51:3000/?map=true'  : '/building-management/?map=true'
export const mapTargetUrl = isDevelop ? 'http://*************/building-management/?map=true' : '/building-management/?map=true'

//派单信息页面的路径
export const dispatchInfoTargetUrl = isDevelop ? 'http://*************/dispatch-management/' : '/dispatch-management/'

//合同查看详情页面路径

export const contractTargetUrl = isDevelop ? 'http://*************/design-solution/' : '/design-solution/'
//http://*************/design-solution/

//往来账单页面路径
export const currentBillTargetUrl = isDevelop ? 'http://*************/receivables-payable-bill/#/' : '/receivables-payable-bill/#/'

//新增收款页面路径
export const newReceiptTargetUrl = isDevelop ? 'http://*************/receipt-bill' : '/receipt-bill'

//新建报价所要到的页面路径
export const newAddQuotationAppUrl = isDevelop ? 'http://*************/design-solution/' : '/design-solution/'

//审合同页面
export const checkContractAppUrl = isDevelop ? 'http://*************/contract-check-management/' : '/contract-check-management/'

//日程应用
export const scheduleManagementAppUrl = isDevelop ? 'http://*************/schedule-management/' : '/schedule-management/'

//报价工具页面路径 新版
export const solutionDesignManagementAppUrl = isDevelop ? 'http://*************/solution-design-management/' : '/solution-design-management/'

// 旧版报价工具页面路径
export const oldOpennessDesignManagementAppUrl = isDevelop ? 'http://*************/openness-design-management/' : '/openness-design-management/'

//im路径
export const imAppUrl = isDevelop ? 'http://*************/im/chat/' : '/im/chat/'

//打开协作流转路径
export const DOCUMENT_CIRCULATION_AUDIT_URL = isDevelop ? 'http://*************/document-flow/' : '/document-flow/'


export const subApplicationEventEnum = {
  changeClose: 'change-close',//改变iframe的X按钮 显示
  closeDialog: 'close-dialog',//关闭dialog
}

/**
 * 客户应用
 */
export const customerManagementAppUrl = isDevelop ? 'http://*************/customer-management/' : '/customer-management/'

/**
 * 采购
 */
export const orderProcurementUrl = isDevelop ? 'http://*************/order-procurement/#/purchasInfo' : '/order-procurement/#/purchasInfo'

//责权
export const responsibilityAuthorityAppUrl = isDevelop ? 'http://*************/claim-management/' : '/claim-management/'

//特权单
export const privilegeManagementAppUrl = isDevelop ? 'http://*************/privilege-management/' : '/privilege-management/'

//合同管理页面
export const contractManagementUrl = isDevelop ? 'http://*************/contract-management-overview/' : '/contract-management-overview/'

export const abnormalFillOrderAppUrl = isDevelop ? 'http://*************/abnormal-report' : '/abnormal-report'

//报价页签要用的页面
export const quoteTabAppUrl = isDevelop ? 'http://*************/quotation-mobile/#/pagesQuotation/pages/QuotationList/index' : '/quotation-mobile/#/pagesQuotation/pages/QuotationList/index'

/**
 * 预算报价
 */
export const budgetQuoteAppUrl = isDevelop ? 'http://*************/budget-quotation/' : '/budget-quotation/'

/**
 * 送货详情
 */
export const deliveryHandingAppUrl = isDevelop ? 'http://*************/after-sales-service/#/pageInstallationScheduling/delivery-details/delivery-details' : '/after-sales-service/#/pageInstallationScheduling/delivery-details/delivery-details'

/**
 * 安装详情
 */
export const installDetailsAppUrl = isDevelop ? 'http://*************/after-sales-service/#/pageInstallationScheduling/dispatch-order-detail/dispatch-order-detail' : '/after-sales-service/#/pageInstallationScheduling/dispatch-order-detail/dispatch-order-detail'

/**
 * 售后详情
 */
export const afterSaleDetailsAppUrl = isDevelop ? 'http://*************/after-sales-service/#/pageInstallationSign/after-sales-declaration-detail/after-sales-declaration-detail' : '/after-sales-service/#/pageInstallationSign/after-sales-declaration-detail/after-sales-declaration-detail'


/**
 * 项目管理核算
 */
export const ProjectManageAccountAppUrl = isDevelop ? 'http://*************/project-management/#/project-accounting-management/task-list/' : '/project-management/#/project-accounting-management/task-list/'

/**
 * 满意度交付
 */
export const installSatisfactionAppUrl = isDevelop ? 'http://*************/after-sales-service/#/pageInstallationSign/satisfaction-delivery-form/satisfaction-delivery-form' : '/after-sales-service/#/pageInstallationSign/satisfaction-delivery-form/satisfaction-delivery-form'

/**
 * 创建群聊
 */
export const CreateGroupChatUrl = isDevelop ? 'http://*************/im/create-group' : '/im/create-group'


/**
 * 售后报单
 */
export const AfterSaleFillOrderAppUrl = isDevelop ? 'http://*************/delivery-scheduling-service/targetCustomerDeclaration' : '/delivery-scheduling-service/targetCustomerDeclaration'


/**
 * 项目页签
 */
export const CustomerProjectAppUrl = isDevelop ? 'http://*************/create-project-middle/#/pageCollection/orderProjectList/order-project-list' : '/create-project-middle/#/pageCollection/orderProjectList/order-project-list'