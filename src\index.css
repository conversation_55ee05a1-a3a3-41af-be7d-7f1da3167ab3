/* 全局样式 */
html, body, #root {
  margin: 0;
  padding: 0;
  height: 100%;
}

/* 移动设备横屏设置 */
@media screen and (max-width: 600px) and (orientation: portrait) {
  #root {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vh;
    height: 100vw;
    transform-origin: top left;
    transform: rotate(90deg) translate(0, -100%);
  }

  body {
    overflow: hidden;
  }
} 

/*滚动条的设置*/
div ::-webkit-scrollbar {
  width: 7.5px;
  height: 7.5px;
}

/* 滚动条的滑块 */
div ::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 5px transparent;
  border-radius: 10px;
  background-color: transparent;
}

div :hover::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 5px #e3e7ef;
  background-color: #c1c1c1;
}

/* 滚动条轨道背景色 */
div ::-webkit-scrollbar-track{
  box-shadow: inset 0 0 5px transparent;
  background-color: transparent;
  border-radius: 10px;
}

/*:hover::-webkit-scrollbar-track {*/
/*  box-shadow: inset 0 0 5px #E1E1E1;*/
/*  background-color: #E1E1E1;*/
/*}*/

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5);
}


input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{
  -webkit-appearance: none;
  -moz-appearance: none;
}

input[type='number'] {
  -moz-appearance: textfield;
}

input[type="checkbox"] {
  background-color: #2b6bff;
}