import { Outlet } from 'umi';
import { ThemeProvider, createTheme, Stack } from '@mui/material';
import { zhCN } from '@mui/material/locale';
import themeConfig from '../config/themeConfig';
import { EventProvider } from '../components/Context/EventContext/EventContext';
import '../index.css';
import { CloudbaseProvider } from '@/components/Context/CloudBaseContext/CloudbaseContext';
import Loading from '@/components/CommonComponent/Loading';
import Notification from '@/components/CommonComponent/Notification';
import { Provider } from 'react-redux';
import store from '@/redux/store';
export default function UmiLayoutWrapper() {
  // 在组件中直接创建theme，确保它在组件渲染时可用
  const theme = createTheme(themeConfig, zhCN);

  return (
    <ThemeProvider theme={theme}>
      <Provider store={store}>
        <EventProvider>
          <CloudbaseProvider>
            <Stack sx={{
              minHeight: '100vh',
              width: '100%'
            }}>
              <Outlet />
            </Stack>
            <Loading />
            <Notification />
          </CloudbaseProvider>
        </EventProvider>
      </Provider>
    </ThemeProvider >
  );
}
