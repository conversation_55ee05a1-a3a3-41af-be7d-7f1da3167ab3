/**
 * @description Redux Store配置
 * <AUTHOR> Management Team
 * @date 2022
 * 本文件配置Redux Store，整合所有的reducer切片并导出类型定义
 */
import { configureStore } from '@reduxjs/toolkit';
import dataSlice from './slice/dataSlice';

/**
 * 配置Redux Store
 * 组合所有reducer切片，并配置中间件
 */
const store = configureStore({
  // 合并所有reducer
  reducer: {
    data: dataSlice,
  },
  // // 配置中间件
  // middleware: (getDefaultMiddleware) => getDefaultMiddleware({ 
  //   // 关闭序列化检查，为了存储表格flexgrid。用于工具栏导出
  //   serializableCheck: false,
  // }),
});

/**
 * 导出RootState类型
 * 用于在组件中使用useSelector时提供类型支持
 */
export type RootState = ReturnType<typeof store.getState>;

/**
 * 导出AppDispatch类型
 * 用于在组件中使用useDispatch时提供类型支持
 */
export type AppDispatch = typeof store.dispatch;

export default store;
