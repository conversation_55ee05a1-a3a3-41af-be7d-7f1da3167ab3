import request from '@/util/request';

export const getTenantList = async () => {
  return await request.get('/account/getTenantList', {}, { useBaseUrl: false });
};

export const getDictionaryList = async () => {
  return await request.get('/dictionary/dict-query', {}, { useBaseUrl: false });
};

export const syncDictionaryList = async (data: any) => {
  return await request.post(`/dictionary/dict-sync/`, data, {
    useBaseUrl: false,
  });
};
