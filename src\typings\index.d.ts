// 微信小程序全局对象
declare namespace wx {
  interface Cloud {
    init(config?: any): Cloud;
    callFunction(options: {
      name: string;
      data?: any;
      success?: (res: any) => void;
      fail?: (err: any) => void;
      complete?: () => void;
    }): Promise<any>;
  }

  const cloud: Cloud;
}

// 云开发SDK类型声明
interface Window {
  cloud?: {
    init(config?: any): any;
    callFunction(options: {
      name: string;
      data?: any;
      success?: (res: any) => void;
      fail?: (err: any) => void;
      complete?: () => void;
    }): Promise<any>;
  };
}

export type Attachment = {
  id: string;
  fileName: string;
  url: string;
}