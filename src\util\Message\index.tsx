/**
 * <AUTHOR>
 * @date 2022/12/23
 */
export default class Message {
	static info(option: MessageOption) {}

	static warn(option: MessageOption) {}

	static error(option: MessageOption) {}

	static success(option: MessageOption) {}
}

export type MessageOption = string | IMessageOption;

export type MessagePosition =
	| 'top-left'
	| 'top-right'
	| 'bottom-left'
	| 'bottom-right'
	| 'top-center'
	| 'bottom-center';

export interface IMessageOption {
	position?: MessagePosition;
	duration?: number;
	message?: string;
	onClose?: Function;
}
