import { redirectToLogin } from '@/components/Context/CloudBaseContext/CloudbaseContext';
import { extend, RequestOptionsInit, ResponseError } from 'umi-request';
import message from './Message';

// 定义响应数据类型
export interface ResponseData<T = any> {
  code: number;
  message: string;
  data: T;
  error?: any;
}

// 定义请求配置类型
export interface RequestConfig extends RequestOptionsInit {
  showError?: boolean; // 是否显示错误信息
  loading?: boolean; // 是否显示加载状态
  useBaseUrl?: boolean; // 是否使用baseUrl
}

// 获取API基础URL
const getBaseUrl = () => {
  return process.env.API_BASE_URL || '';
};

// 创建请求实例
const umiRequest = extend({
  prefix: '', // 请求前缀
  timeout: 100000, // 超时时间
  errorHandler: (error: ResponseError) => {
    // 全局错误处理
    console.error('请求错误:', error);
    return {
      code: 500,
      message: '请求失败',
      data: null,
      error,
    };
  },
});

// 请求拦截器
umiRequest.interceptors.request.use((url, options) => {
  // 在这里可以添加请求头、token等

  const headers = {
    ...options.headers,
    // 从localStorage或全局状态获取accessToken
    Authorization: `Bearer ${localStorage.getItem('token') || ''}`,
    // 可以添加其他请求头
  };

  return {
    url,
    options: { ...options, headers },
  };
});

// 响应拦截器
umiRequest.interceptors.response.use(async (response) => {
  // 在这里可以处理响应数据
  try {
    const data = await response.clone().json();

    // 如果响应状态码不是2xx，则抛出错误
    if (response.status < 200 || response.status >= 300) {
      if (response.status === 401) {
        redirectToLogin();
      } else {
        throw new Error(data.message || '请求失败');
      }
    }

    return response;
  } catch (error) {
    console.error('响应拦截器错误:', error);
    throw error;
  }
});

// 处理响应数据
const handleResponse = (response: any) => {
  try {
    // 检查响应对象
    if (!response) {
      throw new Error('响应对象为空');
    }

    // 如果响应已经是ResponseData格式，直接返回
    if (response.code !== undefined && response.data !== undefined) {
      if (response.code === 200) {
        return response;
      } else {
        return response;
      }
    }

    // 如果响应失败，抛出错误
    // throw new Error(response.message || '请求失败');
  } catch (error) {
    console.error('处理响应数据失败:', error);
    throw error;
  }
};

// 创建请求工具
const request = {
  /**
   * GET请求
   * @param url 请求地址
   * @param params 请求参数
   * @param config 请求配置
   * @returns Promise<T> 成功时直接返回数据，失败时返回错误响应
   */
  get: async <T = any>(
    url: string,
    params?: Record<string, any>,
    config?: RequestConfig,
  ): Promise<T> => {
    try {
      const options: RequestOptionsInit = {
        method: 'GET',
        params,
        ...config,
      };

      const fullUrl =
        config?.useBaseUrl !== false ? `${getBaseUrl()}${url}` : url;
      const response = await umiRequest(fullUrl, options);

      return handleResponse(response);
    } catch (error) {
      console.error('GET请求错误:', error);
      if (config?.showError !== false) {
        message.error('请求失败，请稍后重试');
      }
      throw error;
    }
  },

  /**
   * POST请求
   * @param url 请求地址
   * @param data 请求数据
   * @param config 请求配置
   * @returns Promise<T> 成功时直接返回数据，失败时返回错误响应
   */
  post: async <T = any>(
    url: string,
    data?: Record<string, any>,
    config?: RequestConfig,
  ): Promise<T> => {
    try {
      const options: RequestOptionsInit = {
        method: 'POST',
        data,
        ...config,
      };

      const fullUrl =
        config?.useBaseUrl !== false ? `${getBaseUrl()}${url}` : url;
      const response = await umiRequest(fullUrl, options);

      return handleResponse(response);
    } catch (error) {
      console.error('POST请求错误:', error);
      if (config?.showError !== false) {
        message.error('请求失败，请稍后重试');
      }
      throw error;
    }
  },

  /**
   * PUT请求
   * @param url 请求地址
   * @param data 请求数据
   * @param config 请求配置
   * @returns Promise<T> 成功时直接返回数据，失败时返回错误响应
   */
  put: async <T = any>(
    url: string,
    data?: Record<string, any>,
    config?: RequestConfig,
  ): Promise<T> => {
    try {
      const options: RequestOptionsInit = {
        method: 'PUT',
        data,
        ...config,
      };

      const fullUrl =
        config?.useBaseUrl !== false ? `${getBaseUrl()}${url}` : url;
      const response = await umiRequest(fullUrl, options);

      return handleResponse(response);
    } catch (error) {
      console.error('PUT请求错误:', error);
      if (config?.showError !== false) {
        message.error('请求失败，请稍后重试');
      }
      throw error;
    }
  },

  /**
   * DELETE请求
   * @param url 请求地址
   * @param data 请求数据
   * @param config 请求配置
   * @returns Promise<T> 成功时直接返回数据，失败时返回错误响应
   */
  delete: async <T = any>(
    url: string,
    data?: Record<string, any>,
    config?: RequestConfig,
  ): Promise<T> => {
    try {
      const options: RequestOptionsInit = {
        method: 'DELETE',
        data,
        ...config,
      };

      const fullUrl =
        config?.useBaseUrl !== false ? `${getBaseUrl()}${url}` : url;
      const response = await umiRequest(fullUrl, options);

      return handleResponse(response);
    } catch (error) {
      console.error('DELETE请求错误:', error);
      if (config?.showError !== false) {
        message.error('请求失败，请稍后重试');
      }
      throw error;
    }
  },
};

export default request;
