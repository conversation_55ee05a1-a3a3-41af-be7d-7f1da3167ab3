/**
 * Token 工具函数
 */

import dayjs from "dayjs";

// 常见的token存储key名称
const TOKEN_KEYS = [
  "token",
  "access_token",
  "accessToken",
  "auth_token",
  "authToken",
  "jwt_token",
  "jwtToken",
  "bearer_token",
  "Authorization",
];

/**
 * 从localStorage获取token
 * @param customKey 自定义的key名称
 * @returns token字符串或null
 */
export const getTokenFromStorage = (customKey?: string): string | null => {
  try {
    // 如果提供了自定义key，优先使用
    if (customKey) {
      const token = localStorage.getItem(customKey);
      if (token) return token;
    }

    // 遍历常见的token key
    for (const key of TOKEN_KEYS) {
      const token = localStorage.getItem(key);
      if (token) {
        return token;
      }
    }

    return null;
  } catch (error) {
    console.error("获取token失败:", error);
    return null;
  }
};

/**
 * 从sessionStorage获取token
 * @param customKey 自定义的key名称
 * @returns token字符串或null
 */
export const getTokenFromSessionStorage = (
  customKey?: string
): string | null => {
  try {
    if (customKey) {
      const token = sessionStorage.getItem(customKey);
      if (token) return token;
    }

    for (const key of TOKEN_KEYS) {
      const token = sessionStorage.getItem(key);
      if (token) {
        return token;
      }
    }

    return null;
  } catch (error) {
    console.error("获取sessionStorage token失败:", error);
    return null;
  }
};

/**
 * 解析JWT token (如果是JWT格式)
 * @param token JWT token字符串
 * @returns 解析后的payload对象或null
 */
export const parseJwtToken = (token: string): any => {
  try {
    if (!token || typeof token !== "string") return null;

    // JWT token通常以Bearer开头，需要去掉
    const cleanToken = token.replace(/^Bearer\s+/i, "");

    // JWT token由三部分组成，用.分隔
    const parts = cleanToken.split(".");
    if (parts.length !== 3) return null;

    // 解码payload部分
    const payload = parts[1];
    const decoded = atob(payload);
    return JSON.parse(decoded);
  } catch (error) {
    console.error("解析JWT token失败:", error);
    return null;
  }
};

/**
 * 检查token是否过期
 * @param token JWT token字符串
 * @returns boolean
 */
export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = parseJwtToken(token);
    if (!payload || !payload.exp) return true;

    // exp是Unix时间戳(秒)，需要转换为毫秒
    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();
    console.log(dayjs(expirationTime).format("YY-MM-DD HH:mm:ss"));
    return currentTime >= expirationTime;
  } catch (error) {
    console.error("检查token过期状态失败:", error);
    return true;
  }
};

/**
 * 获取有效的token（优先从localStorage，然后sessionStorage）
 * @param customKey 自定义key
 * @returns 有效的token或null
 */
export const getValidToken = (customKey?: string): string | null => {
  // 先从localStorage获取
  let token = getTokenFromStorage(customKey);

  // 如果localStorage没有，再从sessionStorage获取
  if (!token) {
    token = getTokenFromSessionStorage(customKey);
  }

  // 检查token是否有效
  if (token && !isTokenExpired(token)) {
    return token;
  }

  return null;
};
