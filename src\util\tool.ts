import Message from '@/util/Message';
import forEach from 'lodash/forEach';

export const isExcelFile = (file: File) => {
    const excelMineTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
    return (file.name.endsWith('.xlsx')) && excelMineTypes.includes(file.type);
}

/**
 * @description 文件下载
 * @param fileUrl 文件地址
 * @param fileName 文件名
 */
export const fileDownload = (fileUrl?: string | null, fileName?: string | null) => {

    if (fileUrl && fileName) {
        fetch(fileUrl).then((res) => {
            res.blob().then(blob => {
                const a = document.createElement('a');
                const href = window.URL.createObjectURL(blob);
                a.setAttribute('href', href);
                a.setAttribute('download', fileName);
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(href);
                document.body.removeChild(a);
            });
        }).catch((err) => {
            Message.warn(`请检查文件地址是否正确,${err}`);
        });
    }

};

//拼接上一级父节点直到没有找到父节点方法
export const handleSpecialShow = (arr: any[], pid: any, str: string = ''): any => {
    let val: any = null
    forEach(arr, (value) => {
        if (value?.id === pid) {
            str = (value?.label ? value?.label + '-' : '') + str
            val = value
            return false
        }
    })
    if (val?.pid) {
        return handleSpecialShow(arr, val?.pid, str)
    } else {
        return str
    }
}

//给出指定节点的id, 找到指定节点的2级节点
export const findSecondLevelNode = (nodeId: any, nodes: any[]) => {
    // 如果没有传入节点ID或节点数组，直接返回null
    if (!nodeId || !nodes?.length) return null;

    const nodeMap = new Map();
    const buildNodeMap = (items: any[]) => {
        items.forEach(item => {
            nodeMap.set(item.id, item);
            if (item.children?.length) {
                buildNodeMap(item.children);
            }
        });
    };
    buildNodeMap(nodes);

    let currentNode = nodeMap.get(nodeId);
    if (!currentNode) return null;

    // 如果当前节点就是根节点，返回其第一个子节点（二级节点）
    if (currentNode.pid === "0") {
        return currentNode.children?.[0] || null;
    }

    // 如果当前节点是二级节点（其父节点是根节点），直接返回当前节点
    const parentNode = nodeMap.get(currentNode.pid);
    if (parentNode?.pid === "0") {
        return currentNode;
    }

    // 向上查找直到找到二级节点
    while (currentNode && currentNode.pid !== "0") {
        const parent = nodeMap.get(currentNode.pid);
        if (parent?.pid === "0") {
            return currentNode;
        }
        currentNode = parent;
    }

    return null;
};

// 递归找到当前选中节点的根节点
export const findRootNode = (arr: any[], nodeId: string, nodes: any[]): any => {
    // 扁平化的节点中直接查找
    const node = arr.find((item: any) => item.id === nodeId);
    if (!node) return null;

    // 如果没有pid或pid为空字符串，则该节点为根节点
    if (!node.pid) return node;

    // 否则递归查找父节点
    return findRootNode(arr, node.pid, nodes);
};

/**
 * 树转数组
 * @param arr 树形数组
 * @returns 扁平化后的数组
 */
export function flatTree<T extends { children?: T[] }>(arr: T[] = []): T[] {
    if (!Array.isArray(arr)) {
        return [];
    }
    const res: T[] = [];
    res.push(...arr);
    for (const item of arr) {
        if (item.children && item.children.length) {
            res.push(...flatTree(item.children));
        }
    }
    return res;
}


/**
 * 数组转树
 * @param arr
 * @param id
 * @returns
 */
export function getChildren<T extends { children?: T[], id?: string, pid?: string }>(arr: T[], id?: string | null): T[] {
    const temp: T[] = [];
    for (const item of arr) {
        if (item.pid == id) {
            temp.push({ ...item, children: getChildren(arr, item?.id) });
        }
    }
    return temp;
}


//把树结构数组抽成 [[根节点]，[一级节点],[二级节点]...] 这种数据
export function convertTreeToArray(node: any, key: any) {
    if (!node) {
        return [];
    }
    const result = [];
    // 如果输入是数组，直接使用它作为第一层节点
    const queue = Array.isArray(node) ? [...node] : (node[key] && Array.isArray(node[key]) ? [...node[key]] : []);
    while (queue.length > 0) {
        const levelNodes = [];
        const levelSize = queue.length;
        for (let i = 0; i < levelSize; i++) {
            const current = queue.shift();
            levelNodes.push(current);
            if (current?.[key] && Array.isArray(current[key]) && current[key].length > 0) {
                queue.push(...current[key]);
            }
        }
        if (levelNodes.length > 0) {
            result.push(levelNodes);
        }
    }
    return result;
}

//替换树结构中的key
export function replaceKeyInTree(tree: any[], oldKey: string, newKey: string): any {
    if (!Array.isArray(tree)) {
        return [];
    }
    tree.forEach((item: any) => {
        if (oldKey in item) {
            item[newKey] = item[oldKey];
            delete item[oldKey];
        }
        if (Array.isArray(item.children)) {
            replaceKeyInTree(item.children, oldKey, newKey);
        }
    });
    return tree;
}
