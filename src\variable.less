//主色
@primary: #2b6bff;
@lightPrimary: #477efe;
@darkPrimary: #0f57ff;

@primaryBgcolor:#2b6bff1a;
//form表单label字体颜色
@formFontColor: #666666;

//错误文字提示信息颜色
@error: #d9534f;

//form比表单border颜色
@formBorderColor:#eeeeee;
@formBorderHoverColor: rgba(0, 0, 0, 0.42);
@formBorderFocusColor:#3f51b5;
//备注信息框背景颜色
@bgColor: #f5f5f5;
//提示文字
@promptText: #c5c8cd;

//下划线
.commonUnderLine(){
  border-bottom: 1px solid @formBorderColor;
  position: relative;
  &::before {
    border-bottom: 1px solid transparent;
    left: 0;
    bottom: 0;
    content: "\00a0";
    position: absolute;
    right: 0;
    -webkit-transition: border-bottom-color 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    transition: border-bottom-color 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    pointer-events: none;
  }
  &::after {
    border-bottom: 2px solid @darkPrimary;
    left: 0;
    bottom: 0;
    content: "";
    position: absolute;
    right: 0;
    -webkit-transform: scaleX(0);
    -moz-transform: scaleX(0);
    -ms-transform: scaleX(0);
    transform: scaleX(0);
    -webkit-transition: -webkit-transform 200ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
    transition: transform 200ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
    pointer-events: none;
  }

  &:hover:before {
    border-bottom: 2px solid @formBorderHoverColor;
  }
  &:focus-within:after {
    transform: scaleX(1)
  }
}
